/* eslint-disable max-len */
import { Col, ColProps, Row, RowProps, Space, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDocumentDataFragment } from '../../../../api/fragments/ApplicationDocumentData';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import {
    ApplicationDocumentKind,
    CompanyTheme,
    CustomerKind,
    DrivingLicenseType,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    LocalCustomerManagementModule,
    PorscheIdData,
} from '../../../../api/types';
import PorscheIDLogin from '../../../../components/PorscheID/PorscheIDLogin';
import SearchCapCustomerButton from '../../../../components/cap/searchCustomersAndLeads/SearchCapCustomerButton';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import CheckboxField from '../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../themes/hooks';
import { allowedExtensions } from '../../../../utilities/extensions';
import { KycPresetFieldsRenderer } from '../../../../utilities/kycPresets';
import isKYCPresetHasCurrentVehicleFields from '../../../../utilities/kycPresets/isKYCPresetHasCurrentVehicleFields';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import { CheckboxContainer } from '../../../portal/ConfiguratorApplicationEntrypoint/ApplicantKYCPage/VehicleInterest';
import { Title } from '../../../portal/EventApplicationEntrypoint/ApplicantForm/shared';
import type { KYCJourneyValues } from '../../../portal/StandardApplicationEntrypoint/KYCPage/shared';
import GroupedCustomerDetails, {
    defaultFieldsColSpan,
} from '../../../portal/StandardApplicationEntrypoint/KYCPage/shared';
import KycUploadDocumentField from '../../../portal/StandardApplicationEntrypoint/KYCPage/uploadDocuments/KycUploadDocumentField';
import { useSearchCapCustomerContext } from '../C@P/SearchCapCustomerOnKYC/ContextManager';
import ResetKYCButton from './ResetKYCButton';
import { TradeInVehicleDetails } from './TradeInVehicleItem';
import { allowedCountryForSections, HeaderContainer, OccupySpace } from './shared';

const isDrivingLicenseFieldDisabled = values => {
    if (values.length > 0) {
        const value = values[0];
        if (value.type === DrivingLicenseType.Qualified) {
            return value.validity && value.expiryDate && value.class && value.issueDate;
        }

        if (value.type === DrivingLicenseType.NotApplicable) {
            return true;
        }

        return false;
    }

    return false;
};

const isUaeDrivingLicenseFieldDisabled = values => {
    if (values.length > 0) {
        const value = values[0];
        const hasNessararyValue =
            value.expiryDate && value.issueDate && value.uploadDLCopy && value.uploadEIDPassportCopy;

        if (value.isUAEResident) {
            return hasNessararyValue && value.emiratesId;
        }

        return hasNessararyValue && value.passport && value.issuedCountry;
    }

    return false;
};

export type CustomerDetailsProps = {
    // active selected preset
    // can be from applicant or corporate
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    showRemarks: boolean;
    showCommentsToInsurer?: boolean;
    // Is current apply for finance / insurance from details page
    isApplyingFromDetails?: boolean;
    setIsCorporate?: Dispatch<SetStateAction<boolean>>;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    hasGuarantorPreset: boolean;
    customerKind: CustomerKind;
    withFinancing: boolean;
    hasVSOUpload: boolean;
    hasUploadDocuments: boolean;
    showTabs?: boolean;
    currentTab?: CustomerKind;
    showResetButton?: boolean;
    resetFormHandler?: () => void;
    gutter?: RowProps['gutter'];
    showSearchCapCustomerButton?: boolean;
    immediateOpenCapCustomerSearch?: boolean;
    showTitle?: boolean;
    isApplyingFromApplyNew?: boolean;
    isGuarantorCompleted?: boolean;
    porscheIdIntegrationEnabled?: boolean;
    isPorscheIdLoginMandatory?: boolean;
    onPorscheIDCustomerFetched?: (porscheIdData: PorscheIdData) => void;
    setIsPorscheIDFetchLoading?: Dispatch<SetStateAction<Boolean>>;
    applicationId?: string;
    endpointId?: string;
    routerId?: string;
    submitDraftWithPorscheId?: () => Promise<string | null>;
    isEvent?: boolean;
    colSpan?: Pick<ColProps, 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'>;
    enableMobileVerification?: boolean;
} & UploadDocumentProp;

const DEFAULT_GUTTER = 24;
const CustomerDetailsContent = ({
    kycPresets,
    kycExtraSettings,
    withFinancing,
    hasVSOUpload,
    hasUploadDocuments,
    hasGuarantorPreset,
    isApplyingFromDetails,
    setPrefill,
    showRemarks,
    showCommentsToInsurer,
    customerKind,
    currentTab,
    gutter = DEFAULT_GUTTER,
    uploadDocument,
    removeDocument,
    isApplyingFromApplyNew,
    isGuarantorCompleted,
    colSpan = defaultFieldsColSpan,
    enableMobileVerification,
}: CustomerDetailsProps) => {
    const { t } = useTranslation('customerDetails');
    const { values, setFieldValue, initialValues } = useFormikContext<KYCJourneyValues>();
    const { FormFields, Checkbox } = useThemeComponents();

    const router = useRouter();
    const company = useCompany(true);
    const countryCode = useMemo(() => {
        if (company) {
            return company.countryCode;
        }

        if (router) {
            return router.company.countryCode;
        }

        return null;
    }, [company, router]);

    const { showVSOUpload, showUploadDocument } = useMemo(
        () => ({
            showVSOUpload: withFinancing && hasVSOUpload,
            showUploadDocument: withFinancing && hasUploadDocuments,
        }),
        [withFinancing, hasVSOUpload, hasUploadDocuments]
    );

    const uploadDocumentKind = useMemo(() => {
        switch (currentTab) {
            case CustomerKind.Corporate:
                return ApplicationDocumentKind.CorporateIdentity;

            case CustomerKind.Guarantor:
                return ApplicationDocumentKind.GuarantorIdentity;

            case CustomerKind.Local:
                return ApplicationDocumentKind.CustomerIdentity;

            default:
                throw Error('CustomerKind not supported!');
        }
    }, [currentTab]);

    const uploadVSO = useCallback(
        (file: File) => uploadDocument(ApplicationDocumentKind.VsoUpload, file),
        [uploadDocument]
    );

    const uploadCustomerDocuments = useCallback(
        (file: File) => uploadDocument(uploadDocumentKind, file),
        [uploadDocument, uploadDocumentKind]
    );

    const remove = useCallback(file => removeDocument(file.id), [removeDocument]);

    // To disable it if the field is already filled
    // for applying from details
    const getIsFieldDisabled = useCallback(
        (path: LocalCustomerFieldKey) => {
            if ((isApplyingFromDetails || isApplyingFromApplyNew) && initialValues?.customer) {
                switch (path) {
                    case LocalCustomerFieldKey.DrivingLicense:
                        return isDrivingLicenseFieldDisabled(initialValues.customer.fields?.DrivingLicense?.value);

                    case LocalCustomerFieldKey.UaeDrivingLicense:
                        return isUaeDrivingLicenseFieldDisabled(
                            initialValues.customer.fields?.UAEDrivingLicense?.value
                        );

                    case LocalCustomerFieldKey.DrivingLicenseTh:
                        return isDrivingLicenseFieldDisabled(initialValues.customer.fields?.DrivingLicenseTh?.value);

                    case LocalCustomerFieldKey.DrivingLicenseMy:
                        return isDrivingLicenseFieldDisabled(initialValues.customer.fields?.DrivingLicenseMy?.value);

                    case LocalCustomerFieldKey.Phone:
                        return !isNil(initialValues.customer.fields?.Phone?.value?.value);

                    default:
                        return !isNil(initialValues.customer.fields?.[path]?.value);
                }
            }

            return false;
        },
        [isApplyingFromDetails, initialValues, isApplyingFromApplyNew]
    );

    useEffect(() => {
        if (!values.tradeInVehicle?.length) {
            setFieldValue('tradeInVehicle', [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }]);
        }

        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.DrivingLicense) &&
            !values.customer.fields?.DrivingLicense
        ) {
            setFieldValue('customer.fields.DrivingLicense', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: undefined }],
            });
        }

        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.DrivingLicenseTh) &&
            !values.customer.fields?.DrivingLicenseTh
        ) {
            setFieldValue('customer.fields.DrivingLicenseTh', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: undefined }],
            });
        }

        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.DrivingLicenseMy) &&
            !values.customer.fields?.DrivingLicenseMy
        ) {
            setFieldValue('customer.fields.DrivingLicenseMy', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: undefined }],
            });
        }

        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.UaeDrivingLicense) &&
            !values.customer.fields?.UAEDrivingLicense
        ) {
            setFieldValue('customer.fields.UAEDrivingLicense', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: undefined }],
            });
        }
    }, [
        setFieldValue,
        values.customer.fields?.DrivingLicense,
        values.tradeInVehicle?.length,
        values.customer.fields?.UAEDrivingLicense,
        kycPresets,
        values.customer.fields?.DrivingLicenseTh,
        values.customer.fields?.DrivingLicenseMy,
    ]);

    return (
        <>
            <Row gutter={[24, 12]}>
                {allowedCountryForSections.includes(countryCode) ? (
                    <GroupedCustomerDetails
                        colSpan={colSpan}
                        customerFields={kycPresets}
                        customerKind={currentTab}
                        enableMobileVerification={enableMobileVerification}
                        kycExtraSettings={kycExtraSettings}
                        prefix="customer.fields"
                        removeDocument={removeDocument}
                        setPrefill={setPrefill}
                        uploadDocument={uploadDocument}
                    />
                ) : (
                    <KycPresetFieldsRenderer
                        colSpan={colSpan}
                        customerType="customer"
                        enableMobileVerification={enableMobileVerification}
                        extraSettings={kycExtraSettings}
                        fields={kycPresets}
                        gutter={gutter}
                        isFieldDisabled={field => getIsFieldDisabled(field.key)}
                        markMyinfo={false}
                        prefix="customer.fields"
                        removeDocument={removeDocument}
                        uploadDocument={uploadDocument}
                    />
                )}

                {showVSOUpload && (
                    <Col span={24}>
                        <FormFields.MultipleDraggerField
                            {...t('customerDetails:fields.uploadVSO', { returnObjects: true })}
                            customRemove={removeDocument ? remove : null}
                            customUpload={uploadDocument ? uploadVSO : null}
                            extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                            maxCount={3}
                            name="vsoUpload"
                            sizeLimitInMiB={20}
                            required
                        />
                    </Col>
                )}

                {showUploadDocument && (
                    <KycUploadDocumentField
                        customRemove={removeDocument ? remove : null}
                        customUpload={uploadDocument ? uploadCustomerDocuments : null}
                        label={`uploadDocuments.${currentTab}`}
                        name={`uploadDocuments.${currentTab}`}
                    />
                )}

                {showRemarks && (
                    <Col span={24} style={{ marginTop: '-12px' }}>
                        <FormFields.InputField
                            {...t('customerDetails:fields.remark', { returnObjects: true })}
                            name="remarks"
                        />
                    </Col>
                )}

                {showCommentsToInsurer && (
                    <Col span={24}>
                        <FormFields.InputField
                            {...t('customerDetails:fields.commentsToInsurer', { returnObjects: true })}
                            name="commentsToInsurer"
                        />
                    </Col>
                )}
            </Row>

            {customerKind !== CustomerKind.Guarantor &&
                isKYCPresetHasCurrentVehicleFields(kycPresets) &&
                ((values.tradeInVehicle ?? []).every(vehicle => vehicle.source === LocalCustomerFieldSource.MyInfo) ? (
                    <Row gutter={[24, 12]}>
                        <Col span={24}>
                            <TradeInVehicleDetails kycPresets={kycPresets} withMyInfo />
                        </Col>
                    </Row>
                ) : (
                    <Row gutter={[24, 12]}>
                        <Col span={24}>
                            <TradeInVehicleDetails kycPresets={kycPresets} withMyInfo={false} />
                        </Col>
                    </Row>
                ))}

            {customerKind !== CustomerKind.Guarantor &&
                hasGuarantorPreset &&
                (!isApplyingFromApplyNew || (isApplyingFromApplyNew && !isGuarantorCompleted)) && (
                    <OccupySpace>
                        <CheckboxContainer>
                            <CheckboxField customComponent={Checkbox} name="hasGuarantor">
                                <Typography>{t('customerDetails:fields.hasGuarantor.label')}</Typography>
                            </CheckboxField>
                        </CheckboxContainer>
                    </OccupySpace>
                )}
        </>
    );
};

const CustomerDetails = ({
    showTabs,
    setIsCorporate,
    showResetButton = false,
    resetFormHandler = () => {},
    kycPresets,
    customerKind,
    showSearchCapCustomerButton = false,
    immediateOpenCapCustomerSearch = false,
    porscheIdIntegrationEnabled = false,
    isPorscheIdLoginMandatory = false,
    showTitle = true,
    ...props
}: CustomerDetailsProps) => {
    const { t } = useTranslation('customerDetails');
    const [currentTab, setCurrentTab] = useState<CustomerKind>(customerKind);
    const { theme, Tabs } = useThemeComponents();
    const searchCapCustomerManager = useSearchCapCustomerContext();

    const tabsList = useMemo(
        () =>
            showTabs && !props.isApplyingFromApplyNew && !props.isApplyingFromDetails ? (
                <Tabs
                    defaultActiveKey={currentTab}
                    items={[
                        { key: CustomerKind.Local, label: t(`customerDetails:panelTitles.customerDetails`) },
                        { key: CustomerKind.Corporate, label: t(`customerDetails:panelTitles.corporateDetails`) },
                    ]}
                    onChange={(key: CustomerKind) => {
                        setCurrentTab(key);
                        setIsCorporate(key === CustomerKind.Corporate);
                    }}
                    onTabClick={(key, event) => event.stopPropagation()}
                    weight="semi-bold"
                />
            ) : null,
        [showTabs, props.isApplyingFromApplyNew, props.isApplyingFromDetails, Tabs, currentTab, t, setIsCorporate]
    );

    const header = useMemo(() => {
        let title = '';

        switch (currentTab) {
            case CustomerKind.Local: {
                title = t(`customerDetails:panelTitles.customerDetails`);
                break;
            }

            case CustomerKind.Corporate: {
                title = t(`customerDetails:panelTitles.corporateDetails`);
                break;
            }

            case CustomerKind.Guarantor: {
                title = t(`customerDetails:panelTitles.guarantorDetails`);
                break;
            }

            default:
                return '';
        }

        return (
            <Space size={10}>
                <Title>{title}</Title>
                {showResetButton && theme !== CompanyTheme.Porsche && theme !== CompanyTheme.PorscheV3 && (
                    <ResetKYCButton onConfirm={resetFormHandler} />
                )}
            </Space>
        );
    }, [currentTab, resetFormHandler, showResetButton, t, theme]);

    useEffect(() => {
        if (
            immediateOpenCapCustomerSearch &&
            searchCapCustomerManager &&
            !searchCapCustomerManager.userChooseCreateNew
        ) {
            searchCapCustomerManager.showSearchComponent(false);
        }
    }, [immediateOpenCapCustomerSearch, searchCapCustomerManager]);

    return (
        <>
            {showTitle && (
                <HeaderContainer>
                    {header}
                    {showSearchCapCustomerButton && <SearchCapCustomerButton />}
                </HeaderContainer>
            )}
            {porscheIdIntegrationEnabled && !isPorscheIdLoginMandatory && (
                <PorscheIDLogin
                    applicationId={props.applicationId}
                    endpointId={props.endpointId}
                    isEvent={props.isEvent}
                    onPorscheIDCustomerFetched={props.onPorscheIDCustomerFetched}
                    routerId={props.routerId}
                    setIsPorscheIDFetchLoading={props.setIsPorscheIDFetchLoading}
                    submitDraft={props.submitDraftWithPorscheId}
                />
            )}
            {tabsList}
            <CustomerDetailsContent
                {...props}
                currentTab={currentTab}
                customerKind={customerKind}
                kycPresets={kycPresets}
            />
        </>
    );
};

export default CustomerDetails;
