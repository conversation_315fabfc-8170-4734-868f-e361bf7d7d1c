import { useFormikContext } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import type { BankDetailsDataFragment } from '../../../../../api/fragments/BankDetailsData';
import { CustomerKind, LocalCustomerFieldKey, type LocalCustomerManagementModule } from '../../../../../api/types';
import { KycPresetField } from '../../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../../utilities/kycPresets/shared';
import colSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';

export type CustomerFieldProps = {
    name: string;
    customerType: string;
    index: number;
    createdAt?: string | Date;
    customerKind: CustomerKind;
    isDisabled?: boolean;
    kycFieldValues: ApplicationFormValues['applicantKYC'];
    extraSettings: LocalCustomerManagementModule['extraSettings'];
    bank?: BankDetailsDataFragment;
} & Partial<UploadDocumentProp>;

const bankEditableField = {
    hlf: {
        scheme: true,
        inspected: true,
        price: true,
        omv: true,
        loanAmount: true,
        tenure: true,
        interestRate: true,
        packageId: true,
        email: true,
    },
    uob: {
        loanAmount: true,
        tenure: true,
        packageId: true,
    },
};

const disabledKYCField = (field: string, bank?: BankDetailsDataFragment) => {
    switch (bank?.integration.__typename) {
        case 'HlfBankIntegration':
        case 'HlfBankV2Integration': {
            return !(field.toLowerCase() in bankEditableField.hlf) && !bankEditableField.hlf[field.toLowerCase()];
        }

        case 'UobBankIntegration':
            return !(field.toLowerCase() in bankEditableField.uob) && !bankEditableField.uob[field.toLowerCase()];

        default:
            return false;
    }
};
const CustomerField = ({
    name,
    index,
    createdAt,
    customerKind,
    uploadDocument,
    removeDocument,
    isDisabled,
    kycFieldValues,
    extraSettings,
    bank,
    customerType,
}: CustomerFieldProps) => {
    // name :: KYC preset fields
    const { setFieldValue } = useFormikContext<ApplicationFormValues>();
    const [representation, setRepresentation] = useState<string | undefined>(undefined);

    const field = useMemo(() => kycFieldValues[index], [kycFieldValues, index]);

    const updateFieldValue = useCallback(
        (newValue: string) => {
            switch (field.key) {
                case LocalCustomerFieldKey.Title:
                case LocalCustomerFieldKey.Salutation:
                case LocalCustomerFieldKey.SalutationBmw:
                case LocalCustomerFieldKey.FirstName:
                case LocalCustomerFieldKey.LastName:
                case LocalCustomerFieldKey.FullName:
                case LocalCustomerFieldKey.Email:
                case LocalCustomerFieldKey.IdentityNumber:
                case LocalCustomerFieldKey.Nationality:
                case LocalCustomerFieldKey.Country:
                case LocalCustomerFieldKey.PostalCode:
                case LocalCustomerFieldKey.Address:
                case LocalCustomerFieldKey.UnitNumber:
                case LocalCustomerFieldKey.Passport:
                case LocalCustomerFieldKey.Race:
                case LocalCustomerFieldKey.Gender:
                case LocalCustomerFieldKey.MaritalStatus:
                case LocalCustomerFieldKey.ResidentialStatus:
                case LocalCustomerFieldKey.Road:
                case LocalCustomerFieldKey.District:
                case LocalCustomerFieldKey.Region:
                case LocalCustomerFieldKey.City:
                case LocalCustomerFieldKey.CorrespondenceAddress:
                case LocalCustomerFieldKey.CorrespondenceCity:
                case LocalCustomerFieldKey.CorrespondenceDistrict:
                case LocalCustomerFieldKey.EmploymentStatus:
                case LocalCustomerFieldKey.Occupation:
                case LocalCustomerFieldKey.CompanyAddress:
                case LocalCustomerFieldKey.CompanyCity:
                case LocalCustomerFieldKey.CompanyName:
                case LocalCustomerFieldKey.CompanyPhoneticName:
                case LocalCustomerFieldKey.CompanyDistrict:
                case LocalCustomerFieldKey.CorporateName:
                case LocalCustomerFieldKey.CorporateIndustryCategory:
                case LocalCustomerFieldKey.CorporateIdentityNumber:
                case LocalCustomerFieldKey.Citizenship:
                case LocalCustomerFieldKey.AddressType:
                case LocalCustomerFieldKey.Education:
                case LocalCustomerFieldKey.Emirate:
                case LocalCustomerFieldKey.IncomeType:
                case LocalCustomerFieldKey.ResidenceType:
                case LocalCustomerFieldKey.PurchaseIntention:
                case LocalCustomerFieldKey.Comments:
                case LocalCustomerFieldKey.BusinessTitle: {
                    setFieldValue(`${name}.stringValue`, newValue);
                    break;
                }

                case LocalCustomerFieldKey.CorporatePhone:
                case LocalCustomerFieldKey.Telephone:
                case LocalCustomerFieldKey.Phone:
                    setFieldValue(`${name}.phoneValue.value`, newValue);
                    break;

                case LocalCustomerFieldKey.CorporateRegistrationDate:
                case LocalCustomerFieldKey.Birthday:
                case LocalCustomerFieldKey.DriverLicensePassDate:
                case LocalCustomerFieldKey.CreationDate:
                case LocalCustomerFieldKey.DateOfJoining:
                case LocalCustomerFieldKey.PreferredFirstPaymentDate: {
                    setFieldValue(`${name}.dateValue`, newValue);
                    break;
                }

                case LocalCustomerFieldKey.UaeDrivingLicense:
                case LocalCustomerFieldKey.DrivingLicense:
                case LocalCustomerFieldKey.DrivingLicenseTh:
                case LocalCustomerFieldKey.DrivingLicenseMy:
                    setFieldValue(`${name}.drivingLicenseValue`, newValue);
                    break;

                case LocalCustomerFieldKey.NoClaimDiscount:
                case LocalCustomerFieldKey.CorporateAnnualRevenue:
                case LocalCustomerFieldKey.CorporateNumberOfEmployee:
                case LocalCustomerFieldKey.CorporateRegisteredCapital:
                case LocalCustomerFieldKey.MonthlyIncome:
                case LocalCustomerFieldKey.OtherIncome:
                case LocalCustomerFieldKey.TimeOfEmployment:
                case LocalCustomerFieldKey.TimeOfAddress:
                    setFieldValue(`${name}.numberValue`, newValue);
                    break;

                case LocalCustomerFieldKey.ResidentialStatusVwfs:
                case LocalCustomerFieldKey.JobTitle:
                case LocalCustomerFieldKey.RelationshipWithApplicant:
                    setFieldValue(`${name}.stringDescriptionValue`, newValue);
                    break;

                case LocalCustomerFieldKey.UploadIdentity:
                case LocalCustomerFieldKey.UploadDrivingLicense:
                case LocalCustomerFieldKey.UploadPassport:
                case LocalCustomerFieldKey.UploadOtherDocument:
                    setFieldValue(`${name}.fileValue`, newValue);
                    break;

                case LocalCustomerFieldKey.ReferenceDetailSet: {
                    setFieldValue(`${name}.referenceDetailValue`, newValue);
                    break;
                }

                case LocalCustomerFieldKey.SalaryTransferredBankSet: {
                    setFieldValue(`${name}.salaryTransferredBankValue`, newValue);
                    break;
                }

                case LocalCustomerFieldKey.UaeIdentitySet: {
                    setFieldValue(`${name}.uaeIdentityValue`, newValue);
                    break;
                }

                default:
                    throw new Error('LocalCustomerField not support');
            }
        },
        [field.key, setFieldValue, name]
    );

    const bankDisable = useMemo(() => disabledKYCField(field.key, bank), [bank, field.key]);

    return (
        <KycPresetField
            key={field.key}
            bankDisable={bankDisable}
            colSpan={colSpan}
            createdAt={createdAt}
            customerKind={customerKind}
            customerType={customerType}
            extraSettings={extraSettings}
            field={field}
            fromJourney={false}
            isDisabled={isDisabled}
            prefix={name}
            removeDocument={removeDocument}
            uploadDocument={uploadDocument}
        />
    );
};

export default CustomerField;
