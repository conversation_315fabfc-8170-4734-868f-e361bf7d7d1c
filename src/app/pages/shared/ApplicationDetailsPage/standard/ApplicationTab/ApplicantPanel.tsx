import { <PERSON><PERSON>, Col, Typography } from 'antd';
import { FieldArray, useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Fragment, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import * as permissionKind from '../../../../../../shared/permissions';
import {
    ApplicationDataFragment,
    BankDetailsDataFragment,
    CurrentUserDataFragment,
    CustomerDetailSpecsFragment,
    CustomerSpecsFragment,
} from '../../../../../api/fragments';
import {
    ApplicationStage,
    CapValuesOnApplication,
    CustomerKind,
    KycFieldPurpose,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
} from '../../../../../api/types';
import { useAccount } from '../../../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import hasPermissions from '../../../../../utilities/hasPermissions';
import {
    IdentityDetailsKey,
    ReferenceDetailsKey,
} from '../../../../portal/StandardApplicationEntrypoint/KYCPage/shared';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import type { ApplicationFormValues } from '../shared';
import CustomerField from './CustomerField';

const colSpan = { lg: 8, md: 12, xs: 24 };

const StyledLink = styled(Link)`
    font-weight: 380;
    margin-left: 10px;
    font-size: 16px;
    color: var(--ant-primary-color);
`;

export const SubTitle = styled(Typography)`
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 1rem;
`;

export const CustomerDetailsKey = [
    LocalCustomerFieldKey.CreationDate,
    LocalCustomerFieldKey.Title,
    LocalCustomerFieldKey.NonBinaryTitle,
    LocalCustomerFieldKey.Salutation,
    LocalCustomerFieldKey.SalutationBmw,
    LocalCustomerFieldKey.LastNameFront,
    LocalCustomerFieldKey.FirstName,
    LocalCustomerFieldKey.LastName,
    LocalCustomerFieldKey.LastNameJapan,
    LocalCustomerFieldKey.FirstNameJapan,
    LocalCustomerFieldKey.FullName,
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Birthday,
    LocalCustomerFieldKey.Nationality,
    LocalCustomerFieldKey.Citizenship,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Race,
    LocalCustomerFieldKey.Gender,
    LocalCustomerFieldKey.NonBinaryGender,
    LocalCustomerFieldKey.MaritalStatus,
    LocalCustomerFieldKey.Education,
    LocalCustomerFieldKey.ResidentialStatus,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.RelationshipWithApplicant,
    LocalCustomerFieldKey.Passport,
];

export const CorrespondenceKey = [
    LocalCustomerFieldKey.CorrespondenceAddress,
    LocalCustomerFieldKey.CorrespondenceCity,
    LocalCustomerFieldKey.CorrespondenceDistrict,
];

export const CorporateInformationKey = [
    LocalCustomerFieldKey.CorporateName,
    LocalCustomerFieldKey.CorporateIdentityNumber,
    LocalCustomerFieldKey.CorporateRegistrationDate,
    LocalCustomerFieldKey.CorporateIndustryCategory,
    LocalCustomerFieldKey.CorporateRegisteredCapital,
    LocalCustomerFieldKey.CorporateAnnualRevenue,
    LocalCustomerFieldKey.CorporateNumberOfEmployee,
    LocalCustomerFieldKey.CorporatePhone,
];

export const AddressDetailsKey = [
    LocalCustomerFieldKey.ResidentialStatusVwfs,
    LocalCustomerFieldKey.ResidenceType,
    LocalCustomerFieldKey.Country,
    LocalCustomerFieldKey.Region,
    LocalCustomerFieldKey.City,
    LocalCustomerFieldKey.District,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.Address,
    LocalCustomerFieldKey.Road,
    LocalCustomerFieldKey.UnitNumber,
    LocalCustomerFieldKey.TimeOfAddress,
    LocalCustomerFieldKey.AddressType,
    LocalCustomerFieldKey.Emirate,
    LocalCustomerFieldKey.DeliveryAddress,
];

export const EmploymentDetailsKey = [
    LocalCustomerFieldKey.JobTitle,
    LocalCustomerFieldKey.JobTitleTh,
    LocalCustomerFieldKey.Occupation,
    LocalCustomerFieldKey.EmploymentStatus,
    LocalCustomerFieldKey.TimeOfEmployment,
    LocalCustomerFieldKey.CompanyName,
    LocalCustomerFieldKey.CompanyPhoneticName,
    LocalCustomerFieldKey.CompanyCity,
    LocalCustomerFieldKey.CompanyDistrict,
    LocalCustomerFieldKey.CompanyAddress,
    LocalCustomerFieldKey.IncomeType,
    LocalCustomerFieldKey.MonthlyIncome,
    LocalCustomerFieldKey.OtherIncome,
    LocalCustomerFieldKey.DateOfJoining,
    LocalCustomerFieldKey.PreferredFirstPaymentDate,
    LocalCustomerFieldKey.SalaryTransferredBankSet,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.BusinessTitle,
];

export const OthersKey = [
    LocalCustomerFieldKey.NoClaimDiscount,
    LocalCustomerFieldKey.DriverLicensePassDate,
    LocalCustomerFieldKey.DrivingLicense,
    LocalCustomerFieldKey.DrivingLicenseTh,
    LocalCustomerFieldKey.DrivingLicenseMy,
    LocalCustomerFieldKey.UaeDrivingLicense,
    LocalCustomerFieldKey.PurchaseIntention,
    LocalCustomerFieldKey.Comments,
    LocalCustomerFieldKey.Hobby,
];

export enum FeatureInUsed {
    // default refers applications
    Default = 'default',
    Customer = 'customer',
}

export type ApplicantPanelProps<FormValues> = {
    isMask: boolean;
    defaultExpanded?: boolean;
    customerKind: CustomerKind;
    showCustomerLink: boolean;
    application?: ApplicationDataFragment;
    forCI?: boolean;
    disabled?: boolean;
    // optional applicant's versioning(i.e. Last Modified) field to display. It will not be shown if it's not provided.
    applicantVersioning?: {
        updated: string;
        offset: string;
    };
    stage?: ApplicationStage;
    featureInUsed?: FeatureInUsed;
    capValues?: CapValuesOnApplication;
    customer?: CustomerDetailSpecsFragment | CustomerSpecsFragment;
};

export const useGroupByCustomerData = (kycFieldValues: ApplicationFormValues['applicantKYC']) =>
    useMemo(() => {
        const data = {
            customerDetails: [],
            addressDetails: [],
            correspondenceAddress: [],
            corporateInformation: [],
            employmentDetails: [],
            identityDetails: [],
            referenceDetails: [],
            others: [],
        };

        (kycFieldValues ?? []).forEach(({ key, purpose }, index) => {
            if (purpose.includes(KycFieldPurpose.Kyc)) {
                if (CustomerDetailsKey.includes(key)) {
                    data.customerDetails.push({
                        key,
                        index,
                    });

                    return;
                }

                if (EmploymentDetailsKey.includes(key)) {
                    data.employmentDetails.push({
                        key,
                        index,
                    });

                    return;
                }

                if (CorporateInformationKey.includes(key)) {
                    data.corporateInformation.push({
                        key,
                        index,
                    });

                    return;
                }

                if (AddressDetailsKey.includes(key)) {
                    data.addressDetails.push({
                        key,
                        index,
                    });

                    return;
                }

                if (CorrespondenceKey.includes(key)) {
                    data.correspondenceAddress.push({
                        key,
                        index,
                    });

                    return;
                }

                if (IdentityDetailsKey.includes(key)) {
                    data.identityDetails.push({
                        key,
                        index,
                    });

                    return;
                }

                if (ReferenceDetailsKey.includes(key)) {
                    data.referenceDetails.push({
                        key,
                        index,
                    });

                    return;
                }

                if (OthersKey.includes(key)) {
                    data.others.push({
                        key,
                        index,
                    });
                }
            }
        });

        return data;
    }, [kycFieldValues]);

type GroupedCustomerDataProps = {
    kycFieldValues: ApplicationFormValues['applicantKYC'];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    customerType: string;
    createdAt?: string | Date;
    customerKind: CustomerKind;
    isDisabled?: boolean;
    isMask?: boolean;
    bank?: BankDetailsDataFragment;
};

export const GroupedCustomerData = ({
    kycFieldValues,
    kycExtraSettings,
    customerType,
    createdAt,
    customerKind,
    isDisabled,
    isMask,
    bank,
}: GroupedCustomerDataProps) => {
    const { t } = useTranslation('applicationDetails');

    const groupedData = useGroupByCustomerData(kycFieldValues);

    const configuration = useMemo(
        () =>
            [
                {
                    key: 'customerDetails',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.customerDetails'),
                    data: groupedData.customerDetails,
                },
                {
                    key: 'corporateInformation',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.corporateInformation'),
                    data: groupedData.corporateInformation,
                },
                {
                    key: 'addressDetails',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.addressDetails'),
                    data: groupedData.addressDetails,
                },
                {
                    key: 'correspondenceAddress',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.correspondenceAddress'),
                    data: groupedData.correspondenceAddress,
                },
                {
                    key: 'employmentDetails',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.employmentDetails'),
                    data: groupedData.employmentDetails,
                },
                {
                    key: 'identityDetails',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.identityDetails'),
                    data: groupedData.identityDetails,
                },
                {
                    key: 'referenceDetails',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.referenceDetails'),
                    data: groupedData.referenceDetails,
                },
                {
                    key: 'others',
                    label: t('applicationDetails:panels.application.applicant.subHeaders.others'),
                    data: groupedData.others,
                },
            ].filter(item => item.data.length > 0),
        [
            t,
            groupedData.addressDetails,
            groupedData.corporateInformation,
            groupedData.correspondenceAddress,
            groupedData.customerDetails,
            groupedData.employmentDetails,
            groupedData.identityDetails,
            groupedData.others,
            groupedData.referenceDetails,
        ]
    );

    return (
        <>
            {configuration.map(({ key, label, data }) => (
                <Fragment key={label}>
                    <Col span={24}>
                        <SubTitle>{label}</SubTitle>
                    </Col>
                    <FieldArray
                        name={`${customerType}.fields`}
                        render={() => (
                            <>
                                {data.map(({ key, index }) => (
                                    <CustomerField
                                        key={`${label}.${key}`}
                                        bank={bank}
                                        createdAt={createdAt}
                                        customerKind={customerKind}
                                        customerType={customerType}
                                        extraSettings={kycExtraSettings}
                                        index={index}
                                        isDisabled={isDisabled}
                                        kycFieldValues={kycFieldValues}
                                        name={`${customerType}.fields`}
                                    />
                                ))}
                            </>
                        )}
                    />
                </Fragment>
            ))}
        </>
    );
};

const getShowCommentsToBank = (application: ApplicationDataFragment, user: CurrentUserDataFragment) => {
    switch (application.__typename) {
        case 'StandardApplication':
        case 'EventApplication':
        case 'ConfiguratorApplication':
        case 'FinderApplication':
            return application.bank?.showCommentsField;

        default:
            return false;
    }
};

const getShowCommentsToInsurer = (application: ApplicationDataFragment, user: CurrentUserDataFragment) => {
    switch (application.__typename) {
        case 'FinderApplication':
            return application.insurer?.showCommentsField;

        case 'StandardApplication':
        case 'ConfiguratorApplication': {
            const { insurers } =
                ((application.module?.__typename === 'StandardApplicationModule' ||
                    application.module?.__typename === 'ConfiguratorModule') &&
                    application.module) ||
                {};

            const insurer = (insurers ?? []).find(insurer => insurer?.id === application.insurancing?.insurerId);

            return insurer?.showCommentsField;
        }

        default:
            return false;
    }
};

const getShowComments = (
    stage: ApplicationStage,
    application: ApplicationDataFragment,
    user: CurrentUserDataFragment
) => {
    if (!application || !user) {
        return { showCommentsToBank: false, showCommentsToInsurer: false };
    }

    const showCommentsToBank = stage === ApplicationStage.Financing && getShowCommentsToBank(application, user);
    const showCommentsToInsurer = stage === ApplicationStage.Insurance && getShowCommentsToInsurer(application, user);

    return { showCommentsToBank, showCommentsToInsurer };
};

const StyledHeader = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 18px;
`;

export const useAdditionalCustomerFields = (
    customer: CustomerDetailSpecsFragment | CustomerSpecsFragment,
    featureInUsed?: FeatureInUsed
) =>
    useMemo(() => {
        const customerCiamId = (() => {
            switch (customer?.__typename) {
                case 'LocalCustomer':
                    return customer.customerCiamId;

                default:
                    return null;
            }
        })();

        const businessPartnerId = (() => {
            if (featureInUsed !== FeatureInUsed.Customer) {
                return null;
            }

            switch (customer?.__typename) {
                case 'LocalCustomer':
                    if (customer.businessPartnerIds?.length > 0) {
                        return customer.businessPartnerIds.join(', ');
                    }

                    return null;

                default:
                    return null;
            }
        })();

        return {
            customerCiamId,
            businessPartnerId,
        };
    }, [customer, featureInUsed]);

const ApplicantPanel = <
    FormValues extends {
        applicantKYC?: ApplicationFormValues['applicantKYC'];
        guarantorKYC?: ApplicationFormValues['applicantKYC'];
        corporateKYC?: ApplicationFormValues['applicantKYC'];
        versioning?: ApplicationFormValues['versioning'];
        bank?: ApplicationFormValues['bank'];
    },
>({
    isMask,
    defaultExpanded,
    customerKind,
    showCustomerLink,
    application,
    disabled,
    applicantVersioning,
    stage,
    forCI,
    featureInUsed = FeatureInUsed.Default,
    capValues,
    customer,
}: ApplicantPanelProps<FormValues>) => {
    const { t } = useTranslation(['applicationDetails', 'customerDetails']);
    const { values } = useFormikContext<FormValues>();
    const router = useRouter();
    const navigate = useNavigate();
    const user = useAccount();
    const { permissions: accountPermissions } = user;
    const company = useCompany(true);

    const hasPermission = company
        ? hasPermissions(company.permissions, [permissionKind.manageCustomers]) &&
          hasPermissions(accountPermissions, [permissionKind.updateCustomer])
        : hasPermissions(accountPermissions, [permissionKind.updateCustomer]);

    const isDisabled = disabled || !hasPermission;
    const customerTypeFieldName = useMemo(
        () => (customerKind === CustomerKind.Guarantor ? 'guarantor' : 'customer'),
        [customerKind]
    );

    const { FormFields } = useThemeComponents();

    const valueData = useMemo(() => {
        switch (customerKind) {
            case CustomerKind.Guarantor:
                return values.guarantorKYC;

            case CustomerKind.Corporate:
                return values.corporateKYC;

            default:
                return values.applicantKYC;
        }
    }, [customerKind, values]);

    const { showCommentsToBank, showCommentsToInsurer } = useMemo(
        () => getShowComments(stage, application, user),
        [stage, application, user]
    );

    const kycExtraSettings = useMemo(() => {
        if (application?.__typename === 'EventApplication') {
            return application.event?.kycExtraSettings;
        }

        switch (application?.module?.__typename) {
            case 'ConfiguratorModule':
            case 'FinderApplicationPrivateModule':
            case 'FinderApplicationPublicModule':
            case 'StandardApplicationModule':
                return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                    ? application.module.customerModule.extraSettings
                    : null;

            default:
                return null;
        }
    }, [application]);

    const header = useCallback(
        (application: ApplicationDataFragment) => {
            if (featureInUsed === FeatureInUsed.Customer) {
                return t('customerDetails:panelTitles.customerPanelTitle');
            }

            let link = `/admin/customers/${application?.applicant?.versioning?.suiteId}`;

            if (forCI) {
                if (router) {
                    const endpoints = router?.endpoints;
                    const customerDetailsEndpoint = endpoints.find(
                        endpoint =>
                            endpoint.__typename === 'CustomerListEndpoint' &&
                            endpoint.applicationModuleIds.includes(application?.moduleId)
                    );
                    link = isNil(customerDetailsEndpoint)
                        ? ''
                        : `${customerDetailsEndpoint.pathname}/${application?.applicant?.versioning?.suiteId}`;
                }

                if (!showCustomerLink || link === '') {
                    return t('applicationDetails:panels.application.applicant.header');
                }
            }

            return (
                <StyledHeader>
                    {t('applicationDetails:panels.application.applicant.header')}
                    <Button
                        onClick={e => {
                            e.stopPropagation();
                            navigate(link);
                        }}
                        type="primary"
                    >
                        {t('applicationDetails:panels.application.applicant.customerLink')}
                    </Button>
                </StyledHeader>
            );
        },
        [featureInUsed, forCI, navigate, router, showCustomerLink, t]
    );

    const { businessPartnerId, customerCiamId } = useAdditionalCustomerFields(customer, featureInUsed);

    return (
        <ApplicationDetailsPanel
            defaultExpanded={defaultExpanded}
            forCI={forCI}
            header={
                customerKind === CustomerKind.Guarantor
                    ? t('applicationDetails:panels.application.applicant.guarantorHeader')
                    : header(application)
            }
            name={SectionKey.ApplicantDetails}
        >
            {(capValues || businessPartnerId || customerCiamId) && (
                <>
                    <Col span={24}>
                        <SubTitle>{t('applicationDetails:panels.application.porscheIdentifier.header')}</SubTitle>
                    </Col>
                    {(capValues || businessPartnerId) && (
                        <Col key="capBusinessPartnerId" {...colSpan}>
                            <FormFields.DisplayField
                                key="capBusinessPartnerId"
                                label={t('applicationDetails:fields.capBusinessPartnerid.label')}
                                value={capValues?.businessPartnerId || businessPartnerId}
                            />
                        </Col>
                    )}
                    {customerCiamId && (
                        <Col key="customerCiamId" {...colSpan}>
                            <FormFields.DisplayField
                                key="customerCiamId"
                                label={t('applicationDetails:fields.customerCiamId.label')}
                                value={customerCiamId}
                            />
                        </Col>
                    )}
                </>
            )}
            <GroupedCustomerData
                bank={values.bank}
                createdAt={application?.versioning?.createdAt}
                customerKind={customerKind}
                customerType={customerTypeFieldName}
                isDisabled={isDisabled}
                isMask={isMask}
                kycExtraSettings={kycExtraSettings}
                kycFieldValues={valueData}
            />

            {showCommentsToBank && (
                <>
                    <Col span={24}>
                        <SubTitle>{t('applicationDetails:fields.remarks.label')}</SubTitle>
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.InputField name="remarks" />
                    </Col>
                </>
            )}

            {showCommentsToInsurer && (
                <>
                    <Col span={24}>
                        <SubTitle>{t('applicationDetails:fields.commentsToInsurer.label')}</SubTitle>
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.InputField name="commentsToInsurer" />
                    </Col>
                </>
            )}

            {applicantVersioning && (
                <Col {...colSpan}>
                    <FormFields.DisplayFieldWithUTCOffset
                        {...t('applicationDetails:fields.updatedAt', {
                            returnObjects: true,
                            offset: applicantVersioning.offset,
                        })}
                        value={applicantVersioning.updated}
                    />
                </Col>
            )}
        </ApplicationDetailsPanel>
    );
};

export default ApplicantPanel;
