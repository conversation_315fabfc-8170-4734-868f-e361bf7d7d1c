import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { formatInterestRate, getCalculationMode } from '../../../../../calculator/fieldHooks/shared';
import MonthlyInstalmentField from '../../../../../components/fields/MonthlyInstalmentField';
import { useThemeComponents } from '../../../../../themes/hooks';
import useFormats from '../../../../../utilities/useFormats';
import usePromoCodeInfo from '../../../../../utilities/usePromoCodeInfo';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import colSpan from '../colSpan';
import type { AFCApplicationFormValues } from '../shared';
import MarketDisplayField from './MarketDisplayField';

export type AFCFinancingPanelProps = {
    application: Exclude<ApplicationDataFragment, { __typename: 'LaunchpadApplication' }>;
};

const AFCFinancingPanel = ({ application }: AFCFinancingPanelProps) => {
    const { t } = useTranslation(['applicationDetails', 'calculators', 'affinAutoFinanceCentre']);
    const { values } = useFormikContext<AFCApplicationFormValues>();
    const { formatAmount, formatAmountWithCurrency, formatPercentage } = useFormats(
        application.module.company.currency,
        application.module.company.roundings.amount.decimals,
        application.module.company.roundings.percentage.decimals
    );
    const calculationMode = getCalculationMode(values.financeProduct);
    const { FormFields } = useThemeComponents();
    const translated = useTranslatedString();
    const promoCodeInfo = usePromoCodeInfo(
        values.promoCode,
        application.module.company.id,
        values.promoCode?.promoType.__typename === 'GiftPromoType' ? null : values.promoCode?.promoType.amount
    );

    const totalDealerOptions = useMemo(() => {
        const { dealerOptions } = values.financing;

        return dealerOptions?.reduce<number>((total, { amount }) => total + (amount || 0), 0) ?? 0;
    }, [values.financing]);

    const { isUcclLeasing, isBalloonGFV, isHpWithBalloon } = useMemo(
        () => ({
            isUcclLeasing: values.financeProduct.__typename === 'LocalUcclLeasing',
            isBalloonGFV: values.financeProduct.__typename === 'LocalHirePurchaseWithBalloonGFV',
            isHpWithBalloon: values.financeProduct.__typename === 'LocalHirePurchaseWithBalloon',
        }),
        [values.financeProduct]
    );

    const isLeasePurchase = useMemo(
        () => values.financeProduct.__typename === 'LocalLeasePurchase',
        [values.financeProduct]
    );

    if (application.__typename === 'MobilityApplication') {
        throw new Error('MobilityApplication not supported');
    }

    return (
        <>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.bank', { returnObjects: true })}
                    value={translated(values.financeProduct.bank.legalName)}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.product', { returnObjects: true })}
                    value={translated(values.financeProduct.legalName)}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.interestRate', { returnObjects: true })}
                    value={formatInterestRate(values.financing.interestRate, calculationMode, t)}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.term', { returnObjects: true })}
                    value={t('applicationDetails:formats.termMonth', { count: values.financing.term })}
                />
            </Col>
            {values.financing?.downPaymentWithAdditional?.amount ? (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.downPaymentWithAdditional', { returnObjects: true })}
                        value={`${formatAmountWithCurrency(
                            values.financing.downPaymentWithAdditional?.amount
                        )} (${formatPercentage(values.financing.downPaymentWithAdditional?.percentage)})`}
                    />
                </Col>
            ) : (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.downPayment', { returnObjects: true })}
                        value={`${formatAmountWithCurrency(values.financing.downPayment?.amount)} (${formatPercentage(
                            values.financing.downPayment?.percentage
                        )})`}
                    />
                </Col>
            )}
            {values.financing.discount?.amount > 0 && (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.discount', { returnObjects: true })}
                            value={`${formatAmountWithCurrency(values.financing.discount?.amount)} (${formatPercentage(
                                values.financing.discount?.percentage
                            )})`}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.discountDescription', { returnObjects: true })}
                            value={values.financing.discount?.description}
                        />
                    </Col>
                </>
            )}

            {values.bank.showMarginOfFinance ? (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.hirePurchase', { returnObjects: true })}
                            value={`${formatAmountWithCurrency(values.financing.loan?.amount)}`}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.marginOfFinance', { returnObjects: true })}
                            value={`(${formatPercentage(values.financing.loan?.percentage)})`}
                        />
                    </Col>
                </>
            ) : (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.loan', { returnObjects: true })}
                        value={`${formatAmountWithCurrency(values.financing.loan?.amount)} (${formatPercentage(
                            values.financing.loan?.percentage
                        )})`}
                    />
                </Col>
            )}

            {isUcclLeasing && (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.licensePlateFee', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.financing.licensePlateFee)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.commission', { returnObjects: true })}
                            value={t('calculators:commissionValue', { value: values.financing.commission?.toFixed(2) })}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.monthlyPaymentFixedRate', {
                                returnObjects: true,
                            })}
                            value={formatAmountWithCurrency(values.financing.monthlyPaymentFixedInterestRate)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.licenseAndFuelTax', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.financing.licenseAndFuelTax)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.displacement', { returnObjects: true })}
                            value={t('calculators:displacementValue', { value: values.financing.displacement })}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.insuranceFee', { returnObjects: true })}
                            value={`${formatAmountWithCurrency(
                                values.financing.insuranceFee?.amount
                            )} (${values.financing.insuranceFee?.percentage?.toFixed(2)}%)`}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.taxLoss', { returnObjects: true })}
                            value={`${
                                values.financing.taxLoss ? formatAmountWithCurrency(values.financing.taxLoss) : '-'
                            }`}
                        />
                    </Col>
                </>
            )}

            {isBalloonGFV && (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.assuredResaleValue', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.financing.assuredResaleValue?.amount)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.estimatedSurplusBalloon', {
                                returnObjects: true,
                            })}
                            value={formatAmountWithCurrency(values.financing.estimatedSurplusBalloon)}
                        />
                    </Col>
                </>
            )}

            {isLeasePurchase && (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.mileage', { returnObjects: true })}
                            value={values.financing?.mileage ? formatAmount(values.financing.mileage) : '-'}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.residualValue', {
                                returnObjects: true,
                            })}
                            value={
                                values.financing?.residualValue
                                    ? `${formatAmountWithCurrency(
                                          values.financing.residualValue?.amount
                                      )} (${values.financing.residualValue?.percentage?.toFixed(2)}%)`
                                    : '-'
                            }
                        />
                    </Col>
                </>
            )}
            {isHpWithBalloon && (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.balloonPayment', { returnObjects: true })}
                        value={`${formatAmountWithCurrency(
                            values.financing.balloonPayment?.amount
                        )} (${formatPercentage(values.financing.balloonPayment?.percentage)})`}
                    />
                </Col>
            )}

            {values.financing.dealerOptions?.length && (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.dealerOptions', { returnObjects: true })}
                        value={formatAmountWithCurrency(totalDealerOptions)}
                    />
                </Col>
            )}

            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.totalPrice', { returnObjects: true })}
                    value={formatAmountWithCurrency(values.financing.totalPrice)}
                />
            </Col>

            <MonthlyInstalmentField
                application={application}
                monthlyInstalments={values.financing.monthlyInstalments}
            />
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.promoCode', { returnObjects: true })}
                    value={promoCodeInfo}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.financing.deposit', { returnObjects: true })}
                    value={formatAmountWithCurrency(application.deposit?.amount)}
                />
            </Col>

            {values.financing.affinAutoFinanceCentre && (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.affinAutoFinanceCentre', { returnObjects: true })}
                        value={t(
                            // eslint-disable-next-line max-len
                            `affinAutoFinanceCentre:options.${values.financing.affinAutoFinanceCentre}`
                        )}
                    />
                </Col>
            )}

            <MarketDisplayField application={application} />
        </>
    );
};

export default AFCFinancingPanel;
