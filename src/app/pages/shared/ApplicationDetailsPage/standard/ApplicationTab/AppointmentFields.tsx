import { Col, ColProps } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ApplicationDataFragment,
    AppointmentModuleSpecsFragment,
    VisitAppointmentModuleSpecsFragment,
} from '../../../../../api/fragments';
import { useGetModuleSpecsQuery, useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../../api/queries';
import { ApplicationStatus } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getOffset } from '../../../../../utilities/date';
import { retrieveAppointmentModule } from '../../../../portal/EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import { ApplicationModuleFragment } from '../../../../portal/StandardApplicationEntrypoint/AppointmentPage/shared';
// eslint-disable-next-line max-len
import useAppointmentAvailability from '../../../../portal/StandardApplicationEntrypoint/AppointmentPage/useAppointmentAvailability';
import colSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';
import { AppointmentDateTime } from '../useUpdates';

type AppointmentFieldsProps = {
    application?: ApplicationDataFragment;
    status: ApplicationStatus;
    showAppointmentType?: boolean;
    forCI?: boolean;
    overrideColSpan?: ColProps;
    isTestDrive?: boolean;
};

export const useBookedAppointmentTimeSlots = (
    moduleId: string
): {
    bookedAppointmentTimeSlots: (string | Date)[];
    module: AppointmentModuleSpecsFragment | VisitAppointmentModuleSpecsFragment;
} => {
    const { data: moduleData } = useGetModuleSpecsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { moduleId },
        skip: !moduleId,
    });

    const { module: moduleSpecs } = moduleData || {};

    // this should never happen, this is only for type safety
    const typedModule = useMemo(() => {
        if (moduleSpecs?.__typename !== 'AppointmentModule' && moduleSpecs?.__typename !== 'VisitAppointmentModule') {
            return null;
        }

        return moduleSpecs;
    }, [moduleSpecs]);

    const { data } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: typedModule?.id,
        },
        skip: !typedModule,
    });

    const bookedAppointmentTimeSlots = useMemo(
        () => data?.retrieveBlockedAppointmentTimeSlot || [],
        [data?.retrieveBlockedAppointmentTimeSlot]
    );

    return {
        module: typedModule,
        bookedAppointmentTimeSlots,
    };
};

const AppointmentFields = ({
    application,
    status,
    forCI,
    overrideColSpan = colSpan,
    showAppointmentType = true,
    isTestDrive = false,
}: AppointmentFieldsProps) => {
    const { t } = useTranslation(['applicationDetails']);
    const { values, setFieldValue } = useFormikContext<ApplicationFormValues & AppointmentDateTime>();

    const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
        application.module as ApplicationModuleFragment
    );

    const { bookedAppointmentTimeSlots, module: completeModuleAppointment } = useBookedAppointmentTimeSlots(
        isTestDrive ? appointmentModule?.id : visitAppointmentModule?.id
    );

    const { availableTimeSlots, isDisabledDate } = useAppointmentAvailability(
        completeModuleAppointment,
        bookedAppointmentTimeSlots,
        values.appointmentDate,
        application.module.company.timeZone,
        isTestDrive
            ? application.appointmentStage?.bookingTimeSlot?.slot
            : application.visitAppointmentStage?.bookingTimeSlot?.slot
    );

    useEffect(() => {
        if (values.appointmentDate && availableTimeSlots.length === 1) {
            setFieldValue('appointmentTime', availableTimeSlots[0].value);
        }
        if (!values.appointmentDate) {
            setFieldValue('appointmentTime', null);
        }
    }, [availableTimeSlots, setFieldValue, values.appointmentDate]);

    const { FormFields } = useThemeComponents();

    const disableUpdate = [
        ApplicationStatus.Cancelled,
        ApplicationStatus.AppointmentCheckIn,
        ApplicationStatus.Completed,
        ApplicationStatus.TestDriveStarted,
        ApplicationStatus.TestDriveCompleted,
    ].includes(status);

    const appointmentType = useMemo(() => (isTestDrive ? 'testDrive' : 'appointment'), [isTestDrive]);

    return (
        <>
            {showAppointmentType && (
                <Col {...overrideColSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.appointmentType', { returnObjects: true })}
                        value={t(`applicationDetails:fields.appointmentType.${appointmentType}`)}
                    />
                </Col>
            )}
            <Col {...overrideColSpan}>
                {disableUpdate ? (
                    <FormFields.DisplayField
                        {...t(`applicationDetails:fields.appointmentDate`, {
                            returnObjects: true,
                        })}
                        value={dayjs(values.appointmentDate)
                            .tz(application.module.company.timeZone)
                            .format(t('common:formats.datePicker'))}
                    />
                ) : (
                    <FormFields.DatePickerField
                        {...t(`applicationDetails:fields.appointmentDate`, {
                            returnObjects: true,
                        })}
                        disabled={disableUpdate}
                        disabledDate={isDisabledDate}
                        name="appointmentDate"
                        picker="date"
                        required
                    />
                )}
            </Col>
            <Col {...overrideColSpan}>
                {disableUpdate ? (
                    <FormFields.DisplayField
                        {...t(`applicationDetails:fields.appointmentTime`, {
                            returnObjects: true,
                            offset: getOffset(t, application.module.company.timeZone),
                        })}
                        value={dayjs(values.appointmentDate)
                            .tz(application.module.company.timeZone)
                            .format(t('common:formats.timePicker'))}
                    />
                ) : (
                    <FormFields.SelectField
                        {...t(`applicationDetails:fields.appointmentTime`, {
                            returnObjects: true,
                            offset: getOffset(t, application.module.company.timeZone),
                        })}
                        disabled={disableUpdate}
                        name="appointmentTime"
                        options={availableTimeSlots}
                        showSearch={!forCI}
                        style={{ width: '100%' }}
                        required
                    />
                )}
            </Col>
        </>
    );
};

export default AppointmentFields;
