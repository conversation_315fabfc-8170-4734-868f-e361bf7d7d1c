import { useApolloClient } from '@apollo/client';
import { MenuProps, message, Modal } from 'antd';
import { useFormikContext } from 'formik';
import { isEmpty, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled, { css } from 'styled-components';
import * as permissionKind from '../../../../../shared/permissions';
import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { useApproveApplicationMutation } from '../../../../api/mutations/approveApplication';
import { useCancelApplicationMutation } from '../../../../api/mutations/cancelApplication';
import { useCompleteApplicationMutation } from '../../../../api/mutations/completeApplication';
import { useDeclineApplicationMutation } from '../../../../api/mutations/declineApplication';
import { useRefreshApplicationStatusMutation } from '../../../../api/mutations/refreshApplicationStatus';
import { useRequestDisbursementMutation } from '../../../../api/mutations/requestDisbursement';
import {
    RequestReleaseLetterMutation,
    RequestReleaseLetterMutationVariables,
    RequestReleaseLetterDocument,
} from '../../../../api/mutations/requestReleaseLetter';
import { useGetApplicationAuditTrailKindsQuery } from '../../../../api/queries/getApplicationAuditTrailKinds';
import {
    ApplicationStage,
    ApplicationDocumentKind,
    ApplicationStatus,
    BankIntegrationProvider,
    AuditTrailKind,
    LayoutType,
} from '../../../../api/types';
import { CapSubmissionValue } from '../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import { useAccountContext } from '../../../../components/contexts/AccountContextManager';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { usePasswordModal } from '../../../../components/fields/DownloadModal/PasswordModal';
import breakpoints from '../../../../utilities/breakpoints';
import { downloadApplicationDocuments } from '../../../../utilities/export';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import hasPermissions from '../../../../utilities/hasPermissions';
import { useSelectCapCustomerOnApplicationDetails } from '../../JourneyPage/C@P';
import useEscapeKey from '../../useEscapeKey';
import ActionButton from '../generic/ActionButton';
import ContinueApplicationAction, { mayContinueApplication } from '../generic/Actions/ContinueApplicationAction';
import DropdownActions from '../generic/Actions/DropdownActions';
import useApplyNewButton from '../generic/Actions/useApplyNewButton';
import {
    ActionKey,
    isApplicationRequestForFinancing,
    type AFCApplicationFormValues,
    type ApplicationFormValues,
} from './shared';
import useValidateRequestReleaseLetter from './useValidateRequestReleaseLetter';

export const Container = styled.div<{ forCI?: boolean }>`
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    height: ${props => (props.forCI ? '84px' : '62px')};
    background-color: white;
    border-top: solid 0.5px #f7f7f2;
    border-bottom: solid 1px #cecec0;
    box-shadow: 0 -20px 20px -20px #0000001a;
    margin: 4rem 0rem 0rem 0rem;
    padding: 0rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    z-index: 11;

    ${({ forCI }) =>
        forCI &&
        css`
            @media screen and (min-width: ${breakpoints.md}) {
                padding: 0rem 3.75rem;
            }
        `}

    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        props.forCI &&
        css`
            @media (max-width: ${breakpoints.md}) {
                padding-left: clamp(16px, 1.25vw + 12px, 24px);
                padding-right: clamp(16px, 1.25vw + 12px, 24px);
            }
        `};
`;

export const SubActionContainer = styled.div<{ forCI?: boolean }>`
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    ${({ forCI }) =>
        forCI &&
        css`
            gap: 1rem;
        `}

    & .ant-btn {
        @media (max-width: ${breakpoints.xs}) {
            min-width: 32vw;
        }
    }

    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        props.forCI &&
        `
            @media (max-width: ${breakpoints.xs}) {
                gap: 0.5rem;
            }
        `};
`;

type ActionsTypeProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >;
    stage: ApplicationStage;
};

const disableAction = (
    application: ActionsTypeProps['application'],
    bankIntegration: BankIntegrationProvider,
    values: AFCApplicationFormValues,
    applicationStatusHistory: string[],
    actionKind: ActionKey
): boolean => {
    switch (actionKind) {
        case ActionKey.Void: {
            switch (bankIntegration) {
                case BankIntegrationProvider.Uob:
                    // Enabled after Bank Review in Progress, before Completed or Cancelled
                    return (
                        applicationStatusHistory.includes(AuditTrailKind.BankReviewInProgress) &&
                        !(
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCancelled) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCompleted) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationDeclined)
                        )
                    );

                default:
                    // Enabled after Submitted to Bank, before Completed or Cancelled
                    return (
                        applicationStatusHistory.includes(AuditTrailKind.ApplicationSubmittedToBank) &&
                        !(
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCancelled) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCompleted) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationDeclined)
                        )
                    );
            }
        }
        case ActionKey.Complete: {
            // Enabled after Approved, before Completed or Cancelled
            return (
                applicationStatusHistory.includes(AuditTrailKind.ApplicationApproved) &&
                !(
                    applicationStatusHistory.includes(AuditTrailKind.ApplicationCancelled) ||
                    applicationStatusHistory.includes(AuditTrailKind.ApplicationCompleted) ||
                    applicationStatusHistory.includes(AuditTrailKind.ApplicationDeclined)
                )
            );
        }

        case ActionKey.RequestReleaseLetter:
            switch (bankIntegration) {
                // Enabled after Approved and Chassis & Engine No. filled
                case BankIntegrationProvider.Email:
                    return !!(
                        application.financingStage?.status === ApplicationStatus.Approved &&
                        values?.otherVehicleInformation?.chassisNo &&
                        values?.otherVehicleInformation?.engineNo
                    );

                default:
                    return false;
            }

        case ActionKey.RequestDisbursement:
            switch (bankIntegration) {
                // Enabled after Approved and all 4 documents uploaded
                case BankIntegrationProvider.Dbs:
                case BankIntegrationProvider.Maybank:
                case BankIntegrationProvider.Email:
                    return !!(
                        application.financingStage?.status === ApplicationStatus.Approved &&
                        application.documents.find(({ kind }) => kind === ApplicationDocumentKind.SignedVsa) &&
                        application.documents.find(({ kind }) => kind === ApplicationDocumentKind.InsuranceCoverNote) &&
                        application.documents.find(({ kind }) => kind === ApplicationDocumentKind.LogCard) &&
                        application.documents.find(({ kind }) => kind === ApplicationDocumentKind.Invoice)
                    );

                default:
                    return false;
            }

        default:
            return false;
    }
};

export const mayResubmit = (
    application: ApplicationDataFragment,
    bankIntegration?: BankIntegrationProvider,
    applicationStatusHistory?: string[]
) => {
    if (!bankIntegration) {
        return true;
    }
    // application cannot be resubmitted while journey is ongoing
    switch (application.__typename) {
        case 'StandardApplication':
        case 'ConfiguratorApplication':
        case 'EventApplication':
        case 'FinderApplication': {
            switch (bankIntegration) {
                case BankIntegrationProvider.Dbs:
                    return false;

                case BankIntegrationProvider.Uob:
                    // Enabled after Submitted to Bank, before Completed or Cancelled
                    return (
                        application.draftFlow.isReceived &&
                        applicationStatusHistory.includes(AuditTrailKind.BankReviewInProgress) &&
                        !(
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCancelled) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCompleted) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationDeclined)
                        )
                    );

                default:
                    // Enabled after Bank Review in Progress, before Completed or Cancelled

                    return (
                        application.draftFlow.isReceived &&
                        applicationStatusHistory.includes(AuditTrailKind.ApplicationSubmittedToSystem) &&
                        !(
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCancelled) ||
                            applicationStatusHistory.includes(AuditTrailKind.ApplicationCompleted)
                        )
                    );
            }
        }

        default:
            return false;
    }
};

const useShowContinueApplication = ({
    application,
    stage,
}: {
    application: ActionsTypeProps['application'];
    stage: ApplicationStage;
}) => {
    const isRequestForFinancing = useMemo(() => isApplicationRequestForFinancing(application), [application]);

    const canContinueApplication = useMemo(
        () => mayContinueApplication(application, stage) || (isRequestForFinancing && application.draftFlow.isReceived),
        [application, isRequestForFinancing, stage]
    );

    return [isRequestForFinancing, canContinueApplication];
};

const mayShowSubmitChanges = (application, isRequestForFinancing) => {
    const { module } = application;
    switch (module.__typename) {
        case 'StandardApplicationModule': {
            return (
                application.draftFlow.isReceived &&
                (isNil(application.financingStage?.isDraft) ||
                    (!isNil(application.financingStage?.isDraft) &&
                        !application.financingStage?.isDraft &&
                        application.draftFlow.isApplyNewForFinancingReceived))
            );
        }
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
        case 'ConfiguratorModule': {
            return (
                application.draftFlow.isReceived &&
                !isRequestForFinancing &&
                (isNil(application.financingStage?.isDraft) ||
                    (!isNil(application.financingStage?.isDraft) &&
                        !application.financingStage?.isDraft &&
                        application.draftFlow.isApplyNewForFinancingReceived))
            );
        }

        default:
            return application.draftFlow.isReceived && !isRequestForFinancing;
    }
};

const Actions = ({ application, stage }: ActionsTypeProps) => {
    const apolloClient = useApolloClient();
    const passwordModal = usePasswordModal();
    const { token } = useAccountContext();
    const { t } = useTranslation(['applicationDetails']);
    const navigate = useNavigate();
    const { forCI } = useApplicationDetailsExtraContext();
    const selectCapBusinessPartnerAndLeadModal = useSelectCapCustomerOnApplicationDetails();

    const { values, setTouched, setErrors } = useFormikContext<ApplicationFormValues>();
    const { data: applicationAuditTrailKindsData } = useGetApplicationAuditTrailKindsQuery({
        variables: {
            applicationId: application.id,
            stage: ApplicationStage.Financing,
        },
        fetchPolicy: 'no-cache',
    });

    const applicationStatusHistory = useMemo(
        () => applicationAuditTrailKindsData?.result ?? [],
        [applicationAuditTrailKindsData?.result]
    );

    const bankIntegration = application.bank?.integration?.provider;

    const [approveMutation] = useApproveApplicationMutation();
    const [declineMutation] = useDeclineApplicationMutation();
    const [cancelMutation] = useCancelApplicationMutation();
    const [completeMutation] = useCompleteApplicationMutation();
    const [requestDisbursement] = useRequestDisbursementMutation();
    const [refreshApplicationStatus] = useRefreshApplicationStatusMutation();

    const onCancel = useCallback(() => navigate(-1), [navigate]);
    useEscapeKey(onCancel);

    const performAction = useCallback(
        async (action: ActionKey, capValues?: CapSubmissionValue) => {
            const { submittedKey, submittingKey, mutation } = (() => {
                const baseVariables = { applicationId: application.id, stage };
                switch (action) {
                    case ActionKey.Approve:
                        return {
                            mutation: () => approveMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.approvingApplication',
                            submittedKey: 'applicationDetails:messages.approvedApplication',
                        };

                    case ActionKey.Void:
                        return {
                            mutation: () => cancelMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.cancellingApplication',
                            submittedKey: 'applicationDetails:messages.cancelledApplication',
                        };

                    case ActionKey.Decline:
                        return {
                            mutation: () => declineMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.decliningApplication',
                            submittedKey: 'applicationDetails:messages.declinedApplication',
                        };

                    case ActionKey.Complete:
                        return {
                            mutation: () => completeMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.completingApplication',
                            submittedKey: 'applicationDetails:messages.completedApplication',
                        };

                    case ActionKey.RequestDisbursement:
                        return {
                            mutation: () => requestDisbursement({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.requestingDisbursement',
                            submittedKey: 'applicationDetails:messages.requestedDisbursement',
                        };

                    case ActionKey.RefreshApplicationStatus:
                        return {
                            mutation: () => refreshApplicationStatus({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.refreshingApplicationStatus',
                            submittedKey: 'applicationDetails:messages.refreshedApplicationStatus',
                        };

                    default:
                        throw new Error('not implemented');
                }
            })();

            message.loading({
                content: t(submittingKey),
                key: 'primary',
                duration: 0,
            });

            try {
                await mutation();

                message.success({
                    content: t(submittedKey),
                    key: 'primary',
                });
                navigate(-1);
            } catch (error) {
                message.destroy('primary');
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors?.$root) {
                    message.error(apolloErrors?.$root);
                }
            }
        },
        [
            t,
            application.id,
            stage,
            approveMutation,
            cancelMutation,
            declineMutation,
            completeMutation,
            requestDisbursement,
            refreshApplicationStatus,
            navigate,
        ]
    );

    const validateRequestReleaseLetter = useValidateRequestReleaseLetter();

    const requestReleaseLetter = useCallback(async () => {
        const validated = validateRequestReleaseLetter(values);
        if (!isEmpty(validated)) {
            setErrors(validated);
            setTouched(validated, false);

            return;
        }

        message.loading({
            content: t('applicationDetails:messages.requestingReleaseLetter'),
            key: 'primary',
            duration: 0,
        });

        await apolloClient.mutate<RequestReleaseLetterMutation, RequestReleaseLetterMutationVariables>({
            mutation: RequestReleaseLetterDocument,
            variables: {
                applicationId: values.id,
                settings: {
                    chassisNo: values.otherVehicleInformation.chassisNo,
                    engineNo: values.otherVehicleInformation.engineNo,
                },
            },
        });

        message.success({
            content: t('applicationDetails:messages.requestedReleaseLetter'),
            key: 'primary',
        });

        navigate(-1);
    }, [apolloClient, t, values, navigate, setErrors, setTouched, validateRequestReleaseLetter]);

    const onDownloadDocuments = useCallback(async () => {
        const password = await downloadApplicationDocuments(application.id, token, stage);

        if (password) {
            passwordModal.open(password);
        }
    }, [application.id, passwordModal, stage, token]);

    const confirmAction = useCallback(
        (action: ActionKey, capValues?: CapSubmissionValue) => {
            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${action}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    performAction(action, capValues);
                },
            });
        },
        [performAction, t]
    );

    const applyNewButton = useApplyNewButton(application, stage);
    const actions = useMemo<MenuProps['items']>(
        () =>
            [
                {
                    key: ActionKey.Void,
                    label:
                        stage === ApplicationStage.Appointment
                            ? t('applicationDetails:appointmentActions.void')
                            : t('applicationDetails:actions.void'),
                    disabled: !disableAction(
                        application,
                        bankIntegration,
                        values,
                        applicationStatusHistory,
                        ActionKey.Void
                    ),
                    onClick: () => confirmAction(ActionKey.Void),
                },
                {
                    key: ActionKey.Complete,
                    label:
                        stage === ApplicationStage.Appointment
                            ? t('applicationDetails:appointmentActions.complete')
                            : t('applicationDetails:actions.complete'),
                    disabled: !disableAction(
                        application,
                        bankIntegration,
                        values,
                        applicationStatusHistory,
                        ActionKey.Complete
                    ),
                    onClick: () => confirmAction(ActionKey.Complete),
                },
                {
                    key: ActionKey.Approve,
                    label: t('applicationDetails:actions.approve'),
                    onClick: () => confirmAction(ActionKey.Approve),
                    disabled: !hasPermissions(application.permissions, [permissionKind.approvalApplication]),
                },
                {
                    key: ActionKey.Decline,
                    label: t('applicationDetails:actions.decline'),
                    onClick: () => confirmAction(ActionKey.Decline),
                    disabled: !hasPermissions(application.permissions, [permissionKind.approvalApplication]),
                },
                bankIntegration === BankIntegrationProvider.Email && {
                    key: ActionKey.RequestReleaseLetter,
                    label: t('applicationDetails:actions.requestReleaseLetter'),
                    disabled: !disableAction(
                        application,
                        bankIntegration,
                        values,
                        applicationStatusHistory,
                        ActionKey.RequestReleaseLetter
                    ),
                    onClick: () => requestReleaseLetter(),
                },
                {
                    key: ActionKey.RequestDisbursement,
                    label: t('applicationDetails:actions.requestDisbursement'),
                    disabled: !disableAction(
                        application,
                        bankIntegration,
                        values,
                        applicationStatusHistory,
                        ActionKey.RequestDisbursement
                    ),
                    onClick: () => performAction(ActionKey.RequestDisbursement),
                },
                (bankIntegration === BankIntegrationProvider.Uob ||
                    bankIntegration === BankIntegrationProvider.Maybank) && {
                    key: ActionKey.RefreshApplicationStatus,
                    label: t('applicationDetails:actions.refreshApplicationStatus'),
                    onClick: () => performAction(ActionKey.RefreshApplicationStatus),
                },
                {
                    key: ActionKey.DownloadAllDocuments,
                    label: t('applicationDetails:actions.downloadAllFiles'),
                    onClick: onDownloadDocuments,
                },
                applyNewButton,
            ].filter(Boolean),
        [
            stage,
            t,
            application,
            bankIntegration,
            values,
            applicationStatusHistory,
            onDownloadDocuments,
            applyNewButton,
            confirmAction,
            requestReleaseLetter,
            performAction,
        ]
    );

    const canResubmit = useMemo(
        () => mayResubmit(application, bankIntegration, applicationStatusHistory),
        [application, applicationStatusHistory, bankIntegration]
    );

    const [isRequestForFinancing, canContinueApplication] = useShowContinueApplication({
        application,
        stage,
    });

    const submitButton = useMemo(() => {
        // application is now received, we can submit changes
        if (mayShowSubmitChanges(application, isRequestForFinancing)) {
            return (
                <ActionButton
                    key="submit"
                    disabled={!canResubmit}
                    forCI={forCI}
                    form="applicationForm"
                    htmlType="submit"
                    size="large"
                    type="primary"
                >
                    {t('applicationDetails:buttons.submit')}
                </ActionButton>
            );
        }

        return null;
    }, [application, isRequestForFinancing, canResubmit, forCI, t]);

    return (
        <Container forCI={forCI}>
            {passwordModal.render()}
            <SubActionContainer forCI={forCI}>
                <DropdownActions actions={actions} forCI={forCI} />
                {submitButton}

                {canContinueApplication && (
                    // application is not received or request financing is still in progress,
                    // we may continue the application
                    <ContinueApplicationAction
                        application={application}
                        forCI={forCI}
                        isRequestForFinancing={isRequestForFinancing}
                        stage={stage}
                    />
                )}
                {selectCapBusinessPartnerAndLeadModal.render()}
            </SubActionContainer>
        </Container>
    );
};

export default Actions;
