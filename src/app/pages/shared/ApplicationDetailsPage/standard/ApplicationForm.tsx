/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import * as permissionKind from '../../../../../shared/permissions';
import { ApplicationStage, LocalCustomerFieldKey } from '../../../../api/types';
import getCalculatorValuesFromApplication from '../../../../calculator/getCalculatorValuesFromApplication';
import getInsuranceCalculatorValueFromApplication from '../../../../calculator/getInsuranceCalculatorValueFromApplication';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { useRouter } from '../../../../components/contexts/shared';
import Form from '../../../../components/fields/Form';
import hasPermissions from '../../../../utilities/hasPermissions';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useAgreements from '../../../../utilities/useAgreements';
import useDebounce from '../../../../utilities/useDebounce';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { useGetInitialQuotationValues, useQuotationValidator } from '../../JourneyPage/QuotationDetails';
import { GenericFormApplication } from '../generic/shared';
import Actions from './Actions';
import ApplicationAction from './ApplicationTab/ApplicationAction';
import type { ApplicationFormValues } from './shared';
import useSubmitChanges from './useSubmitChanges';

export const StyledForm = styled(Form)`
    .ant-form-item-label > label {
        color: rgba(0, 0, 0, 0.75);
    }

    .ant-form-item-label > label.ant-form-item-required::before {
        display: none;
    }
`;

export type ApplicationFormProps = {
    application: GenericFormApplication;
    stage: ApplicationStage;
};

const ApplicationForm = ({ application, stage }: ApplicationFormProps) => {
    const router = useRouter();
    const routerId = router?.id;
    const { forCI } = useApplicationDetailsExtraContext();

    const {
        applicantAgreements,
        agreementValues: agreements,
        guarantorAgreements,
        guarantorAgreementValues,
    } = useAgreements({ application, stage });

    const usedApplicantKyc = useMemo(
        () =>
            application.applicant.__typename === 'CorporateCustomer'
                ? application.corporateKYC
                : application.applicantKYC,
        [application.applicant, application.corporateKYC, application.applicantKYC]
    );

    const applicantKyc = useMemo(
        () =>
            usedApplicantKyc.filter(preset => {
                if (
                    preset.key === LocalCustomerFieldKey.DrivingLicense ||
                    preset.key === LocalCustomerFieldKey.DrivingLicenseTh ||
                    preset.key === LocalCustomerFieldKey.DrivingLicenseMy
                ) {
                    return application.configuration.testDrive;
                }

                return true;
            }),
        [usedApplicantKyc, application.configuration.testDrive]
    );

    const kycExtraSettings = useMemo(() => {
        if (application?.__typename === 'EventApplication') {
            return application.event?.kycExtraSettings;
        }

        switch (application?.module?.__typename) {
            case 'ConfiguratorModule':
            case 'FinderApplicationPrivateModule':
            case 'FinderApplicationPublicModule':
            case 'StandardApplicationModule':
                return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                    ? application.module.customerModule.extraSettings
                    : null;

            default:
                return null;
        }
    }, [application]);

    // Validation for KYC should be based on `customer` instead of `applicant.fields`
    // since `applicant.fields` from server, the values stored is only the non-empty one
    const applicantValidator = useKYCFormValidator({
        field: applicantKyc,
        extraSettings: kycExtraSettings,
        moduleCountryCode: application.module.company.countryCode,
        prefix: 'customer.fields',
        saveDraft: false,
        bankProvider: application.bank?.integration.provider,
    });

    const quotationValidator = useQuotationValidator(
        application.bank?.integration.__typename === 'EnbdBankIntegration' &&
            application.__typename !== 'EventApplication' &&
            application.configuration.withFinancing,
        0,
        'quotation'
    );

    const getQuotationInitialValue = useGetInitialQuotationValues();

    const initialValues = useMemo((): ApplicationFormValues => {
        const { availableAssignees, documents, promoCode, endpoint, guarantor, ...otherApplicationValues } =
            application;
        // split documents into corresponding categories

        return {
            ...otherApplicationValues,
            applicantAgreements,
            customer: {
                ...otherApplicationValues.applicant,
                fields: getInitialValues(
                    otherApplicationValues.applicant.fields,
                    otherApplicationValues.applicant.__typename === 'CorporateCustomer'
                        ? otherApplicationValues.corporateKYC
                        : otherApplicationValues.applicantKYC
                ),
            },
            ...(guarantor && {
                guarantor: {
                    ...guarantor,
                    fields: getInitialValues(guarantor.fields, otherApplicationValues.guarantorKYC),
                },
                guarantorAgreements,
            }),
            financing: getCalculatorValuesFromApplication(application),
            insurancing:
                application.__typename !== 'EventApplication' &&
                application.__typename !== 'LaunchpadApplication' &&
                application.insurancing
                    ? getInsuranceCalculatorValueFromApplication(application.insurancing)
                    : undefined,
            vehicle: application.vehicle,
            bank: application.bank,
            financeProduct: application.financeProduct,
            withFinancing:
                application.__typename !== 'EventApplication' ? application.configuration.withFinancing : false,
            promoCode,
            agreements,
            guarantorAgreementValues,
            withInsurance:
                application.__typename !== 'EventApplication' ? application.configuration.withInsurance : false,
            remarks: application.remarks,
            commentsToInsurer:
                (application.__typename === 'StandardApplication' ||
                    application.__typename === 'ConfiguratorApplication' ||
                    application.__typename === 'FinderApplication') &&
                application.commentsToInsurer
                    ? application.commentsToInsurer
                    : undefined,
            quotation: getQuotationInitialValue({
                bank: application.bank,
                financing: application.financing,
                isFinancingEnabled:
                    application.__typename !== 'EventApplication' && application.__typename !== 'LaunchpadApplication'
                        ? application.configuration.withFinancing
                        : false,
                initialValues:
                    application.__typename !== 'EventApplication' && application.__typename !== 'LaunchpadApplication'
                        ? application.quotation
                        : undefined,
            }),
            lead: application.lead,
        };
    }, [
        application,
        applicantAgreements,
        guarantorAgreements,
        agreements,
        guarantorAgreementValues,
        getQuotationInitialValue,
    ]);

    const validations = useMemo(
        () => validators.compose(applicantValidator, quotationValidator),
        [applicantValidator, quotationValidator]
    );

    const validate = useValidator(validations);

    const apolloClient = useApolloClient();
    const { t } = useTranslation('applicationDetails');

    const navigate = useNavigate();

    const onSubmit = useDebounce(
        useHandleError(
            async (values: ApplicationFormValues) => {
                message.loading({
                    content: t('applicationDetails:messages.submittingChanges'),
                    key: 'primary',
                    duration: 0,
                });

                const { application: nextApplication, redirectionLink } = await useSubmitChanges(
                    apolloClient,
                    initialValues,
                    values,
                    !isNil(routerId),
                    stage,
                    hasPermissions(application.permissions, [permissionKind.allowRecalculateApplication])
                );

                if (nextApplication.__typename === 'MobilityApplication') {
                    throw new Error('MobilityApplication is not supported');
                }

                // inform about success
                message.success({
                    content: t('applicationDetails:messages.submittedChanges'),
                    key: 'primary',
                });

                // when the user submit changes in CI and continue existing journey
                if (redirectionLink) {
                    window.open(redirectionLink);
                } else if (forCI) {
                    navigate(-1);
                }
            },
            [t, apolloClient, initialValues, routerId, forCI, stage, navigate, application]
        )
    );

    return (
        <Formik<ApplicationFormValues> initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <StyledForm id="applicationForm" name="applicationForm" onSubmitCapture={handleSubmit}>
                    <ApplicationAction
                        application={application}
                        isMask={application.applicant.isMaskingCustomerData}
                        stage={stage}
                    />
                    <Actions application={application} stage={stage} />
                </StyledForm>
            )}
        </Formik>
    );
};

export default ApplicationForm;
