/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message, Modal } from 'antd';
import dayjs from 'dayjs';
import isEqual from 'fast-deep-equal';
import { Formik, FormikHelpers } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import { useUpdateAppointmentDataMutation } from '../../../../api/mutations/updateAppointmentData';
import { useUpdateTestDriveDataMutation } from '../../../../api/mutations/updateTestDriveData';
import { ApplicationStage } from '../../../../api/types';
import getCalculatorValuesFromApplication from '../../../../calculator/getCalculatorValuesFromApplication';
import getInsuranceCalculatorValueFromApplication from '../../../../calculator/getInsuranceCalculatorValueFromApplication';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import hasPermissions from '../../../../utilities/hasPermissions';
import { getInitialValues, KYCPresetFormFields, prepareKYCFieldPayload } from '../../../../utilities/kycPresets';
import useAgreements from '../../../../utilities/useAgreements';
import useDebounce from '../../../../utilities/useDebounce';
import useHandleError from '../../../../utilities/useHandleError';
import useDialCodeByCountry from '../../../admin/CompanyDetailsPage/useDialCodeByCountry';
import { useNavigateWithBlock } from '../../blockBackRedirection';
import { StyledForm } from '../standard/ApplicationForm';
import type { AFCApplicationFormValues, ApplicationFormValues } from '../standard/shared';
import useSubmitChanges from '../standard/useSubmitChanges';
import { AppointmentDateTime, hasCustomerUpdate, prepareAppointmentChanges } from '../standard/useUpdates';
import AppointmentFooterAction from './AppointmentFooterAction';
import FormAction from './FormAction';
import InsuranceFooterAction from './InsuranceFooterAction';
import ReservationActions from './ReservationFooter';
import VisitAppointmentFooterAction from './VisitAppointmentFooterAction';
import { GenericFormApplication, getAppointmentDateAndTime } from './shared';

export type GenericFormProps = {
    application: GenericFormApplication;
    stage: ApplicationStage;
    endpointId?: string;
};

const GenericForm = ({ application, stage }: GenericFormProps) => {
    const apolloClient = useApolloClient();
    const routerId = useRouter()?.id;
    const { t } = useTranslation(['applicationDetails']);

    const navigate = useNavigateWithBlock();
    const { forCI } = useApplicationDetailsExtraContext();

    const {
        applicantAgreements,
        agreementValues: agreements,
        guarantorAgreements,
        guarantorAgreementValues,
        showroomVisitAgreementValues,
    } = useAgreements({ application, stage });

    const combinedApplicantAgreements = useMemo(
        () => ({
            ...agreements,
            ...((application.__typename === 'EventApplication' || application.__typename === 'LaunchpadApplication') &&
                application.configuration.visitAppointment &&
                showroomVisitAgreementValues),
        }),
        [agreements, application, showroomVisitAgreementValues]
    );

    const company = useCompany(true);
    const dialCodeByCountry = useDialCodeByCountry();
    const [updateAppointmentData] = useUpdateAppointmentDataMutation();
    const [updateTestDriveData] = useUpdateTestDriveDataMutation();

    const [appointmentDate, appointmentTime] = useMemo(
        () => getAppointmentDateAndTime(stage, application, t),
        [stage, application, t]
    );

    const initialValues = useMemo((): ApplicationFormValues & AppointmentDateTime => {
        const { availableAssignees, documents, promoCode, endpoint, guarantor, ...otherApplicationValues } =
            application;

        // split documents into corresponding categories
        return {
            ...otherApplicationValues,
            applicantAgreements,
            customer: {
                ...otherApplicationValues.applicant,
                fields: getInitialValues(
                    otherApplicationValues.applicant.fields,
                    otherApplicationValues.applicant.__typename === 'CorporateCustomer'
                        ? otherApplicationValues.corporateKYC
                        : otherApplicationValues.applicantKYC
                ),
            },
            ...(guarantor && {
                guarantor: {
                    ...guarantor,
                    fields: getInitialValues(guarantor.fields, otherApplicationValues.guarantorKYC),
                },
                guarantorAgreements,
            }),
            financing: getCalculatorValuesFromApplication(application),
            insurancing:
                application.__typename !== 'EventApplication' &&
                application.__typename !== 'LaunchpadApplication' &&
                application.insurancing
                    ? getInsuranceCalculatorValueFromApplication(application.insurancing)
                    : undefined,
            vehicle: application.vehicle,
            bank: application.bank,
            financeProduct: application.financeProduct,
            withFinancing:
                application.__typename !== 'EventApplication' ? application.configuration.withFinancing : undefined,
            agreements: combinedApplicantAgreements,
            guarantorAgreementValues,
            withInsurance:
                application.__typename !== 'EventApplication' ? application.configuration.withInsurance : undefined,
            appointmentDate,
            appointmentTime,
            remarks: application.remarks,
            commentsToInsurer:
                (application.__typename === 'StandardApplication' ||
                    application.__typename === 'ConfiguratorApplication' ||
                    application.__typename === 'FinderApplication') &&
                application.commentsToInsurer
                    ? application.commentsToInsurer
                    : undefined,
            lead: application.lead,
        };
    }, [
        application,
        applicantAgreements,
        guarantorAgreements,
        combinedApplicantAgreements,
        guarantorAgreementValues,
        appointmentDate,
        appointmentTime,
    ]);

    const checkSameCustomer = useCallback(
        (valuesToSubmit: KYCPresetFormFields): boolean => {
            const initialValuesFields = initialValues.customer.fields;
            const countryCodeOrName = company?.countryCode;
            const dialCode = dialCodeByCountry(countryCodeOrName);
            const updates = prepareKYCFieldPayload(valuesToSubmit);

            return !hasCustomerUpdate(updates, initialValuesFields, valuesToSubmit, { dialCode });
        },
        [company?.countryCode, dialCodeByCountry, initialValues.customer.fields]
    );

    const hasChanges = useCallback(
        (valuesToSubmit: ApplicationFormValues) => {
            const isSameCustomer = checkSameCustomer(valuesToSubmit.customer.fields);

            const appointmentSlot =
                stage === ApplicationStage.Appointment
                    ? valuesToSubmit.appointmentStage?.bookingTimeSlot?.slot
                    : valuesToSubmit.visitAppointmentStage?.bookingTimeSlot?.slot;
            const initialSlot =
                stage === ApplicationStage.Appointment
                    ? initialValues.appointmentStage?.bookingTimeSlot?.slot
                    : initialValues.visitAppointmentStage?.bookingTimeSlot?.slot;

            const isSameAppointmentDate = dayjs(appointmentSlot).isSame(initialSlot, 'minutes');

            const isSameMileage = isEqual(
                valuesToSubmit.appointmentStage?.mileage,
                initialValues.appointmentStage?.mileage
            );
            const isSameRegistrationNumber =
                valuesToSubmit.appointmentStage?.registrationNumber ===
                initialValues.appointmentStage?.registrationNumber;

            return {
                appointment: !isSameAppointmentDate && isSameCustomer && isSameMileage && isSameRegistrationNumber,
                testDrive: isSameAppointmentDate && isSameCustomer && (!isSameMileage || !isSameRegistrationNumber),
            };
        },
        [
            checkSameCustomer,
            initialValues.appointmentStage?.bookingTimeSlot?.slot,
            initialValues.appointmentStage?.mileage,
            initialValues.appointmentStage?.registrationNumber,
            initialValues.visitAppointmentStage?.bookingTimeSlot?.slot,
            stage,
        ]
    );

    const checkIsAppointmentDataOnly = useCallback(
        (valuesToSubmit: ApplicationFormValues) => {
            if (
                (stage !== ApplicationStage.Appointment && stage !== ApplicationStage.VisitAppointment) ||
                (stage === ApplicationStage.Appointment && !valuesToSubmit.appointmentStage) ||
                (stage === ApplicationStage.VisitAppointment && !valuesToSubmit.visitAppointmentStage)
            ) {
                return false;
            }

            return hasChanges(valuesToSubmit).appointment;
        },
        [stage, hasChanges]
    );

    const checkIsTestDriveDataOnly = useCallback(
        (valuesToSubmit: ApplicationFormValues) => {
            if (stage !== ApplicationStage.Appointment || !valuesToSubmit.appointmentStage) {
                return false;
            }

            return hasChanges(valuesToSubmit).testDrive;
        },
        [stage, hasChanges]
    );

    const onSubmit = useHandleError(
        async (values: ApplicationFormValues & AppointmentDateTime, { setFieldValue }) => {
            message.loading({
                content: t('applicationDetails:messages.submittingChanges'),
                key: 'primary',
                duration: 0,
            });
            const valuesToSubmit = prepareAppointmentChanges(values, stage, application.module.company.timeZone);
            const isAppointmentDataOnly = checkIsAppointmentDataOnly(valuesToSubmit);
            const isTestDriveDataOnly = checkIsTestDriveDataOnly(valuesToSubmit);

            if (isAppointmentDataOnly) {
                await updateAppointmentData({
                    variables: {
                        applicationId: values.id,
                        appointmentData: {
                            bookingTimeSlot:
                                stage === ApplicationStage.Appointment
                                    ? valuesToSubmit.appointmentStage?.bookingTimeSlot?.slot
                                    : valuesToSubmit.visitAppointmentStage?.bookingTimeSlot?.slot,
                        },
                        stage,
                    },
                });

                // inform about success
                message.success({
                    content: t('applicationDetails:messages.submittedChanges'),
                    key: 'primary',
                });

                // Navigate back too, to make it same with submit changes

                if (forCI) {
                    navigate(-1);
                }

                return;
            }

            if (isTestDriveDataOnly) {
                await updateTestDriveData({
                    variables: {
                        applicationId: values.id,
                        testDriveData: {
                            registrationNumber: valuesToSubmit.appointmentStage?.registrationNumber,
                            mileage: valuesToSubmit.appointmentStage?.mileage,
                        },
                    },
                });

                // inform about success
                message.success({
                    content: t('applicationDetails:messages.submittedChanges'),
                    key: 'primary',
                });

                // Navigate back too, to make it same with submit changes
                if (forCI) {
                    navigate(-1);
                }

                return;
            }

            const submitChanges = await useSubmitChanges(
                apolloClient,
                initialValues,
                valuesToSubmit,
                !isNil(routerId),
                stage,
                hasPermissions(application.permissions, [permissionKind.allowRecalculateApplication])
            );

            const { application: nextApplication, redirectionLink } = submitChanges;

            if (nextApplication.__typename === 'MobilityApplication') {
                throw new Error('MobilityApplication is not supported');
            }

            // inform about success
            message.success({
                content: t('applicationDetails:messages.submittedChanges'),
                key: 'primary',
            });

            if (redirectionLink) {
                window.open(redirectionLink);
            } else if (forCI) {
                navigate(-1);
            }
        },
        [
            t,
            stage,
            application.module.company.timeZone,
            application.permissions,
            forCI,
            checkIsAppointmentDataOnly,
            checkIsTestDriveDataOnly,
            apolloClient,
            initialValues,
            routerId,
            updateAppointmentData,
            navigate,
            updateTestDriveData,
        ]
    );

    const confirmSubmit = useCallback(
        (values: ApplicationFormValues & AppointmentDateTime, heplers: FormikHelpers<AFCApplicationFormValues>) => {
            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${stage.toLowerCase()}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    onSubmit(values, heplers);
                },
            });
        },
        [t, stage, onSubmit]
    );
    const debounceConfirmSubmit = useDebounce(confirmSubmit);

    const footer = useMemo(() => {
        switch (stage) {
            case ApplicationStage.Insurance:
                return <InsuranceFooterAction application={application} stage={stage} />;

            case ApplicationStage.Appointment:
                return <AppointmentFooterAction application={application} stage={stage} />;

            case ApplicationStage.Reservation:
                return <ReservationActions application={application} stage={stage} />;

            case ApplicationStage.VisitAppointment:
                return <VisitAppointmentFooterAction application={application} stage={stage} />;

            default:
                throw new Error('Invalid application stage');
        }
    }, [stage, application]);

    return (
        <Formik<ApplicationFormValues> initialValues={initialValues} onSubmit={debounceConfirmSubmit}>
            {({ handleSubmit }) => (
                <>
                    <StyledForm id="applicationForm" name="applicationForm" onSubmitCapture={handleSubmit}>
                        <FormAction
                            application={application}
                            isMask={application.applicant.isMaskingCustomerData}
                            stage={stage}
                        />
                    </StyledForm>
                    {footer}
                </>
            )}
        </Formik>
    );
};

export default GenericForm;
