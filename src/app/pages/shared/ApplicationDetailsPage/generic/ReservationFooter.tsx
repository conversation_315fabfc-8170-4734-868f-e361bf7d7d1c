import { MenuProps, message, Modal } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { useCancelApplicationMutation } from '../../../../api/mutations/cancelApplication';
import { useCompleteApplicationMutation } from '../../../../api/mutations/completeApplication';
import { useConcludeAgreementApplicationMutation } from '../../../../api/mutations/concludeAgreementApplication';
import { useGetApplicationAuditTrailsQuery } from '../../../../api/queries/getApplicationAuditTrails';
import { ApplicationStage, ApplicationStatus } from '../../../../api/types';
import { CapSubmissionValue } from '../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { getApplicationStatus } from '../../../../utilities/application';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import { useNavigateWithBlock } from '../../blockBackRedirection';
import useEscapeKey from '../../useEscapeKey';
import { Container, mayResubmit, SubActionContainer } from '../standard/Actions';
import { ActionKey } from '../standard/shared';
import ActionButton from './ActionButton';
import ContinueApplicationAction, { mayContinueApplication } from './Actions/ContinueApplicationAction';
import DropdownActions from './Actions/DropdownActions';
import useApplyNewButton from './Actions/useApplyNewButton';

type ReservationActionsProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >;
    stage?: ApplicationStage;
    status?: ApplicationStatus;
};

const ReservationActions = ({
    application,
    stage = ApplicationStage.Reservation,
    status: applicationStatus,
}: ReservationActionsProps) => {
    const { t } = useTranslation('applicationDetails');
    const { forCI } = useApplicationDetailsExtraContext();

    const navigate = useNavigateWithBlock();

    const { data: applicationAuditTrailData } = useGetApplicationAuditTrailsQuery({
        variables: {
            applicationId: application.id,
            stage,
            pagination: { limit: 50, offset: 0 },
        },
    });

    const applicationStatusHistory = useMemo(() => {
        if (applicationAuditTrailData?.result?.items) {
            return applicationAuditTrailData?.result?.items.map(item => item.kind);
        }

        return [];
    }, [applicationAuditTrailData]);
    const bankIntegration = application.bank?.integration?.provider;

    const status = useMemo(
        () => applicationStatus || getApplicationStatus(application, stage),
        [applicationStatus, application, stage]
    );

    const mayAgreementConcluded = useMemo(
        () =>
            (application.module.__typename === 'FinderApplicationPublicModule' ||
                application.module.__typename === 'FinderApplicationPrivateModule') &&
            application.module.emailContents.reminderEmail.defaultValue.isActive &&
            status === ApplicationStatus.SubmittedToSystem,
        [application.module, status]
    );

    const onCancel = useCallback(() => navigate(-1), [navigate]);

    useEscapeKey(onCancel);

    const [cancelMutation] = useCancelApplicationMutation();
    const [completeMutation] = useCompleteApplicationMutation();
    const [concludeAgreementMutation] = useConcludeAgreementApplicationMutation();

    const submit = useCallback(
        async (action: ActionKey) => {
            const { submittedKey, submittingKey, mutation } = (() => {
                const baseVariables = { applicationId: application.id, stage };
                switch (action) {
                    case ActionKey.Void:
                        return {
                            mutation: () => cancelMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.cancellingApplication',
                            submittedKey: 'applicationDetails:messages.cancelledApplication',
                        };

                    case ActionKey.Complete:
                        return {
                            mutation: () => completeMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.completingApplication',
                            submittedKey: 'applicationDetails:messages.completedApplication',
                        };

                    case ActionKey.AgreementConcluded:
                        return {
                            mutation: () => concludeAgreementMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.concludingAgreementApplication',
                            submittedKey: 'applicationDetails:messages.concludedAgreementApplication',
                        };

                    default:
                        throw new Error('not implemented');
                }
            })();

            message.loading({
                content: t(submittingKey),
                key: 'primary',
                duration: 0,
            });

            try {
                await mutation();

                message.success({
                    content: t(submittedKey),
                    key: 'primary',
                });

                navigate(-1);
            } catch (error) {
                message.destroy('primary');
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors?.$root) {
                    message.error(apolloErrors?.$root);
                }
            }
        },
        [t, application.id, stage, cancelMutation, completeMutation, concludeAgreementMutation, navigate]
    );

    const confirmAction = useCallback(
        (action: ActionKey) => {
            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${action}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    submit(action);
                },
            });
        },
        [submit, t]
    );

    const applyNewButton = useApplyNewButton(application, stage);
    const actions = useMemo<MenuProps['items']>(
        () =>
            [
                {
                    key: ActionKey.Void,
                    label: t('applicationDetails:actions.void'),
                    onClick: () => confirmAction(ActionKey.Void),
                },
                {
                    key: ActionKey.Complete,
                    label: t('applicationDetails:actions.complete'),
                    onClick: () => confirmAction(ActionKey.Complete),
                },
                mayAgreementConcluded && {
                    key: ActionKey.AgreementConcluded,
                    label: t('applicationDetails:actions.agreementConcluded'),
                    onClick: () => confirmAction(ActionKey.AgreementConcluded),
                },
                applyNewButton,
            ].filter(Boolean),
        [t, mayAgreementConcluded, application.lead?.status, applyNewButton, confirmAction]
    );

    const submitButton = useMemo(() => {
        if (application.draftFlow.isReceived) {
            const hasOnlyReservation =
                application.stages.length === 1 && application.stages.includes(ApplicationStage.Reservation);
            if (hasOnlyReservation || mayResubmit(application, bankIntegration, applicationStatusHistory)) {
                return (
                    <ActionButton
                        key="submit"
                        forCI={forCI}
                        form="applicationForm"
                        htmlType="submit"
                        size="large"
                        type="primary"
                    >
                        {t('applicationDetails:buttons.submit')}
                    </ActionButton>
                );
            }

            // see: AN-2320, DO NOT show submit button if submissions are not allowed
            return null;
        }

        return null;
    }, [application, bankIntegration, applicationStatusHistory, forCI, t]);

    const canContinueApplication = mayContinueApplication(application, stage);

    // no action available
    if (!canContinueApplication && !actions.length && !submitButton) {
        return null;
    }

    return (
        <Container forCI={forCI}>
            <SubActionContainer forCI={forCI}>
                <DropdownActions actions={actions} forCI={forCI} />
                {submitButton}
                {canContinueApplication && (
                    <ContinueApplicationAction application={application} forCI={forCI} stage={stage} />
                )}
            </SubActionContainer>
        </Container>
    );
};

export default ReservationActions;
