/* eslint-disable max-len */
import { type MenuProps, message, Modal } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import type { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { useCancelApplicationMutation } from '../../../../api/mutations/cancelApplication';
import { useCompleteApplicationMutation } from '../../../../api/mutations/completeApplication';
import {
    ConfirmBookingApplicationMutationVariables,
    useConfirmBookingApplicationMutation,
} from '../../../../api/mutations/confirmBookingApplication';
import { ApplicationStage, ApplicationStatus, LeadStatus } from '../../../../api/types';
import { type CapSubmissionValue } from '../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import { SubmissionPurpose } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { getApplicationStatus } from '../../../../utilities/application';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import useCheckCapIntegrationIsOn from '../../../../utilities/useCheckCapIntegrationIsOn';
import { hasCapPrequalificationCheck, useSelectCapCustomerOnApplicationDetails } from '../../JourneyPage/C@P';
import { useNavigateWithBlock } from '../../blockBackRedirection';
import useEscapeKey from '../../useEscapeKey';
import { Container, SubActionContainer } from '../standard/Actions';
import { ActionKey } from '../standard/shared';
import type { ApplicationFormValues } from '../standard/shared';
import AppointmentSubmitAction, { useAppointmentSubmit } from './Actions/AppointmentSubmitAction';
import ContinueApplicationAction, { mayContinueApplication } from './Actions/ContinueApplicationAction';
import DropdownActions from './Actions/DropdownActions';
import useApplyNewButton from './Actions/useApplyNewButton';
import useFormUpdateValues from './Actions/useFormUpdateValues';
import useTestDriveEndModal from './TestDriveModal/TestDriveEndedModal';
import useTestDriveStartModal from './TestDriveModal/TestDriveStartedModal';
import type { TestDriveModalProps } from './TestDriveModal/types';

type AppointmentFooterActionProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >;
    stage?: ApplicationStage;
    status?: ApplicationStatus;
};

const AppointmentFooterAction = ({
    application,
    stage = ApplicationStage.Appointment,
    status: applicationStatus,
}: AppointmentFooterActionProps) => {
    const { t } = useTranslation('applicationDetails');
    const { forCI, refetch } = useApplicationDetailsExtraContext();
    const selectCapBusinessPartnerAndLeadModal = useSelectCapCustomerOnApplicationDetails();

    const navigate = useNavigateWithBlock();
    const location = useLocation();

    const { setFieldValue } = useFormikContext<ApplicationFormValues>();

    const status = useMemo(
        () => applicationStatus || getApplicationStatus(application, stage),
        [applicationStatus, application, stage]
    );

    const hasTestDriveSetting = useMemo(
        () => !isNil(application.draftFlow.isTestDriveProcessStarted),
        [application.draftFlow]
    );

    const onCancel = useCallback(() => navigate(-1), [navigate]);

    useEscapeKey(onCancel);

    const [cancelMutation] = useCancelApplicationMutation();
    const [completeMutation] = useCompleteApplicationMutation();
    const [confirmBookingMutation] = useConfirmBookingApplicationMutation();

    const { getFormUpdates } = useFormUpdateValues({
        stage,
        timeZone: application.module?.company?.timeZone,
        permissions: application.permissions,
    });

    const onStartTestDriveFinish = useCallback<TestDriveModalProps['onTestDriveStarted']>(
        (data, innerValues) => {
            setFieldValue('appointmentStage.mileage.start', innerValues.mileage.start);
            setFieldValue('appointmentStage.registrationNumber', innerValues.registrationNumber);

            if (data.result.redirectionLink) {
                window.location.replace(data.result.redirectionLink);
            } else {
                navigate(-1);
            }
        },
        [navigate, setFieldValue]
    );

    const [startTestDrive, renderStartTestDrive] = useTestDriveStartModal(
        application,
        stage,
        onStartTestDriveFinish,
        window.location.href
    );
    const [endTestDrive, renderEndTestDrive] = useTestDriveEndModal(application, stage, refetch);

    const submit = useCallback(
        async (action: ActionKey, capValues?: CapSubmissionValue) => {
            // Handle special cases for test drive
            if (action === ActionKey.StartTestDrive) {
                startTestDrive();

                return;
            }

            if (action === ActionKey.EndTestDrive) {
                endTestDrive(capValues);

                return;
            }

            const { submittedKey, submittingKey, mutation } = (() => {
                const baseVariables = { applicationId: application.id, stage };
                switch (action) {
                    case ActionKey.Void:
                        return {
                            mutation: () => cancelMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.cancellingApplication',
                            submittedKey: 'applicationDetails:messages.cancelledApplication',
                        };

                    case ActionKey.Complete:
                        return {
                            mutation: () => completeMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.completingApplication',
                            submittedKey: 'applicationDetails:messages.completedApplication',
                        };

                    case ActionKey.ConfirmBooking: {
                        const { hasChanges, updates } = getFormUpdates();

                        const appointmentDetails = (() => {
                            if (!hasChanges) {
                                return undefined;
                            }

                            if (Array.isArray(updates)) {
                                return (
                                    updates.find(update => update.updateAppointmentDetails)?.updateAppointmentDetails ??
                                    undefined
                                );
                            }

                            return updates.updateAppointmentDetails ?? undefined;
                        })();

                        const variables: ConfirmBookingApplicationMutationVariables = {
                            ...baseVariables,
                            capValues,
                            appointmentDetails,
                        };

                        return {
                            mutation: () => confirmBookingMutation({ variables }),
                            submittingKey: 'applicationDetails:messages.confirmingBooking',
                            submittedKey: 'applicationDetails:messages.confirmedBooking',
                        };
                    }

                    default:
                        throw new Error('not implemented');
                }
            })();

            message.loading({
                content: t(submittingKey),
                key: 'primary',
                duration: 0,
            });

            try {
                await mutation();

                message.success({
                    content: t(submittedKey),
                    key: 'primary',
                });

                if (location.key === 'default') {
                    navigate('..', { replace: true });
                } else {
                    navigate(-1);
                }
            } catch (error) {
                message.destroy('primary');
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors?.$root) {
                    message.error(apolloErrors?.$root);
                }
            }
        },
        [
            t,
            startTestDrive,
            endTestDrive,
            application.id,
            stage,
            cancelMutation,
            completeMutation,
            getFormUpdates,
            confirmBookingMutation,
            location.key,
            navigate,
        ]
    );

    const confirmAction = useCallback(
        (action: ActionKey, capValues?: CapSubmissionValue) => {
            if (
                action === ActionKey.StartTestDrive ||
                action === ActionKey.EndTestDrive ||
                action === ActionKey.ConvertToApplication
            ) {
                submit(action, capValues);

                return;
            }

            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${action}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    submit(action, capValues);
                },
            });
        },
        [submit, t]
    );

    const capIntegrationIsOn = useCheckCapIntegrationIsOn({ lead: application.lead });
    const requireToQualifyLead = useMemo(() => hasCapPrequalificationCheck(application), [application]);

    const confirmBookingAction = useCallback(() => {
        const businessPartnerGuid = application?.lead?.capValues?.businessPartnerGuid;
        const leadGuid = application?.lead?.capValues?.leadGuid;

        if (!capIntegrationIsOn || !requireToQualifyLead || (businessPartnerGuid && leadGuid)) {
            confirmAction(ActionKey.ConfirmBooking, { businessPartnerGuid, leadGuid });
        } else {
            selectCapBusinessPartnerAndLeadModal.open({
                lead: application.lead,
                submissionPurpose: SubmissionPurpose.ConfirmBooking,
                applicationId: application.id,
                stage,
                functions: {
                    createNewBpFn: capValue => confirmAction(ActionKey.ConfirmBooking, capValue),
                    createNewLeadFn: capValue => confirmAction(ActionKey.ConfirmBooking, capValue),
                    selectExistingLeadFn: capValue => confirmAction(ActionKey.ConfirmBooking, capValue),
                },
            });
        }
    }, [
        application,
        capIntegrationIsOn,
        requireToQualifyLead,
        confirmAction,
        selectCapBusinessPartnerAndLeadModal,
        stage,
    ]);

    const applyNewButton = useApplyNewButton(application, stage);
    const actions = useMemo<Array<MenuProps['items'][number] & { disabled: boolean }>>(() => {
        const isCompleted = [
            ApplicationStatus.TestDriveCompleted,
            ApplicationStatus.Cancelled,
            ApplicationStatus.Completed,
        ].includes(status);

        const allActions = [
            {
                key: ActionKey.Void,
                label: t('applicationDetails:appointmentActions.void'),
                onClick: () => confirmAction(ActionKey.Void),
                disabled: isCompleted,
            },
            {
                key: ActionKey.Complete,
                label: t('applicationDetails:appointmentActions.complete'),
                onClick: () => confirmAction(ActionKey.Complete),
                disabled: isCompleted || hasTestDriveSetting || status !== ApplicationStatus.BookingConfirmed,
            },
            {
                key: ActionKey.ConfirmBooking,
                label: t('applicationDetails:actions.confirmBooking'),
                onClick: confirmBookingAction,
                disabled:
                    isCompleted ||
                    status !== ApplicationStatus.NewAppointment ||
                    application.lead?.status === LeadStatus.SubmittingToCap,
            },
            {
                key: ActionKey.StartTestDrive,
                label: t('applicationDetails:actions.startTestDrive'),
                onClick: () => confirmAction(ActionKey.StartTestDrive),
                disabled:
                    isCompleted ||
                    !hasTestDriveSetting ||
                    !!application.appointmentStage.checkOutTime ||
                    ![ApplicationStatus.BookingConfirmed, ApplicationStatus.SigningInitiated].includes(status),
            },
            {
                key: ActionKey.EndTestDrive,
                label: t('applicationDetails:actions.endTestDrive'),
                onClick: () => confirmAction(ActionKey.EndTestDrive),
                disabled:
                    isCompleted ||
                    !hasTestDriveSetting ||
                    !application.appointmentStage.checkOutTime ||
                    !!application.appointmentStage.checkInTime,
            },
            ...(applyNewButton ? [{ ...applyNewButton, disabled: false }] : []),
        ];

        return allActions.filter(action => !action.disabled);
    }, [
        status,
        t,
        hasTestDriveSetting,
        confirmBookingAction,
        application.lead?.status,
        application.appointmentStage.checkOutTime,
        application.appointmentStage.checkInTime,
        applyNewButton,
        confirmAction,
    ]);

    const canResubmit = useAppointmentSubmit({ application, stage, status });
    const canContinueApplication = mayContinueApplication(application, stage);

    if (!canResubmit && !canContinueApplication && !actions.length) {
        return null;
    }

    return (
        <>
            {renderStartTestDrive()}
            {renderEndTestDrive()}
            <Container forCI={forCI}>
                <SubActionContainer forCI={forCI}>
                    <DropdownActions actions={actions} forCI={forCI} />
                    {canResubmit && (
                        <AppointmentSubmitAction
                            application={application}
                            forCI={forCI}
                            stage={stage}
                            status={status}
                        />
                    )}
                    {canContinueApplication && (
                        <ContinueApplicationAction application={application} forCI={forCI} stage={stage} />
                    )}
                    {selectCapBusinessPartnerAndLeadModal.render()}
                </SubActionContainer>
            </Container>
        </>
    );
};

export default AppointmentFooterAction;
