import { message } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../../shared/permissions';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { UpdateApplicationMutationVariables } from '../../../../../api/mutations/updateApplication';
import { useGetApplicationAuditTrailsQuery } from '../../../../../api/queries/getApplicationAuditTrails';
import { ApplicationStage, ApplicationStatus } from '../../../../../api/types';
import { getApplicationStatus } from '../../../../../utilities/application';
import hasPermissions from '../../../../../utilities/hasPermissions';
import { mayResubmit } from '../../standard/Actions';
import type { ApplicationFormValues } from '../../standard/shared';
import getApplicationUpdates, {
    hasUpdates,
    prepareAppointmentChanges,
    PrepareAppointmentChangesValues,
    useDialCode,
} from '../../standard/useUpdates';
import ActionButton from '../ActionButton';

type AppointmentSubmitProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >;
    stage?: ApplicationStage;
    status?: ApplicationStatus;
};

export const useVisitAppointmentSubmit = ({
    application,
    status: applicationStatus,
    stage,
}: AppointmentSubmitProps) => {
    const status = useMemo(
        () => applicationStatus || getApplicationStatus(application, stage),
        [applicationStatus, application, stage]
    );

    const mayUpdateAppointment = useMemo(() => {
        if (!application.draftFlow.isReceived) {
            return false;
        }

        switch (application.__typename) {
            case 'StandardApplication':
            case 'FinderApplication':
            case 'ConfiguratorApplication': {
                if (!application.draftFlow.isApplyNewForAppointmentReceived) {
                    return false;
                }

                break;
            }
            default:
                break;
        }

        return (
            isNil(application.visitAppointmentStage?.bookingTimeSlot?.slot) ||
            status === ApplicationStatus.NewAppointment
        );
    }, [application, status]);

    const { data: applicationAuditTrailData } = useGetApplicationAuditTrailsQuery({
        variables: {
            applicationId: application.id,
            stage,
            pagination: { limit: 50, offset: 0 },
        },
        fetchPolicy: 'cache-and-network',
    });

    const applicationStatusHistory = useMemo(() => {
        if (applicationAuditTrailData?.result?.items) {
            return applicationAuditTrailData?.result?.items.map(item => item.kind);
        }

        return [];
    }, [applicationAuditTrailData]);
    const bankIntegration = application.bank?.integration?.provider;
    const canResubmit = mayResubmit(application, bankIntegration, applicationStatusHistory) || mayUpdateAppointment;

    return (
        application.draftFlow.isReceived &&
        ![
            ApplicationStatus.Completed,
            ApplicationStatus.Cancelled,
            ApplicationStatus.TestDriveStarted,
            ApplicationStatus.TestDriveCompleted,
        ].includes(status) &&
        canResubmit
    );
};

const VisitAppointmentSubmitAction = ({
    application,
    stage = ApplicationStage.Appointment,
    status,
    forCI,
}: AppointmentSubmitProps & { forCI?: boolean }) => {
    const { t } = useTranslation('applicationDetails');
    const dialCode = useDialCode();
    const canSubmit = useVisitAppointmentSubmit({ application, stage, status });
    const { values, validateForm, initialValues, submitForm } = useFormikContext<ApplicationFormValues>();
    const actionText = t('applicationDetails:buttons.update');

    const onUpdate = useCallback(async () => {
        await validateForm();

        const newValues = prepareAppointmentChanges(
            values as PrepareAppointmentChangesValues,
            stage,
            application.module.company.timeZone
        );
        const updates: UpdateApplicationMutationVariables['updates'] = getApplicationUpdates(
            initialValues,
            newValues,
            stage,
            hasPermissions(application.permissions, [permissionKind.allowRecalculateApplication])
        );

        const isNeedUpdate = hasUpdates(updates, newValues, initialValues, { dialCode });
        if (!isNeedUpdate) {
            message.warn(t('applicationDetails:messages.noUpdated', { action: actionText }));

            return;
        }

        await submitForm();
    }, [
        validateForm,
        values,
        stage,
        application.module.company.timeZone,
        application.permissions,
        initialValues,
        dialCode,
        submitForm,
        t,
        actionText,
    ]);

    if (canSubmit) {
        return (
            <ActionButton forCI={forCI} onClick={onUpdate} size="large" type="primary">
                {actionText}
            </ActionButton>
        );
    }

    return null;
};

export default VisitAppointmentSubmitAction;
