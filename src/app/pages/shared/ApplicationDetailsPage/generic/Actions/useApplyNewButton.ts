import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { ProductionFinanceProductDetailsFragment } from '../../../../../api/fragments/ProductionFinanceProductDetails';
import { ApplyNewDocument, ApplyNewMutation, ApplyNewMutationVariables } from '../../../../../api/mutations/applyNew';
import { ApplicationStage } from '../../../../../api/types';
import {
    hasAppointmentScenario,
    hasFinancingScenario,
    hasInsuranceScenario,
} from '../../../../admin/ModuleDetailsPage/modules/implementations/shared';
import { hasAppointmentModule, hasFinancingModule, hasInsuranceModule } from '../../standard/ApplicationTab';
import { useAvailableFinanceProducts } from '../../standard/ApplicationTab/calculator/CalculatorFinanceModal';
import { ActionKey, isApplicationRequestForFinancing } from '../../standard/shared';
import { GenericFormApplication } from '../shared';
import { mayContinueApplication } from './ContinueApplicationAction';

const retrieveHasApplyNewSubmissionAndIncompleteJourney = (application: ApplicationDataFragment) => {
    if (application.__typename === 'MobilityApplication' || application.__typename === 'EventApplication') {
        return false;
    }

    const { draftFlow, stages, module } = application;

    /**
     * user reach applying new submission reach kyc page and didnt proceed
     * :: (!isNil(application?.financingStage?.isDraft) && !application?.financingStage?.isDraft) ::
     *
     *  OR
     *
     * user passed KYC Page and following steps are incomplete
     * :: (!application?.financingStage?.isDraft && !draftFlow.isApplyNewForFinancingReceived) ::
     */

    const financingIncomplete =
        stages.includes(ApplicationStage.Financing) &&
        draftFlow.hasApplyNewSubmission &&
        ((!isNil(application?.financingStage?.isDraft) && application?.financingStage?.isDraft) ||
            (!application?.financingStage?.isDraft && !draftFlow.isApplyNewForFinancingReceived));

    const insuranceIncomplete =
        stages.includes(ApplicationStage.Insurance) &&
        draftFlow.hasApplyNewSubmission &&
        ((!isNil(application?.insuranceStage?.isDraft) && application?.insuranceStage?.isDraft) ||
            (!application?.insuranceStage?.isDraft && !draftFlow.isApplyNewForInsuranceReceived));

    const appointmentIncomplete =
        (module.__typename === 'StandardApplicationModule' ||
            module.__typename === 'FinderApplicationPrivateModule' ||
            module.__typename === 'FinderApplicationPublicModule' ||
            module.__typename === 'ConfiguratorModule') &&
        hasAppointmentScenario(module.scenarios) &&
        draftFlow.hasApplyNewSubmission &&
        !isNil(application.appointmentStage) &&
        !draftFlow.isApplyNewForAppointmentReceived;

    return financingIncomplete || insuranceIncomplete || appointmentIncomplete;
};

const mayApplyNew = (
    application: ApplicationDataFragment,
    stage: ApplicationStage,
    availableFinanceProducts: ProductionFinanceProductDetailsFragment[] = []
) => {
    if (
        application.__typename === 'MobilityApplication' ||
        application.__typename === 'EventApplication' ||
        application.__typename === 'LaunchpadApplication' ||
        application.__typename === 'SalesOfferApplication' ||
        !application.draftFlow.isReceived ||
        mayContinueApplication(application, stage) ||
        retrieveHasApplyNewSubmissionAndIncompleteJourney(application)
    ) {
        return false;
    }

    const { module } = application;
    switch (module.__typename) {
        case 'StandardApplicationModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
        case 'ConfiguratorModule': {
            // request for financing and apply new should not display at same time
            const isRequestForFinancing = isApplicationRequestForFinancing(application);
            if (isRequestForFinancing) {
                return false;
            }

            if (
                hasFinancingScenario(module.scenarios) &&
                hasFinancingModule(application, availableFinanceProducts) &&
                (!application?.stages.includes(ApplicationStage.Financing) ||
                    (application?.stages.includes(ApplicationStage.Financing) && application?.financingStage?.isDraft))
            ) {
                return true;
            }

            // has insurance scenario and has available insurer but no insurance stage
            if (
                hasInsuranceScenario(module.scenarios) &&
                hasInsuranceModule(application) &&
                (!application?.stages.includes(ApplicationStage.Insurance) ||
                    (application?.stages.includes(ApplicationStage.Insurance) && application?.insuranceStage?.isDraft))
            ) {
                return true;
            }

            // has appointment scenario and has appointment module but no appointment stage
            if (
                hasAppointmentScenario(module.scenarios) &&
                hasAppointmentModule(application) &&
                !application.stages.includes(ApplicationStage.Appointment)
            ) {
                return true;
            }

            return false;
        }

        default:
            return false;
    }
};

const useApplyNewButton = (application: GenericFormApplication, stage?: ApplicationStage) => {
    const { t } = useTranslation(['applicationDetails']);
    const apolloClient = useApolloClient();
    const availableFinanceProducts = useAvailableFinanceProducts(application);
    const canApplyNew = mayApplyNew(application, stage, availableFinanceProducts);

    const onClick = useCallback(async () => {
        try {
            message.loading({
                content: t('applicationDetails:messages.applyingNew'),
                key: 'primary',
                duration: 0,
            });
            const result = await apolloClient.mutate<ApplyNewMutation, ApplyNewMutationVariables>({
                mutation: ApplyNewDocument,
                variables: {
                    applicationId: application.id,
                    stage,
                },
            });

            if (result.data.redirectLink) {
                window.open(result.data.redirectLink);
            }
        } catch (error) {
            console.error(error);
        } finally {
            message.destroy('primary');
        }
    }, [apolloClient, application.id, stage, t]);

    if (!canApplyNew) {
        return null;
    }

    return { label: t('applicationDetails:actions.applyNew'), key: ActionKey.ApplyNew, onClick };
};

export default useApplyNewButton;
