import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { useFormikContext } from 'formik';
import { isEmpty, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ContinueApplicationDocument,
    ContinueApplicationMutation,
    ContinueApplicationMutationVariables,
} from '../../../../../../api/mutations/continueApplication';
import { ApplicationStage } from '../../../../../../api/types';
import type { ApplicationFormValues } from '../../../standard/shared';
import ActionButton from '../../ActionButton';
import { useContinueApplicationModal } from '../../ContinueApplicationModal';
import { SelectedOptions } from '../../ContinueApplicationModal/types';
import useContinueApplicationOptions from '../../ContinueApplicationModal/useContinueApplicationOptions';
import { GenericFormApplication } from '../../shared';
import useValidateContinueApplication, {
    hasProceedWithCustomerDevice,
    isApplicationFromPublicAccess,
} from './useValidateContinueApplication';

export const mayContinueApplication = (application: GenericFormApplication, stage: ApplicationStage) => {
    if (!application.draftFlow.isReceived) {
        return true;
    }

    if (application.__typename === 'EventApplication') {
        return false;
    }

    const { module, draftFlow } = application;
    switch (module.__typename) {
        case 'StandardApplicationModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
        case 'ConfiguratorModule': {
            if (
                stage === ApplicationStage.Financing &&
                !isNil(application?.financingStage?.isDraft) &&
                ((!isNil(application?.financingStage?.isDraft) && application?.financingStage?.isDraft) ||
                    (!application?.financingStage?.isDraft && !draftFlow.isApplyNewForFinancingReceived))
            ) {
                return true;
            }

            if (
                stage === ApplicationStage.Insurance &&
                !isNil(application?.insuranceStage?.isDraft) &&
                ((!isNil(application?.insuranceStage?.isDraft) && application?.insuranceStage?.isDraft) ||
                    (!application?.insuranceStage?.isDraft && !draftFlow.isApplyNewForInsuranceReceived))
            ) {
                return true;
            }

            if (
                stage === ApplicationStage.Appointment &&
                !isNil(application?.appointmentStage) &&
                !application.draftFlow.isApplyNewForAppointmentReceived
            ) {
                return true;
            }

            return false;
        }

        default:
            return false;
    }
};

type ContinueApplicationActionProps = {
    application: GenericFormApplication;
    stage: ApplicationStage;
    isRequestForFinancing?: boolean;
    forCI?: boolean;
};

const ContinueApplicationAction = ({
    application,
    stage,
    isRequestForFinancing,
    forCI,
}: ContinueApplicationActionProps) => {
    const { t } = useTranslation('applicationDetails');

    const { values } = useFormikContext<ApplicationFormValues>();

    const customerFields = useMemo(() => Object.keys(values.customer.fields), [values.customer.fields]);
    const validateContinueApplication = useValidateContinueApplication({ customerFields });
    const validated = useMemo(() => validateContinueApplication(values), [validateContinueApplication, values]);

    const continueApplicationModal = useContinueApplicationModal();
    const continueApplicationOptions = useContinueApplicationOptions({
        application,
        continueWithCustomerDevice: false,
        skipPayment: false,
        isValidToProceedWithCustomerDevice: isEmpty(validated),
        customerFields,
    });

    const apolloClient = useApolloClient();
    const callContinue = useCallback(
        async ({ isProceedWithCustomerDevice = false, isSkipDeposit = false, continueRequestFinancing = false }) => {
            try {
                message.loading({
                    content: t('applicationDetails:messages.continueApplication'),
                    key: 'primary',
                    duration: 0,
                });
                const result = await apolloClient.mutate<
                    ContinueApplicationMutation,
                    ContinueApplicationMutationVariables
                >({
                    mutation: ContinueApplicationDocument,
                    variables: {
                        applicationId: application.id,
                        stage,
                        isSkipDeposit,
                        isProceedWithCustomerDevice,
                        continueRequestFinancing,
                    },
                });

                if (result.data.redirectLink && !isProceedWithCustomerDevice) {
                    window.open(result.data.redirectLink);
                    message.destroy('primary');
                } else {
                    message.success({
                        content: t('applicationDetails:messages.emailSentForProceedWithCustomerDevice'),
                        key: 'primary',
                    });
                }

                continueApplicationModal.close();
            } catch (error) {
                message.destroy('primary');
                console.error(error);
            }
        },
        [t, apolloClient, application.id, stage, continueApplicationModal]
    );

    const continueApplication = useCallback(
        async (selectedOptions?: SelectedOptions) => {
            // Only set continue for request financing, when it's already received
            // Otherwise, it will continue the journey as it's not finished yet
            // It will not proceed as remote flow, as it's already received
            if (isRequestForFinancing && application.draftFlow.isReceived) {
                await callContinue({
                    continueRequestFinancing: true,
                    isProceedWithCustomerDevice: isApplicationFromPublicAccess(application) === true,
                });

                return;
            }

            const isProceedWithCustomerDevice =
                (hasProceedWithCustomerDevice(application) === true && selectedOptions?.continueWithCustomerDevice) ||
                isApplicationFromPublicAccess(application) === true;

            const isSkipDeposit = selectedOptions?.skipPayment;

            await callContinue({ isProceedWithCustomerDevice, isSkipDeposit });
        },
        [application, callContinue, isRequestForFinancing]
    );

    const onClick = useCallback(() => {
        if (continueApplicationOptions?.length > 0) {
            continueApplicationModal.open();

            return;
        }

        continueApplication();
    }, [continueApplicationModal, continueApplicationOptions, continueApplication]);

    return (
        <>
            <ActionButton forCI={forCI} onClick={onClick} size="large" type="primary">
                {t('applicationDetails:buttons.continueApplication')}
            </ActionButton>
            {continueApplicationModal.render({
                checkboxOptions: continueApplicationOptions,
                onContinue: options => {
                    continueApplication(options);
                    continueApplicationModal.close();
                },
            })}
        </>
    );
};

export default ContinueApplicationAction;
