import { message } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { useGetApplicationAuditTrailsQuery } from '../../../../../api/queries/getApplicationAuditTrails';
import { ApplicationStage, ApplicationStatus } from '../../../../../api/types';
import { getApplicationStatus } from '../../../../../utilities/application';
import { mayResubmit } from '../../standard/Actions';
import type { ApplicationFormValues } from '../../standard/shared';
import ActionButton from '../ActionButton';
import useFormUpdateValues from './useFormUpdateValues';

type AppointmentSubmitProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >;
    stage?: ApplicationStage;
    status?: ApplicationStatus;
};

export const useAppointmentSubmit = ({ application, status: applicationStatus, stage }: AppointmentSubmitProps) => {
    const status = useMemo(
        () => applicationStatus || getApplicationStatus(application, stage),
        [applicationStatus, application, stage]
    );

    const mayUpdateAppointment = useMemo(() => {
        if (!application.draftFlow.isReceived) {
            return false;
        }

        switch (application.__typename) {
            case 'StandardApplication':
            case 'FinderApplication':
            case 'ConfiguratorApplication': {
                if (!application.draftFlow.isApplyNewForAppointmentReceived) {
                    return false;
                }

                break;
            }
            default:
                break;
        }

        return (
            isNil(application.appointmentStage?.bookingTimeSlot?.slot) || status === ApplicationStatus.NewAppointment
        );
    }, [application, status]);

    const { data: applicationAuditTrailData } = useGetApplicationAuditTrailsQuery({
        variables: {
            applicationId: application.id,
            stage,
            pagination: { limit: 50, offset: 0 },
        },
        fetchPolicy: 'cache-and-network',
    });

    const applicationStatusHistory = useMemo(() => {
        if (applicationAuditTrailData?.result?.items) {
            return applicationAuditTrailData?.result?.items.map(item => item.kind);
        }

        return [];
    }, [applicationAuditTrailData]);
    const bankIntegration = application.bank?.integration?.provider;
    const canResubmit = mayResubmit(application, bankIntegration, applicationStatusHistory) || mayUpdateAppointment;

    return (
        application.draftFlow.isReceived &&
        ![
            ApplicationStatus.Completed,
            ApplicationStatus.Cancelled,
            ApplicationStatus.TestDriveStarted,
            ApplicationStatus.TestDriveCompleted,
        ].includes(status) &&
        canResubmit
    );
};

const AppointmentSubmitAction = ({
    application,
    stage = ApplicationStage.Appointment,
    status,
    forCI,
}: AppointmentSubmitProps & { forCI?: boolean }) => {
    const { t } = useTranslation('applicationDetails');
    const canSubmit = useAppointmentSubmit({ application, stage, status });

    const hasTestDriveSetting = useMemo(
        () => !isNil(application.draftFlow?.isTestDriveProcessStarted),
        [application.draftFlow.isTestDriveProcessStarted]
    );

    const { submitForm, validateForm } = useFormikContext<ApplicationFormValues>();
    const actionText = hasTestDriveSetting
        ? t('applicationDetails:buttons.update')
        : t('applicationDetails:buttons.submit');

    const { getFormUpdates } = useFormUpdateValues({
        stage,
        timeZone: application.module.company.timeZone,
        permissions: application.permissions,
    });

    const onUpdate = useCallback(async () => {
        await validateForm();

        const { hasChanges } = getFormUpdates();

        if (!hasChanges) {
            message.warn(t('applicationDetails:messages.noUpdated', { action: actionText }));

            return;
        }

        await submitForm();
    }, [getFormUpdates, validateForm, submitForm, t, actionText]);

    if (canSubmit) {
        return (
            <ActionButton forCI={forCI} onClick={onUpdate} size="large" type="primary">
                {actionText}
            </ActionButton>
        );
    }

    return null;
};

export default AppointmentSubmitAction;
