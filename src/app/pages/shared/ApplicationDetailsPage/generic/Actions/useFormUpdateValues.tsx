import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import * as permissionKind from '../../../../../../shared/permissions';
import type { UpdateApplicationMutationVariables } from '../../../../../api/mutations/updateApplication';
import type { ApplicationStage } from '../../../../../api/types';
import hasPermissions from '../../../../../utilities/hasPermissions';
import type { ApplicationFormValues } from '../../standard/shared';
import getApplicationUpdates, {
    hasUpdates,
    prepareAppointmentChanges,
    type PrepareAppointmentChangesValues,
    useDialCode,
} from '../../standard/useUpdates';

type UseFormUpdateValuesProps = {
    stage: ApplicationStage;
    timeZone?: string;
    permissions?: string[];
};

type UseFormUpdateValuesResult = {
    getFormUpdates: () => {
        updates: UpdateApplicationMutationVariables['updates'];
        hasChanges: boolean;
    };
};

/**
 * A hook for getting form update values that can be used in mutation requests
 *
 * @param props Configuration options
 * @returns An object containing functions to get form updates and a flag indicating if there are changes
 */
const useFormUpdateValues = ({
    stage,
    timeZone,
    permissions = [],
}: UseFormUpdateValuesProps): UseFormUpdateValuesResult => {
    const dialCode = useDialCode();
    const { values, initialValues } = useFormikContext<ApplicationFormValues>();

    const getFormUpdates = useCallback(() => {
        const newValues = prepareAppointmentChanges(values as PrepareAppointmentChangesValues, stage, timeZone);

        const updates: UpdateApplicationMutationVariables['updates'] = getApplicationUpdates(
            initialValues,
            newValues,
            stage,
            hasPermissions(permissions, [permissionKind.allowRecalculateApplication])
        );

        const hasChanges = hasUpdates(updates, newValues, initialValues, { dialCode });

        return {
            updates,
            hasChanges,
        };
    }, [values, stage, timeZone, initialValues, permissions, dialCode]);

    return useMemo(() => ({ getFormUpdates }), [getFormUpdates]);
};

export default useFormUpdateValues;
