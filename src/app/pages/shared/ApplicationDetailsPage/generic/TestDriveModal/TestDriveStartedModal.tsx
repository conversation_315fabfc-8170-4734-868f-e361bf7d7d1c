import { Col, Row } from 'antd';
import { Gutter } from 'antd/lib/grid/row';
import { Formik } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useStartTestDriveMutation } from '../../../../../api/mutations/startTestDrive';
import { useUpdateTestDriveDataMutation } from '../../../../../api/mutations/updateTestDriveData';
import { ApplicationStage, AppointmentChangedVehicleKind, LayoutType } from '../../../../../api/types';
import FormAutoTouch from '../../../../../components/FormAutoTouch';
import { useRouter } from '../../../../../components/contexts/shared';
import Form from '../../../../../components/fields/Form';
import CheckboxField from '../../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../../themes/hooks';
import useHandleError from '../../../../../utilities/useHandleError';
import { parseNumber } from '../../../../admin/FinderVehicleDetailsPage/AddLtaDrawer/useParse';
import TestDriveChangedVehicle from './TestDriveChangedVehicle';
import { mileageFormatter, useMileageSuffix } from './shared';
import { TestDriveInnerFormValue, TestDriveInnerModalProps, TestDriveModalProps } from './types';
import { useTestDriveStartedModalValidator } from './useTestDriveModalValidator';

const StyledDiv = styled.div`
    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3
            ? `
        & .ant-form-item {
            margin-bottom: 0px;
        }
    `
            : `
        & .ant-form-item {
            margin-bottom: 10px;
        }
    `};

    & .ant-form-item-explain.ant-form-item-explain-connected:not(:has(> div.ant-form-item-explain-error)) {
        height: auto;
    }
`;

const TestDriveStartedInnerModal = ({
    application,
    handleSubmit,
    submitForm,
    isSubmitting,
    isValid,
    isOpen,
    onClose,
    setTouched,
    resetForm,
    initialValues,
    setFieldValue,
}: TestDriveInnerModalProps) => {
    const { t } = useTranslation(['applicationDetails']);

    const { FormFields, Modal, Checkbox, gutter } = useThemeComponents();

    const router = useRouter();

    const testDriveModalGutter = useMemo(() => {
        if (router?.layout?.__typename === 'PorscheV3Layout') {
            return [0, 16] as [Gutter, Gutter];
        }

        const { testDriveModal } = gutter;

        return testDriveModal;
    }, [gutter, router?.layout?.__typename]);

    const mileageSuffix = useMileageSuffix(application.vehicle);
    const handleClose = useCallback(() => {
        setFieldValue('hasChangedVehicle', false);

        resetForm();
        setTouched({ 'mileage.start': false, registrationNumber: false });
        onClose();
    }, [onClose, resetForm, setTouched]);

    return (
        <Modal
            cancelText={t('applicationDetails:buttons.cancel')}
            closable={false}
            maskClosable={false}
            okButtonProps={{
                loading: isSubmitting,
                disabled: isSubmitting,
            }}
            okText={t('applicationDetails:buttons.save')}
            onCancel={handleClose}
            onOk={submitForm}
            open={isOpen}
            title={t('applicationDetails:testDriveModal.started.title')}
            width={572}
            destroyOnClose
        >
            <FormAutoTouch />
            <Form id="testDriveStart" name="testDriveStart" onSubmitCapture={handleSubmit}>
                <StyledDiv>
                    <Row gutter={testDriveModalGutter}>
                        <Col span={24}>
                            <FormFields.InputNumberField
                                {...t(`applicationDetails:fields.testDrive.mileageStart`, { returnObjects: true })}
                                addonAfter={mileageSuffix}
                                formatter={mileageFormatter}
                                name="mileage.start"
                                required
                            />
                        </Col>
                        <Col span={24}>
                            <FormFields.InputField
                                {...t(`applicationDetails:fields.testDrive.registrationNumber`, {
                                    returnObjects: true,
                                })}
                                name="registrationNumber"
                                required
                            />
                        </Col>
                        <Col span={24}>
                            <CheckboxField customComponent={Checkbox} name="hasChangedVehicle">
                                {t(`applicationDetails:fields.testDrive.hasChangedVehicle.label`)}
                            </CheckboxField>
                        </Col>
                        <TestDriveChangedVehicle application={application} setFieldValue={setFieldValue} />
                    </Row>
                </StyledDiv>
            </Form>
        </Modal>
    );
};

const TestDriveStartedFormik = ({
    application,
    isOpen,
    onClose,
    onTestDriveStarted: onFinish,
    testDriveRedirectUrl,
}: TestDriveModalProps) => {
    const { t } = useTranslation(['applicationDetails']);

    const [updateTestDriveData] = useUpdateTestDriveDataMutation();
    const [startTestDriveMutation] = useStartTestDriveMutation();

    const { notification } = useThemeComponents();

    const onSubmit = useHandleError<TestDriveInnerFormValue>(
        async values => {
            notification.loading({
                content: t('applicationDetails:messages.startingTestDrive'),
                key: 'primary',
                duration: 0,
            });

            await updateTestDriveData({
                variables: {
                    applicationId: application.id,
                    testDriveData: {
                        mileage: {
                            start: values.mileage?.start,
                            end: values.mileage?.end,
                        },
                        registrationNumber: values.registrationNumber,
                        hasChangedVehicle: values.hasChangedVehicle,
                        changedVehicle: values.hasChangedVehicle ? values.changedVehicle : undefined,
                    },
                },
            });

            const { data } = await startTestDriveMutation({
                variables: {
                    applicationId: application.id,
                    testDriveRedirectUrl,
                },
            });

            onClose();

            notification.success({
                content: t('applicationDetails:messages.startedTestDrive'),
                key: 'primary',
            });

            if (onFinish) {
                onFinish(data, values);
            }
        },
        [
            notification,
            t,
            updateTestDriveData,
            application.id,
            startTestDriveMutation,
            testDriveRedirectUrl,
            onClose,
            onFinish,
        ]
    );

    const initialValues = useMemo<TestDriveInnerFormValue>(() => {
        switch (application.vehicle.__typename) {
            case 'FinderVehicle': {
                const mileageStart = parseNumber(application.vehicle.listing.vehicle.mileage.localize);

                return {
                    mileage: {
                        start: mileageStart,
                    },
                    registrationNumber: '',
                    hasChangedVehicle: false,
                    changedVehicle: {
                        kind: AppointmentChangedVehicleKind.Text,
                        text: {
                            variant: '',
                            model: '',
                            subModel: '',
                        },
                    },
                };
            }

            default: {
                return {
                    mileage: {
                        start: null,
                    },
                    registrationNumber: '',
                    hasChangedVehicle: false,
                    changedVehicle: {
                        kind: AppointmentChangedVehicleKind.Local,
                        local: {
                            vehicleId: null,
                        },
                    },
                };
            }
        }
    }, [application.vehicle]);

    const validate = useTestDriveStartedModalValidator();

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({
                handleSubmit,
                submitForm,
                isSubmitting,
                isValid,
                resetForm,
                setTouched,
                setValues,
                setFieldValue,
                values,
            }) => (
                <TestDriveStartedInnerModal
                    application={application}
                    handleSubmit={handleSubmit}
                    initialValues={initialValues}
                    isOpen={isOpen}
                    isSubmitting={isSubmitting}
                    isValid={isValid}
                    onClose={onClose}
                    resetForm={resetForm}
                    setFieldValue={setFieldValue}
                    setTouched={setTouched}
                    submitForm={submitForm}
                />
            )}
        </Formik>
    );
};

const getAllowStartTestDrive = (application: TestDriveModalProps['application'], stage: ApplicationStage) =>
    !isNil(application.vehicle) &&
    !isNil(application.draftFlow.isTestDriveProcessStarted) &&
    isNil(application.appointmentStage?.checkOutTime) &&
    stage === ApplicationStage.Appointment;

const TestDriveStartedModal = ({ application, stage, ...rest }: TestDriveModalProps) => {
    if (!getAllowStartTestDrive(application, stage)) {
        return null;
    }

    return <TestDriveStartedFormik {...rest} application={application} stage={stage} />;
};

const useTestDriveStartModal = (
    application: TestDriveModalProps['application'],
    stage: ApplicationStage,
    onFinish?: TestDriveModalProps['onTestDriveStarted'],
    testDriveRedirectUrl?: string
) => {
    const [isOpen, setOpen] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => {
                if (getAllowStartTestDrive(application, stage)) {
                    setOpen(true);
                }
            },
            close: () => setOpen(false),
        }),
        [application, stage]
    );

    return useMemo(
        () =>
            [
                actions.open,
                () => (
                    <TestDriveStartedModal
                        application={application}
                        isOpen={isOpen}
                        onClose={actions.close}
                        onTestDriveStarted={onFinish}
                        stage={stage}
                        testDriveRedirectUrl={testDriveRedirectUrl}
                    />
                ),
            ] as const,
        [actions.open, actions.close, application, isOpen, onFinish, stage, testDriveRedirectUrl]
    );
};

export default useTestDriveStartModal;
