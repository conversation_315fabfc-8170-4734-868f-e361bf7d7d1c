import { Col, Row } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useEndTestDriveMutation } from '../../../../../api/mutations/endTestDrive';
import { useUpdateTestDriveDataMutation } from '../../../../../api/mutations/updateTestDriveData';
import { ApplicationStage } from '../../../../../api/types';
import { CapSubmissionValue } from '../../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import Form from '../../../../../components/fields/Form';
import { useThemeComponents } from '../../../../../themes/hooks';
import useHandleError from '../../../../../utilities/useHandleError';
import type { ApplicationFormValues } from '../../standard/shared';
import { mileageFormatter, useMileageSuffix } from './shared';
import { TestDriveInnerFormValue, TestDriveInnerModalProps, TestDriveModalProps } from './types';
import { useTestDriveEndedModalValidator } from './useTestDriveModalValidator';

const TestDriveEndedInnerModal = ({
    application,
    handleSubmit,
    setTouched,
    submitForm,
    isSubmitting,
    isValid,
    isOpen,
    resetForm,
    onClose,
}: TestDriveInnerModalProps) => {
    const { t } = useTranslation(['applicationDetails']);

    const { FormFields, Modal } = useThemeComponents();

    const milageSuffix = useMileageSuffix(application.vehicle);

    const handleClose = useCallback(() => {
        onClose();
        resetForm();
        setTouched({ 'mileage.end': false });
    }, [onClose, resetForm, setTouched]);

    return (
        <Modal
            cancelText={t('applicationDetails:buttons.cancel')}
            closable={false}
            maskClosable={false}
            okButtonProps={{
                loading: isSubmitting,
                disabled: !isValid,
            }}
            okText={t('applicationDetails:buttons.save')}
            onCancel={handleClose}
            onOk={submitForm}
            open={isOpen}
            title={t('applicationDetails:testDriveModal.ended.title')}
            width={480}
        >
            <Form id="testDriveEnd" name="testDriveEnd" onSubmitCapture={handleSubmit}>
                <Row>
                    <Col span={24}>
                        <FormFields.InputNumberField
                            {...t(`applicationDetails:fields.testDrive.mileageEnd`, { returnObjects: true })}
                            addonAfter={milageSuffix}
                            formatter={mileageFormatter}
                            itemProps={{ style: { margin: 0 } }}
                            name="mileage.end"
                            required
                        />
                    </Col>
                </Row>
            </Form>
        </Modal>
    );
};

const TestDriveEndedFormik = ({
    application,
    isOpen,
    onClose,
    capValues,
    refetchApplication,
    onTestDriveEnded,
}: TestDriveModalProps) => {
    const { t } = useTranslation(['applicationDetails']);

    const { setFieldValue } = useFormikContext<ApplicationFormValues>() ?? {};

    const [updateTestDriveData] = useUpdateTestDriveDataMutation();
    const [endTestDriveMutation] = useEndTestDriveMutation();

    const { notification } = useThemeComponents();

    const onSubmit = useHandleError<TestDriveInnerFormValue>(
        async values => {
            notification.loading({
                content: t('applicationDetails:messages.endingTestDrive'),
                key: 'primary',
                duration: 0,
            });

            await updateTestDriveData({
                variables: {
                    applicationId: application.id,
                    testDriveData: {
                        mileage: {
                            start: values.mileage.start,
                            end: values.mileage.end,
                        },
                        registrationNumber: values.registrationNumber,
                    },
                },
            });

            // Update the formik values
            if (setFieldValue) {
                setFieldValue('appointmentStage.mileage.end', values.mileage.end);
            }

            await endTestDriveMutation({
                variables: {
                    applicationId: application.id,
                },
            });

            notification.success({
                content: t('applicationDetails:messages.endedTestDrive'),
                key: 'primary',
            });

            onClose();

            if (refetchApplication) {
                await refetchApplication();
            }

            if (onTestDriveEnded) {
                onTestDriveEnded();
            }
        },
        [
            notification,
            t,
            updateTestDriveData,
            application.id,
            setFieldValue,
            endTestDriveMutation,
            onClose,
            refetchApplication,
            onTestDriveEnded,
        ]
    );

    const initialValues = useMemo<TestDriveInnerFormValue>(
        () => ({
            mileage: {
                start: application.appointmentStage?.mileage?.start,
                end: application.appointmentStage?.mileage?.end,
            },
            registrationNumber: application.appointmentStage?.registrationNumber,
        }),
        [application.appointmentStage]
    );

    const validate = useTestDriveEndedModalValidator();

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit, submitForm, isSubmitting, isValid, resetForm, setTouched }) => (
                <TestDriveEndedInnerModal
                    application={application}
                    handleSubmit={handleSubmit}
                    isOpen={isOpen}
                    isSubmitting={isSubmitting}
                    isValid={isValid}
                    onClose={onClose}
                    resetForm={resetForm}
                    setTouched={setTouched}
                    submitForm={submitForm}
                />
            )}
        </Formik>
    );
};

const getAllowEndTestDrive = (application: TestDriveModalProps['application'], stage: ApplicationStage) =>
    application.draftFlow.isTestDriveProcessStarted &&
    !isNil(application.appointmentStage?.checkOutTime) &&
    stage === ApplicationStage.Appointment;

const TestDriveEndedModal = ({ application, stage, ...rest }: TestDriveModalProps) => {
    if (!getAllowEndTestDrive(application, stage)) {
        return null;
    }

    return <TestDriveEndedFormik {...rest} application={application} stage={stage} />;
};

const useTestDriveEndModal = (
    application: TestDriveModalProps['application'],
    stage: ApplicationStage,
    refreshApplication?: TestDriveModalProps['refetchApplication'],
    onTestDriveEnded?: TestDriveModalProps['onTestDriveEnded']
) => {
    const [isOpen, setOpen] = useState(false);
    const [capValues, setCapValues] = useState<CapSubmissionValue>(null);

    const actions = useMemo(
        () => ({
            open: (capValues: CapSubmissionValue) => {
                if (getAllowEndTestDrive(application, stage)) {
                    setOpen(true);
                    setCapValues(capValues);
                }
            },
            close: () => setOpen(false),
        }),
        [application, stage]
    );

    return useMemo(
        () =>
            [
                (capValues: CapSubmissionValue) => actions.open(capValues),
                () => (
                    <TestDriveEndedModal
                        application={application}
                        capValues={capValues}
                        isOpen={isOpen}
                        onClose={actions.close}
                        onTestDriveEnded={onTestDriveEnded}
                        refetchApplication={refreshApplication}
                        stage={stage}
                    />
                ),
            ] as const,
        [actions, application, capValues, isOpen, refreshApplication, stage, onTestDriveEnded]
    );
};

export default useTestDriveEndModal;
