import { MenuProps, Modal, message } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { useCompleteLeadMutation } from '../../../../api/mutations/completeLead';
import { useMarkLeadAsLostMutation } from '../../../../api/mutations/markLeadAsLost';
import { useQualifyLeadWithCapValuesMutation } from '../../../../api/mutations/qualifyLeadWithCapValues';
import { useResubmitLeadToCapMutation } from '../../../../api/mutations/resubmitLeadToCap';
import { useUnqualifyLeadMutation } from '../../../../api/mutations/unqualifyLead';
import { LeadStatus } from '../../../../api/types';
import { CapSubmissionValue } from '../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import { SubmissionPurpose } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useLeadDetailsExtraContext } from '../../../../components/contexts/LeadDetailsExtraContext';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import useCheckCapIntegrationIsOn from '../../../../utilities/useCheckCapIntegrationIsOn';
import { LeadFormApplication } from '../../../admin/LeadDetailsPage/shared';
import {
    useMayPrequalifyLead,
    useMayResubmitLeadToCap,
    useSelectCapCustomerOnApplicationDetails,
} from '../../JourneyPage/C@P';
import useEscapeKey from '../../useEscapeKey';
import { Container, SubActionContainer } from '../standard/Actions';
import { ActionKey } from '../standard/shared';
import DropdownActions from './Actions/DropdownActions';
import LeadUpdateAction from './Actions/LeadUpdateAction';

type LeadFooterActionProps = {
    lead: LeadFormApplication;
};

const LeadFooterAction = ({ lead }: LeadFooterActionProps) => {
    const { t } = useTranslation('applicationDetails');
    const navigate = useNavigate();
    const { forCI, refetch } = useLeadDetailsExtraContext();
    const selectCapBusinessPartnerAndLeadModal = useSelectCapCustomerOnApplicationDetails();

    const onCancel = useCallback(() => navigate(-1), [navigate]);

    useEscapeKey(onCancel);

    // const [completeLeadMutation] = useCompleteLeadMutation();
    // const [markLeadAsLost] = useMarkLeadAsLostMutation();
    const [qualifyLeadMutation] = useQualifyLeadWithCapValuesMutation();
    const [unqualifyLead] = useUnqualifyLeadMutation();
    const [resubmitLeadToCap] = useResubmitLeadToCapMutation();

    const submit = useCallback(
        async (action: ActionKey, capValues?: CapSubmissionValue, selectedResponsibleSalesId?: string) => {
            const { submittedKey, submittingKey, mutation } = (() => {
                const baseVariables = { leadId: lead.id };
                switch (action) {
                    case ActionKey.QualifyContact:
                        return {
                            mutation: () =>
                                qualifyLeadMutation({
                                    variables: { ...baseVariables, capValues, selectedResponsibleSalesId },
                                }),
                            submittingKey: 'applicationDetails:messages.qualifyApplication',
                            submittedKey: 'applicationDetails:messages.qualifiedApplication',
                        };

                    case ActionKey.UnqualifyContact:
                        return {
                            mutation: () => unqualifyLead({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.unqualifyApplication',
                            submittedKey: 'applicationDetails:messages.unqualifiedApplication',
                        };

                    case ActionKey.ResubmitToCap:
                        return {
                            mutation: () =>
                                resubmitLeadToCap({
                                    variables: { ...baseVariables, capValues, isTestDrive: false },
                                }),
                            submittingKey: 'applicationDetails:messages.resubmitApplicationToCap',
                            submittedKey: 'applicationDetails:messages.resubmittedApplicationToCap',
                        };

                    // case ActionKey.CompleteLead:
                    //     return {
                    //         mutation: () => completeLeadMutation({ variables: baseVariables }),
                    //         submittingKey: 'applicationDetails:messages.completeLead',
                    //         submittedKey: 'applicationDetails:messages.leadCompleted',
                    //     };

                    // case ActionKey.MarkLeadAsLost:
                    //     return {
                    //         mutation: () => markLeadAsLost({ variables: baseVariables }),
                    //         submittingKey: 'applicationDetails:messages.markLeadAsLost',
                    //         submittedKey: 'applicationDetails:messages.leadMarkedAsLost',
                    //     };

                    default:
                        throw new Error('not implemented');
                }
            })();

            message.loading({
                content: t(submittingKey),
                key: 'primary',
                duration: 0,
            });

            try {
                await mutation();

                message.success({
                    content: t(submittedKey),
                    key: 'primary',
                });

                navigate(-1);
            } catch (error) {
                message.destroy('primary');
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors?.$root) {
                    message.error(apolloErrors?.$root);
                }
            } finally {
                refetch?.();
            }
        },
        [
            t,
            lead.id,
            qualifyLeadMutation,
            unqualifyLead,
            resubmitLeadToCap,
            // completeLeadMutation,
            // markLeadAsLost,
            navigate,
            refetch,
        ]
    );

    const mayPrequalifyLead = useMayPrequalifyLead(lead);
    const mayResubmitLeadToCap = useMayResubmitLeadToCap(lead);
    const capIntegrationIsOn = useCheckCapIntegrationIsOn({ lead });

    const confirmAction = useCallback(
        (action: ActionKey, capValues?: CapSubmissionValue, selectedResponsibleSalesId?: string) => {
            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${action}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    submit(action, capValues, selectedResponsibleSalesId);
                },
            });
        },
        [submit, t]
    );

    const qualifyAction = useCallback(() => {
        const businessPartnerGuid = lead?.capValues?.businessPartnerGuid;
        const leadGuid = lead?.capValues?.leadGuid;

        if (businessPartnerGuid && leadGuid) {
            confirmAction(ActionKey.QualifyContact, { businessPartnerGuid, leadGuid });
        } else {
            selectCapBusinessPartnerAndLeadModal.open({
                lead,
                submissionPurpose: SubmissionPurpose.Qualify,
                functions: {
                    createNewBpFn: (capValue, responsibleSalesPersonId) =>
                        confirmAction(ActionKey.QualifyContact, capValue, responsibleSalesPersonId),
                    createNewLeadFn: (capValue, responsibleSalesPersonId) =>
                        confirmAction(ActionKey.QualifyContact, capValue, responsibleSalesPersonId),
                    selectExistingLeadFn: (capValue, responsibleSalesPersonId) =>
                        confirmAction(ActionKey.QualifyContact, capValue, responsibleSalesPersonId),
                },
            });
        }
    }, [lead, confirmAction, selectCapBusinessPartnerAndLeadModal]);

    const resubmitToCapAction = useCallback(() => {
        const businessPartnerGuid = lead?.capValues?.businessPartnerGuid;
        const leadGuid = lead?.capValues?.leadGuid;

        if (businessPartnerGuid && leadGuid) {
            confirmAction(ActionKey.ResubmitToCap, { businessPartnerGuid, leadGuid });
        } else {
            selectCapBusinessPartnerAndLeadModal.open({
                lead,
                submissionPurpose: SubmissionPurpose.Resubmission,
                functions: {
                    createNewBpFn: capValue => confirmAction(ActionKey.ResubmitToCap, capValue),
                    createNewLeadFn: capValue => confirmAction(ActionKey.ResubmitToCap, capValue),
                    selectExistingLeadFn: capValue => confirmAction(ActionKey.ResubmitToCap, capValue),
                },
            });
        }
    }, [lead, confirmAction, selectCapBusinessPartnerAndLeadModal]);

    const actions = useMemo<MenuProps['items']>(
        () =>
            [
                capIntegrationIsOn &&
                    mayPrequalifyLead && {
                        key: ActionKey.QualifyContact,
                        label: t('applicationDetails:actions.qualifyApplication'),
                        onClick: qualifyAction,
                    },
                capIntegrationIsOn &&
                    mayResubmitLeadToCap && {
                        key: ActionKey.ResubmitToCap,
                        label: t('applicationDetails:actions.resubmitApplicationToCap'),
                        onClick: resubmitToCapAction,
                    },
                capIntegrationIsOn &&
                    mayPrequalifyLead &&
                    lead.status !== LeadStatus.Unqualified && {
                        key: ActionKey.UnqualifyContact,
                        label: t('applicationDetails:actions.unqualifyApplication'),
                        onClick: () => confirmAction(ActionKey.UnqualifyContact),
                    },
                // VF-1138: Temporarily hide the 'Mark as Lost' and 'Complete Lead' action buttons
                // As the status is not synced with C@P
                // ![LeadStatus.Completed, LeadStatus.Lost].includes(lead.status) &&
                //     lead.isLead && {
                //         key: ActionKey.CompleteLead,
                //         label: t(`applicationDetails:actions.completeLead`),
                //         onClick: () => confirmAction(ActionKey.CompleteLead),
                //     },
                // ![LeadStatus.Completed, LeadStatus.Lost].includes(lead.status) &&
                //     lead.isLead && {
                //         key: ActionKey.MarkLeadAsLost,
                //         label: t(`applicationDetails:actions.markLeadAsLost`),
                //         onClick: () => confirmAction(ActionKey.MarkLeadAsLost),
                //     },
            ].filter(Boolean),
        [
            capIntegrationIsOn,
            mayPrequalifyLead,
            t,
            qualifyAction,
            mayResubmitLeadToCap,
            resubmitToCapAction,
            lead.status,
            confirmAction,
        ]
    );

    // TODO: what instances we can do updates now with Lead
    const canUpdate = ![LeadStatus.Drafted, LeadStatus.Completed].includes(lead?.status);

    // no action available
    if (!canUpdate && !actions.length) {
        return null;
    }

    return (
        <Container forCI={forCI}>
            <SubActionContainer forCI={forCI}>
                <DropdownActions actions={actions} forCI={forCI} />
                {canUpdate && <LeadUpdateAction forCI={forCI} lead={lead} />}
                {selectCapBusinessPartnerAndLeadModal.render()}
            </SubActionContainer>
        </Container>
    );
};

export default LeadFooterAction;
