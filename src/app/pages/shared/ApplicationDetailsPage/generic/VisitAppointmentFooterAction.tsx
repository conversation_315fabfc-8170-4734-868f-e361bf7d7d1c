import { MenuProps, message, Modal } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { useCancelApplicationMutation } from '../../../../api/mutations/cancelApplication';
import { useCompleteApplicationMutation } from '../../../../api/mutations/completeApplication';
import {
    ConfirmBookingApplicationMutationVariables,
    useConfirmBookingApplicationMutation,
} from '../../../../api/mutations/confirmBookingApplication';
import { useContactApplicationMutation } from '../../../../api/mutations/contactApplication';
import { ApplicationStage, ApplicationStatus } from '../../../../api/types';
import { CapSubmissionValue } from '../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import { SubmissionPurpose } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { getApplicationStatus } from '../../../../utilities/application';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import useCheckCapIntegrationIsOn from '../../../../utilities/useCheckCapIntegrationIsOn';
import { hasCapPrequalificationCheck, useSelectCapCustomerOnApplicationDetails } from '../../JourneyPage/C@P';
import { useNavigateWithBlock } from '../../blockBackRedirection';
import { Container, SubActionContainer } from '../standard/Actions';
import { ActionKey } from '../standard/shared';
import { mayContinueApplication } from './Actions/ContinueApplicationAction';
import DropdownActions from './Actions/DropdownActions';
import VisitAppointmentSubmitAction, { useVisitAppointmentSubmit } from './Actions/VisitAppointmentSubmitAction';
import useFormUpdateValues from './Actions/useFormUpdateValues';

type VisitAppointmentFooterActionProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >;
    stage?: ApplicationStage;
    status?: ApplicationStatus;
};

const VisitAppointmentFooterAction = ({
    application,
    stage = ApplicationStage.Appointment,
    status: applicationStatus,
}: VisitAppointmentFooterActionProps) => {
    const { t } = useTranslation('applicationDetails');
    const { forCI } = useApplicationDetailsExtraContext();
    const selectCapBusinessPartnerAndLeadModal = useSelectCapCustomerOnApplicationDetails();

    const navigate = useNavigateWithBlock();
    const location = useLocation();

    const status = useMemo(
        () => applicationStatus || getApplicationStatus(application, stage),
        [applicationStatus, application, stage]
    );

    const [cancelMutation] = useCancelApplicationMutation();
    const [completeMutation] = useCompleteApplicationMutation();
    const [contactApplication] = useContactApplicationMutation();
    const [confirmBookingMutation] = useConfirmBookingApplicationMutation();

    const { getFormUpdates } = useFormUpdateValues({
        stage,
        timeZone: application.module?.company?.timeZone,
        permissions: application.permissions,
    });

    const submit = useCallback(
        async (action: ActionKey, capValues?: CapSubmissionValue) => {
            const { submittedKey, submittingKey, mutation } = (() => {
                const baseVariables = { applicationId: application.id, stage };
                switch (action) {
                    case ActionKey.Void:
                        return {
                            mutation: () => cancelMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.cancellingApplication',
                            submittedKey: 'applicationDetails:messages.cancelledApplication',
                        };

                    case ActionKey.Complete:
                        return {
                            mutation: () => completeMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.completingApplication',
                            submittedKey: 'applicationDetails:messages.completedApplication',
                        };

                    case ActionKey.Contact:
                        return {
                            mutation: () => contactApplication({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.contactingApplication',
                            submittedKey: 'applicationDetails:messages.contactedApplication',
                        };

                    case ActionKey.ConfirmBooking: {
                        const { hasChanges, updates } = getFormUpdates();

                        const appointmentDetails = (() => {
                            if (!hasChanges) {
                                return undefined;
                            }

                            if (Array.isArray(updates)) {
                                return (
                                    updates.find(update => update.updateVisitAppointmentDetails)
                                        ?.updateVisitAppointmentDetails ?? undefined
                                );
                            }

                            return updates.updateVisitAppointmentDetails ?? undefined;
                        })();

                        const variables: ConfirmBookingApplicationMutationVariables = {
                            ...baseVariables,
                            capValues,
                            appointmentDetails,
                        };

                        return {
                            mutation: () => confirmBookingMutation({ variables }),
                            submittingKey: 'applicationDetails:messages.confirmingBooking',
                            submittedKey: 'applicationDetails:messages.confirmedBooking',
                        };
                    }

                    default:
                        throw new Error('not implemented');
                }
            })();

            message.loading({
                content: t(submittingKey),
                key: 'primary',
                duration: 0,
            });

            try {
                await mutation();

                message.success({
                    content: t(submittedKey),
                    key: 'primary',
                });

                if (location.key === 'default') {
                    navigate('..', { replace: true });
                } else {
                    navigate(-1);
                }
            } catch (error) {
                message.destroy('primary');
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors?.$root) {
                    message.error(apolloErrors?.$root);
                }
            }
        },
        [
            t,
            application.id,
            stage,
            cancelMutation,
            completeMutation,
            contactApplication,
            getFormUpdates,
            confirmBookingMutation,
            location.key,
            navigate,
        ]
    );

    const confirmAction = useCallback(
        (action: ActionKey, capValues?: CapSubmissionValue) => {
            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${action}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    submit(action, capValues);
                },
            });
        },
        [submit, t]
    );

    const capIntegrationIsOn = useCheckCapIntegrationIsOn({ lead: application.lead });
    const requireToQualifyLead = useMemo(() => hasCapPrequalificationCheck(application), [application]);

    const confirmBookingAction = useCallback(() => {
        const businessPartnerGuid = application?.lead?.capValues?.businessPartnerGuid;
        const leadGuid = application?.lead?.capValues?.leadGuid;

        if (!capIntegrationIsOn || !requireToQualifyLead || (businessPartnerGuid && leadGuid)) {
            confirmAction(ActionKey.ConfirmBooking, { businessPartnerGuid, leadGuid });
        } else {
            selectCapBusinessPartnerAndLeadModal.open({
                lead: application.lead,
                submissionPurpose: SubmissionPurpose.ConfirmBooking,
                applicationId: application.id,
                stage,
                functions: {
                    createNewBpFn: capValue => confirmAction(ActionKey.ConfirmBooking, capValue),
                    createNewLeadFn: capValue => confirmAction(ActionKey.ConfirmBooking, capValue),
                    selectExistingLeadFn: capValue => confirmAction(ActionKey.ConfirmBooking, capValue),
                },
            });
        }
    }, [
        application,
        capIntegrationIsOn,
        requireToQualifyLead,
        confirmAction,
        selectCapBusinessPartnerAndLeadModal,
        stage,
    ]);

    const actions = useMemo<MenuProps['items']>(
        () =>
            [
                {
                    key: ActionKey.Void,
                    label: t('applicationDetails:appointmentActions.void'),
                    onClick: () => confirmAction(ActionKey.Void),
                },
                status === ApplicationStatus.BookingConfirmed && {
                    key: ActionKey.Complete,
                    label: t('applicationDetails:appointmentActions.complete'),
                    onClick: () => confirmAction(ActionKey.Complete),
                },

                status === ApplicationStatus.NewAppointment && {
                    key: ActionKey.ConfirmBooking,
                    label: t('applicationDetails:actions.confirmBooking'),
                    onClick: confirmBookingAction,
                },
            ].filter(Boolean),
        [t, status, confirmAction, confirmBookingAction]
    );

    const canResubmit = useVisitAppointmentSubmit({ application, stage, status });
    const canContinueApplication = mayContinueApplication(application, stage);

    if (!canResubmit && !canContinueApplication && !actions.length) {
        return null;
    }

    return (
        <Container forCI={forCI}>
            <SubActionContainer forCI={forCI}>
                <DropdownActions actions={actions} forCI={forCI} />
                {canResubmit && (
                    <VisitAppointmentSubmitAction
                        application={application}
                        forCI={forCI}
                        stage={stage}
                        status={status}
                    />
                )}
                {selectCapBusinessPartnerAndLeadModal.render()}
            </SubActionContainer>
        </Container>
    );
};

export default VisitAppointmentFooterAction;
