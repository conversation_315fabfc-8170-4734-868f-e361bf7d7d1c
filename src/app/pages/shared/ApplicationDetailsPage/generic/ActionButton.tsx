import { type ComponentType, useMemo } from 'react';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import type { ButtonProps } from '../../../../themes/types';
import { StyledActionButton } from '../standard/sharedUI';

const ActionButton = (props: ButtonProps & { forCI?: boolean }) => {
    const { Button } = useThemeComponents();

    const router = useRouter();

    const UsedButton = useMemo(
        () => (router ? Button : (StyledActionButton as unknown as ComponentType<ButtonProps>)),
        [router, Button]
    );

    return <UsedButton {...props} />;
};

export default ActionButton;
