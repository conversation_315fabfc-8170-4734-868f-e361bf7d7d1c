import { Modal, message, MenuProps } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import * as permissionKind from '../../../../../shared/permissions';
import { useApproveApplicationMutation } from '../../../../api/mutations/approveApplication';
import { useCancelApplicationMutation } from '../../../../api/mutations/cancelApplication';
import { useDeclineApplicationMutation } from '../../../../api/mutations/declineApplication';
import { ApplicationStage, ApplicationStatus } from '../../../../api/types';
import { CapSubmissionValue } from '../../../../components/cap/searchCustomersAndLeads/CapSubmissionValue';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { getApplicationStatus } from '../../../../utilities/application';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import hasPermissions from '../../../../utilities/hasPermissions';
import { useSelectCapCustomerOnApplicationDetails } from '../../JourneyPage/C@P';
import useEscapeKey from '../../useEscapeKey';
import { Container, SubActionContainer } from '../standard/Actions';
import { ActionKey } from '../standard/shared';
import ContinueApplicationAction, { mayContinueApplication } from './Actions/ContinueApplicationAction';
import DropdownActions from './Actions/DropdownActions';
import useApplyNewButton from './Actions/useApplyNewButton';

import { GenericFormApplication } from './shared';

type FormFooterActionProps = {
    application: GenericFormApplication;
    stage?: ApplicationStage;
};

const InsuranceFooterAction = ({ application, stage }: FormFooterActionProps) => {
    const { t } = useTranslation('applicationDetails');
    const { forCI } = useApplicationDetailsExtraContext();
    const selectCapBusinessPartnerAndLeadModal = useSelectCapCustomerOnApplicationDetails();

    const navigate = useNavigate();

    const onCancel = useCallback(() => navigate(-1), [navigate]);

    useEscapeKey(onCancel);

    const [approveMutation] = useApproveApplicationMutation();
    const [declineMutation] = useDeclineApplicationMutation();
    const [cancelMutation] = useCancelApplicationMutation();

    const performAction = useCallback(
        async (action: ActionKey, capValues?: CapSubmissionValue) => {
            const { submittedKey, submittingKey, mutation } = (() => {
                const baseVariables = { applicationId: application.id, stage };

                switch (action) {
                    case ActionKey.Approve:
                        return {
                            mutation: () => approveMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.approvingApplication',
                            submittedKey: 'applicationDetails:messages.approvedApplication',
                        };

                    case ActionKey.Void:
                        return {
                            mutation: () => cancelMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.cancellingApplication',
                            submittedKey: 'applicationDetails:messages.cancelledApplication',
                        };

                    case ActionKey.Decline:
                        return {
                            mutation: () => declineMutation({ variables: baseVariables }),
                            submittingKey: 'applicationDetails:messages.decliningApplication',
                            submittedKey: 'applicationDetails:messages.declinedApplication',
                        };

                    default:
                        throw new Error('not implemented');
                }
            })();

            message.loading({
                content: t(submittingKey),
                key: 'primary',
                duration: 0,
            });

            try {
                await mutation();

                message.success({
                    content: t(submittedKey),
                    key: 'primary',
                });
                navigate(-1);
            } catch (error) {
                message.destroy('primary');
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors?.$root) {
                    message.error(apolloErrors?.$root);
                }
            }
        },
        [t, approveMutation, cancelMutation, declineMutation, navigate, application.id, stage]
    );

    const confirmAction = useCallback(
        (action: ActionKey, capValues?: CapSubmissionValue) => {
            Modal.confirm({
                className: 'static-modal',
                title: t(`applicationDetails:confirmModal.title.${action}`),
                okText: t('applicationDetails:buttons.ok'),
                cancelText: t('applicationDetails:buttons.cancel'),
                onOk: () => {
                    performAction(action, capValues);
                },
            });
        },
        [performAction, t]
    );

    const status = useMemo(() => getApplicationStatus(application, stage), [application, stage]);

    const applyNewButton = useApplyNewButton(application, stage);
    const actions = useMemo<MenuProps['items']>(
        () =>
            [
                {
                    key: ActionKey.Void,
                    label: t('applicationDetails:actions.void'),
                    disabled:
                        status === ApplicationStatus.InsuranceCancelled ||
                        status === ApplicationStatus.InsuranceDeclined,
                    onClick: () => confirmAction(ActionKey.Void),
                },
                {
                    key: ActionKey.Approve,
                    label: t('applicationDetails:actions.approve'),
                    onClick: () => confirmAction(ActionKey.Approve),
                    disabled:
                        !hasPermissions(application.permissions, [permissionKind.approvalApplication]) ||
                        (status !== ApplicationStatus.SubmittedToInsuranceCompany &&
                            status !== ApplicationStatus.InsuranceCompanyReviewInProgress),
                },
                {
                    key: ActionKey.Decline,
                    label: t('applicationDetails:actions.decline'),
                    onClick: () => confirmAction(ActionKey.Decline),
                    disabled:
                        !hasPermissions(application.permissions, [permissionKind.approvalApplication]) ||
                        (status !== ApplicationStatus.SubmittedToInsuranceCompany &&
                            status !== ApplicationStatus.InsuranceCompanyReviewInProgress),
                },
                applyNewButton,
            ].filter(Boolean),
        [t, status, application.permissions, application?.lead.status, applyNewButton, confirmAction]
    );

    const canContinueApplication = mayContinueApplication(application, stage);

    // no action available
    if (!canContinueApplication && !actions.length) {
        return null;
    }

    return (
        <Container forCI={forCI}>
            <SubActionContainer forCI={forCI}>
                <DropdownActions actions={actions} forCI={forCI} />
                {canContinueApplication && (
                    <ContinueApplicationAction application={application} forCI={forCI} stage={stage} />
                )}
                {selectCapBusinessPartnerAndLeadModal.render()}
            </SubActionContainer>
        </Container>
    );
};

export default InsuranceFooterAction;
