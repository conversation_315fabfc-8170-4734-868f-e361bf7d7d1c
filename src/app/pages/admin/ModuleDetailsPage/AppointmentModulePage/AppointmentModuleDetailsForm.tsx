import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModuleSpecsFragment } from '../../../../api/fragments/AppointmentModuleSpecs';
import { ModuleRole } from '../../../../api/types';
import InputField from '../../../../components/fields/InputField';
import InputNumberField from '../../../../components/fields/InputNumberField';
import ModuleSelectField from '../../../../components/fields/ModuleSelectField';
import SelectField from '../../../../components/fields/SelectField';
import SwitchField from '../../../../components/fields/SwitchField';
import TranslatedTextAreaField from '../../../../components/fields/TranslatedTextAreaField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import useSystemOptions from '../../../../utilities/useSystemOptions';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import useTooltip from '../../../../utilities/useTooltip';
import AvailableTimeSlot from '../modules/implementations/shared/AvailableTimeSlot';
import { FormValues } from './shared';

type AppointmentModuleDetailsFormProps = {
    module: AppointmentModuleSpecsFragment;
};

const AppointmentModuleDetailsForm = ({ module }: AppointmentModuleDetailsFormProps) => {
    const { t } = useTranslation(['moduleDetails', 'appointmentModuleDetails']);
    const { values, setFieldValue } = useFormikContext<FormValues>();
    const { dayOfWeek } = useSystemOptions();
    const { yesNoSwitch } = useSystemSwitchData();

    useEffect(() => {
        if (!values.hasTestDriveProcess) {
            setFieldValue('hasTestDriveSigning', false);
        }
    }, [setFieldValue, values.hasTestDriveProcess]);

    useEffect(() => {
        if (!values.hasTestDriveSigning) {
            setFieldValue('signingModuleId', null);
        }
    }, [setFieldValue, values.hasTestDriveSigning]);

    const testDriveProcessTooltip = useTooltip('testDriveProcess');

    return (
        <CollapsibleWrapper defaultActiveKey="appointmentModuleDetails">
            <Panel key="appointmentModuleDetails" header={t('moduleDetails:sections.mainDetails')}>
                <Row gutter={10}>
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('appointmentModuleDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <TranslatedTextAreaField
                            {...t('appointmentModuleDetails:fields.bookingInformation', { returnObjects: true })}
                            name="bookingInformation"
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('appointmentModuleDetails:fields.unavailableDayOfWeek', { returnObjects: true })}
                            mode="multiple"
                            name="unavailableDayOfWeek"
                            options={dayOfWeek}
                            showSearch
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <InputNumberField
                            {...t('appointmentModuleDetails:fields.advancedBookingLimit', { returnObjects: true })}
                            addonAfter={t('appointmentModuleDetails:suffix.days')}
                            name="advancedBookingLimit"
                            required
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <InputNumberField
                            {...t('appointmentModuleDetails:fields.maxAdvancedBookingLimit', { returnObjects: true })}
                            addonAfter={t('appointmentModuleDetails:suffix.days')}
                            name="maxAdvancedBookingLimit"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...t('appointmentModuleDetails:fields.hasTestDriveProcess', { returnObjects: true })}
                            {...yesNoSwitch}
                            name="hasTestDriveProcess"
                            tooltip={testDriveProcessTooltip}
                        />
                    </Col>

                    {values.hasTestDriveProcess && (
                        <>
                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...t('appointmentModuleDetails:fields.hasTestDriveSigning', {
                                        returnObjects: true,
                                    })}
                                    {...yesNoSwitch}
                                    name="hasTestDriveSigning"
                                />
                            </Col>
                            {values.hasTestDriveSigning && (
                                <Col md={8} xs={24}>
                                    <ModuleSelectField
                                        {...t(`appointmentModuleDetails:fields.signingModule`, { returnObjects: true })}
                                        companyId={module.company.id}
                                        moduleRole={ModuleRole.Signing}
                                        name="signingModuleId"
                                        addNoneOption
                                    />
                                </Col>
                            )}
                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...t('appointmentModuleDetails:fields.showRemoteFlowButtonInKYCPage', {
                                        returnObjects: true,
                                    })}
                                    {...yesNoSwitch}
                                    name="showRemoteFlowButtonInKYCPage"
                                />
                            </Col>
                        </>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...t('appointmentModuleDetails:fields.isReminderTimeEnabled', {
                                returnObjects: true,
                            })}
                            {...yesNoSwitch}
                            name="isReminderTimeEnabled"
                        />
                    </Col>
                    {values.isReminderTimeEnabled && (
                        <Col md={8} xs={24}>
                            <InputNumberField
                                {...t('appointmentModuleDetails:fields.timeToSendReminder', {
                                    returnObjects: true,
                                })}
                                addonAfter={t('appointmentModuleDetails:suffix.hours')}
                                addonBefore={t('appointmentModuleDetails:prefix.after')}
                                name="timeToSendReminder"
                                precision={2}
                            />
                        </Col>
                    )}

                    <Col span={24}>
                        <AvailableTimeSlot name="bookingTimeSlot" timeZone={module.company.timeZone} />
                    </Col>
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default AppointmentModuleDetailsForm;
