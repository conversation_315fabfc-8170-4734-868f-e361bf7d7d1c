import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModuleSpecsFragment } from '../../../../api/fragments/AppointmentModuleSpecs';
import {
    UpdateAppointmentModuleMutation,
    UpdateAppointmentModuleMutationVariables,
    UpdateAppointmentModuleDocument,
} from '../../../../api/mutations/updateAppointmentModule';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import AppointmentModuleDetailsForm from './AppointmentModuleDetailsForm';
import { FormValues } from './shared';

export type AppointmentModuleDetailsProps = {
    module: AppointmentModuleSpecsFragment;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredNumber('advancedBookingLimit'),
    validators.only(value => value?.isReminderTimeEnabled, validators.requiredNumber('timeToSendReminder'))
);

const AppointmentModuleDetails = ({ module }: AppointmentModuleDetailsProps) => {
    const { t } = useTranslation(['moduleDetails', 'appointmentModuleDetails']);

    const validate = useValidator(validator);

    const initialValues = useMemo(
        (): FormValues => ({
            ...pick(
                [
                    'displayName',
                    'bookingInformation',
                    'advancedBookingLimit',
                    'unavailableDayOfWeek',
                    'maxAdvancedBookingLimit',
                    'hasTestDriveProcess',
                    'hasTestDriveSigning',
                    'signingModuleId',
                    'showRemoteFlowButtonInKYCPage',
                    'timeToSendReminder',
                    'isReminderTimeEnabled',
                ],
                module
            ),
            // Remap slot based on company timezone
            bookingTimeSlot: module.bookingTimeSlot.map(item => ({
                ...item,
                // Use current date instead of date from saved data
                // as initial value. To accommodate DST changes
                // So it matches the label of the field also
                slot: dayjs()
                    .set('hour', dayjs(item.slot).hour())
                    .set('minute', dayjs(item.slot).minute())
                    .set('second', 0)
                    .tz(module.company.timeZone),
            })),
        }),
        [module]
    );

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError<FormValues>(
        async values => {
            // submitting message
            message.loading({
                content: t('appointmentModuleDetails:messages.updateAppointmentDetailsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            const settings: UpdateAppointmentModuleMutationVariables['settings'] = {
                ...values,

                // Change slot to company timezone
                bookingTimeSlot: values.bookingTimeSlot.map(item => ({
                    ...item,
                    slot: dayjs(item.slot)
                        .set('hour', item.slot.hour())
                        .set('minute', item.slot.minute())
                        .set('second', 0)
                        .tz(module.company.timeZone, true)
                        .toDate(),
                })),
            };

            // submit creation
            await apolloClient
                .mutate<UpdateAppointmentModuleMutation, UpdateAppointmentModuleMutationVariables>({
                    mutation: UpdateAppointmentModuleDocument,
                    variables: {
                        moduleId: module.id,
                        settings,
                    },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('appointmentModuleDetails:messages.updateAppointmentDetailsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, module.company.timeZone, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateMainDetails" name="updateMainDetails" onSubmitCapture={handleSubmit}>
                    <AppointmentModuleDetailsForm module={module} />
                </Form>
            )}
        </Formik>
    );
};

export default AppointmentModuleDetails;
