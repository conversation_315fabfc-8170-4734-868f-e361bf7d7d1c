/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { Form, message } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
    CreateCtsModuleSettingDocument,
    CreateCtsModuleSettingMutation,
    CreateCtsModuleSettingMutationVariables,
} from '../../../../../api/mutations/createCtsModuleSetting';
import { CtsSettingInitialInput } from '../../../../../api/types';
import { useDealerContext } from '../../../../../components/contexts/DealerContextManager';
import DealershipPriceDisclaimerField, {
    DealershipPriceDisclaimerFieldProps,
} from '../../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import SwitchField from '../../../../../components/fields/SwitchField';
import useHandleError from '../../../../../utilities/useHandleError';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import useValidator from '../../../../../utilities/useValidator';
import { CompanySpecs } from '../../../AddModulePage/shared';
import MarketTypeGroup from '../../modules/implementations/shared/MarketTypeGroup';
import useModuleOptions from '../../modules/implementations/shared/useModuleOptions';
import usePriceDisclaimerToolTipByMarket from '../../usePriceDisclaimerToolTipByMarket';
import validator from './validators';

export type CompanySpecsForCts = Pick<
    CompanySpecs,
    'modules' | 'paymentSettings' | 'liveChatSettings' | 'autoplaySettings'
>;

export type AddCtsSettingsFormProps = {
    moduleId: string;
    company: CompanySpecsForCts;
    onComplete: () => void;
};

const initialValues: CtsSettingInitialInput = {
    displayName: '',
    identifier: '',
    bankModuleId: null,
    vehicleModuleIds: [],
    insuranceModuleId: null,
    priceDisclaimer: {
        defaultValue: { defaultValue: '', overrides: [] },
        overrides: [],
    },
    preownedPriceDisclaimer: {
        defaultValue: { defaultValue: '', overrides: [] },
        overrides: [],
    },
    finderModuleIds: [],
    isActive: true,
};

type CtsDealershipPriceDisclaimerFieldProps = Omit<DealershipPriceDisclaimerFieldProps, 'itemProps'>;

const CtsDealershipPriceDisclaimerField = (props: CtsDealershipPriceDisclaimerFieldProps) => {
    const {
        values: { market },
    } = useFormikContext<CtsSettingInitialInput>();

    const priceDisclaimerToolTip = usePriceDisclaimerToolTipByMarket(market);
    const priceDisclaimerItemProps = useMemo(() => ({ tooltip: priceDisclaimerToolTip }), [priceDisclaimerToolTip]);

    return <DealershipPriceDisclaimerField {...props} itemProps={priceDisclaimerItemProps} />;
};

const AddCtsSettingsForm = ({ moduleId, onComplete, company }: AddCtsSettingsFormProps) => {
    const { t } = useTranslation(['moduleDetails', 'ctsModuleDetails']);
    const { dealersFromApi } = useDealerContext();

    const options = useModuleOptions(company);

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError(
        async (values: CtsSettingInitialInput) => {
            message.loading({
                content: t('ctsModuleDetails:settings.create.messages.submitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient
                .mutate<CreateCtsModuleSettingMutation, CreateCtsModuleSettingMutationVariables>({
                    mutation: CreateCtsModuleSettingDocument,
                    variables: { moduleId, settings: values },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            message.success({
                content: t('ctsModuleDetails:settings.create.messages.success'),
                key: 'primary',
            });

            // close the drawer
            onComplete();
        },
        [onComplete, t, apolloClient, moduleId]
    );

    const vehicleModuleOptions = useMemo(() => [...options.vehicles, ...options.finderVehicles], [options]);

    const validate = useValidator(validator);
    const { yesNoSwitch } = useSystemSwitchData();

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="addCtsSettings" layout="vertical" name="addCtsSettings" onSubmitCapture={handleSubmit}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                    <InputField
                        {...t('moduleDetails:fields.ctsSettingId', { returnObjects: true })}
                        name="identifier"
                        required
                    />
                    <SwitchField
                        {...t('moduleDetails:fields.isActive', {
                            returnObjects: true,
                        })}
                        name="isActive"
                        {...yesNoSwitch}
                    />

                    <CtsDealershipPriceDisclaimerField
                        {...t('moduleDetails:fields.priceDisclaimer', { returnObjects: true })}
                        dealers={dealersFromApi}
                        name="priceDisclaimer"
                    />
                    <CtsDealershipPriceDisclaimerField
                        {...t('moduleDetails:fields.preownedPriceDisclaimer', { returnObjects: true })}
                        dealers={dealersFromApi}
                        name="preownedPriceDisclaimer"
                    />
                    <MarketTypeGroup dealers={dealersFromApi} name="market" asFragment required />
                    <SelectField
                        {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                        name="bankModuleId"
                        options={options.financing}
                        required
                        showSearch
                    />
                    <SelectField
                        {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                        name="insuranceModuleId"
                        options={options.insurance}
                        allowClear
                        showSearch
                    />
                    <SelectField
                        {...t('moduleDetails:fields.vehicleModuleId', { returnObjects: true })}
                        mode="multiple"
                        name="vehicleModuleIds"
                        options={vehicleModuleOptions}
                        required
                        showSearch
                    />
                </Form>
            )}
        </Formik>
    );
};

export default AddCtsSettingsForm;
