import { Button, Descriptions, Grid, Tabs } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import * as permissionKind from '../../../../../shared/permissions';
import ConsolePageWithHeader from '../../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../../utilities/hasPermissions';
import useFormattedSimpleVersioning from '../../../../utilities/useFormattedSimpleVersioning';
import TradeInModuleActions from './TradeInModuleActions';
import TradeInModuleInnerPage from './TradeInModuleInnerPage';
import { HeaderTabs, TradeInModulePageProps } from './shared';

const TradeInModulePage = ({ module }: TradeInModulePageProps) => {
    const { t } = useTranslation(['tradeInModuleDetails']);
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState(HeaderTabs.MainDetails);
    const screens = Grid.useBreakpoint();
    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: module.versioning,
        timeZone: module.company.timeZone,
    });

    const permissions = {
        hasDeletePermission: hasPermissions(module.permissions, [permissionKind.deleteModule]),
    };

    const content = (
        <Descriptions column={{ xs: 1, md: 2 }} bordered>
            <Descriptions.Item label={t('tradeInModuleDetails:content.displayName')}>
                {module.displayName}
            </Descriptions.Item>
            <Descriptions.Item label={t('tradeInModuleDetails:content.company')}>
                {module.company.displayName}
            </Descriptions.Item>
            <Descriptions.Item {...t('common:fields.updated', { returnObjects: true, offset })}>
                {updated}
            </Descriptions.Item>
        </Descriptions>
    );

    return (
        <ConsolePageWithHeader
            content={content}
            extra={<TradeInModuleActions module={module} permissions={permissions} />}
            footer={[
                <Button form="updateTradeInModuleForm" htmlType="submit" type="primary">
                    {t('tradeInModuleDetails:actions.update')}
                </Button>,
            ]}
            header={{
                footer: (
                    <Tabs activeKey={activeTab} onChange={(activeKey: HeaderTabs) => setActiveTab(activeKey)}>
                        <Tabs.TabPane
                            key={HeaderTabs.MainDetails}
                            tab={t('tradeInModuleDetails:tabs.mainDetails.title')}
                        />
                        <Tabs.TabPane
                            key={HeaderTabs.Authorization}
                            tab={t('tradeInModuleDetails:tabs.authorization.title')}
                        />
                    </Tabs>
                ),
            }}
            onBack={() => navigate('/admin/system/modules')}
            title={screens.md ? t('tradeInModuleDetails:title', { name: module.displayName }) : module.displayName}
        >
            <TradeInModuleInnerPage activeTab={activeTab} module={module} />
        </ConsolePageWithHeader>
    );
};

export default TradeInModulePage;
