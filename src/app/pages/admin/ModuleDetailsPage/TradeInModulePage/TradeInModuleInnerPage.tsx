import React from 'react';
import { TradeInModuleSpecsFragment } from '../../../../api/fragments/TradeInModuleSpecs';
import TradeInModuleAuthorization from './TradeInModuleAuthorization';
import TradeInModuleMainDetails from './TradeInModuleMainDetails';
import { HeaderTabs } from './shared';

type TradeInModuleInnerPageProps = {
    activeTab: HeaderTabs;
    module: TradeInModuleSpecsFragment;
};

const TradeInModuleInnerPage = ({ activeTab, module }: TradeInModuleInnerPageProps) => {
    switch (activeTab) {
        case HeaderTabs.MainDetails:
            return <TradeInModuleMainDetails module={module} />;

        case HeaderTabs.Authorization:
            return <TradeInModuleAuthorization module={module} />;

        default:
            return null;
    }
};

export default TradeInModuleInnerPage;
