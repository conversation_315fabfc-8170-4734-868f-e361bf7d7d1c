import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import { EventApplicationModuleSpecsFragment } from '../../../../api/fragments/EventApplicationModuleSpecs';
import CascaderField from '../../../../components/fields/CascaderField';
import InputField from '../../../../components/fields/InputField';
import SelectField from '../../../../components/fields/SelectField';
import SwitchField from '../../../../components/fields/SwitchField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import useModuleOptions from '../modules/implementations/shared/useModuleOptions';
import { FormValues } from './type';

type EventApplicationModuleProps = {
    module: EventApplicationModuleSpecsFragment;
};

const EventApplicationModuleMainDetailsForm = ({ module }: EventApplicationModuleProps) => {
    const { t } = useTranslation(['moduleDetails']);

    const { yesNoSwitch } = useSystemSwitchData();
    const { values } = useFormikContext<FormValues>();
    const options = useModuleOptions(module.company, { addNoneOptions: { promoCodes: true } });

    return (
        <CollapsibleWrapper defaultActiveKey="moduleDetails_mainDetails">
            <Panel key="moduleDetails_mainDetails" header={t('moduleDetails:sections.mainDetails')}>
                <Row gutter={10}>
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.isOcrEnabled', { returnObjects: true })}
                            name="isOcrEnabled"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                            name="appointmentModuleId"
                            options={options.appointments}
                            showSearch
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.visitAppointmentModuleId', { returnObjects: true })}
                            name="visitAppointmentModuleId"
                            options={options.visitAppointments}
                            showSearch
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <CascaderField
                            {...t('moduleDetails:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>

                    {values.appointmentModuleId && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.displayAppointmentDatepicker', { returnObjects: true })}
                                name="displayAppointmentDatepicker"
                            />
                        </Col>
                    )}

                    {values.visitAppointmentModuleId && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.displayVisitAppointmentDatepicker', {
                                    returnObjects: true,
                                })}
                                name="displayVisitAppointmentDatepicker"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            allowClear
                            {...t('moduleDetails:fields.capModuleId', { returnObjects: true })}
                            name="capModuleId"
                            options={options.cap}
                            showSearch
                        />
                    </Col>
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default EventApplicationModuleMainDetailsForm;
