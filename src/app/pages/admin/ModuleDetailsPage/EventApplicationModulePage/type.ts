import { EventApplicationModuleSpecsFragment } from '../../../../api/fragments/EventApplicationModuleSpecs';
import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments/UploadFileWithPreviewFormData';
import {
    EventApplicationModuleEmailSubmitOrderInput,
    EventApplicationModuleUpdateSettings,
} from '../../../../api/types';
import { EmailOverrideContent } from '../FinderApplicationModulePage/type';

export enum HeaderTabs {
    Vehicles = 'vehicles',
    EmailContents = 'emailContents',
    ModuleDetails = 'moduleDetails',
}

export enum EditMode {
    Vehicles = 'vehicles',
    FinanceProducts = 'financeProducts',
    ModuleDetails = 'moduleDetails',
    EmailContents = 'emailContents',
}

export const FOOTER_ID = 'eventApplicationModuleFooterLayout';

export type EventApplicationEmailWithOverrides<T> = {
    defaultValue: T;
    overrides: (T & EmailOverrideContent)[];
};

export type EventApplicationModuleConfirmEmailContents = EventApplicationModuleEmailSubmitOrderInput & {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
};

export type EventApplicationModuleEmailFormValues = {
    submitOrder: EventApplicationEmailWithOverrides<EventApplicationModuleConfirmEmailContents>;
};

export type EventApplicationModuleCounterKey =
    | 'leadCounter'
    | 'applicationCounter'
    | 'reservationCounter'
    | 'appointmentCounter'
    | 'eventCounter';

export type FormValues = EventApplicationModuleUpdateSettings &
    Partial<Pick<EventApplicationModuleSpecsFragment, EventApplicationModuleCounterKey>>;
