import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { omit, pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationMarketTypeFragmentFragment } from '../../../../api/fragments/ApplicationMarketTypeFragment';
import { EventApplicationModuleSpecsFragment } from '../../../../api/fragments/EventApplicationModuleSpecs';
import {
    UpdateEventApplicationModuleMainDetailsDocument,
    UpdateEventApplicationModuleMainDetailsMutation,
    UpdateEventApplicationModuleMainDetailsMutationVariables,
} from '../../../../api/mutations/updateEventApplicationModuleMainDetails';
import { ApplicationModuleMarketTypeInput } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import CounterSettingDetails from '../shared/CounterSettingDetails';
import EventApplicationModuleMainDetailsForm from './EventApplicationModuleMainDetailsForm';
import { EventApplicationModuleCounterKey, FormValues } from './type';

const counterFields: EventApplicationModuleCounterKey[] = [
    'leadCounter',
    'applicationCounter',
    'reservationCounter',
    'appointmentCounter',
    'eventCounter',
];

export type MainDetailsProps = {
    module: EventApplicationModuleSpecsFragment;
};

export const getFinancingApplicationMarket = (
    marketType: ApplicationMarketTypeFragmentFragment
): ApplicationModuleMarketTypeInput => {
    switch (marketType.__typename) {
        case 'SingaporeApplicationMarket':
            return { singapore: { coe: marketType.coe } };

        case 'NewZealandApplicationMarket':
            return {
                newZealand: {
                    ppsr: marketType.ppsr,
                    estFee: marketType.estFee,
                    bankEstFee: marketType.bankEstFee,
                    nzFees: marketType.nzFees,
                },
            };

        default:
            return null;
    }
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredBoolean('isOcrEnabled'),
    validators.requiredBoolean('showResetKYCButton')
);

const EventApplicationModuleMainDetails = ({ module }: MainDetailsProps) => {
    const { t } = useTranslation(['moduleDetails']);

    const validate = useValidator(validator);

    const initialValues = useMemo(
        (): FormValues => ({
            ...pick(['market', 'depositAmount', 'promoCodeModuleId', 'capModuleId', ...counterFields], module),
            displayName: module.displayName,
            isOcrEnabled: module.isOcrEnabled,
            showResetKYCButton: module.showResetKYCButton,
            displayAppointmentDatepicker: module.displayAppointmentDatepicker,
            displayVisitAppointmentDatepicker: module.displayVisitAppointmentDatepicker ?? false,
            appointmentModuleId: module.appointmentModuleId,
            liveChatSettingId: module.liveChatSettingId,
            visitAppointmentModuleId: module.visitAppointmentModuleId,
        }),
        [module]
    );

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const settingsWithoutCounters = omit(counterFields, values);
            const settings = {
                ...settingsWithoutCounters,
                capModuleId: values.capModuleId ?? null,
            };

            // submitting message
            message.loading({
                content: t('eventApplicationModuleDetails:messages.updateMainDetailsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // submit creation
            await apolloClient
                .mutate<
                    UpdateEventApplicationModuleMainDetailsMutation,
                    UpdateEventApplicationModuleMainDetailsMutationVariables
                >({
                    mutation: UpdateEventApplicationModuleMainDetailsDocument,
                    variables: {
                        moduleId: module.id,
                        settings,
                    },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('eventApplicationModuleDetails:messages.updateMainDetailsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateMainDetails" name="updateMainDetails" onSubmitCapture={handleSubmit}>
                    <EventApplicationModuleMainDetailsForm module={module} />
                    <CounterSettingDetails names={counterFields} />
                </Form>
            )}
        </Formik>
    );
};

export default EventApplicationModuleMainDetails;
