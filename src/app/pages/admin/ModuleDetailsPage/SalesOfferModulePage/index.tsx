import { Button, Descriptions, Grid, Tabs } from 'antd';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import * as permissionKind from '../../../../../shared/permissions';
// eslint-disable-next-line max-len
import { SalesOfferModuleWithPermissionsSpecsFragment } from '../../../../api/fragments/SalesOfferModuleWithPermissionsSpecs';
import ConsolePageWithHeader from '../../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../../utilities/hasPermissions';
import useFormattedSimpleVersioning from '../../../../utilities/useFormattedSimpleVersioning';
import SalesOfferModuleAction from './SalesOfferModuleAction';
import SalesOfferModuleTabs from './SalesOfferModuleTabs';
import { HeaderTabs } from './enums';

export type SalesOfferModulePageProps = {
    module: SalesOfferModuleWithPermissionsSpecsFragment;
};

const SalesOfferModulePage = ({ module }: SalesOfferModulePageProps) => {
    const { t } = useTranslation(['common', 'salesOfferModuleDetails']);

    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: module.versioning,
        timeZone: module.company.timeZone,
    });

    const [activeTab, setActiveTab] = useState(HeaderTabs.MainDetials);

    const navigate = useNavigate();
    const screens = Grid.useBreakpoint();

    const permissions = {
        hasDeletePermission: hasPermissions(module.permissions, [permissionKind.deleteModule]),
    };

    const formUpdate = useMemo(() => {
        switch (activeTab) {
            case HeaderTabs.MainDetials:
                return 'updateMainDetails';

            case HeaderTabs.EmailContents:
                return 'salesOfferModuleEmailSection';

            default:
                return 'form';
        }
    }, [activeTab]);

    const content = (
        <Descriptions column={{ xs: 1, md: 2 }} bordered>
            <Descriptions.Item label={t('salesOfferModuleDetails:descriptions.company')}>
                {module.company.displayName}
            </Descriptions.Item>
            <Descriptions.Item label={t('salesOfferModuleDetails:descriptions.financing')}>
                {module.bankModule.displayName}
            </Descriptions.Item>
            <Descriptions.Item label={t('salesOfferModuleDetails:descriptions.insurance')}>
                {module.insuranceModule.displayName}
            </Descriptions.Item>
            <Descriptions.Item label={t('salesOfferModuleDetails:descriptions.vehicle')}>
                {module.vehicleModule.displayName}
            </Descriptions.Item>
            <Descriptions.Item label={t('salesOfferModuleDetails:descriptions.company')}>
                {module.company.displayName}
            </Descriptions.Item>
            <Descriptions.Item label={t('salesOfferModuleDetails:descriptions.porscheVehicleData')}>
                {module.vehicleDataWithPorscheCodeIntegrationSetting.module.displayName}
            </Descriptions.Item>

            <Descriptions.Item {...t('common:fields.updated', { returnObjects: true, offset })}>
                {updated}
            </Descriptions.Item>
        </Descriptions>
    );

    return (
        <ConsolePageWithHeader
            content={content}
            extra={<SalesOfferModuleAction module={module} permissions={permissions} />}
            footer={[
                <Button key="update" form={formUpdate} htmlType="submit" type="primary">
                    {t('salesOfferModuleDetails:actions.update')}
                </Button>,
            ]}
            header={{
                footer: (
                    <Tabs onChange={(activeKey: HeaderTabs) => setActiveTab(activeKey)}>
                        <Tabs.TabPane
                            key={HeaderTabs.MainDetials}
                            tab={t('salesOfferModuleDetails:tabs.moduleDetails')}
                        />
                        <Tabs.TabPane
                            key={HeaderTabs.EmailContents}
                            tab={t('salesOfferModuleDetails:tabs.emailContents')}
                        />
                    </Tabs>
                ),
            }}
            onBack={() => navigate(`/admin/system/modules`)}
            title={screens.md ? t('salesOfferModuleDetails:title', { name: module.displayName }) : module.displayName}
        >
            <SalesOfferModuleTabs activeTabs={activeTab} module={module} />
        </ConsolePageWithHeader>
    );
};

export default SalesOfferModulePage;
