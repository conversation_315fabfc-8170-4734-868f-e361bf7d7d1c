import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { get, pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfiguratorModuleSpecsFragment } from '../../../../api/fragments/ConfiguratorModuleSpecs';
import {
    UpdateConfiguratorModuleDocument,
    UpdateConfiguratorModuleMutation,
    UpdateConfiguratorModuleMutationVariables,
} from '../../../../api/mutations/updateConfiguratorModule';
import {
    ApplicationMarket,
    ConfiguratorModuleUpdateSettings,
    EventLeadOriginType,
    EventMediumType,
} from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import { hasValue } from '../../../../utilities/fp';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { getFinancingApplicationMarket } from '../StandardApplicationModulePage/StandardApplicationModuleMainDetails';
import { hasAppointmentScenario, hasPaymentScenario } from '../modules/implementations/shared';
import {
    getDefaultDealershipPaymentSettingValue,
    processDealershipPaymentSettingInput,
} from '../modules/implementations/shared/getDealershipPaymentSetting';
import CounterSettingDetails from '../shared/CounterSettingDetails';
import { newZealandMarketValidator, singaporeMarketValidator } from '../shared/validators';
import ConfiguratorModuleForm from './ConfiguratorModuleConfigurationForm';
import { type ConfiguratorModuleConfigurationFormValues, ConfiguratorModuleCounterKey } from './type';

const counterFields: ConfiguratorModuleCounterKey[] = [
    'applicationCounter',
    'reservationCounter',
    'leadCounter',
    'appointmentCounter',
    'insuranceCounter',
];

type ConfiguratorModuleConfigurationProps = {
    module: ConfiguratorModuleSpecsFragment;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.only(
        values => hasPaymentScenario(get('scenarios', values)),
        validators.compose(
            validators.requiredString('paymentSetting.defaultId'),
            validators.requiredNumber('depositAmount.defaultValue')
        )
    ),
    validators.only(
        values => hasAppointmentScenario(values?.scenarios),
        validators.requiredString('appointmentModuleId')
    ),
    validators.only(values => hasValue('bankModuleId')(values), validators.requiredString('market')),
    validators.only(values => {
        const showInsuranceCalculator = get('showInsuranceCalculator', values);

        return showInsuranceCalculator;
    }, validators.requiredString('insuranceModuleId')),
    validators.only(values => {
        const showFinanceCalculator = get('showFinanceCalculator', values);

        return showFinanceCalculator;
    }, validators.requiredString('bankModuleId')),
    validators.only(values => {
        const market = get('market', values);
        const marketType = get('marketType', values);

        return market === ApplicationMarket.Singapore && !!marketType;
    }, singaporeMarketValidator),
    validators.only(values => {
        const market = get('market', values);
        const marketType = get('marketType', values);

        return market === ApplicationMarket.NewZealand && !!marketType;
    }, newZealandMarketValidator),
    validators.requiredArray('scenarios', true),
    validators.requiredBoolean('tradeIn'),
    validators.requiredString('assignee.defaultId'),
    validators.requiredBoolean('showResetKYCButton'),
    validators.only(
        ({ capModuleId }) => capModuleId,
        validators.compose(
            validators.requiredBoolean('capPrequalification'),
            validators.requiredStringEnum('leadOrigin', EventLeadOriginType),
            validators.requiredStringEnum('leadMedium', EventMediumType),
            validators.only(
                ({ leadMedium }) => leadMedium !== EventMediumType.Walkin,
                validators.requiredString('leadCampaignId')
            )
        )
    )
);

const ConfiguratorModuleConfiguration = ({ module }: ConfiguratorModuleConfigurationProps) => {
    const { t } = useTranslation('configuratorModuleDetail');
    const apolloClient = useApolloClient();
    const validate = useValidator(validator);
    const initialValues: ConfiguratorModuleConfigurationFormValues = useMemo(
        () => ({
            ...pick(
                [
                    'displayName',
                    'testDrive',
                    'tradeIn',
                    'isTradeInAmountVisible',
                    'scenarios',
                    'assignee',
                    'myInfoSettingId',
                    'liveChatSettingId',
                    'termsTitle',
                    'termsText',
                    'priceDisclaimer',
                    'promoCodeModuleId',
                    'isInventoryEnabled',
                    'market',
                    'showResetKYCButton',
                    'showFromValueOnVehicleDetails',
                    'showRemoteFlowButtonInKYCPage',
                    'skipForDeposit',
                    'bankDisplayPreference',
                    'externalUrl',
                    'displayAppointmentDatepicker',
                    'appointmentModuleId',
                    'insurerDisplayPreference',
                    'insurerIds',
                    'insuranceModuleId',
                    'financingPreference',
                    'isInsuranceOptional',
                    'showFinanceCalculator',
                    'showInsuranceCalculator',
                    'depositAmount',
                    'bankIds',
                    'capModuleId',
                    'isSearchCapCustomerOptional',
                    'capPrequalification',
                    'leadOrigin',
                    'leadMedium',
                    'leadCampaignId',
                    ...counterFields,
                    'isCustomerDataRetreivalByPorscheId',
                    'isPorscheIdLoginMandatory',
                ],
                module
            ),
            marketType: getFinancingApplicationMarket(module.marketType),
            bankModuleId: module?.bankModule?.id,
            paymentSetting: getDefaultDealershipPaymentSettingValue(module.paymentSetting),
        }),
        [module]
    );

    const onSubmit = useHandleError(
        async (values: ConfiguratorModuleConfigurationFormValues) => {
            const { scenarios } = values;
            message.loading({
                content: t('configuratorModuleDetail:messages.submitting'),
                key: 'primary',
                duration: 0,
            });

            const settings: ConfiguratorModuleUpdateSettings = {
                ...pick(
                    [
                        'displayName',
                        'tradeIn',
                        'isTradeInAmountVisible',
                        'scenarios',
                        'assignee',
                        'myInfoSettingId',
                        'liveChatSettingId',
                        'termsTitle',
                        'termsText',
                        'priceDisclaimer',
                        'promoCodeModuleId',
                        'isInventoryEnabled',
                        'market',
                        'marketType',
                        'bankModuleId',
                        'showResetKYCButton',
                        'showFromValueOnVehicleDetails',
                        'showRemoteFlowButtonInKYCPage',
                        'skipForDeposit',
                        'bankDisplayPreference',
                        'externalUrl',
                        'displayAppointmentDatepicker',
                        'appointmentModuleId',
                        'insurerDisplayPreference',
                        'insurerIds',
                        'insuranceModuleId',
                        'financingPreference',
                        'isInsuranceOptional',
                        'showFinanceCalculator',
                        'showInsuranceCalculator',
                        'bankIds',
                        'capModuleId',
                        'isSearchCapCustomerOptional',
                        'capPrequalification',
                        'leadOrigin',
                        'leadMedium',
                        'leadCampaignId',
                        'isCustomerDataRetreivalByPorscheId',
                        'isPorscheIdLoginMandatory',
                    ],
                    values
                ),
                testDrive: hasAppointmentScenario(scenarios),
                depositAmount: null,
                bankModuleId: values.showFinanceCalculator ? values.bankModuleId : null,
                capModuleId: values.capModuleId || null,
            };

            if (hasPaymentScenario(scenarios)) {
                // assign value if deposit
                settings.paymentSetting = processDealershipPaymentSettingInput(values.paymentSetting);
                settings.depositAmount = values.depositAmount;
            }

            await apolloClient
                .mutate<UpdateConfiguratorModuleMutation, UpdateConfiguratorModuleMutationVariables>({
                    mutation: UpdateConfiguratorModuleDocument,
                    variables: { moduleId: module.id, settings },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            message.success({
                content: t('configuratorModuleDetail:messages.success'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateForm" name="updateForm" onSubmitCapture={handleSubmit}>
                    <ConfiguratorModuleForm module={module} />
                    <CounterSettingDetails names={counterFields} />
                </Form>
            )}
        </Formik>
    );
};

export default ConfiguratorModuleConfiguration;
