import { ConfiguratorModuleSpecsFragment } from '../../../../api/fragments/ConfiguratorModuleSpecs';
import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments/UploadFileWithPreviewFormData';
import { ConfiguratorModuleEmailContentsSetting, ConfiguratorModuleUpdateSettings } from '../../../../api/types';

export enum HeaderTabs {
    Vehicles = 'vehicles',
    FinanceProducts = 'financeProducts',
    MainDetails = 'mainDetails',
    Email = 'email',
}

export type ConfiguratorEmailFormValues = {
    saveOrder: ConfiguratorModuleEmailContentsSetting['saveOrder'] & {
        bannerImage?: File | UploadFileWithPreviewFormDataFragment;
        introImage?: File | UploadFileWithPreviewFormDataFragment;
    };
    submitOrder: ConfiguratorModuleEmailContentsSetting['submitOrder'] & {
        introImage?: File | UploadFileWithPreviewFormDataFragment;
    };
};

export type ConfiguratorModuleCounterKey =
    | 'applicationCounter'
    | 'reservationCounter'
    | 'leadCounter'
    | 'appointmentCounter'
    | 'insuranceCounter';

export type ConfiguratorModuleConfigurationFormValues = ConfiguratorModuleUpdateSettings &
    Partial<Pick<ConfiguratorModuleSpecsFragment, ConfiguratorModuleCounterKey>>;
