import {
    StandardApplicationModuleEmailContentInput,
    StandardApplicationModuleEmailContentSalesPersonInput,
    StandardApplicationModuleEmailShareComparisonContentInput,
    DealerUploadedFileWithPreviewDataFragment,
    StandardApplicationModuleUpdateSettings,
} from '../../../../api';

export enum HeaderTabs {
    Vehicles = 'vehicles',
    FinanceProducts = 'financeProducts',
    ModuleDetails = 'moduleDetails',
    Emails = 'emails',
}

export enum EditMode {
    ModuleDetails = 'moduleDetails',
    Emails = 'emails',
}

export type StandardApplicationCounterFieldKey =
    | 'leadCounter'
    | 'applicationCounter'
    | 'reservationCounter'
    | 'appointmentCounter'
    | 'insuranceCounter';

type EmailContentWithIntroImage = StandardApplicationModuleEmailContentInput & {
    introImage?: DealerUploadedFileWithPreviewDataFragment;
};

type EmailContentShareSubmissionWithIntroImage = StandardApplicationModuleEmailShareComparisonContentInput & {
    introImage?: DealerUploadedFileWithPreviewDataFragment;
};

export type StandardApplicationEmailFormValues = {
    customer: {
        share: EmailContentShareSubmissionWithIntroImage;
        comparisonShare: EmailContentShareSubmissionWithIntroImage;
        proceedWithCustomerDevice: EmailContentWithIntroImage;
        submissionConfirmation: EmailContentWithIntroImage;
        approved: EmailContentWithIntroImage;
        rejected: EmailContentWithIntroImage;
        cancelled: EmailContentWithIntroImage;
    };
    salesPerson: {
        [key in keyof StandardApplicationModuleEmailContentSalesPersonInput]: EmailContentWithIntroImage;
    };
};

export type FormValues = StandardApplicationModuleUpdateSettings;
