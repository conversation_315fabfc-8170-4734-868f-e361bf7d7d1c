import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { StandardApplicationModuleSpecsFragment } from '../../../../api/fragments/StandardApplicationModuleSpecs';
import { EventMediumType, ModuleType } from '../../../../api/types';
import CascaderField from '../../../../components/fields/CascaderField';
import DealerDepositAmountField from '../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipCascaderField from '../../../../components/fields/DealershipFields/DealershipCascaderField';
import DealershipPriceDisclaimerField from '../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import InputField from '../../../../components/fields/InputField';
import SelectField from '../../../../components/fields/SelectField';
import SwitchField from '../../../../components/fields/SwitchField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import useEventOptions from '../../../../utilities/useEventOptions';
import useLeadGenTooltip from '../../../../utilities/useLeadGenTooltip';
import useMyInfoOption from '../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import useBankOptions from '../../FinanceProductDetailsPage/shared/useBankOptions';
import FlexibleDiscount from '../modules/implementations/shared/FlexibleDiscount';
import MarketTypeGroup from '../modules/implementations/shared/MarketTypeGroup';
import useBankDisplayInSharePdfOptions from '../modules/implementations/shared/useBankDisplayInSharePdfOptions';
import useDisplayPreferenceOptions from '../modules/implementations/shared/useDisplayPreferenceOptions';
import useInsurerOptions from '../modules/implementations/shared/useInsurerOptions';
import useModuleOptions from '../modules/implementations/shared/useModuleOptions';
import usePriceDisclaimerToolTipByMarket from '../usePriceDisclaimerToolTipByMarket';
import { FormValues } from './type';
import useScenarioObserver from './useScenarioObserver';
import useScenarioOptions from './useScenarioOptions';

type StandardApplicationModuleProps = {
    module: StandardApplicationModuleSpecsFragment;
};

const StandardApplicationModuleMainDetailsForm = ({ module }: StandardApplicationModuleProps) => {
    const { t } = useTranslation(['moduleDetails']);
    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();
    const { insurerList, loading: insurerLoading } = useInsurerOptions(module.company.id);
    const { options: bankOptions } = useBankOptions();

    const { values, setFieldValue } = useFormikContext<FormValues>();

    const { eventLeadOriginTypeOptions, eventMediumTypeOptions } = useEventOptions();
    const definedFieldsTooltip = useLeadGenTooltip('definedFields');

    const priceDisclaimerToolTipText = usePriceDisclaimerToolTipByMarket(values.market);
    const priceDisclaimerToolTip = priceDisclaimerToolTipText
        ? {
              itemProps: { tooltip: priceDisclaimerToolTipText },
          }
        : null;

    useEffect(() => {
        if (!values.priceDisclaimer) {
            setFieldValue('priceDisclaimer', {
                defaultValue: { defaultValue: '', overrides: [] },
                overrides: [],
            });
        }

        if (!values.depositAmount) {
            setFieldValue('depositAmount', {
                defaultValue: 0,
                overrides: [],
            });
        }

        if (!values.hasDealerOptions) {
            setFieldValue('includeDealerOptionsForFinancing', true);
        }
    }, [setFieldValue, values]);

    const { yesNoSwitch } = useSystemSwitchData();

    const options = useModuleOptions(module.company, { addNoneOptions: { promoCodes: true, payment: true } });
    const displayPreferenceOptions = useDisplayPreferenceOptions();
    const bankDisplayInSharePdfOptions = useBankDisplayInSharePdfOptions();

    const { scenarioOptions } = useScenarioOptions();
    const scenario = useScenarioObserver(ModuleType.StandardApplicationModule);

    return (
        <CollapsibleWrapper defaultActiveKey="moduleMainDetails">
            <Panel key="moduleMainDetails" header={t('moduleDetails:sections.mainDetails')}>
                <Row gutter={10}>
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.applicationScenario', { returnObjects: true })}
                            mode="multiple"
                            name="scenarios"
                            options={scenarioOptions}
                            showSearch
                        />
                    </Col>
                    {scenario.hasPayment && (
                        <>
                            <Col md={8} xs={24}>
                                <DealershipCascaderField
                                    {...t('moduleDetails:fields.paymentSetting', { returnObjects: true })}
                                    name="paymentSetting"
                                    options={options?.payment || []}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <DealerDepositAmountField
                                    {...t('moduleDetails:fields.depositAmount', { returnObjects: true })}
                                    name="depositAmount"
                                />
                            </Col>
                        </>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.isOcrEnabled', { returnObjects: true })}
                            name="isOcrEnabled"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <DealershipPriceDisclaimerField
                            {...t('moduleDetails:fields.priceDisclaimer', { returnObjects: true })}
                            {...priceDisclaimerToolTip}
                            name="priceDisclaimer"
                        />
                    </Col>

                    {hasMyInfoModule && (
                        <Col md={8} xs={24}>
                            <CascaderField
                                {...t('moduleDetails:fields.myInfoSetting', { returnObjects: true })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {options.cap.length ? (
                        <Col md={8} xs={24}>
                            <SelectField
                                allowClear
                                {...t('moduleDetails:fields.capModuleId', { returnObjects: true })}
                                name="capModuleId"
                                onChange={value => {
                                    if (!value || !values.capPrequalification) {
                                        setFieldValue('isSearchCapCustomerOptional', false);
                                        setFieldValue('capPrequalification', false);
                                    }
                                }}
                                options={options.cap}
                                showSearch
                            />
                        </Col>
                    ) : null}

                    {values.capModuleId && (
                        <>
                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.isSearchCapCustomerOptional', { returnObjects: true })}
                                    name="isSearchCapCustomerOptional"
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.prequalification', { returnObjects: true })}
                                    name="capPrequalification"
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.capLeadOrigin', { returnObjects: true })}
                                    name="leadMedium"
                                    options={eventMediumTypeOptions}
                                    tooltip={definedFieldsTooltip}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.capLeadSource', { returnObjects: true })}
                                    name="leadOrigin"
                                    options={eventLeadOriginTypeOptions}
                                    tooltip={definedFieldsTooltip}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <InputField
                                    {...t('moduleDetails:fields.capCampaignId', { returnObjects: true })}
                                    name="leadCampaignId"
                                    required={values.leadMedium !== EventMediumType.Walkin}
                                />
                            </Col>
                        </>
                    )}

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurer', { returnObjects: true })}
                                loading={insurerLoading}
                                mode="multiple"
                                name="insurerIds"
                                options={insurerList}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bank', { returnObjects: true })}
                                mode="multiple"
                                name="bankIds"
                                options={bankOptions}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                                name="appointmentModuleId"
                                options={options.appointments}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && values.appointmentModuleId && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.displayAppointmentDatepicker', { returnObjects: true })}
                                name="displayAppointmentDatepicker"
                            />
                        </Col>
                    )}

                    {!module.bankModuleId && values.showFinanceCalculator && (
                        <Col md={8} xs={24}>
                            <SelectField
                                required
                                {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                                name="bankModuleId"
                                options={options.financing}
                                showSearch
                            />
                        </Col>
                    )}
                    {(values.showInsuranceCalculator || scenario.hasInsurance || scenario.hasLeadCapture) && (
                        <Col md={8} xs={24}>
                            <SelectField
                                required
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.tradeIn', { returnObjects: true })}
                            name="tradeIn"
                        />
                    </Col>
                    {values?.tradeIn && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isTradeInAmountVisible', { returnObjects: true })}
                                name="isTradeInAmountVisible"
                            />
                        </Col>
                    )}

                    <MarketTypeGroup name="market" required />

                    {scenario.hasFinancing && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isFinancingOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyFinancing}
                                name="isFinancingOptional"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.hasDealerOptions', { returnObjects: true })}
                            name="hasDealerOptions"
                        />
                    </Col>

                    {values.hasDealerOptions && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.includeDealerOptionsForFinancing', { returnObjects: true })}
                                name="includeDealerOptionsForFinancing"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFinanceCalculator', { returnObjects: true })}
                            disabled={scenario.hasFinancing}
                            name="showFinanceCalculator"
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isInsuranceOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyInsurance}
                                name="isInsuranceOptional"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showInsuranceCalculator', { returnObjects: true })}
                            disabled={scenario.hasInsurance}
                            name="showInsuranceCalculator"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFromValueOnVehicleDetails', { returnObjects: true })}
                            name="showFromValueOnVehicleDetails"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayPreference', { returnObjects: true })}
                            name="bankDisplayPreference"
                            options={displayPreferenceOptions}
                            showSearch
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayInSharePdf', { returnObjects: true })}
                            name="bankDisplayInSharePdf"
                            options={bankDisplayInSharePdfOptions}
                            showSearch
                        />
                    </Col>

                    {values.insurerIds.length >= 1 && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurerDisplayPreference', { returnObjects: true })}
                                name="insurerDisplayPreference"
                                options={displayPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showRemoteFlowButtonInKYCPage', { returnObjects: true })}
                            name="showRemoteFlowButtonInKYCPage"
                        />
                    </Col>

                    {scenario.hasPayment && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.skipForDeposit', { returnObjects: true })}
                                name="skipForDeposit"
                            />
                        </Col>
                    )}

                    <FlexibleDiscount name="flexibleDiscount" />
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default StandardApplicationModuleMainDetailsForm;
