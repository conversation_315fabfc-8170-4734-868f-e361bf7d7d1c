import { useFormikContext } from 'formik';
import { useEffect, useMemo } from 'react';
import {
    FinancingPreferenceValue,
    FinderApplicationPublicModule,
    ModuleType,
    PreferenceValue,
} from '../../../../api/types';
import {
    hasAppointmentScenario,
    hasFinancingScenario,
    hasInsuranceScenario,
    hasPaymentScenario,
} from '../modules/implementations/shared';
import { FormValues as StandardApplicationModuleFormValues } from './type';

type FormValues = Pick<
    StandardApplicationModuleFormValues,
    | 'scenarios'
    | 'displayAppointmentDatepicker'
    | 'appointmentModuleId'
    | 'insurerIds'
    | 'isInsuranceOptional'
    | 'showInsuranceCalculator'
    | 'isFinancingOptional'
    | 'bankModuleId'
    | 'showFinanceCalculator'
> &
    Pick<FinderApplicationPublicModule, 'displayFinanceCalculator' | 'financingPreference'>;

const useScenarioObserver = (type: ModuleType) => {
    const { values, setFieldValue, initialValues } = useFormikContext<FormValues>();

    useEffect(() => {
        if (!hasAppointmentScenario(values.scenarios)) {
            setFieldValue('displayAppointmentDatepicker', false);

            if (!initialValues.appointmentModuleId) {
                setFieldValue('appointmentModuleId', undefined);
            }
        }

        if (!hasInsuranceScenario(values.scenarios)) {
            setFieldValue('insurerIds', []);
            setFieldValue('isInsuranceOptional', false);
            setFieldValue('showInsuranceCalculator', false);
        }

        if (!hasFinancingScenario(values.scenarios)) {
            if (type === ModuleType.StandardApplicationModule) {
                setFieldValue('isFinancingOptional', false);
            }

            if (
                type === ModuleType.ConfiguratorModule ||
                type === ModuleType.FinderApplicationPrivateModule ||
                type === ModuleType.FinderApplicationPublicModule
            ) {
                setFieldValue('financingPreference', FinancingPreferenceValue.Mandatory);
            }

            if (
                (((type === ModuleType.ConfiguratorModule || type === ModuleType.StandardApplicationModule) &&
                    !values.showFinanceCalculator) ||
                    ((type === ModuleType.FinderApplicationPrivateModule ||
                        type === ModuleType.FinderApplicationPublicModule) &&
                        values.displayFinanceCalculator === PreferenceValue.No)) &&
                !initialValues.bankModuleId
            ) {
                setFieldValue('bankModuleId', undefined);
            }
        }

        if (hasInsuranceScenario(values.scenarios)) {
            setFieldValue('showInsuranceCalculator', true);

            if (values.scenarios?.length === 1) {
                setFieldValue('isInsuranceOptional', false);
            }
        }

        if (hasFinancingScenario(values.scenarios)) {
            // only apply to standard and configurator
            if (type === ModuleType.ConfiguratorModule || type === ModuleType.StandardApplicationModule) {
                setFieldValue('showFinanceCalculator', true);
            }

            if (
                type === ModuleType.FinderApplicationPrivateModule ||
                type === ModuleType.FinderApplicationPublicModule
            ) {
                setFieldValue('displayFinanceCalculator', PreferenceValue.Yes);
            }

            if (values.scenarios?.length === 1) {
                if (type === ModuleType.StandardApplicationModule) {
                    setFieldValue('isFinancingOptional', false);
                }

                if (
                    type === ModuleType.ConfiguratorModule ||
                    type === ModuleType.FinderApplicationPrivateModule ||
                    type === ModuleType.FinderApplicationPublicModule
                ) {
                    setFieldValue('financingPreference', FinancingPreferenceValue.Mandatory);
                }
            }
        }
    }, [
        initialValues.appointmentModuleId,
        initialValues.bankModuleId,
        setFieldValue,
        values.scenarios,
        type,
        values.showFinanceCalculator,
        values.displayFinanceCalculator,
    ]);

    return useMemo(
        () => ({
            hasInsurance: hasInsuranceScenario(values.scenarios),
            hasOnlyInsurance: hasInsuranceScenario(values.scenarios) && values.scenarios?.length === 1,
            hasFinancing: hasFinancingScenario(values.scenarios),
            hasOnlyFinancing: hasFinancingScenario(values.scenarios) && values.scenarios?.length === 1,
            hasPayment: hasPaymentScenario(values.scenarios),
            hasAppointment: hasAppointmentScenario(values.scenarios),
            hasLeadCapture: true, // always true
        }),
        [values.scenarios]
    );
};

export default useScenarioObserver;
