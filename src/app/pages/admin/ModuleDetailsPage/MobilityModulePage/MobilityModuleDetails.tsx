import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { pick, get, isNil, omit } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MobilityModuleSpecsFragment } from '../../../../api/fragments/MobilityModuleSpecs';
import {
    UpdateMobilityModuleDocument,
    UpdateMobilityModuleMutation,
    UpdateMobilityModuleMutationVariables,
} from '../../../../api/mutations/updateMobilityModule';
import { MobilityModuleUpdateSettings } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { defaultLocation, hasPaymentScenario } from '../modules/implementations/shared';
import CounterSettingDetails from '../shared/CounterSettingDetails';
import MobilityModuleMainDetailsForm from './MobilityModuleDetailsForm';

export type FormValues = MobilityModuleUpdateSettings & Partial<Pick<MobilityModuleSpecsFragment, 'bookingsCounter'>>;

export type MobilityModuleDetailsProps = {
    module: MobilityModuleSpecsFragment;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredNumber('minimumAdvancedBooking'),
    validators.requiredNumber('durationBeforeNextBooking'),
    validators.requiredNumber('amendmentCutOff'),
    validators.only(values => values.externalModelInfo === true, validators.requiredString('baseUrl')),
    validators.requiredArray('scenarios', true),
    validators.only(values => hasPaymentScenario(values.scenarios), validators.requiredString('paymentSettingsId')),

    validators.requiredBoolean('signing.isEnabled'),
    validators.only(values => values.signing.isEnabled === true, validators.requiredString('signing.moduleId')),

    validators.forEach(
        'locations',
        validators.compose(
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { address, url, phone, email } = value;

                return !!address || !!url || !!phone || !!email;
            }, validators.requiredString('name')),
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { name, url, phone, email } = value;

                return !!name || !!url || !!phone || !!email;
            }, validators.requiredString('address')),
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { name, address, phone, email } = value;

                return !!name || !!address || !!phone || !!email;
            }, validators.requiredString('url')),
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { name, address, url, email } = value;

                return !!name || !!address || !!url || !!email;
            }, validators.requiredString('phone')),
            validators.only(
                (values, error, { prefix }) => {
                    const value = get(prefix, values);
                    const { name, address, url, phone } = value;

                    return !!name || !!address || !!url || !!phone;
                },
                validators.compose(validators.requiredString('email'), validators.validEmail('email'))
            )
        )
    ),
    validators.unavailableTimeRange('unavailableTimeRange'),
    validators.requiredNumber('availableNumberOfBookingRange.value'),
    validators.requiredBoolean('showResetKYCButton')
);

const MobilityModuleDetails = ({ module }: MobilityModuleDetailsProps) => {
    const { t } = useTranslation(['moduleDetails', 'mobilityModuleDetails']);

    const validate = useValidator(validator);

    const initialValues = useMemo(
        (): FormValues => ({
            ...pick(
                [
                    'displayName',
                    'minimumAdvancedBooking',
                    'durationBeforeNextBooking',
                    'amendmentCutOff',
                    'rentalDisclaimer',
                    'externalModelInfo',
                    'baseUrl',
                    'scenarios',
                    'unavailableTimeRange',
                    'rentalRequirement',
                    'unavailableDayOfWeek',
                    'availableNumberOfBookingRange',
                    'persistKYCData',
                    'bookingCode',
                    'showResetKYCButton',
                    'promoCodeModuleId',
                    'liveChatSettingId',
                    'paymentSettingsId',
                    'sendEmailToCustomer',
                    'sendPDFToCustomer',
                    'homeDelivery',
                    'giftVoucherModuleId',
                    'bookingsCounter',
                ],
                module
            ),
            locations:
                module.locations.length === 0
                    ? [defaultLocation]
                    : module.locations.map(location => ({ ...omit('assignee', location) })),
            signing: {
                isEnabled: module.signing.isEnabled,
                moduleId: module.signing.moduleId,
            },
        }),
        [module]
    );

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError<MobilityModuleUpdateSettings>(
        async values => {
            // submitting message
            message.loading({
                content: t('mobilityModuleDetails:messages.updateMobilityDetailsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            const settings: MobilityModuleUpdateSettings = {
                ...pick(
                    [
                        'displayName',
                        'minimumAdvancedBooking',
                        'durationBeforeNextBooking',
                        'amendmentCutOff',
                        'rentalDisclaimer',
                        'externalModelInfo',
                        'baseUrl',
                        'scenarios',
                        'unavailableTimeRange',
                        'unavailableDayOfWeek',
                        'availableNumberOfBookingRange',
                        'persistKYCData',
                        'rentalRequirement',
                        'bookingCode',
                        'signing',
                        'showResetKYCButton',
                        'promoCodeModuleId',
                        'sendEmailToCustomer',
                        'sendPDFToCustomer',
                        'homeDelivery',
                        'giftVoucherModuleId',
                    ],
                    values
                ),
                paymentSettingsId: null,
                locations: values.locations.filter(location => !!location.name),
                liveChatSettingId: values.liveChatSettingId,
            };

            if (hasPaymentScenario(values.scenarios)) {
                // assign value if scenario has payment
                settings.paymentSettingsId = values.paymentSettingsId;
            }

            const updatedHomeDelivery = {
                ...omit(['id', 'assignee'], settings.homeDelivery),
                isEnable: settings.homeDelivery.isEnable,
            };
            // submit creation
            await apolloClient
                .mutate<UpdateMobilityModuleMutation, UpdateMobilityModuleMutationVariables>({
                    mutation: UpdateMobilityModuleDocument,
                    variables: {
                        moduleId: module.id,
                        settings: {
                            ...settings,
                            homeDelivery: updatedHomeDelivery,
                            locations: values.locations
                                .map(location => ({
                                    address: location.address,
                                    assigneeId: location.assigneeId,
                                    email: location.email,
                                    name: location.name,
                                    id: location.id,
                                    phone: location.phone,
                                    url: location.url,
                                }))
                                .filter(location => !!location.name),
                            unavailableTimeRange: values.unavailableTimeRange.filter(
                                range => !(isNil(range.end) && isNil(range.start))
                            ),
                        },
                    },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('mobilityModuleDetails:messages.updateMobilityDetailsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateMainDetails" name="updateMainDetails" onSubmitCapture={handleSubmit}>
                    <MobilityModuleMainDetailsForm module={module} />
                    <CounterSettingDetails names={['bookingsCounter']} />
                </Form>
            )}
        </Formik>
    );
};

export default MobilityModuleDetails;
