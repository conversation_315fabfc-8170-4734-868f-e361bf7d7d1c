import { Button, Col, Space, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LocalCustomerManagementModuleSpecsFragment } from '../../../../api/fragments';
import { LocalCustomerFieldKey } from '../../../../api/types';
import SortableTransferField from '../../../../components/fields/SortableTransferField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';

import { useKycFieldDefinitionsFromCountry } from '../../UpdateKYCPresetPage/KYCFieldDefinitions';
import useRenderKYCFieldName from '../../UpdateKYCPresetPage/useRenderKYCFieldName';
import KYCFieldsExtraSettings from './KYCFieldsExtraSettings';
import { KYCFieldFormValues } from './shared';

type KycFieldsFormProps = {
    company: LocalCustomerManagementModuleSpecsFragment['company'];
};

type RecordType = {
    key: string;
    title: string;
    description: string;
};

const filterOption = (inputValue: string, option: RecordType) =>
    option.title.toLowerCase().indexOf(inputValue.toLowerCase()) > -1;

const KYCFieldsForm = ({ company }: KycFieldsFormProps) => {
    const { t } = useTranslation(['localCustomerModuleDetails']);
    const { values } = useFormikContext<KYCFieldFormValues>();

    const renderKYCFieldName = useRenderKYCFieldName();

    const definitions = useKycFieldDefinitionsFromCountry(company.countryCode);

    const [dataSource, setDataSource] = useState(
        definitions.map(field => ({
            key: field.key,
            title: renderKYCFieldName(field.key),
            description: field.key,
        })) || []
    );
    const sortSelectedKeys = useCallback(
        (input: string[]) => definitions.filter(field => input.includes(field.key)).map(field => field.key),
        [definitions]
    );

    const hasBirthdayField = useMemo(
        () => values.kycFields.includes(LocalCustomerFieldKey.Birthday),
        [values.kycFields]
    );

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <CollapsibleWrapper defaultActiveKey="localCustomerModuleDetails_kycFieldsForm">
                <Panel
                    key="localCustomerModuleDetails_kycFieldsForm"
                    extra={
                        <Button
                            form="updateKYCFields"
                            htmlType="submit"
                            onClick={e => {
                                e.stopPropagation();
                            }}
                            type="primary"
                        >
                            {t('localCustomerModuleDetails:kycFieldsForm.mainActionButtons.update')}
                        </Button>
                    }
                    header={
                        <Typography.Title level={5}>
                            {t('localCustomerModuleDetails:kycFieldsForm.title')}
                        </Typography.Title>
                    }
                >
                    <Col span={24}>
                        <SortableTransferField
                            dataSource={dataSource}
                            filterOption={filterOption}
                            listStyle={{ width: '100%', height: 425 }}
                            name="kycFields"
                            preprocessTargetKeys={sortSelectedKeys}
                            render={item => item.title}
                            setDataSource={setDataSource}
                            titles={[
                                t('localCustomerModuleDetails:kycFieldsForm.subTitles.kycFieldPool'),
                                t('localCustomerModuleDetails:kycFieldsForm.subTitles.selectedKycField'),
                            ]}
                            showSearch
                        />
                    </Col>
                    <KYCFieldsExtraSettings
                        hasBirthdayField={hasBirthdayField}
                        pageType="Admin"
                        showAdditionalSettings
                    />
                </Panel>
            </CollapsibleWrapper>
        </Space>
    );
};

export default KYCFieldsForm;
