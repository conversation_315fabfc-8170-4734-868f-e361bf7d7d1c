import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { useApolloClient } from '@apollo/client';
import { Button, Col, Divider, Row, Space, Table, Typography, message } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { get, isEmpty, pick } from 'lodash/fp';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { WebsiteModuleSpecsFragment } from '../../../../api/fragments/WebsiteModuleSpecs';
import {
    CreateWebsiteSocialMediaDocument,
    CreateWebsiteSocialMediaMutation,
    CreateWebsiteSocialMediaMutationVariables,
} from '../../../../api/mutations/createWebsiteSocialMedia';
import { DeleteWebsiteSocialMediaDocument } from '../../../../api/mutations/deleteWebsiteSocialMedia';
import {
    DeleteWebsiteSocialMediaAssetMutation,
    DeleteWebsiteSocialMediaAssetMutationVariables,
} from '../../../../api/mutations/deleteWebsiteSocialMediaAsset';
import { useUpdateWebsiteModuleMutation } from '../../../../api/mutations/updateWebsiteModule';
import {
    UpdateWebsiteSocialMediaDocument,
    UpdateWebsiteSocialMediaMutation,
    UpdateWebsiteSocialMediaMutationVariables,
} from '../../../../api/mutations/updateWebsiteSocialMedia';
import ArrayField from '../../../../components/fields/ArrayField';
import CascaderField from '../../../../components/fields/CascaderField';
import Form from '../../../../components/fields/Form';
import FormItem from '../../../../components/fields/FormItem';
import MarkdownField from '../../../../components/fields/MarkdownField';
import SocialMediaUrlFieldArray from '../../../../components/fields/SocialMediaUrlFieldArray';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import FormFields from '../../../../themes/admin/Fields/FormFields';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { StyledExtraConfigurationTableHeader } from '../../../shared/AdminSharedUI';
import { defaultSocialMediaValue } from '../../CompanyDetailsPage/CompanyForm/shared';
import useModuleOptions from '../modules/implementations/shared/useModuleOptions';
import useHandleWebsiteSocialMediaAsset from './useHandleWebsiteSocialMediaAsset';
import { OnImportHandler } from './useImportLocationsModal';

type WebsiteModuleFormProps = {
    module: WebsiteModuleSpecsFragment;
    setOnImport: (handler: OnImportHandler) => unknown;
};

export type WebsiteModuleFormValues = Pick<
    WebsiteModuleSpecsFragment,
    'displayName' | 'phone' | 'email' | 'options' | 'address' | 'socialMedia'
>;

const validations = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.validPhone('phone.value', 'phone.prefix'),
    validators.requiredValue('email'),
    validators.validEmail('email'),
    validators.forEach(
        'options.locations',
        validators.compose(
            validators.requiredString('country'),
            validators.requiredString('region'),
            validators.requiredString('url')
        )
    ),
    validators.forEach(
        'socialMedia',
        validators.only(
            (values, error, { prefix }) => {
                const value = get(prefix, values);
                const { url, icon } = value;

                return !!url || !isEmpty(icon);
            },
            validators.compose(validators.requiredUploadFile('icon'), validators.requiredString('url'))
        )
    )
);

const COL_SPAN = { md: 8, xs: 24 };
const WebsiteFormFields = ({ setOnImport, module }: WebsiteModuleFormProps) => {
    const { t } = useTranslation(['websiteModuleDetails', 'moduleDetails']);
    const { Table: StyledTable } = useThemeComponents();
    const options = useModuleOptions(module.company);

    const {
        values: {
            options: { locations },
            socialMedia,
        },
        setFieldValue,
    } = useFormikContext<WebsiteModuleFormValues>();

    const addSocialMedia = useCallback(() => {
        setFieldValue('socialMedia', [...socialMedia, defaultSocialMediaValue]);
    }, [socialMedia, setFieldValue]);

    useEffect(() => {
        setOnImport(() => {
            const handler: OnImportHandler = values => setFieldValue('options', values);

            return handler;
        });
    }, [setOnImport, setFieldValue]);

    return (
        <CollapsibleWrapper
            defaultActiveKey={['websiteModuleDetails', 'websiteModuleDetails_footer', 'websiteModuleDetails_Region']}
        >
            <Panel key="websiteModuleDetails" header={t('websiteModuleDetails:form.sections.main')}>
                <Row gutter={10}>
                    <Col {...COL_SPAN}>
                        <FormFields.InputField
                            label={t('websiteModuleDetails:form.fields.displayName')}
                            name="displayName"
                            required
                        />
                    </Col>
                    <Col {...COL_SPAN}>
                        <FormFields.PhoneAndPrefixField
                            label={t('websiteModuleDetails:form.fields.phone')}
                            name="phone"
                            required
                        />
                    </Col>
                    <Col {...COL_SPAN}>
                        <FormFields.InputField
                            label={t('websiteModuleDetails:form.fields.email')}
                            name="email"
                            required
                        />
                    </Col>

                    <Col {...COL_SPAN}>
                        <CascaderField
                            {...t('moduleDetails:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>
                </Row>
            </Panel>
            <Panel key="websiteModuleDetails_footer" header={t('websiteModuleDetails:form.sections.footer')}>
                <Row gutter={10}>
                    <Col span={24}>
                        <MarkdownField
                            label={t('websiteModuleDetails:form.fields.address')}
                            name="address"
                            required={false}
                        />
                    </Col>

                    <Col span={24}>
                        <Divider />
                        <StyledExtraConfigurationTableHeader>
                            <Typography.Title level={5} style={{ fontSize: '14px' }}>
                                {t('websiteModuleDetails:form.fields.socialMedia')}
                            </Typography.Title>
                        </StyledExtraConfigurationTableHeader>
                        <SocialMediaUrlFieldArray addSocialMedia={addSocialMedia} name="socialMedia" />
                    </Col>
                </Row>
            </Panel>
            <Panel
                key="websiteModuleDetails_Region"
                className="added-bottom-padding"
                header={t('websiteModuleDetails:dividers.locations')}
            >
                <ArrayField
                    name="options.locations"
                    render={({ push, remove }) => (
                        <Space direction="vertical" style={{ width: '100%' }}>
                            <StyledTable
                                dataSource={locations}
                                pagination={false}
                                rowKey={(record, index) => `${index.toString()}`}
                            >
                                <Table.Column
                                    dataIndex="region"
                                    render={(_, __, rowIndex) => (
                                        <FormFields.InputField name={`options.locations[${rowIndex}].region`} />
                                    )}
                                    title={<FormItem label={t('websiteModuleDetails:form.fields.region')} required />}
                                />
                                <Table.Column
                                    dataIndex="country"
                                    render={(_, __, rowIndex) => (
                                        <FormFields.InputField name={`options.locations[${rowIndex}].country`} />
                                    )}
                                    title={<FormItem label={t('websiteModuleDetails:form.fields.country')} required />}
                                />
                                <Table.Column
                                    dataIndex="url"
                                    render={(_, __, rowIndex) => (
                                        <FormFields.InputField name={`options.locations[${rowIndex}].url`} />
                                    )}
                                    title={<FormItem label={t('websiteModuleDetails:form.fields.url')} required />}
                                />
                                <Table.Column
                                    render={(_, __, rowIndex) => (
                                        <Button
                                            icon={<DeleteOutlined />}
                                            onClick={() => remove(rowIndex)}
                                            size="small"
                                            style={{ marginBottom: 16 }}
                                            type="link"
                                            danger
                                        />
                                    )}
                                    width={60}
                                />
                            </StyledTable>
                            <Button
                                icon={<PlusOutlined />}
                                onClick={() => push({ country: '', region: '', url: '' })}
                                type="primary"
                            >
                                {t('websiteModuleDetails:labels.addLocationOption')}
                            </Button>
                        </Space>
                    )}
                />
            </Panel>
        </CollapsibleWrapper>
    );
};

const WebsiteModuleForm = ({ module, setOnImport }: WebsiteModuleFormProps) => {
    const initialValues = useMemo<WebsiteModuleFormValues>(
        () => pick(['displayName', 'phone', 'email', 'options', 'liveChatSettingId', 'address', 'socialMedia'], module),
        [module]
    );

    const validate = useValidator(validations);

    const { t } = useTranslation('websiteModuleDetails');

    const handleWebsiteSocialMediaAsset = useHandleWebsiteSocialMediaAsset();
    const apolloClient = useApolloClient();

    const [update] = useUpdateWebsiteModuleMutation();
    const onSubmit = useHandleError<WebsiteModuleFormValues>(
        async values => {
            message.loading({
                content: t('websiteModuleDetails:messages.updating'),
                key: 'primary',
                duration: 0,
            });

            const { socialMedia, ...settings } = values;

            await update({
                variables: {
                    moduleId: module.id,
                    settings,
                },
            });

            const filteredSocialMedia = socialMedia.filter(media => !!media.url);
            const addedSocialMedia = socialMedia.filter(media => !media?.id);

            // loop existing social media
            const existingPromises = (initialValues.socialMedia || []).map(async initialSocialMedia => {
                const currentMedia = filteredSocialMedia.find(media => media.id === initialSocialMedia.id);

                // it this does not exist anymore
                // we delete it
                if (!currentMedia) {
                    // delete social media asset
                    return handleWebsiteSocialMediaAsset(initialSocialMedia.id, initialSocialMedia.icon, module).then(
                        () => {
                            // delete social media field after
                            apolloClient.mutate<
                                DeleteWebsiteSocialMediaAssetMutation,
                                DeleteWebsiteSocialMediaAssetMutationVariables
                            >({
                                mutation: DeleteWebsiteSocialMediaDocument,
                                variables: { socialMediaId: initialSocialMedia.id },
                            });
                        }
                    );
                }

                // if it exists, we update
                return apolloClient
                    .mutate<UpdateWebsiteSocialMediaMutation, UpdateWebsiteSocialMediaMutationVariables>({
                        mutation: UpdateWebsiteSocialMediaDocument,
                        variables: {
                            socialMediaId: currentMedia.id,
                            socialMediaInput: {
                                url: currentMedia.url,
                                altText: currentMedia.altText,
                            },
                        },
                    })
                    .then(() => handleWebsiteSocialMediaAsset(currentMedia.id, currentMedia.icon));
            });

            // get all newly added social media
            const createPromises = addedSocialMedia.map(async media => {
                const currentInput = pick(['url', 'altText'], media);

                return apolloClient
                    .mutate<CreateWebsiteSocialMediaMutation, CreateWebsiteSocialMediaMutationVariables>({
                        mutation: CreateWebsiteSocialMediaDocument,
                        variables: { moduleId: module.id, socialMediaInput: currentInput },
                    })
                    .then(createMedia =>
                        handleWebsiteSocialMediaAsset(createMedia.data.createWebsiteSocialMedia.id, media.icon)
                    );
            });

            await Promise.all([...existingPromises, ...createPromises]);

            message.destroy('primary');

            message.success({
                content: t('websiteModuleDetails:messages.updateSuccess'),
                key: 'WEBSITE_MODULE_UPDATE_SUCCESS',
            });
        },
        [apolloClient, handleWebsiteSocialMediaAsset, initialValues.socialMedia, module, t, update]
    );

    return (
        <Formik<WebsiteModuleFormValues> initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form
                    id="websiteModuleMainDetailsForm"
                    name="websiteModuleMainDetailsForm"
                    onSubmitCapture={handleSubmit}
                >
                    <WebsiteFormFields module={module} setOnImport={setOnImport} />
                </Form>
            )}
        </Formik>
    );
};

export default WebsiteModuleForm;
