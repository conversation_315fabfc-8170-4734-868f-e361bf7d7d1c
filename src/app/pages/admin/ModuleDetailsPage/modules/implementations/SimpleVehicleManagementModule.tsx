import { Col, Row } from 'antd';
import { TFunction } from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { SimpleVehicleManagementModuleSpecsFragment } from '../../../../../api/fragments/SimpleVehicleManagementModuleSpecs';
import {
    CreateSimpleVehicleManagementModuleMutation,
    CreateSimpleVehicleManagementModuleMutationVariables,
    CreateSimpleVehicleManagementModuleDocument,
} from '../../../../../api/mutations/createSimpleVehicleManagementModule';
import { TemplateType, SimpleVehicleManagementSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import SimpleVehicleManagementModulePage, {
    SimpleVehicleManagementModuleProps,
} from '../../SimpleVehicleManagementModulePage';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type SimpleVehicleManagementModuleImplementation = ModuleImplementation<
    SimpleVehicleManagementModuleSpecsFragment,
    SimpleVehicleManagementSettings
>;

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredStringEnum('defaultTemplate', TemplateType)
);

const computeTemplateList = (name: string, t: TFunction) => [
    {
        value: TemplateType.AfcTemplate,
        label: t('moduleDetails:explanations.templates.AFCTemplate.label'),
    },
];

const Form: SimpleVehicleManagementModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);
    const templateOptions = useMemo(() => computeTemplateList(company.displayName, t), [company.displayName, t]);
    const options = useModuleOptions(company, { addNoneOptions: { porscheMasterData: true } });

    return (
        <Row gutter={10}>
            <Col md={8} xs={24}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <SelectField
                    {...t('moduleDetails:fields.importExportTemplate', { returnObjects: true })}
                    name="defaultTemplate"
                    options={templateOptions}
                    required
                    showSearch
                />
            </Col>
            {/* only has created Porsche MasterData module, will show this selection */}
            {options.porscheMasterData.length > 1 && (
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('moduleDetails:fields.porscheMasterDataModule', { returnObjects: true })}
                        name="porscheMasterDataModuleId"
                        options={options.porscheMasterData}
                        showSearch
                    />
                </Col>
            )}
        </Row>
    );
};

const implementation: ModuleImplementation<
    SimpleVehicleManagementModuleSpecsFragment,
    SimpleVehicleManagementSettings
> = {
    PageComponent: ({ module }: SimpleVehicleManagementModuleProps) => (
        <SimpleVehicleManagementModulePage module={module} />
    ),
    getTitle: t => t('moduleDetails:implementations.SimpleVehicleManagementModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) =>
        apolloClient
            .mutate<CreateSimpleVehicleManagementModuleMutation, CreateSimpleVehicleManagementModuleMutationVariables>({
                mutation: CreateSimpleVehicleManagementModuleDocument,
                variables: { companyId, settings: values },
            })
            .then(({ data }) => data.module),
    initialValues: {
        defaultTemplate: TemplateType.AfcTemplate,
        porscheMasterDataModuleId: null,
        sendWeeklyReminderForImage: true,
        emailRecipient: [],
    },
};

export default implementation;
