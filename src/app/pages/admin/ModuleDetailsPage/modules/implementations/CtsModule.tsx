import { Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { CtsModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/CtsModuleWithPermissionsSpecs';
import {
    CreateCtsModuleDocument,
    CreateCtsModuleMutation,
    CreateCtsModuleMutationVariables,
} from '../../../../../api/mutations/createCtsModule';
import { CtsModuleInitialSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import CtsModulePage from '../../CtsModulePage';
import { ModuleImplementation } from './types';

type FormValues = CtsModuleInitialSettings;
type CtsModuleImplementation = ModuleImplementation<CtsModuleWithPermissionsSpecsFragment, FormValues>;
type CtsModulePageProps = {
    module: CtsModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(validators.requiredNonEmptyString('displayName'));

const Form: CtsModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    return (
        <Row gutter={10}>
            <Col span={24}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
        </Row>
    );
};

const implementation: CtsModuleImplementation = {
    PageComponent: ({ module }: CtsModulePageProps) => <CtsModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.CtsModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        return apolloClient
            .mutate<CreateCtsModuleMutation, CreateCtsModuleMutationVariables>({
                mutation: CreateCtsModuleDocument,
                variables: { companyId, settings },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        displayName: '',
    },
};

export default implementation;
