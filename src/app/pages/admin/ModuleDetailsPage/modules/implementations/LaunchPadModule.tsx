import { <PERSON>, Divide<PERSON>, <PERSON>, Space } from 'antd';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { LaunchPadModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/LaunchPadModuleWithPermissionsSpecs';
import {
    CreateLaunchPadModuleDocument,
    CreateLaunchPadModuleMutation,
    CreateLaunchPadModuleMutationVariables,
} from '../../../../../api/mutations/createLaunchpadModule';
import { LaunchPadModuleInitialSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import useEventOptions from '../../../../../utilities/useEventOptions';
import useLeadGenTooltip from '../../../../../utilities/useLeadGenTooltip';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import LaunchPadModulePage from '../../LaunchPadModulePage';
import useGetFinderPrivateEndpointOptions from '../../shared/useGetFinderPrivateEndpointOptions';
import { CounterHelp, CounterSettingGroup, createCounterSettingsValidator } from './shared';
import useCompanyUsers from './shared/useCompanyUsers';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type FormValues = Omit<LaunchPadModuleInitialSettings, 'capCampaignIds'> & { capCampaignIds: string };
type LaunchPadModuleImplementation = ModuleImplementation<LaunchPadModuleWithPermissionsSpecsFragment, FormValues>;
type LaunchPadModulePageProps = {
    module: LaunchPadModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredString('agreementsModuleId'),
    validators.requiredString('customerModuleId'),
    validators.requiredString('vehicleModuleId'),
    createCounterSettingsValidator('leadCounter'),
    createCounterSettingsValidator('appointmentCounter')
);

const Form: LaunchPadModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails', 'launchPadModuleDetails']);

    const options = useModuleOptions(company, {
        addNoneOptions: { appointments: true, cap: true, visitAppointments: true },
    });

    const finderPrivateEndpointOptions = useGetFinderPrivateEndpointOptions(company.id);

    const { eventLeadOriginTypeOptions, eventMediumTypeOptions } = useEventOptions();
    const definedFieldsTooltip = useLeadGenTooltip('definedFields');
    const userOptions = useCompanyUsers(company);

    return (
        <>
            <Row gutter={10}>
                <Col span={24}>
                    <InputField
                        {...t('launchPadModuleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
            </Row>
            <Divider>Dependencies</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <Row gutter={10}>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.agreementsModuleId', { returnObjects: true })}
                            name="agreementsModuleId"
                            options={options.agreements}
                            required
                            showSearch
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.customerModuleId', { returnObjects: true })}
                            name="customerModuleId"
                            options={options.customers}
                            required
                            showSearch
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.vehicleModuleId', { returnObjects: true })}
                            name="vehicleModuleId"
                            options={options.vehicles}
                            required
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.appointmentModuleId', {
                                returnObjects: true,
                            })}
                            name="appointmentModuleId"
                            options={options.appointments}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.visitAppointmentModuleId', {
                                returnObjects: true,
                            })}
                            name="visitAppointmentModuleId"
                            options={options.visitAppointments}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            allowClear
                            {...t('launchPadModuleDetails:fields.capModuleId', { returnObjects: true })}
                            name="capModuleId"
                            options={options.cap}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.capLeadOrigin', { returnObjects: true })}
                            name="capLeadMedium"
                            options={eventMediumTypeOptions}
                            tooltip={definedFieldsTooltip}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('launchPadModuleDetails:fields.capLeadSource', { returnObjects: true })}
                            name="capLeadOrigin"
                            options={eventLeadOriginTypeOptions}
                            tooltip={definedFieldsTooltip}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <InputField
                            {...t('launchPadModuleDetails:fields.capCampaignIds', { returnObjects: true })}
                            name="capCampaignIds"
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            allowClear
                            {...t('launchPadModuleDetails:fields.financeAndInsuranceCalculator', {
                                returnObjects: true,
                            })}
                            name="financeAndInsuranceCalculator"
                            options={options.showroomSales}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            allowClear
                            {...t('launchPadModuleDetails:fields.finderAssignedStock', { returnObjects: true })}
                            name="finderAssignedStock"
                            options={finderPrivateEndpointOptions}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            allowClear
                            {...t('launchPadModuleDetails:fields.salesManager', { returnObjects: true })}
                            name="salesManager"
                            options={userOptions}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            allowClear
                            {...t('launchPadModuleDetails:fields.salesOfferModule', { returnObjects: true })}
                            name="salesOfferModuleId"
                            options={options.salesOffers}
                            showSearch
                        />
                    </Col>
                </Row>
            </Space>
            <Divider>ID Generators</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <CounterHelp />
                <Row gutter={10}>
                    <CounterSettingGroup name="leadCounter" />
                    <CounterSettingGroup name="appointmentCounter" />
                </Row>
            </Space>
        </>
    );
};

const implementation: LaunchPadModuleImplementation = {
    PageComponent: ({ module }: LaunchPadModulePageProps) => <LaunchPadModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.LaunchPadModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        return apolloClient
            .mutate<CreateLaunchPadModuleMutation, CreateLaunchPadModuleMutationVariables>({
                mutation: CreateLaunchPadModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...settings,
                        capCampaignIds: settings.capCampaignIds.split(',').map(campaignId => campaignId.trim()),
                    },
                },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        displayName: '',
        customerModuleId: null,
        agreementsModuleId: null,
        vehicleModuleId: null,
        appointmentModuleId: null,
        visitAppointmentModuleId: null,
        capModuleId: null,
        capLeadOrigin: null,
        capLeadMedium: null,
        capCampaignIds: '',
        financeAndInsuranceCalculator: null,
        finderAssignedStock: null,
        salesManager: null,
        dealerVehicles: [],
        salesOfferModuleId: null,
    },
};

export default implementation;
