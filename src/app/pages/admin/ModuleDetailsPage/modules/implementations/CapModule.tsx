import { Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { CapModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/CapModuleWithPermissionsSpecs';
import {
    CreateCapModuleDocument,
    CreateCapModuleMutation,
    CreateCapModuleMutationVariables,
} from '../../../../../api/mutations/createCapModule';
import { CapModuleSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import CapModulePage from '../../CapModulePage';
import { ModuleImplementation } from './types';

type FormValues = CapModuleSettings;
type CapModuleImplementation = ModuleImplementation<CapModuleWithPermissionsSpecsFragment, FormValues>;
type CapModulePageProps = {
    module: CapModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(validators.requiredNonEmptyString('displayName'));

const Form: CapModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    return (
        <Row gutter={10}>
            <Col span={24}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
        </Row>
    );
};

const implementation: CapModuleImplementation = {
    PageComponent: ({ module }: CapModulePageProps) => <CapModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.CapModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        return apolloClient
            .mutate<CreateCapModuleMutation, CreateCapModuleMutationVariables>({
                mutation: CreateCapModuleDocument,
                variables: { companyId, settings },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        displayName: '',
    },
};

export default implementation;
