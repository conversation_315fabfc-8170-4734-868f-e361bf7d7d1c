import { Col, Divide<PERSON>, <PERSON>, Space } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import { VisitAppointmentModuleSpecsFragment } from '../../../../../api/fragments';
import {
    CreateVisitAppointmentModuleDocument,
    CreateVisitAppointmentModuleMutation,
    CreateVisitAppointmentModuleMutationVariables,
} from '../../../../../api/mutations/createVisitAppointmentModule';
import { TimeSlotInput, VisitAppointmentModuleInitialSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import InputNumberField from '../../../../../components/fields/InputNumberField';
import SelectField from '../../../../../components/fields/SelectField';
import TranslatedTextAreaField from '../../../../../components/fields/TranslatedTextAreaField';
import useSystemOptions from '../../../../../utilities/useSystemOptions';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import VisitAppointmentModulePage, { VisitAppointmentModulePageProps } from '../../VisitAppointmentModulePage';
import AvailableTimeSlot from './shared/AvailableTimeSlot';
import { ModuleImplementation } from './types';

type VisitAppointmentModuleImplementation = ModuleImplementation<
    VisitAppointmentModuleSpecsFragment,
    Omit<VisitAppointmentModuleInitialSettings, 'bookingTimeSlot'> & {
        bookingTimeSlot: Array<
            Omit<TimeSlotInput, 'slot'> & {
                slot: dayjs.Dayjs;
            }
        >;
    }
>;

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredNumber('advancedBookingLimit')
);

type FormProps = {
    company: CompanySpecs;
};

// eslint-disable-next-line react/prop-types
export const Form: VisitAppointmentModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails', 'appointmentModuleDetails']);
    const { dayOfWeek } = useSystemOptions();

    const { values, setFieldValue } = useFormikContext<VisitAppointmentModuleInitialSettings>();

    return (
        <>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>

                <Col md={8} xs={24}>
                    <TranslatedTextAreaField
                        {...t('appointmentModuleDetails:fields.bookingInformation', { returnObjects: true })}
                        name="bookingInformation"
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('appointmentModuleDetails:fields.unavailableDayOfWeek', { returnObjects: true })}
                        mode="multiple"
                        name="unavailableDayOfWeek"
                        options={dayOfWeek}
                        showSearch
                    />
                </Col>

                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('appointmentModuleDetails:fields.advancedBookingLimit', { returnObjects: true })}
                        addonAfter={t('appointmentModuleDetails:suffix.days')}
                        name="advancedBookingLimit"
                        required
                    />
                </Col>

                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('appointmentModuleDetails:fields.maxAdvancedBookingLimit', { returnObjects: true })}
                        addonAfter={t('appointmentModuleDetails:suffix.days')}
                        name="maxAdvancedBookingLimit"
                    />
                </Col>
            </Row>

            <Divider>{t('appointmentModuleDetails:sections.bookingTimeSlot')}</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <AvailableTimeSlot name="bookingTimeSlot" timeZone={company?.timeZone} />
            </Space>
        </>
    );
};

export const defaultLocation = { name: '', address: '', url: '', phone: { prefix: null, value: '' }, email: '' };
export const emptyTimeSlot = { start: null, end: null };

const implementation: VisitAppointmentModuleImplementation = {
    PageComponent: ({ module }: VisitAppointmentModulePageProps) => <VisitAppointmentModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.VisitAppointmentModule.className'),
    getValidator: () => validator,
    FormComponent: Form,
    create: async (apolloClient, companyId, settings, companyTimeZone) =>
        apolloClient
            .mutate<CreateVisitAppointmentModuleMutation, CreateVisitAppointmentModuleMutationVariables>({
                mutation: CreateVisitAppointmentModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...settings,
                        bookingTimeSlot: settings.bookingTimeSlot.map(item => ({
                            ...item,
                            slot: dayjs(item.slot)
                                .set('hour', item.slot.hour())
                                .set('minute', item.slot.minute())
                                .set('second', 0)
                                .tz(companyTimeZone, true)
                                .toDate(),
                        })),
                    },
                },
            })
            .then(({ data }) => data.module),

    initialValues: {
        displayName: '',
        advancedBookingLimit: 0,
        maxAdvancedBookingLimit: null,
        bookingInformation: { defaultValue: '', overrides: [] },
        bookingTimeSlot: [],
        unavailableDayOfWeek: [],
    },
};

export default implementation;
