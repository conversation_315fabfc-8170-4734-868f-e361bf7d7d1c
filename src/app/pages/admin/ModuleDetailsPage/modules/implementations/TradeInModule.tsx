import { Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { TradeInModuleSpecsFragment } from '../../../../../api/fragments';
import {
    CreateTradeInModuleDocument,
    CreateTradeInModuleMutation,
    CreateTradeInModuleMutationVariables,
} from '../../../../../api/mutations/createTradeInModule';
import { TradeInModuleSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import PasswordField from '../../../../../components/fields/PasswordField';
import validators from '../../../../../utilities/validators';
import TradeInModulePage from '../../TradeInModulePage';
import type { TradeInModulePageProps } from '../../TradeInModulePage/shared';
import { ModuleImplementation } from './types';

type TradeInModuleImplementation = ModuleImplementation<TradeInModuleSpecsFragment, TradeInModuleSettings>;

const formValidator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredString('setting.baseUrl'),
    validators.requiredString('setting.clientId'),
    validators.requiredString('setting.clientSecret')
);

const colSpan = { md: 12, xs: 24 };

const Form: TradeInModuleImplementation['FormComponent'] = () => {
    const { t } = useTranslation(['moduleDetails']);

    return (
        <Row gutter={10}>
            <Col {...colSpan}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <InputField
                    {...t('moduleDetails:fields.tradeInAppBaseUrl', { returnObjects: true })}
                    name="setting.baseUrl"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <InputField
                    {...t('moduleDetails:fields.tradeInAppClientId', { returnObjects: true })}
                    name="setting.clientId"
                    required
                />
            </Col>

            <Col {...colSpan}>
                <PasswordField
                    {...t('moduleDetails:fields.tradeInAppClientSecret', { returnObjects: true })}
                    name="setting.clientSecret"
                    required
                />
            </Col>
        </Row>
    );
};

const implementation: TradeInModuleImplementation = {
    PageComponent: ({ module }: TradeInModulePageProps) => <TradeInModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.TradeInModule.className'),
    FormComponent: Form,
    getValidator: () => formValidator,
    create: async (apolloClient, companyId, settings) =>
        apolloClient
            .mutate<CreateTradeInModuleMutation, CreateTradeInModuleMutationVariables>({
                mutation: CreateTradeInModuleDocument,
                variables: { companyId, settings },
            })
            .then(({ data }) => data.module),
};

export default implementation;
