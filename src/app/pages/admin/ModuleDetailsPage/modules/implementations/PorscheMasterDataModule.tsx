import { Row, Col, Space } from 'antd';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { PorscheMasterDataModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/PorscheMasterDataModuleWithPermissionsSpecs';
import {
    CreatePorscheMasterDataModuleDocument,
    CreatePorscheMasterDataModuleMutation,
    CreatePorscheMasterDataModuleMutationVariables,
} from '../../../../../api/mutations/createPorscheMasterDataModule';
import { PorscheMasterDataModuleSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import PasswordField from '../../../../../components/fields/PasswordField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import PorscheMasterDataModulePage from '../../PorscheMasterDataModulePage';
import { ModuleImplementation } from './types';

type FormValues = PorscheMasterDataModuleSettings;
type PorscheMasterDataModuleImplementation = ModuleImplementation<
    PorscheMasterDataModuleWithPermissionsSpecsFragment,
    FormValues
>;
type PorscheMasterDataModulePageProps = {
    module: PorscheMasterDataModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(
    validators.requiredString('displayName'),
    validators.requiredString('ggId'),
    validators.requiredString('ppnUrl'),
    validators.requiredString('ppnClientId'),
    validators.requiredString('ppnClientSecret'),
    validators.requiredString('ppnResource'),
    validators.requiredString('pccdUrlPrefix'),
    validators.requiredString('pccdClientId'),
    validators.requiredString('pccdClientSecret'),
    validators.requiredString('pccdToken')
);

const Form: PorscheMasterDataModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Row gutter={10}>
                <Col span={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
            </Row>
            <Row gutter={10}>
                <Col span={24}>
                    <InputField {...t('moduleDetails:fields.ggId', { returnObjects: true })} name="ggId" required />
                </Col>
            </Row>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.ppnUrl', { returnObjects: true })}
                        addonBefore="https://"
                        name="ppnUrl"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.ppnClientId', { returnObjects: true })}
                        name="ppnClientId"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <PasswordField
                        {...t('moduleDetails:fields.ppnClientSecret', { returnObjects: true })}
                        name="ppnClientSecret"
                        forAdmin
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.ppnResource', { returnObjects: true })}
                        name="ppnResource"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.pccdUrlPrefix', { returnObjects: true })}
                        addonBefore="https://"
                        name="pccdUrlPrefix"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.pccdClientId', { returnObjects: true })}
                        name="pccdClientId"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <PasswordField
                        {...t('moduleDetails:fields.pccdClientSecret', { returnObjects: true })}
                        name="pccdClientSecret"
                        forAdmin
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <PasswordField
                        {...t('moduleDetails:fields.pccdToken', { returnObjects: true })}
                        name="pccdToken"
                        forAdmin
                        required
                    />
                </Col>
            </Row>
        </Space>
    );
};

const implementation: PorscheMasterDataModuleImplementation = {
    PageComponent: ({ module }: PorscheMasterDataModulePageProps) => <PorscheMasterDataModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.PorscheMasterDataModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        return apolloClient
            .mutate<CreatePorscheMasterDataModuleMutation, CreatePorscheMasterDataModuleMutationVariables>({
                mutation: CreatePorscheMasterDataModuleDocument,
                variables: { companyId, settings },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        displayName: '',
    },
};

export default implementation;
