import { Col, Row, Space } from 'antd';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { FinderVehicleManagementModuleSpecsFragment } from '../../../../../api/fragments/FinderVehicleManagementModuleSpecs';
import {
    CreateFinderVehicleManagementModuleDocument,
    CreateFinderVehicleManagementModuleMutation,
    CreateFinderVehicleManagementModuleMutationVariables,
} from '../../../../../api/mutations/createFinderVehicleManagementModule';
import { FinderVehicleManagementModuleSetting } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import PasswordField from '../../../../../components/fields/PasswordField';
import SwitchField from '../../../../../components/fields/SwitchField';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import FinderVehicleManagementModulePage, {
    FinderVehicleManagementModulePageProps,
} from '../../FinderVehicleManagementModulePage';
import { ModuleImplementation } from './types';

type FinderVehicleManagementModuleImplementation = ModuleImplementation<
    FinderVehicleManagementModuleSpecsFragment,
    FinderVehicleManagementModuleSetting
>;

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredBoolean('allowLTA'),
    validators.requiredString('secrets.apiKey'),
    validators.requiredString('secrets.secretKey'),
    validators.requiredString('secrets.loginEndpoint'),
    validators.requiredString('secrets.apiEndpoint'),
    validators.requiredString('secrets.finderUrl')
);

const CreateForm: FinderVehicleManagementModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    const { countryCode } = company;

    const { yesNoSwitch } = useSystemSwitchData();

    const isSingapore = countryCode === 'SG';

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>

                {isSingapore && (
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.allowLTA', { returnObjects: true })}
                            name="allowLTA"
                        />
                    </Col>
                )}
            </Row>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField {...t('moduleDetails:fields.apiKey', { returnObjects: true })} name="secrets.apiKey" />
                </Col>
                <Col md={8} xs={24}>
                    <PasswordField
                        {...t('moduleDetails:fields.secretKey', { returnObjects: true })}
                        name="secrets.secretKey"
                        forAdmin
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.loginEndpoint', { returnObjects: true })}
                        name="secrets.loginEndpoint"
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.apiEndpoint', { returnObjects: true })}
                        name="secrets.apiEndpoint"
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.finderUrl', { returnObjects: true })}
                        name="secrets.finderUrl"
                    />
                </Col>
            </Row>
        </Space>
    );
};

const implementation: FinderVehicleManagementModuleImplementation = {
    PageComponent: ({ module }: FinderVehicleManagementModulePageProps) => (
        <FinderVehicleManagementModulePage module={module} />
    ),
    getTitle: t => t('moduleDetails:implementations.FinderVehicleManagementModule.className'),
    FormComponent: CreateForm,
    getValidator: () => validator,
    create: async (apolloClient, companyId, settings) =>
        apolloClient
            .mutate<CreateFinderVehicleManagementModuleMutation, CreateFinderVehicleManagementModuleMutationVariables>({
                mutation: CreateFinderVehicleManagementModuleDocument,
                variables: {
                    companyId,
                    settings,
                },
            })
            .then(({ data }) => data.module),
    initialValues: {
        allowLTA: false,
    },
};

export default implementation;
