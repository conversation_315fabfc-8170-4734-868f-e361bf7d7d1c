import { Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { GiftVoucherModuleSpecsFragment } from '../../../../../api/fragments';
import {
    CreateGiftVoucherModuleDocument,
    CreateGiftVoucherModuleMutation,
    CreateGiftVoucherModuleMutationVariables,
} from '../../../../../api/mutations/createGiftVoucherModule';
import { GiftVoucherModuleInitialSettings } from '../../../../../api/types';
import CascaderField from '../../../../../components/fields/CascaderField';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import GiftVoucherModulePage, { GiftVoucherModulePageProps } from '../../GiftVoucherModulePage';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type GiftVoucherModuleImplementation = ModuleImplementation<
    GiftVoucherModuleSpecsFragment,
    GiftVoucherModuleInitialSettings
>;

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredString('agreementsModuleId'),
    validators.requiredString('customerModuleId')
);

type FormProps = {
    company: CompanySpecs;
};

// eslint-disable-next-line react/prop-types
export const Form: GiftVoucherModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails', 'appointmentModuleDetails']);

    const options = useModuleOptions(company, {
        addNoneOptions: { promoCodes: true, payment: true },
    });

    return (
        <Row gutter={10}>
            <Col md={12} xs={24}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>

            <Col md={12} xs={24}>
                <SelectField
                    {...t('moduleDetails:fields.agreementsModuleId', { returnObjects: true })}
                    name="agreementsModuleId"
                    options={options.agreements}
                    required
                    showSearch
                />
            </Col>
            <Col md={12} xs={24}>
                <SelectField
                    {...t('moduleDetails:fields.customerModuleId', { returnObjects: true })}
                    name="customerModuleId"
                    options={options.customers}
                    required
                    showSearch
                />
            </Col>

            <Col md={12} xs={24}>
                <CascaderField
                    {...t('moduleDetails:fields.paymentModuleId', { returnObjects: true })}
                    name="paymentSettingsId"
                    options={options.payment}
                    required
                    showSearch
                />
            </Col>
        </Row>
    );
};

const implementation: GiftVoucherModuleImplementation = {
    PageComponent: ({ module }: GiftVoucherModulePageProps) => <GiftVoucherModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.GiftVoucherModule.className'),
    getValidator: () => validator,
    FormComponent: Form,
    create: async (apolloClient, companyId, settings) =>
        apolloClient
            .mutate<CreateGiftVoucherModuleMutation, CreateGiftVoucherModuleMutationVariables>({
                mutation: CreateGiftVoucherModuleDocument,
                variables: {
                    companyId,
                    settings,
                },
            })
            .then(({ data }) => data.module),
    initialValues: {
        displayName: '',
    },
};

export default implementation;
