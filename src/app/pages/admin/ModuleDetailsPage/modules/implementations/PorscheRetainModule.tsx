import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { PorscheRetainModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/PorscheRetainModuleWithPermissionsSpecs';
import {
    CreatePorscheRetainModuleDocument,
    CreatePorscheRetainModuleMutation,
    CreatePorscheRetainModuleMutationVariables,
} from '../../../../../api/mutations/createPorscheRetainModule';
import { PorscheRetainModuleSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import SingleSecretUploadField from '../../../../../components/fields/SingleSecretUploadField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import PorscheRetainModulePage from '../../PorscheRetainModulePage';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type FormValues = PorscheRetainModuleSettings;
type PorscheRetainModuleImplementation = ModuleImplementation<
    PorscheRetainModuleWithPermissionsSpecsFragment,
    FormValues
>;
type PorscheRetainModulePageProps = {
    module: PorscheRetainModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredNonEmptyString('link'),
    validators.requiredString('launchPadModuleId'),
    validators.validUrl('notificationUrl', true),
    validators.requiredString('publicCertRetain')
);

const Form: PorscheRetainModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['porscheRetainModuleDetails']);

    const options = useModuleOptions(company);

    return (
        <Row gutter={10}>
            <Col md={8} xs={24}>
                <InputField
                    {...t('porscheRetainModuleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <InputField
                    {...t('porscheRetainModuleDetails:fields.link', { returnObjects: true })}
                    name="link"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <SelectField
                    allowClear
                    {...t('porscheRetainModuleDetails:fields.launchPadModuleId', { returnObjects: true })}
                    name="launchPadModuleId"
                    options={options.launchPad}
                    required
                    showSearch
                />
            </Col>
            <Col md={8} xs={24}>
                <InputField
                    {...t('porscheRetainModuleDetails:fields.notificationUrl', { returnObjects: true })}
                    name="notificationUrl"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <SingleSecretUploadField
                    {...t('porscheRetainModuleDetails:fields.publicCertificate', { returnObjects: true })}
                    name="publicCertRetain"
                    required
                />
            </Col>
        </Row>
    );
};

const implementation: PorscheRetainModuleImplementation = {
    PageComponent: ({ module }: PorscheRetainModulePageProps) => <PorscheRetainModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.PorscheRetainModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        return apolloClient
            .mutate<CreatePorscheRetainModuleMutation, CreatePorscheRetainModuleMutationVariables>({
                mutation: CreatePorscheRetainModuleDocument,
                variables: { companyId, settings },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        displayName: '',
        link: '',
        launchPadModuleId: undefined,
        notificationUrl: '',
        publicCertRetain: '',
    },
};

export default implementation;
