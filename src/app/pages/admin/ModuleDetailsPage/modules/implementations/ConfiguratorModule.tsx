import { <PERSON><PERSON>, Col, Divider, Row, Space } from 'antd';
import { useFormikContext } from 'formik';
import { get } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { ConfiguratorModuleSpecsFragment } from '../../../../../api/fragments/ConfiguratorModuleSpecs';
import {
    CreateConfiguratorModuleDocument,
    CreateConfiguratorModuleMutation,
    CreateConfiguratorModuleMutationVariables,
} from '../../../../../api/mutations/createConfiguratorModule';
import {
    ApplicationMarket,
    ConfiguratorModuleInitialSettings,
    DisplayPreference,
    EmailContentUpdateType,
    FinancingPreferenceValue,
    ModuleType,
} from '../../../../../api/types';
import { useDealerContext } from '../../../../../components/contexts/DealerContextManager';
import Cascader<PERSON>ield from '../../../../../components/fields/CascaderField';
import DealerDepositAmountField from '../../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipCascaderField from '../../../../../components/fields/DealershipFields/DealershipCascaderField';
// eslint-disable-next-line max-len
import DealershipPriceDisclaimerField from '../../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import DealershipSelectField from '../../../../../components/fields/DealershipFields/DealershipSelectField';
// eslint-disable-next-line max-len
import DealershipTranslatedStringField from '../../../../../components/fields/DealershipFields/DealershipTranslatedString';
// eslint-disable-next-line max-len
import DealershipTranslatedTextAreaField from '../../../../../components/fields/DealershipFields/DealershipTranslatedTextArea';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import SwitchField from '../../../../../components/fields/SwitchField';
import { TranslationFieldType } from '../../../../../utilities/common';
import { hasValue } from '../../../../../utilities/fp';
import useMarkDownInfoTooltip from '../../../../../utilities/useMarkDownInfoTooltip';
import useMyInfoOption from '../../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import useBankOptions from '../../../FinanceProductDetailsPage/shared/useBankOptions';
import ConfiguratorModulePage, { ConfiguratorModulePageProps } from '../../ConfiguratorModulePage';
import useScenarioObserver from '../../StandardApplicationModulePage/useScenarioObserver';
import useScenarioOptions from '../../StandardApplicationModulePage/useScenarioOptions';
import { newZealandMarketValidator, singaporeMarketValidator } from '../../shared/validators';
import {
    CounterHelp,
    CounterSettingGroup,
    createCounterSettingsValidator,
    hasAppointmentScenario,
    hasFinancingScenario,
    hasInsuranceScenario,
    hasPaymentScenario,
} from './shared';
import MarketTypeGroup from './shared/MarketTypeGroup';
import { processDealershipPaymentSettingInput } from './shared/getDealershipPaymentSetting';
import useDisplayPreferenceOptions from './shared/useDisplayPreferenceOptions';
import useFinancingPreferenceOptions from './shared/useFinancingPreferenceOptions';
import useInsurerOptions from './shared/useInsurerOptions';
import useModuleOptions from './shared/useModuleOptions';
import useSalesPersonOptions from './shared/useSalesPersonOptions';
import { ModuleImplementation } from './types';

type ConfiguratorModuleImplementation = ModuleImplementation<
    ConfiguratorModuleSpecsFragment,
    ConfiguratorModuleInitialSettings
>;

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredString('agreementsModuleId'),
    validators.requiredString('customerModuleId'),
    validators.requiredString('vehicleModuleId'),
    validators.only(values => hasInsuranceScenario(values?.scenarios), validators.requiredArray('insurerIds')),
    validators.only(values => hasFinancingScenario(values?.scenarios), validators.requiredArray('bankIds')),
    validators.only(
        values => hasAppointmentScenario(values?.scenarios),
        validators.requiredString('appointmentModuleId')
    ),
    validators.only(values => hasValue('bankModuleId')(values), validators.requiredString('market')),
    validators.only(values => {
        const showInsuranceCalculator = get('showInsuranceCalculator', values);

        return showInsuranceCalculator === true;
    }, validators.requiredString('insuranceModuleId')),
    validators.only(values => {
        const showFinanceCalculator = get('showFinanceCalculator', values);

        return showFinanceCalculator;
    }, validators.requiredString('bankModuleId')),
    validators.only(values => {
        const marketType = get('market', values);

        return marketType === ApplicationMarket.Singapore;
    }, singaporeMarketValidator),
    validators.only(values => {
        const marketType = get('market', values);

        return marketType === ApplicationMarket.NewZealand;
    }, newZealandMarketValidator),
    validators.requiredArray('scenarios', true),
    validators.only(
        values => hasPaymentScenario(get('scenarios', values)),
        validators.compose(
            validators.requiredString('paymentSetting.defaultId'),
            validators.requiredNumber('depositAmount.defaultValue')
        )
    ),
    createCounterSettingsValidator('applicationCounter'),
    createCounterSettingsValidator('reservationCounter'),
    createCounterSettingsValidator('leadCounter'),
    createCounterSettingsValidator('appointmentCounter'),
    createCounterSettingsValidator('appointmentCounter'),
    createCounterSettingsValidator('insuranceCounter'),
    validators.requiredString('assignee.defaultId'),
    validators.requiredBoolean('showResetKYCButton'),
    validators.requiredString('externalUrl')
);

type FormProps = {
    company: CompanySpecs;
};

const Form: ConfiguratorModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);
    const markdownInfoTooltip = useMarkDownInfoTooltip();

    const { values } = useFormikContext<ConfiguratorModuleInitialSettings>();

    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();

    const { insurerList, loading: insurerLoading } = useInsurerOptions(company.id);

    const { options: bankOptions } = useBankOptions();

    const { salesPersonOptions, loading } = useSalesPersonOptions(company);

    const { scenarioOptions } = useScenarioOptions();
    const scenario = useScenarioObserver(ModuleType.ConfiguratorModule);

    const options = useModuleOptions(company, {
        addNoneOptions: { promoCodes: true, payment: true, appointments: false },
    });
    const displayPreferenceOptions = useDisplayPreferenceOptions();
    const financingPreferenceOptions = useFinancingPreferenceOptions();

    const { yesNoSwitch } = useSystemSwitchData();

    const { dealersFromApi } = useDealerContext();

    return (
        <>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('moduleDetails:fields.applicationScenario', { returnObjects: true })}
                        mode="multiple"
                        name="scenarios"
                        options={scenarioOptions}
                        showSearch
                    />
                </Col>
                <Col md={8} xs={24}>
                    <DealershipSelectField
                        {...t('moduleDetails:fields.assignee', { returnObjects: true })}
                        loading={loading}
                        name="assignee"
                        options={salesPersonOptions}
                        required
                        showSearch
                    />
                </Col>

                <Col md={8} xs={24}>
                    <SwitchField
                        {...yesNoSwitch}
                        {...t('moduleDetails:fields.tradeIn', { returnObjects: true })}
                        name="tradeIn"
                    />
                </Col>
                {values?.tradeIn && (
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.isTradeInAmountVisible', { returnObjects: true })}
                            name="isTradeInAmountVisible"
                        />
                    </Col>
                )}
                <MarketTypeGroup name="market" required />
                <Col md={8} xs={24}>
                    <DealershipPriceDisclaimerField
                        {...t('moduleDetails:fields.priceDisclaimerTitle', { returnObjects: true })}
                        name="priceDisclaimer"
                        isMultipleEntry
                    />
                </Col>

                <Col md={8} xs={24}>
                    <DealershipTranslatedStringField
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            'moduleDetails:fields.termsTitle',
                            { returnObjects: true }
                        )}
                        dealers={dealersFromApi}
                        name="termsTitle"
                    />
                </Col>

                <Col md={8} xs={24}>
                    <DealershipTranslatedTextAreaField
                        {...t('moduleDetails:fields.termsText', { returnObjects: true })}
                        autoSize={{ minRows: 2, maxRows: 6 }}
                        companyId={company.id}
                        name="termsText"
                        tooltip={markdownInfoTooltip}
                    />
                </Col>

                <Col md={8} xs={24}>
                    <SwitchField
                        {...t('moduleDetails:fields.inventoryEnabled', { returnObjects: true })}
                        {...yesNoSwitch}
                        name="isInventoryEnabled"
                    />
                </Col>

                <Col lg={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.externalUrl', { returnObjects: true })}
                        addonBefore="https://"
                        name="externalUrl"
                        required
                    />
                </Col>
            </Row>
            <Divider>Dependencies</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                    description={t('moduleDetails:alerts.moduleImmutableDependencies.content')}
                    message={t('moduleDetails:alerts.moduleImmutableDependencies.title')}
                    type="warning"
                    showIcon
                />
                <Row gutter={10}>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.agreementsModuleId', { returnObjects: true })}
                            name="agreementsModuleId"
                            options={options.agreements}
                            required
                            showSearch
                        />
                    </Col>
                    {values.showFinanceCalculator && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                                name="bankModuleId"
                                options={options.financing}
                                required
                                showSearch
                            />
                        </Col>
                    )}
                    {(values.showInsuranceCalculator || scenario.hasInsurance || scenario.hasLeadCapture) && (
                        <Col md={12} xs={24}>
                            <SelectField
                                required
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.customerModuleId', { returnObjects: true })}
                            name="customerModuleId"
                            options={options.customers}
                            required
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.vehicleModuleId', { returnObjects: true })}
                            name="vehicleModuleId"
                            options={options.vehicles}
                            required
                            showSearch
                        />
                    </Col>

                    {scenario.hasAppointment && (
                        <>
                            <Col md={12} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                                    name="appointmentModuleId"
                                    options={options.appointments}
                                    required
                                    showSearch
                                />
                            </Col>
                            {values.appointmentModuleId && values.appointmentModuleId !== null && (
                                <Col md={12} xs={24}>
                                    <SwitchField
                                        {...yesNoSwitch}
                                        {...t('moduleDetails:fields.displayAppointmentDatepicker', {
                                            returnObjects: true,
                                        })}
                                        name="displayAppointmentDatepicker"
                                    />
                                </Col>
                            )}
                        </>
                    )}

                    {scenario.hasPayment && (
                        <>
                            <Col md={12} xs={24}>
                                <DealershipCascaderField
                                    {...t('moduleDetails:fields.paymentModuleId', { returnObjects: true })}
                                    name="paymentSetting"
                                    options={options?.payment || []}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={12} xs={24}>
                                <DealerDepositAmountField
                                    {...t('moduleDetails:fields.depositAmount', { returnObjects: true })}
                                    name="depositAmount"
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.skipForDeposit', { returnObjects: true })}
                                    name="skipForDeposit"
                                />
                            </Col>
                        </>
                    )}
                    {hasMyInfoModule && (
                        <Col md={12} xs={24}>
                            <CascaderField
                                {...t('moduleDetails:fields.myInfoSetting', { returnObjects: true })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <CascaderField
                            {...t('moduleDetails:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurer', { returnObjects: true })}
                                loading={insurerLoading}
                                mode="multiple"
                                name="insurerIds"
                                options={insurerList}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bank', { returnObjects: true })}
                                mode="multiple"
                                name="bankIds"
                                options={bankOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.financingPreference', { returnObjects: true })}
                                disabled={scenario.hasOnlyFinancing}
                                name="financingPreference"
                                options={financingPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFinanceCalculator', { returnObjects: true })}
                            disabled={scenario.hasFinancing}
                            name="showFinanceCalculator"
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={12} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isInsuranceOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyInsurance}
                                name="isInsuranceOptional"
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showInsuranceCalculator', { returnObjects: true })}
                            disabled={scenario.hasInsurance}
                            name="showInsuranceCalculator"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFromValueOnVehicleDetails', { returnObjects: true })}
                            name="showFromValueOnVehicleDetails"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayPreference', { returnObjects: true })}
                            name="bankDisplayPreference"
                            options={displayPreferenceOptions}
                            showSearch
                        />
                    </Col>
                    {values.insurerIds.length >= 1 && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurerDisplayPreference', { returnObjects: true })}
                                name="insurerDisplayPreference"
                                options={displayPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}
                </Row>
            </Space>
            <Divider>ID Generators</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <CounterHelp />
                <Row gutter={10}>
                    <CounterSettingGroup name="applicationCounter" />
                    <CounterSettingGroup name="reservationCounter" />
                    <CounterSettingGroup name="leadCounter" />
                    <CounterSettingGroup name="appointmentCounter" />
                    <CounterSettingGroup name="insuranceCounter" />
                </Row>
            </Space>
        </>
    );
};

const implementation: ConfiguratorModuleImplementation = {
    PageComponent: ({ module }: ConfiguratorModulePageProps) => <ConfiguratorModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.ConfiguratorModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) =>
        apolloClient
            .mutate<CreateConfiguratorModuleMutation, CreateConfiguratorModuleMutationVariables>({
                mutation: CreateConfiguratorModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...values,
                        testDrive: hasAppointmentScenario(values.scenarios),
                        bankModuleId: values.showFinanceCalculator ? values.bankModuleId : null,
                        paymentSetting: hasPaymentScenario(values.scenarios)
                            ? processDealershipPaymentSettingInput(values.paymentSetting)
                            : null,
                    },
                },
            })
            .then(({ data }) => data.module),
    initialValues: {
        isOcrEnabled: false,
        testDrive: false,
        tradeIn: false,
        financingPreference: FinancingPreferenceValue.Mandatory,
        isInsuranceOptional: false,
        showFinanceCalculator: true,
        showInsuranceCalculator: true,
        displayName: '',
        agreementsModuleId: null,
        customerModuleId: null,
        vehicleModuleId: null,
        scenarios: [],
        assignee: {
            defaultId: null,
            overrides: [],
        },
        priceDisclaimer: {
            defaultValue: [{ defaultValue: '', overrides: [] }],
            overrides: [],
        },
        termsText: {
            defaultValue: { defaultValue: '', overrides: [] },
            overrides: [],
        },
        termsTitle: {
            defaultValue: { defaultValue: '', overrides: [] },
            overrides: [],
        },
        isInventoryEnabled: false,
        emailContents: {
            emailContentUpdateType: EmailContentUpdateType.Module,
            saveOrder: {
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                subject: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
            },
            submitOrder: {
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                subject: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                isPaymentSectionShown: true,
            },
        },
        showResetKYCButton: false,
        showFromValueOnVehicleDetails: true,
        displayAppointmentDatepicker: false,
        showRemoteFlowButtonInKYCPage: false,
        skipForDeposit: false,
        bankDisplayPreference: DisplayPreference.ShownWhenMoreThanOne,
        insurerIds: [],
        insurerDisplayPreference: DisplayPreference.ShownWhenMoreThanOne,
        externalUrl: null,
        appointmentModuleId: null,
        depositAmount: {
            defaultValue: 0,
            overrides: [],
        },
    },
};

export default implementation;
