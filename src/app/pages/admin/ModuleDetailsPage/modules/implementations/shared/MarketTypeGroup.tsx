import { useField, useFormikContext } from 'formik';
import { isEmpty, isNil } from 'lodash/fp';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { DealerListDataFragment } from '../../../../../../api/fragments/DealerListData';
import { ApplicationMarket, ApplicationMarketTypeInput, ModuleType } from '../../../../../../api/types';
import FormFields from '../../../../../../themes/admin/Fields/FormFields';
import { FormValues } from '../../../StandardApplicationModulePage/type';
import ColumnOrFragment from './marketInput/ColumnOrFragment';
import NewZealandMarketInput from './marketInput/NewZealandMarketInput';
import SingaporeMarketInput from './marketInput/SingaporeMarketInput';
import useMarkets from './useMarkets';

type MarketTypeGroupProps = {
    name: string;
    required?: boolean;
    asFragment?: boolean;
    dealers?: DealerListDataFragment[];
    moduleType?: ModuleType;
};

const MarketTypeGroup = ({ name, required = false, asFragment = false, dealers, moduleType }: MarketTypeGroupProps) => {
    const { t } = useTranslation('moduleDetails');
    const [{ value: market }] = useField<ApplicationMarket>(name);
    const { setFieldValue } = useFormikContext<ApplicationMarketTypeInput>();
    const markets = useMarkets(moduleType);
    const { values: formValue } = useFormikContext<FormValues>();

    // set initialValues on market changed
    useEffect(() => {
        switch (market) {
            case ApplicationMarket.Singapore:
                if (!formValue.marketType.singapore) {
                    setFieldValue(
                        'marketType',
                        {
                            singapore: {
                                coe: { defaultValue: 0, editable: false, overrides: [] },
                            },
                        },
                        false
                    );
                }
                break;

            case ApplicationMarket.NewZealand:
                if (!formValue.marketType.newZealand) {
                    setFieldValue(
                        'marketType',
                        {
                            newZealand: {
                                ppsr: { defaultValue: 0, editable: false, overrides: [] },
                                estFee: { defaultValue: 0, editable: false, overrides: [] },
                                bankEstFee: { editable: false, overrides: [] },
                                nzFees: { viewable: false, overrides: [] },
                            },
                        },
                        false
                    );
                }
                break;

            default: {
                if (!isEmpty(formValue.marketType) || isNil(formValue.marketType)) {
                    setFieldValue('marketType', {}, false);
                }
                break;
            }
        }
    }, [formValue.marketType, market, setFieldValue]);

    const marketInputs = useMemo(() => {
        switch (market) {
            case ApplicationMarket.Singapore:
                return <SingaporeMarketInput asFragment={asFragment} dealers={dealers} name="marketType.singapore" />;

            case ApplicationMarket.NewZealand:
                return <NewZealandMarketInput asFragment={asFragment} dealers={dealers} name="marketType.newZealand" />;

            default:
                return null;
        }
    }, [asFragment, dealers, market]);

    return (
        <>
            <ColumnOrFragment asFragment={asFragment} md={8} xs={24}>
                <FormFields.SelectField
                    {...t('moduleDetails:fields.applicationMarket', { returnObjects: true })}
                    name={name}
                    options={markets}
                    required={required}
                    showSearch
                />
            </ColumnOrFragment>
            {marketInputs}
        </>
    );
};

export default MarketTypeGroup;
