import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Table, Typography } from 'antd';
import { FormItemProps } from 'antd/es/form';
import { FieldArray, useField } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ApplicationMarket } from '../../../../../../api';
// eslint-disable-next-line max-len
import DealershipTranslatedMarkdownField from '../../../../../../components/fields/DealershipFields/DealershipTranslatedMarkdown';
import FormItem from '../../../../../../components/fields/FormItem';
import SelectField from '../../../../../../components/fields/SelectField';
import { useThemeComponents } from '../../../../../../themes/hooks';
import useMarkDownInfoTooltip from '../../../../../../utilities/useMarkDownInfoTooltip';
import { StyledTableContainer } from '../../../../../shared/AdminSharedUI';
import { defaultDiscalimers } from '../../../FinderApplicationModulePage/shared';
import { Disclaimer } from '../../../FinderApplicationModulePage/type';

const { Title } = Typography;

export type DisclaimerFieldArrayFieldArrayProps = {
    name: string;
    market: ApplicationMarket;
    label?: string;
    labelForAdd?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    limit?: number;
    disclaimerTypeOptions: { label: string; value: Disclaimer['type'] }[];
};

export const StyledIcon = styled.div`
    display: flex;
    justify-content: space-evenly;
    height: 100%;
    align-items: center;
`;

const DisclaimerFieldArray = ({
    name,
    market,
    label,
    labelForAdd,
    itemProps,
    limit,
    disclaimerTypeOptions,
    ...props
}: DisclaimerFieldArrayFieldArrayProps) => {
    const { t } = useTranslation(['common', 'finderApplicationModuleDetails', 'moduleDetails']);
    const { StyledFieldArrayTable } = useThemeComponents();

    const [field, , { setValue }] = useField({ name });
    const { value } = field;

    const addDisclaimer = useCallback(() => {
        const defaultType = disclaimerTypeOptions.find(
            option =>
                !(value as Disclaimer[])?.length ||
                !(value as Disclaimer[])?.some(disclaimer => disclaimer.type === option.value)
        )?.value;

        const newDisclaimer = defaultDiscalimers.find(disclaimer => disclaimer.type === defaultType);
        setValue([...value, newDisclaimer]);
    }, [setValue, value, disclaimerTypeOptions]);

    const markdownInfoTooltip = useMarkDownInfoTooltip(market);
    const insuranceMarkdownInfoTooltip = useMarkDownInfoTooltip();

    if (!disclaimerTypeOptions?.length) {
        return null;
    }

    return (
        <>
            <StyledTableContainer>
                <Title level={5} style={{ fontSize: '14px' }}>
                    {t('finderApplicationModuleDetails:sections.disclaimers')}
                </Title>
            </StyledTableContainer>
            <Divider />
            <FormItem {...itemProps} label={label} name={name}>
                <>
                    <FieldArray
                        {...field}
                        name={name}
                        render={arrayHelpers => (
                            <StyledFieldArrayTable
                                dataSource={value}
                                pagination={false}
                                rowKey={(row, index: number) => `${name}_${index.toString()}`}
                            >
                                <Table.Column<Disclaimer>
                                    dataIndex="type"
                                    render={(val, record, index) => (
                                        <SelectField
                                            {...t('finderApplicationModuleDetails:fields.disclaimers.type', {
                                                returnObjects: true,
                                            })}
                                            disabled={
                                                disclaimerTypeOptions.length === 1 ||
                                                !((value as Disclaimer[])?.length === 1)
                                            }
                                            name={`${name}.[${index}].type`}
                                            options={disclaimerTypeOptions}
                                            showSearch
                                        />
                                    )}
                                    title={t('finderApplicationModuleDetails:fields.disclaimers.type.label')}
                                    width={400}
                                />

                                <Table.Column<Disclaimer>
                                    dataIndex="text"
                                    render={(val, record, index) => (
                                        <DealershipTranslatedMarkdownField
                                            {...t('finderApplicationModuleDetails:fields.disclaimers.text', {
                                                returnObjects: true,
                                            })}
                                            disclaimerKind={
                                                record?.type === 'insurance'
                                                    ? 'insuranceDisclaimer'
                                                    : 'financingDisclaimer'
                                            }
                                            name={`${name}.[${index}].value`}
                                            tooltip={
                                                record?.type === 'insurance'
                                                    ? insuranceMarkdownInfoTooltip
                                                    : markdownInfoTooltip
                                            }
                                            isMultipleEntry
                                        />
                                    )}
                                    title={t('finderApplicationModuleDetails:fields.disclaimers.text.label')}
                                    width={400}
                                />
                                <Table.Column
                                    key="actions"
                                    align="center"
                                    render={(_, __, index) => (
                                        <StyledIcon>
                                            <DeleteOutlined
                                                onClick={() => arrayHelpers.remove(index)}
                                                style={{ color: 'var(--ant-primary-color)' }}
                                            />
                                        </StyledIcon>
                                    )}
                                    title={t('common:actions.action')}
                                    width={100}
                                />
                            </StyledFieldArrayTable>
                        )}
                    />
                    <div style={{ marginTop: '10px' }}>
                        <Button
                            disabled={!((value as Disclaimer[])?.length < disclaimerTypeOptions.length)}
                            icon={<PlusCircleOutlined />}
                            onClick={addDisclaimer}
                            type="link"
                        >
                            {t('finderApplicationModuleDetails:actions.addDisclaimer')}
                        </Button>
                    </div>
                </>
            </FormItem>
        </>
    );
};

export default DisclaimerFieldArray;
