import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Divider, Table, Typography } from 'antd';
import { FormItemProps } from 'antd/es/form';
import { FieldArray, useField } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import FormItem from '../../../../../../components/fields/FormItem';
import TimePicker from '../../../../../../components/fields/TimePicker';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { StyledTableContainer } from '../../../../../shared/AdminSharedUI';

const emptyTimeSlot = { start: null, end: null };
const { Title } = Typography;

export type UnavailableTimeRangeFieldArrayProps = {
    name: string;
    label?: string;
    labelForAdd?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    limit?: number;
};

export const StyledIcon = styled.div`
    display: flex;
    justify-content: space-evenly;
    height: 100%;
    align-items: center;
`;

const UnavailableTimeRangeFieldArray = ({
    name,
    label,
    labelForAdd,
    itemProps,
    limit,
    ...props
}: UnavailableTimeRangeFieldArrayProps) => {
    const { t } = useTranslation(['common', 'mobilityModuleDetails']);
    const { StyledFieldArrayTable } = useThemeComponents();

    const [field, , { setValue }] = useField({ name });
    const { value } = field;

    const addTimeRange = useCallback(() => {
        setValue([...value, emptyTimeSlot]);
    }, [setValue, value]);

    return (
        <>
            <StyledTableContainer>
                <Title level={5} style={{ fontSize: '14px' }}>
                    {t('mobilityModuleDetails:sections.unavailableTimeRange')}
                </Title>
            </StyledTableContainer>
            <Divider />
            <FormItem {...itemProps} label={label} name={name}>
                <>
                    <FieldArray
                        {...field}
                        name={name}
                        render={arrayHelpers => (
                            <StyledFieldArrayTable
                                dataSource={value}
                                pagination={false}
                                rowKey={(row, index: number) => `${name}_${index.toString()}`}
                            >
                                <Table.Column
                                    dataIndex="start"
                                    render={(value, record, index) => (
                                        <TimePicker format="h:mm a" minuteStep={15} name={`${name}.[${index}].start`} />
                                    )}
                                    title={t('mobilityModuleDetails:fields.unavailableTimeRange.start.label')}
                                    width={400}
                                />
                                <Table.Column
                                    dataIndex="end"
                                    render={(value, record, index) => (
                                        <TimePicker format="h:mm a" minuteStep={15} name={`${name}.[${index}].end`} />
                                    )}
                                    title={t('mobilityModuleDetails:fields.unavailableTimeRange.end.label')}
                                    width={400}
                                />
                                <Table.Column
                                    key="actions"
                                    align="center"
                                    render={(_, __, index) => (
                                        <StyledIcon>
                                            <DeleteOutlined
                                                onClick={() => {
                                                    arrayHelpers.remove(index);
                                                }}
                                                style={{ color: 'var(--ant-primary-color)' }}
                                            />
                                        </StyledIcon>
                                    )}
                                    title={t('common:actions.action')}
                                    width={100}
                                />
                            </StyledFieldArrayTable>
                        )}
                    />
                    <div style={{ marginTop: '10px' }}>
                        <Button icon={<PlusCircleOutlined />} onClick={addTimeRange} type="link">
                            {t('mobilityModuleDetails:actions.addTimeRange')}
                        </Button>
                    </div>
                </>
            </FormItem>
        </>
    );
};

export default UnavailableTimeRangeFieldArray;
