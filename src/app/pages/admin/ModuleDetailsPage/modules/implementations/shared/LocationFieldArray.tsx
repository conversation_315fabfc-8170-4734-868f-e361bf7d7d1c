import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Table, Typography } from 'antd';
import { FormItemProps } from 'antd/es/form';
import { FieldArray, useField } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { MobilityLocationDataFragment } from '../../../../../../api/fragments';
import { useListUsersQuery } from '../../../../../../api/queries/listUsers';
import { useCompanies } from '../../../../../../components/contexts/CompanyContextManager';
import FormItem from '../../../../../../components/fields/FormItem';
import InputField from '../../../../../../components/fields/InputField';
import SelectField from '../../../../../../components/fields/SelectField';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { StyledTableContainer } from '../../../../../shared/AdminSharedUI';
import { useLocationDeleteModal } from './LocationDeletionModal';
import MobilityHomeDelivery from './MobilityHomeDelivery';
import { defaultLocation } from './scenarios';

const { Title } = Typography;

export type LocationFieldArrayProps = {
    name: string;
    label?: string;
    labelForAdd?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    limit?: number;
};

const StyledIcon = styled.div`
    display: flex;
    justify-content: center;
    height: 100%;
    align-items: center;
`;

const LocationFieldArray = ({ name, label, labelForAdd, itemProps, limit, ...props }: LocationFieldArrayProps) => {
    const { t } = useTranslation(['common', 'mobilityModuleDetails']);
    const { Table: StyledTable } = useThemeComponents();

    const [field, , { setValue }] = useField({ name });
    const { value } = field;
    const companies = useCompanies();

    const addLocation = useCallback(() => {
        setValue([...value, defaultLocation]);
    }, [setValue, value]);

    const { data, loading } = useListUsersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyIds: companies.map(company => company.id),
            },
        },
    });
    const [location, setLocation] = useState<{ locationId: string; index: number }>();

    const assigneeOptions = useMemo(
        () =>
            [
                { label: t('common:none'), value: null },
                ...(data?.list?.items || []).map(user => ({
                    label: user.displayName,
                    value: user.id,
                })),
            ].filter(Boolean),
        [data?.list?.items, t]
    );
    const { open, render } = useLocationDeleteModal();

    return (
        <>
            <StyledTableContainer>
                <Title level={5} style={{ fontSize: '14px' }}>
                    {t('mobilityModuleDetails:sections.locations')}
                </Title>
            </StyledTableContainer>
            <Divider />

            <MobilityHomeDelivery companiesIds={companies.map(company => company.id)} name="homeDelivery" />

            <FormItem {...itemProps} label={label} name={name}>
                <>
                    <FieldArray
                        {...field}
                        name={name}
                        render={arrayHelpers => (
                            <>
                                <StyledTable
                                    dataSource={value}
                                    pagination={false}
                                    rowKey={(row, index: number) => `${name}_${index.toString()}`}
                                >
                                    <Table.Column<MobilityLocationDataFragment>
                                        dataIndex="name"
                                        render={(value, record, index) => (
                                            <InputField name={`${name}.[${index}].name`} isTableInput />
                                        )}
                                        title={t('mobilityModuleDetails:fields.locations.name.label')}
                                    />
                                    <Table.Column<MobilityLocationDataFragment>
                                        dataIndex="address"
                                        render={(value, record, index) => (
                                            <InputField name={`${name}.[${index}].address`} isTableInput />
                                        )}
                                        title={t('mobilityModuleDetails:fields.locations.address.label')}
                                    />
                                    <Table.Column<MobilityLocationDataFragment>
                                        dataIndex="url"
                                        render={(value, record, index) => (
                                            <InputField
                                                addonBefore="https://"
                                                name={`${name}.[${index}].url`}
                                                isTableInput
                                            />
                                        )}
                                        title={t('mobilityModuleDetails:fields.locations.url.label')}
                                    />
                                    <Table.Column<MobilityLocationDataFragment>
                                        dataIndex="phone"
                                        render={(value, record, index) => (
                                            <InputField name={`${name}.[${index}].phone`} isTableInput />
                                        )}
                                        title={t('mobilityModuleDetails:fields.locations.phone.label')}
                                    />
                                    <Table.Column<MobilityLocationDataFragment>
                                        dataIndex="email"
                                        render={(value, record, index) => (
                                            <InputField name={`${name}.[${index}].email`} isTableInput />
                                        )}
                                        title={t('mobilityModuleDetails:fields.locations.email.label')}
                                    />

                                    <Table.Column<MobilityLocationDataFragment>
                                        dataIndex="assigneeId"
                                        render={(value, record, index) => (
                                            <SelectField
                                                filterOption={(input, option) =>
                                                    (option?.label.toString() ?? '')
                                                        .toLowerCase()
                                                        .includes(input.toLowerCase())
                                                }
                                                loading={loading}
                                                name={`${name}.[${index}].assigneeId`}
                                                options={assigneeOptions}
                                                showSearch
                                            />
                                        )}
                                        title={t('mobilityModuleDetails:fields.locations.assignee.label')}
                                    />

                                    <Table.Column<MobilityLocationDataFragment>
                                        key="actions"
                                        align="center"
                                        render={(value, record, index) => (
                                            <StyledIcon>
                                                <DeleteOutlined
                                                    onClick={() => {
                                                        setLocation({ locationId: record.id, index });
                                                        open();
                                                    }}
                                                    style={{ color: 'var(--ant-primary-color)' }}
                                                />
                                            </StyledIcon>
                                        )}
                                        title={t('common:actions.action')}
                                        width={100}
                                    />
                                </StyledTable>

                                {location && render(location.locationId, () => arrayHelpers.remove(location.index))}
                            </>
                        )}
                    />
                    <div style={{ marginTop: '10px' }}>
                        <Button icon={<PlusCircleOutlined />} onClick={addLocation} type="link">
                            {t('mobilityModuleDetails:actions.addLocation')}
                        </Button>
                    </div>
                </>
            </FormItem>
        </>
    );
};

export default LocationFieldArray;
