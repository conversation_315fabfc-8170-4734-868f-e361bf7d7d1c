import { ApplicationScenario } from '../../../../../../api/types';

const hasScenario = (scenario: ApplicationScenario) => (scenarios: ApplicationScenario[]) => {
    // return false if there's no array
    if (!scenarios || !scenarios.length) {
        return false;
    }

    // check if scenario exists
    return scenarios.includes(scenario);
};

export const hasFinancingScenario = hasScenario(ApplicationScenario.Financing);

export const hasLeadCaptureScenario = hasScenario(ApplicationScenario.LeadCapture);

export const hasPaymentScenario = hasScenario(ApplicationScenario.Payment);

export const hasInsuranceScenario = hasScenario(ApplicationScenario.Insurance);

export const hasAppointmentScenario = hasScenario(ApplicationScenario.Appointment);

export const hasVisitAppointmentScenario = hasScenario(ApplicationScenario.VisitAppointment);

export const hasNoFinancingScenario = (scenarios: ApplicationScenario[]) => !hasFinancingScenario(scenarios);

export const defaultLocation = {
    name: '',
    address: '',
    url: '',
    phone: '',
    email: '',
    assigneeId: null,
};
