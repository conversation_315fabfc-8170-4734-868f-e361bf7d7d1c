import { <PERSON><PERSON>, Divider, <PERSON>, Col, Space } from 'antd';
import { useFormikContext } from 'formik';
import { get } from 'lodash/fp';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { StandardApplicationModuleSpecsFragment } from '../../../../../api/fragments/StandardApplicationModuleSpecs';
import {
    CreateStandardApplicationModuleMutation,
    CreateStandardApplicationModuleMutationVariables,
    CreateStandardApplicationModuleDocument,
} from '../../../../../api/mutations/createStandardApplicationModule';
import {
    ApplicationMarket,
    StandardApplicationModuleInitialSettings,
    DisplayPreference,
    BankDisplayInSharePdf,
    ModuleType,
} from '../../../../../api/types';
import CascaderField from '../../../../../components/fields/CascaderField';
import DealerDepositAmountField from '../../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipCascaderField from '../../../../../components/fields/DealershipFields/DealershipCascaderField';
// eslint-disable-next-line max-len
import DealershipPriceDisclaimerField from '../../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import InputField from '../../../../../components/fields/InputField';
import SelectField from '../../../../../components/fields/SelectField';
import SwitchField from '../../../../../components/fields/SwitchField';
import { hasValue } from '../../../../../utilities/fp';
import useMyInfoOption from '../../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import useBankOptions from '../../../FinanceProductDetailsPage/shared/useBankOptions';
import StandardApplicationModulePage, { StandardApplicationModulePageProps } from '../../StandardApplicationModulePage';
import useScenarioObserver from '../../StandardApplicationModulePage/useScenarioObserver';
import useScenarioOptions from '../../StandardApplicationModulePage/useScenarioOptions';
import { newZealandMarketValidator, singaporeMarketValidator } from '../../shared/validators';
import {
    CounterHelp,
    CounterSettingGroup,
    createCounterSettingsValidator,
    hasAppointmentScenario,
    hasFinancingScenario,
    hasInsuranceScenario,
    hasPaymentScenario,
} from './shared';
import MarketTypeGroup from './shared/MarketTypeGroup';
import { processDealershipPaymentSettingInput } from './shared/getDealershipPaymentSetting';
import useBankDisplayInSharePdfOptions from './shared/useBankDisplayInSharePdfOptions';
import useDisplayPreferenceOptions from './shared/useDisplayPreferenceOptions';
import useInsurerOptions from './shared/useInsurerOptions';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type StandardApplicationModuleImplementation = ModuleImplementation<
    StandardApplicationModuleSpecsFragment,
    StandardApplicationModuleInitialSettings
>;

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredString('agreementsModuleId'),
    validators.requiredString('customerModuleId'),
    validators.requiredString('vehicleModuleId'),
    validators.only(values => hasInsuranceScenario(values?.scenarios), validators.requiredArray('insurerIds')),
    validators.only(values => hasFinancingScenario(values?.scenarios), validators.requiredArray('bankIds')),
    validators.only(
        values => hasAppointmentScenario(values?.scenarios),
        validators.requiredString('appointmentModuleId')
    ),
    validators.only(
        values => hasPaymentScenario(get('scenarios', values)),
        validators.compose(
            validators.requiredString('paymentSetting.defaultId'),
            validators.requiredNumber('depositAmount.defaultValue')
        )
    ),
    createCounterSettingsValidator('applicationCounter'),
    createCounterSettingsValidator('reservationCounter'),
    createCounterSettingsValidator('leadCounter'),
    createCounterSettingsValidator('appointmentCounter'),
    createCounterSettingsValidator('insuranceCounter'),
    validators.only(values => hasValue('bankModuleId')(values), validators.requiredString('market')),
    validators.only(values => {
        const showInsuranceCalculator = get('showInsuranceCalculator', values);

        return showInsuranceCalculator === true;
    }, validators.requiredString('insuranceModuleId')),
    validators.only(values => {
        const showFinanceCalculator = get('showFinanceCalculator', values);

        return showFinanceCalculator;
    }, validators.requiredString('bankModuleId')),
    validators.requiredString('market'),
    validators.requiredArray('scenarios', true),
    validators.only(values => {
        const market = get('market', values);

        return market === ApplicationMarket.Singapore;
    }, singaporeMarketValidator),
    validators.only(values => {
        const market = get('market', values);

        return market === ApplicationMarket.NewZealand;
    }, newZealandMarketValidator),
    validators.requiredBoolean('isOcrEnabled'),
    validators.requiredBoolean('tradeIn'),
    validators.requiredBoolean('showResetKYCButton')
);

type FormProps = {
    company: CompanySpecs;
};

// eslint-disable-next-line react/prop-types
export const Form: StandardApplicationModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    const { values, setFieldValue } = useFormikContext<StandardApplicationModuleInitialSettings>();

    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();
    const { insurerList, loading: insurerLoading } = useInsurerOptions(company?.id);
    const { options: bankOptions } = useBankOptions();

    useEffect(() => {
        if (!values.priceDisclaimer) {
            values.priceDisclaimer = {
                defaultValue: { defaultValue: '', overrides: [] },
                overrides: [],
            };
        }

        if (!values.depositAmount) {
            values.depositAmount = {
                defaultValue: 0,
                overrides: [],
            };
        }

        if (!values.hasDealerOptions) {
            setFieldValue('includeDealerOptionsForFinancing', true);
        }
    }, [values, setFieldValue]);

    const { scenarioOptions } = useScenarioOptions();
    const scenario = useScenarioObserver(ModuleType.StandardApplicationModule);

    const options = useModuleOptions(company, { addNoneOptions: { promoCodes: true } });
    const displayPreferenceOptions = useDisplayPreferenceOptions();
    const bankDisplayInSharePdfOptions = useBankDisplayInSharePdfOptions();

    const { yesNoSwitch } = useSystemSwitchData();

    return (
        <>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('moduleDetails:fields.applicationScenario', { returnObjects: true })}
                        mode="multiple"
                        name="scenarios"
                        options={scenarioOptions}
                        showSearch
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SwitchField
                        {...yesNoSwitch}
                        {...t('moduleDetails:fields.isOcrEnabled', { returnObjects: true })}
                        name="isOcrEnabled"
                    />
                </Col>

                <Col md={8} xs={24}>
                    <DealershipPriceDisclaimerField
                        {...t('moduleDetails:fields.priceDisclaimer', { returnObjects: true })}
                        name="priceDisclaimer"
                    />
                </Col>
                {scenario.hasPayment && (
                    <Col md={8} xs={24}>
                        <DealerDepositAmountField
                            {...t('moduleDetails:fields.depositAmount', { returnObjects: true })}
                            name="depositAmount"
                        />
                    </Col>
                )}
                <Col md={8} xs={24}>
                    <SwitchField
                        {...yesNoSwitch}
                        {...t('moduleDetails:fields.tradeIn', { returnObjects: true })}
                        name="tradeIn"
                    />
                </Col>
                {values?.tradeIn && (
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.isTradeInAmountVisible', { returnObjects: true })}
                            name="isTradeInAmountVisible"
                        />
                    </Col>
                )}

                <MarketTypeGroup name="market" required />
            </Row>
            <Divider>Dependencies</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                    description={t('moduleDetails:alerts.moduleImmutableDependencies.content')}
                    message={t('moduleDetails:alerts.moduleImmutableDependencies.title')}
                    type="warning"
                    showIcon
                />
                <Row gutter={10}>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.agreementsModuleId', { returnObjects: true })}
                            name="agreementsModuleId"
                            options={options.agreements}
                            required
                            showSearch
                        />
                    </Col>
                    {values.showFinanceCalculator && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                                name="bankModuleId"
                                options={options.financing}
                                required
                                showSearch
                            />
                        </Col>
                    )}
                    {(values.showInsuranceCalculator || scenario.hasInsurance || scenario.hasLeadCapture) && (
                        <Col md={12} xs={24}>
                            <SelectField
                                required
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.customerModuleId', { returnObjects: true })}
                            name="customerModuleId"
                            options={options.customers}
                            required
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.vehicleModuleId', { returnObjects: true })}
                            name="vehicleModuleId"
                            options={options.vehicles}
                            required
                            showSearch
                        />
                    </Col>
                    {scenario.hasPayment && (
                        <Col md={12} xs={24}>
                            <DealershipCascaderField
                                {...t('moduleDetails:fields.paymentModuleId', { returnObjects: true })}
                                name="paymentSetting"
                                options={options?.payment || []}
                                required
                                showSearch
                            />
                        </Col>
                    )}
                    {hasMyInfoModule && (
                        <Col md={12} xs={24}>
                            <CascaderField
                                {...t('moduleDetails:fields.myInfoSetting', { returnObjects: true })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurer', { returnObjects: true })}
                                loading={insurerLoading}
                                mode="multiple"
                                name="insurerIds"
                                options={insurerList}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bank', { returnObjects: true })}
                                mode="multiple"
                                name="bankIds"
                                options={bankOptions}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && (
                        <>
                            <Col md={12} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                                    name="appointmentModuleId"
                                    options={options.appointments}
                                    required
                                    showSearch
                                />
                            </Col>
                            {values.appointmentModuleId && values.appointmentModuleId !== null && (
                                <Col md={12} xs={24}>
                                    <SwitchField
                                        {...yesNoSwitch}
                                        {...t('moduleDetails:fields.displayAppointmentDatepicker', {
                                            returnObjects: true,
                                        })}
                                        name="displayAppointmentDatepicker"
                                    />
                                </Col>
                            )}
                        </>
                    )}
                    {scenario.hasFinancing && (
                        <Col md={12} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isFinancingOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyFinancing}
                                name="isFinancingOptional"
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.hasDealerOptions', { returnObjects: true })}
                            name="hasDealerOptions"
                        />
                    </Col>

                    {values.hasDealerOptions && (
                        <Col md={12} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.includeDealerOptionsForFinancing', { returnObjects: true })}
                                name="includeDealerOptionsForFinancing"
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFinanceCalculator', { returnObjects: true })}
                            disabled={scenario.hasFinancing}
                            name="showFinanceCalculator"
                        />
                    </Col>
                    {scenario.hasInsurance && (
                        <Col md={12} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isInsuranceOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyInsurance}
                                name="isInsuranceOptional"
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showInsuranceCalculator', { returnObjects: true })}
                            disabled={scenario.hasInsurance}
                            name="showInsuranceCalculator"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFromValueOnVehicleDetails', { returnObjects: true })}
                            name="showFromValueOnVehicleDetails"
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayPreference', { returnObjects: true })}
                            name="bankDisplayPreference"
                            options={displayPreferenceOptions}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayInSharePdf', { returnObjects: true })}
                            name="bankDisplayInSharePdf"
                            options={bankDisplayInSharePdfOptions}
                            showSearch
                        />
                    </Col>
                    {values.insurerIds.length >= 1 && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurerDisplayPreference', { returnObjects: true })}
                                name="insurerDisplayPreference"
                                options={displayPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showRemoteFlowButtonInKYCPage', { returnObjects: true })}
                            name="showRemoteFlowButtonInKYCPage"
                        />
                    </Col>
                </Row>
            </Space>
            <Divider>ID Generators</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <CounterHelp />
                <Row gutter={10}>
                    <CounterSettingGroup name="applicationCounter" />
                    <CounterSettingGroup name="reservationCounter" />
                    <CounterSettingGroup name="leadCounter" />
                    <CounterSettingGroup name="appointmentCounter" />
                    <CounterSettingGroup name="insuranceCounter" />
                </Row>
            </Space>
        </>
    );
};

const implementation: StandardApplicationModuleImplementation = {
    PageComponent: ({ module }: StandardApplicationModulePageProps) => (
        <StandardApplicationModulePage module={module} />
    ),
    getTitle: t => t('moduleDetails:implementations.StandardApplicationModule.className'),
    getValidator: () => validator,
    FormComponent: Form,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        settings.testDrive = hasAppointmentScenario(settings.scenarios);

        if (settings.marketType.singapore && !settings.marketType.singapore.coe.overrides) {
            settings.marketType.singapore.coe.overrides = [];
        }

        if (settings.marketType.newZealand) {
            if (!settings.marketType.newZealand.ppsr.overrides) {
                settings.marketType.newZealand.ppsr.overrides = [];
            }
            if (!settings.marketType.newZealand.estFee.overrides) {
                settings.marketType.newZealand.estFee.overrides = [];
            }
            if (!settings.marketType.newZealand.bankEstFee.overrides) {
                settings.marketType.newZealand.bankEstFee.overrides = [];
            }
            if (!settings.marketType.newZealand.nzFees.overrides) {
                settings.marketType.newZealand.nzFees.overrides = [];
            }
        }

        return apolloClient
            .mutate<CreateStandardApplicationModuleMutation, CreateStandardApplicationModuleMutationVariables>({
                mutation: CreateStandardApplicationModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...values,
                        bankModuleId: values.showFinanceCalculator ? values.bankModuleId : null,
                        paymentSetting: hasPaymentScenario(values.scenarios)
                            ? processDealershipPaymentSettingInput(values.paymentSetting)
                            : null,
                    },
                },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        isOcrEnabled: false,
        testDrive: false,
        tradeIn: false,
        isFinancingOptional: false,
        isInsuranceOptional: false,
        showFinanceCalculator: true,
        showInsuranceCalculator: true,
        displayName: '',
        agreementsModuleId: null,
        bankModuleId: null,
        insuranceModuleId: null,
        customerModuleId: null,
        vehicleModuleId: null,
        scenarios: [],
        market: null,
        priceDisclaimer: {
            defaultValue: { defaultValue: '', overrides: [] },
            overrides: [],
        },
        showResetKYCButton: false,
        showFromValueOnVehicleDetails: true,
        showRemoteFlowButtonInKYCPage: true,
        displayAppointmentDatepicker: false,
        skipForDeposit: false,
        bankDisplayPreference: DisplayPreference.ShownWhenMoreThanOne,
        bankDisplayInSharePdf: BankDisplayInSharePdf.FollowCalculator,
        insurerIds: [],
        insurerDisplayPreference: DisplayPreference.ShownWhenMoreThanOne,
        hasDealerOptions: false,
        includeDealerOptionsForFinancing: true,
        flexibleDiscount: {
            isEnabled: false,
        },
        promoCodeModuleId: null,
        appointmentModuleId: null,
        bankIds: [],
        depositAmount: {
            defaultValue: 0,
            overrides: [],
        },
    },
};

export default implementation;
