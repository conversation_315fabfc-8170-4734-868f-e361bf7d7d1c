import { Divide<PERSON>, Row, Col, Space } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModuleSpecsFragment } from '../../../../../api/fragments';
import {
    CreateAppointmentModuleMutation,
    CreateAppointmentModuleMutationVariables,
    CreateAppointmentModuleDocument,
} from '../../../../../api/mutations/createAppointmentModule';
import { AppointmentModuleInitialSettings, AppointmentTimeSlotInput, ModuleRole } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import InputNumberField from '../../../../../components/fields/InputNumberField';
import ModuleSelectField from '../../../../../components/fields/ModuleSelectField';
import SelectField from '../../../../../components/fields/SelectField';
import SwitchField from '../../../../../components/fields/SwitchField';
import TranslatedTextAreaField from '../../../../../components/fields/TranslatedTextAreaField';
import useSystemOptions from '../../../../../utilities/useSystemOptions';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import useTooltip from '../../../../../utilities/useTooltip';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import AppointmentModulePage, { AppointmentModulePageProps } from '../../AppointmentModulePage';
import AvailableTimeSlot from './shared/AvailableTimeSlot';
import { ModuleImplementation } from './types';

type AppointmentModuleImplementation = ModuleImplementation<
    AppointmentModuleSpecsFragment,
    Omit<AppointmentModuleInitialSettings, 'bookingTimeSlot'> & {
        bookingTimeSlot: Array<
            Omit<AppointmentTimeSlotInput, 'slot'> & {
                slot: dayjs.Dayjs;
            }
        >;
    }
>;

const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredNumber('advancedBookingLimit')
);

type FormProps = {
    company: CompanySpecs;
};

// eslint-disable-next-line react/prop-types
export const Form: AppointmentModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails', 'appointmentModuleDetails']);
    const { dayOfWeek } = useSystemOptions();
    const { yesNoSwitch } = useSystemSwitchData();

    const { values, setFieldValue } = useFormikContext<AppointmentModuleInitialSettings>();
    const testDriveProcessTooltip = useTooltip('testDriveProcess');

    useEffect(() => {
        if (!values.hasTestDriveProcess) {
            setFieldValue('hasTestDriveSigning', false);
            setFieldValue('showRemoteFlowButtonInKYCPage', false);
        }
    }, [setFieldValue, values.hasTestDriveProcess]);

    useEffect(() => {
        if (!values.hasTestDriveSigning) {
            setFieldValue('signingModuleId', null);
        }
    }, [setFieldValue, values.hasTestDriveSigning]);

    return (
        <>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>

                <Col md={8} xs={24}>
                    <TranslatedTextAreaField
                        {...t('appointmentModuleDetails:fields.bookingInformation', { returnObjects: true })}
                        name="bookingInformation"
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('appointmentModuleDetails:fields.unavailableDayOfWeek', { returnObjects: true })}
                        mode="multiple"
                        name="unavailableDayOfWeek"
                        options={dayOfWeek}
                        showSearch
                    />
                </Col>

                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('appointmentModuleDetails:fields.advancedBookingLimit', { returnObjects: true })}
                        addonAfter={t('appointmentModuleDetails:suffix.days')}
                        name="advancedBookingLimit"
                        required
                    />
                </Col>

                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('appointmentModuleDetails:fields.maxAdvancedBookingLimit', { returnObjects: true })}
                        addonAfter={t('appointmentModuleDetails:suffix.days')}
                        name="maxAdvancedBookingLimit"
                    />
                </Col>

                <Col md={8} xs={24}>
                    <SwitchField
                        {...t('appointmentModuleDetails:fields.hasTestDriveProcess', { returnObjects: true })}
                        {...yesNoSwitch}
                        name="hasTestDriveProcess"
                        tooltip={testDriveProcessTooltip}
                        required
                    />
                </Col>

                {values.hasTestDriveProcess && (
                    <>
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...t('appointmentModuleDetails:fields.hasTestDriveSigning', { returnObjects: true })}
                                {...yesNoSwitch}
                                name="hasTestDriveSigning"
                            />
                        </Col>

                        {values.hasTestDriveSigning && (
                            <Col md={8} xs={24}>
                                <ModuleSelectField
                                    {...t(`appointmentModuleDetails:fields.signingModule`, { returnObjects: true })}
                                    companyId={company?.id}
                                    moduleRole={ModuleRole.Signing}
                                    name="signingModuleId"
                                    addNoneOption
                                />
                            </Col>
                        )}

                        <Col md={8} xs={24}>
                            <SwitchField
                                {...t('appointmentModuleDetails:fields.showRemoteFlowButtonInKYCPage', {
                                    returnObjects: true,
                                })}
                                {...yesNoSwitch}
                                name="showRemoteFlowButtonInKYCPage"
                            />
                        </Col>
                    </>
                )}
            </Row>

            <Divider>{t('appointmentModuleDetails:sections.bookingTimeSlot')}</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <AvailableTimeSlot name="bookingTimeSlot" timeZone={company?.timeZone} />
            </Space>
        </>
    );
};

export const defaultLocation = { name: '', address: '', url: '', phone: { prefix: null, value: '' }, email: '' };
export const emptyTimeSlot = { start: null, end: null };

const implementation: AppointmentModuleImplementation = {
    PageComponent: ({ module }: AppointmentModulePageProps) => <AppointmentModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.AppointmentModule.className'),
    getValidator: () => validator,
    FormComponent: Form,
    create: async (apolloClient, companyId, settings, companyTimeZone) =>
        apolloClient
            .mutate<CreateAppointmentModuleMutation, CreateAppointmentModuleMutationVariables>({
                mutation: CreateAppointmentModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...settings,
                        bookingTimeSlot: settings.bookingTimeSlot.map(item => ({
                            ...item,
                            slot: dayjs(item.slot)
                                .set('hour', item.slot.hour())
                                .set('minute', item.slot.minute())
                                .set('second', 0)
                                .tz(companyTimeZone, true)
                                .toDate(),
                        })),
                    },
                },
            })
            .then(({ data }) => data.module),

    initialValues: {
        displayName: '',
        advancedBookingLimit: 0,
        maxAdvancedBookingLimit: null,
        bookingInformation: { defaultValue: '', overrides: [] },
        bookingTimeSlot: [],
        unavailableDayOfWeek: [],
        hasTestDriveProcess: false,
        hasTestDriveSigning: false,
        signingModuleId: null,
        showRemoteFlowButtonInKYCPage: false,
        isReminderTimeEnabled: false,
        timeToSendReminder: null,
    },
};

export default implementation;
