import { <PERSON>, Divide<PERSON>, <PERSON>, Space } from 'antd';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { SalesOfferModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/SalesOfferModuleWithPermissionsSpecs';
import {
    CreateSalesOfferModuleDocument,
    CreateSalesOfferModuleMutation,
    CreateSalesOfferModuleMutationVariables,
} from '../../../../../api/mutations/createSalesOfferModule';
import { CounterMethod, ModuleRole, SalesOfferModuleInitialSettings } from '../../../../../api/types';
import DealershipCascaderField from '../../../../../components/fields/DealershipFields/DealershipCascaderField';
import InputField from '../../../../../components/fields/InputField';
import ModuleSelectField from '../../../../../components/fields/ModuleSelectField';
import SelectField from '../../../../../components/fields/SelectField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import SalesOfferModuleMainDetails, { SalesOfferModulePageProps } from '../../SalesOfferModulePage';
import { CounterHelp, CounterSettingGroup, createCounterSettingsValidator } from './shared';
import MarketTypeGroup from './shared/MarketTypeGroup';
import { processDealershipPaymentSettingInput } from './shared/getDealershipPaymentSetting';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type FormProps = {
    company: CompanySpecs;
};

const implementation: ModuleImplementation<
    SalesOfferModuleWithPermissionsSpecsFragment,
    SalesOfferModuleInitialSettings
> = {
    PageComponent: ({ module }: SalesOfferModulePageProps) => <SalesOfferModuleMainDetails module={module} />,
    getTitle: t => t('moduleDetails:implementations.SalesOfferModule.className'),
    FormComponent: ({ company }: FormProps) => {
        const { t } = useTranslation(['moduleDetails']);

        const options = useModuleOptions(company, {
            addNoneOptions: {
                appointments: true,
            },
        });

        return (
            <>
                <Row gutter={10}>
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>

                    <MarketTypeGroup name="market" required />
                </Row>
                <Divider>Dependencies</Divider>
                <Space direction="vertical" style={{ width: '100%' }}>
                    <Row gutter={10}>
                        <Col md={12} xs={24}>
                            <ModuleSelectField
                                {...t(`moduleDetails:fields.signingModuleId`, { returnObjects: true })}
                                companyId={company?.id}
                                moduleRole={ModuleRole.Signing}
                                name="signingModuleId"
                                addNoneOption
                            />
                        </Col>
                        <Col md={12} xs={24}>
                            <DealershipCascaderField
                                {...t('moduleDetails:fields.paymentSetting', { returnObjects: true })}
                                name="paymentSetting"
                                options={options?.payment || []}
                                required
                                showSearch
                            />
                        </Col>

                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                                name="bankModuleId"
                                options={options.financing}
                                required
                            />
                        </Col>

                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                required
                            />
                        </Col>

                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.vehicleModuleId', { returnObjects: true })}
                                name="vehicleModuleId"
                                options={options.vehicles}
                                required
                            />
                        </Col>

                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.customerModuleId', { returnObjects: true })}
                                name="customerModuleId"
                                options={options.customers}
                                required
                            />
                        </Col>

                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.vehicleDataWithPorscheCodeIntegrationSettingId', {
                                    returnObjects: true,
                                })}
                                name="vehicleDataWithPorscheCodeIntegrationSettingId"
                                options={options.vehicleDataWithPorscheCodes}
                                required
                            />
                        </Col>
                    </Row>
                </Space>

                <Divider>ID Generators</Divider>
                <Space direction="vertical" style={{ width: '100%' }}>
                    <CounterHelp />
                    <Row gutter={10}>
                        <CounterSettingGroup name="applicationCounter" />
                        <CounterSettingGroup name="reservationCounter" />
                        <CounterSettingGroup name="insuranceCounter" />
                        <CounterSettingGroup name="vsaCounter" />
                    </Row>
                </Space>
            </>
        );
    },
    initialValues: {
        vsaCounter: {
            prefix: 'VSA{YY}',
            padding: 3,
            method: CounterMethod.Yearly,
        },
    },
    getValidator: () =>
        validators.compose(
            validators.requiredNonEmptyString('displayName'),
            validators.requiredString('customerModuleId'),
            validators.requiredString('vehicleModuleId'),
            validators.requiredString('bankModuleId'),
            validators.requiredString('signingModuleId'),
            validators.requiredString('insuranceModuleId'),
            validators.requiredString('vehicleDataWithPorscheCodeIntegrationSettingId'),
            createCounterSettingsValidator('applicationCounter'),
            createCounterSettingsValidator('reservationCounter'),
            createCounterSettingsValidator('insuranceCounter')
        ),
    create: async (apolloClient, companyId, values) => {
        const { paymentSetting, ...otherValues } = values;

        return apolloClient
            .mutate<CreateSalesOfferModuleMutation, CreateSalesOfferModuleMutationVariables>({
                mutation: CreateSalesOfferModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...otherValues,
                        paymentSetting: processDealershipPaymentSettingInput(paymentSetting),
                    },
                },
            })
            .then(({ data }) => data.createSalesOfferModule);
    },
};

export default implementation;
