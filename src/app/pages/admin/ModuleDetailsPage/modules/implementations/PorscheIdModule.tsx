import { Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { PorscheIdModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/PorscheIdModuleWithPermissionsSpecs';
import {
    CreatePorscheIdModuleDocument,
    CreatePorscheIdModuleMutation,
    CreatePorscheIdModuleMutationVariables,
} from '../../../../../api/mutations/createPorscheIdModule';
import { PorscheIdModuleSettings } from '../../../../../api/types';
import InputField from '../../../../../components/fields/InputField';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import PorscheIdModulePage from '../../PorscheIdModulePage';
import { ModuleImplementation } from './types';

type FormValues = PorscheIdModuleSettings;
type PorscheIdModuleImplementation = ModuleImplementation<PorscheIdModuleWithPermissionsSpecsFragment, FormValues>;
type PorscheIdModulePageProps = {
    module: PorscheIdModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = validators.compose(validators.requiredNonEmptyString('displayName'));

const Form: PorscheIdModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    return (
        <Row gutter={10}>
            <Col span={24}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
        </Row>
    );
};

const implementation: PorscheIdModuleImplementation = {
    PageComponent: ({ module }: PorscheIdModulePageProps) => <PorscheIdModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.PorscheIdModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) => {
        const settings = values;

        return apolloClient
            .mutate<CreatePorscheIdModuleMutation, CreatePorscheIdModuleMutationVariables>({
                mutation: CreatePorscheIdModuleDocument,
                variables: { companyId, settings },
            })
            .then(({ data }) => data.module);
    },
    initialValues: {
        displayName: '',
        porscheIdSetting: {
            apiKey: '',
            audience: '',
            identityProvider: '',
        },
    },
};

export default implementation;
