import { <PERSON><PERSON>, <PERSON>, Divider, <PERSON>, Space } from 'antd';
import { useFormikContext } from 'formik';
import { get } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { FinderApplicationPublicModuleWithPermissionsSpecsFragment } from '../../../../../api/fragments/FinderApplicationPublicModuleWithPermissionsSpecs';
import {
    CreateFinderApplicationPublicModuleDocument,
    CreateFinderApplicationPublicModuleMutation,
    CreateFinderApplicationPublicModuleMutationVariables,
} from '../../../../../api/mutations/createFinderApplicationModule';
import {
    ApplicationMarket,
    DealerDisclaimersConfiguratorInput,
    DisplayPreference,
    EmailContentUpdateType,
    FinancingPreferenceValue,
    FinderApplicationModuleInitialSettings,
    FinderVehicleCondition,
    ModuleType,
    PreferenceValue,
} from '../../../../../api/types';
import CascaderField from '../../../../../components/fields/CascaderField';
import DealerDepositAmountField from '../../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipCascaderField from '../../../../../components/fields/DealershipFields/DealershipCascaderField';
// eslint-disable-next-line max-len
import DealershipPriceDisclaimerField from '../../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import DealershipSelectField from '../../../../../components/fields/DealershipFields/DealershipSelectField';
// eslint-disable-next-line max-len
import DealershipTranslatedMarkdownField from '../../../../../components/fields/DealershipFields/DealershipTranslatedMarkdown';
import InputField from '../../../../../components/fields/InputField';
import InputNumberField from '../../../../../components/fields/InputNumberField';
import SelectField from '../../../../../components/fields/SelectField';
import SwitchField from '../../../../../components/fields/SwitchField';
import TranslatedMarkdownField from '../../../../../components/fields/TranslatedMarkdownField';
import { TranslationFieldType } from '../../../../../utilities/common';
import { hasValue } from '../../../../../utilities/fp';
import useMyInfoOption from '../../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import validators from '../../../../../utilities/validators';
import { CompanySpecs } from '../../../AddModulePage/shared';
import useBankOptions from '../../../FinanceProductDetailsPage/shared/useBankOptions';
import FinderApplicationModulePage, { FinderApplicationModulePageProps } from '../../FinderApplicationModulePage';
import {
    useDisclaimerTypeOptions,
    useUpdateDisclaimers,
} from '../../FinderApplicationModulePage/FinderApplicationModuleConfigurationForm';
import { defaultDiscalimers } from '../../FinderApplicationModulePage/shared';
import { Disclaimer } from '../../FinderApplicationModulePage/type';
import useScenarioObserver from '../../StandardApplicationModulePage/useScenarioObserver';
import useScenarioOptions from '../../StandardApplicationModulePage/useScenarioOptions';
import { newZealandMarketValidator, singaporeMarketValidator } from '../../shared/validators';
import {
    CounterHelp,
    CounterSettingGroup,
    createCounterSettingsValidator,
    hasAppointmentScenario,
    hasFinancingScenario,
    hasInsuranceScenario,
    hasPaymentScenario,
} from './shared';
import DisclaimerFieldArray from './shared/Disclaimers';
import MarketTypeGroup from './shared/MarketTypeGroup';
import { processDealershipPaymentSettingInput } from './shared/getDealershipPaymentSetting';
import useDisplayPreferenceOptions from './shared/useDisplayPreferenceOptions';
import useFinancingPreferenceOptions from './shared/useFinancingPreferenceOptions';
import useFinderVehicleConditionOptions from './shared/useFinderVehicleConditionOptions';
import useInsurerOptions from './shared/useInsurerOptions';
import useModuleOptions from './shared/useModuleOptions';
import usePreferenceValueOptions from './shared/usePreferenceValues';
import useSalesPersonOptions from './shared/useSalesPersonOptions';
import { ModuleImplementation } from './types';

type FinderApplicationModuleImplementation = ModuleImplementation<
    FinderApplicationPublicModuleWithPermissionsSpecsFragment,
    Omit<FinderApplicationModuleInitialSettings, 'disclaimers'> & {
        disclaimers: Disclaimer[];
    }
>;

export const defaultPricingDisclaimer: DealerDisclaimersConfiguratorInput = {
    defaultValue: [{ defaultValue: '', overrides: [] }],
    overrides: [],
};

export const getValidator = (isPublic: boolean) =>
    validators.compose(
        validators.requiredNonEmptyString('displayName'),
        validators.requiredString('agreementsModuleId'),
        validators.requiredString('customerModuleId'),
        validators.requiredString('vehicleModuleId'),
        validators.only(values => hasInsuranceScenario(values?.scenarios), validators.requiredArray('insurerIds')),
        validators.only(values => hasFinancingScenario(values?.scenarios), validators.requiredArray('bankIds')),
        validators.only(
            values => hasAppointmentScenario(values?.scenarios),
            validators.requiredString('appointmentModuleId')
        ),
        validators.only(values => hasValue('bankModuleId')(values), validators.requiredString('market')),
        validators.only(values => {
            const showInsuranceCalculator = get('showInsuranceCalculator', values);

            return showInsuranceCalculator === true;
        }, validators.requiredString('insuranceModuleId')),
        validators.only(values => {
            const displayFinanceCalculator = get('displayFinanceCalculator', values);

            return (
                displayFinanceCalculator === PreferenceValue.Yes ||
                displayFinanceCalculator === PreferenceValue.Optional
            );
        }, validators.requiredString('bankModuleId')),
        validators.only(values => {
            const marketType = get('market', values);

            return marketType === ApplicationMarket.Singapore;
        }, singaporeMarketValidator),
        validators.only(values => {
            const marketType = get('market', values);

            return marketType === ApplicationMarket.NewZealand;
        }, newZealandMarketValidator),
        validators.requiredArray('scenarios', true),
        validators.only(
            values => hasPaymentScenario(get('scenarios', values)),
            validators.compose(
                validators.requiredString('paymentSetting.defaultId'),
                validators.requiredNumber('depositAmount.defaultValue')
            )
        ),
        createCounterSettingsValidator('applicationCounter'),
        createCounterSettingsValidator('reservationCounter'),
        createCounterSettingsValidator('leadCounter'),
        createCounterSettingsValidator('appointmentCounter'),
        createCounterSettingsValidator('insuranceCounter'),
        validators.only(() => isPublic, validators.requiredString('assignee.defaultId')),
        validators.requiredBoolean('showResetKYCButton'),
        validators.requiredArray('finderVehicleConditions'),
        validators.requiredNumber('reservationPeriod')
    );

export const getInitialValues = (): Omit<
    FinderApplicationModuleInitialSettings,
    | 'applicationCounter'
    | 'appointmentCounter'
    | 'insuranceCounter'
    | 'leadCounter'
    | 'reservationCounter'
    | 'assignee'
    | 'disclaimers'
> & {
    disclaimers: Disclaimer[];
} => ({
    isOcrEnabled: false,
    testDrive: false,
    tradeIn: false,
    financingPreference: FinancingPreferenceValue.Mandatory,
    isInsuranceOptional: false,
    displayFinanceCalculator: PreferenceValue.Yes,
    showInsuranceCalculator: true,
    displayName: '',
    agreementsModuleId: null,
    customerModuleId: null,
    vehicleModuleId: null,
    finderVehicleConditions: [],
    porscheApprovedInfo: {
        defaultValue: '',
        overrides: [],
    },
    reservationPeriod: 0,
    scenarios: [],
    priceDisclaimer: defaultPricingDisclaimer,
    financingDisclaimer: defaultPricingDisclaimer,
    reservationInstructions: {
        defaultValue: { defaultValue: '', overrides: [] },
        overrides: [],
    },
    emailContents: {
        emailContentUpdateType: EmailContentUpdateType.Module,
        submitOrder: {
            defaultValue: {
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                subject: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                isSummaryVehicleVisible: true,
            },
            overrides: [],
        },
        reminderEmail: {
            defaultValue: {
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                subject: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                sendingTime: new Date(),
                isActive: true,
            },
            overrides: [],
        },
    },
    showResetKYCButton: false,
    showFromValueOnVehicleDetails: true,
    displayAppointmentDatepicker: false,
    bankDisplayPreference: DisplayPreference.ShownWhenMoreThanOne,
    insurerIds: [],
    bankIds: [],
    insurerDisplayPreference: DisplayPreference.ShownWhenMoreThanOne,
    showVisitModelPageButton: false,
    modelPageUrl: '',
    skipForDeposit: false,
    useBankDisclaimers: true,
    useInsurerDisclaimers: true,
    disclaimers: defaultDiscalimers,
    depositAmount: {
        defaultValue: 0,
        overrides: [],
    },
});

type FinderApplicationModuleInitialFormValues = Omit<FinderApplicationModuleInitialSettings, 'disclaimers'> & {
    disclaimers: Disclaimer[];
};

type FormProps = {
    company: CompanySpecs;
};

const Form: FinderApplicationModuleImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails', 'common']);

    const { values } = useFormikContext<FinderApplicationModuleInitialFormValues>();

    const disclaimerTypeOptions = useDisclaimerTypeOptions(values.useBankDisclaimers, values.useInsurerDisclaimers);
    useUpdateDisclaimers(disclaimerTypeOptions);

    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();

    const { salesPersonOptions, loading } = useSalesPersonOptions(company);

    const salesPersonOptionsWithNone = useMemo(
        () => [
            {
                value: null,
                label: t('common:options.none'),
            },
            ...salesPersonOptions,
        ],
        [salesPersonOptions, t]
    );

    const { insurerList, loading: insurerLoading } = useInsurerOptions(company.id);

    const { options: bankOptions } = useBankOptions();

    const { scenarioOptions } = useScenarioOptions();
    const scenario = useScenarioObserver(ModuleType.FinderApplicationPublicModule);

    const options = useModuleOptions(company, {
        addNoneOptions: { promoCodes: true, payment: true, appointments: true },
    });
    const displayPreferenceOptions = useDisplayPreferenceOptions();
    const preferenceOptions = usePreferenceValueOptions();
    const financingPreferenceOptions = useFinancingPreferenceOptions();
    const finderVehicleConditionOptions = useFinderVehicleConditionOptions();

    const { yesNoSwitch } = useSystemSwitchData();

    return (
        <>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('moduleDetails:fields.applicationScenario', { returnObjects: true })}
                        mode="multiple"
                        name="scenarios"
                        options={scenarioOptions}
                        showSearch
                    />
                </Col>
                <Col md={8} xs={24}>
                    <DealershipSelectField
                        {...t('moduleDetails:fields.assignee', { returnObjects: true })}
                        companyId={company.id}
                        dealerOptions={salesPersonOptionsWithNone}
                        loading={loading}
                        name="assignee"
                        options={salesPersonOptions}
                        required
                        showSearch
                    />
                </Col>

                <Col md={8} xs={24}>
                    <SwitchField
                        {...yesNoSwitch}
                        {...t('moduleDetails:fields.tradeIn', { returnObjects: true })}
                        name="tradeIn"
                    />
                </Col>
                {values?.tradeIn && (
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.isTradeInAmountVisible', { returnObjects: true })}
                            name="isTradeInAmountVisible"
                        />
                    </Col>
                )}
                <MarketTypeGroup name="market" required />
                <Col md={8} xs={24}>
                    <SwitchField
                        {...yesNoSwitch}
                        {...t('moduleDetails:fields.isOcrEnabled', { returnObjects: true })}
                        name="isOcrEnabled"
                    />
                </Col>

                <Col md={8} xs={24}>
                    <DealershipPriceDisclaimerField
                        {...t('moduleDetails:fields.priceDisclaimerTitle', { returnObjects: true })}
                        name="priceDisclaimer"
                        isMultipleEntry
                    />
                </Col>

                <Col md={8} xs={24}>
                    <DealershipTranslatedMarkdownField
                        {...t('moduleDetails:fields.reservationInstructions', { returnObjects: true })}
                        name="reservationInstructions"
                    />
                </Col>

                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('moduleDetails:fields.reservationPeriod', { returnObjects: true })}
                        addonAfter={t('moduleDetails:suffix.inWorkingDays')}
                        name="reservationPeriod"
                        required
                    />
                </Col>
            </Row>
            <Divider>Dependencies</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                    description={t('moduleDetails:alerts.moduleImmutableDependencies.content')}
                    message={t('moduleDetails:alerts.moduleImmutableDependencies.title')}
                    type="warning"
                    showIcon
                />
                <Row gutter={10}>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.agreementsModuleId', { returnObjects: true })}
                            name="agreementsModuleId"
                            options={options.agreements}
                            required
                            showSearch
                        />
                    </Col>
                    {(values.displayFinanceCalculator === PreferenceValue.Yes ||
                        values.displayFinanceCalculator === PreferenceValue.Optional) && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                                name="bankModuleId"
                                options={options.financing}
                                required
                                showSearch
                            />
                        </Col>
                    )}
                    {(values.showInsuranceCalculator || scenario.hasInsurance || scenario.hasLeadCapture) && (
                        <Col md={12} xs={24}>
                            <SelectField
                                required
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.customerModuleId', { returnObjects: true })}
                            name="customerModuleId"
                            options={options.customers}
                            required
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.vehicleModuleId', { returnObjects: true })}
                            name="vehicleModuleId"
                            options={options.finderVehicles}
                            required
                            showSearch
                        />
                    </Col>
                    {scenario.hasPayment && (
                        <>
                            <Col md={12} xs={24}>
                                <DealershipCascaderField
                                    {...t('moduleDetails:fields.paymentModuleId', { returnObjects: true })}
                                    name="paymentSetting"
                                    options={options?.payment || []}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={12} xs={24}>
                                <DealerDepositAmountField
                                    {...t('moduleDetails:fields.depositAmount', { returnObjects: true })}
                                    name="depositAmount"
                                />
                            </Col>
                        </>
                    )}

                    {hasMyInfoModule && (
                        <Col md={12} xs={24}>
                            <CascaderField
                                {...t('moduleDetails:fields.myInfoSetting', { returnObjects: true })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {hasMyInfoModule && (
                        <Col md={12} xs={24}>
                            <CascaderField
                                {...t('moduleDetails:fields.myInfoSetting', { returnObjects: true })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <CascaderField
                            {...t('moduleDetails:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurer', { returnObjects: true })}
                                loading={insurerLoading}
                                mode="multiple"
                                name="insurerIds"
                                options={insurerList}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bank', { returnObjects: true })}
                                mode="multiple"
                                name="bankIds"
                                options={bankOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && (
                        <>
                            <Col md={12} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                                    name="appointmentModuleId"
                                    options={options.appointments}
                                    required={scenario.hasAppointment}
                                    showSearch
                                />
                            </Col>
                            {values.appointmentModuleId && values.appointmentModuleId !== null && (
                                <Col md={12} xs={24}>
                                    <SwitchField
                                        {...yesNoSwitch}
                                        {...t('moduleDetails:fields.displayAppointmentDatepicker', {
                                            returnObjects: true,
                                        })}
                                        name="displayAppointmentDatepicker"
                                    />
                                </Col>
                            )}
                        </>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.financingPreference', { returnObjects: true })}
                                disabled={scenario.hasOnlyFinancing}
                                name="financingPreference"
                                options={financingPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.showFinanceCalculator', { returnObjects: true })}
                            disabled={scenario.hasFinancing}
                            name="displayFinanceCalculator"
                            options={preferenceOptions}
                            showSearch
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.useBankDisclaimers', { returnObjects: true })}
                            name="useBankDisclaimers"
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={12} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isInsuranceOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyInsurance}
                                name="isInsuranceOptional"
                            />
                        </Col>
                    )}
                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showInsuranceCalculator', { returnObjects: true })}
                            disabled={scenario.hasInsurance}
                            name="showInsuranceCalculator"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.useInsurerDisclaimers', { returnObjects: true })}
                            name="useInsurerDisclaimers"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFromValueOnVehicleDetails', { returnObjects: true })}
                            name="showFromValueOnVehicleDetails"
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayPreference', { returnObjects: true })}
                            name="bankDisplayPreference"
                            options={displayPreferenceOptions}
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.finderVehicleCondition', { returnObjects: true })}
                            mode="multiple"
                            name="finderVehicleConditions"
                            options={finderVehicleConditionOptions}
                            required
                            showSearch
                        />
                    </Col>
                    {values.finderVehicleConditions.includes(FinderVehicleCondition.PorscheApproved) && (
                        <Col md={12} xs={24}>
                            <TranslatedMarkdownField
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    'moduleDetails:fields.porscheApprovedInfo',
                                    { returnObjects: true }
                                )}
                                name="porscheApprovedInfo"
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showVisitModelPageButton', {
                                returnObjects: true,
                            })}
                            name="showVisitModelPageButton"
                        />
                    </Col>

                    {values.showVisitModelPageButton && (
                        <Col md={12} xs={24}>
                            <InputField
                                {...t('moduleDetails:fields.modelPageUrl', { returnObjects: true })}
                                name="modelPageUrl"
                                required
                            />
                        </Col>
                    )}

                    {values.insurerIds.length >= 1 && (
                        <Col md={12} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurerDisplayPreference', { returnObjects: true })}
                                name="insurerDisplayPreference"
                                options={displayPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasPayment && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.skipForDeposit', { returnObjects: true })}
                                name="skipForDeposit"
                            />
                        </Col>
                    )}

                    {(!values.useBankDisclaimers || !values.useInsurerDisclaimers) && (
                        <Col span={24}>
                            <DisclaimerFieldArray
                                disclaimerTypeOptions={disclaimerTypeOptions}
                                market={values.market}
                                name="disclaimers"
                            />
                        </Col>
                    )}
                </Row>
            </Space>
            <Divider>ID Generators</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <CounterHelp />
                <Row gutter={10}>
                    <CounterSettingGroup name="applicationCounter" />
                    <CounterSettingGroup name="reservationCounter" />
                    <CounterSettingGroup name="leadCounter" />
                    <CounterSettingGroup name="appointmentCounter" />
                    <CounterSettingGroup name="insuranceCounter" />
                </Row>
            </Space>
        </>
    );
};

const implementation: FinderApplicationModuleImplementation = {
    PageComponent: ({ module }: FinderApplicationModulePageProps) => <FinderApplicationModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.FinderApplicationModule.className'),
    FormComponent: Form,
    getValidator: () => getValidator(true),
    create: async (apolloClient, companyId, values) =>
        apolloClient
            .mutate<CreateFinderApplicationPublicModuleMutation, CreateFinderApplicationPublicModuleMutationVariables>({
                mutation: CreateFinderApplicationPublicModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...values,
                        disclaimers: {
                            financingDisclaimer: values.disclaimers.find(disclaimer => disclaimer.type === 'financing')
                                ?.value,
                            insuranceDisclaimer: values.disclaimers.find(disclaimer => disclaimer.type === 'insurance')
                                ?.value,
                        },
                        testDrive: hasAppointmentScenario(values.scenarios),
                        bankModuleId:
                            values.displayFinanceCalculator === PreferenceValue.Yes ||
                            values.displayFinanceCalculator === PreferenceValue.Optional
                                ? values.bankModuleId
                                : null,
                        paymentSetting: hasPaymentScenario(values.scenarios)
                            ? processDealershipPaymentSettingInput(values.paymentSetting)
                            : null,
                    },
                },
            })
            .then(({ data }) => data.module),
    initialValues: {
        ...getInitialValues(),
        assignee: {
            defaultId: null,
            overrides: [],
        },
    },
};

export default implementation;
