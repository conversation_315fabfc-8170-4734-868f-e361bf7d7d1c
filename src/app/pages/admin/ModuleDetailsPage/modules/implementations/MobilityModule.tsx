/* eslint-disable max-len */
/* eslint-disable react/prop-types */
import { Alert, Col, Divider, Row, Select, Space } from 'antd';
import { useFormikContext } from 'formik';
import { get, isNil } from 'lodash/fp';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MobilityModuleSpecsFragment } from '../../../../../api/fragments';
import {
    CreateMobilityModuleDocument,
    CreateMobilityModuleMutation,
    CreateMobilityModuleMutationVariables,
} from '../../../../../api/mutations/createMobilityModule';
import { ApplicationScenario, DateTimeUnit, MobilityModuleInitialSettings, ModuleRole } from '../../../../../api/types';
import CascaderField from '../../../../../components/fields/CascaderField';
import DealershipTranslatedMarkdownField from '../../../../../components/fields/DealershipFields/DealershipTranslatedMarkdown';
import DealershipTranslatedTextAreaField from '../../../../../components/fields/DealershipFields/DealershipTranslatedTextArea';
import InputField from '../../../../../components/fields/InputField';
import InputNumberField from '../../../../../components/fields/InputNumberField';
import ModuleSelectField from '../../../../../components/fields/ModuleSelectField';
import SelectField from '../../../../../components/fields/SelectField';
import SwitchField from '../../../../../components/fields/SwitchField';
import useExternalModelInfoTooltip from '../../../../../utilities/useExternalModelInfoTooltip';
import useSystemOptions from '../../../../../utilities/useSystemOptions';
import useSystemSwitchData from '../../../../../utilities/useSystemSwitchData';
import useTooltip from '../../../../../utilities/useTooltip';
import validators from '../../../../../utilities/validators';
import MobilityModulePage, { MobilityModulePageProps } from '../../MobilityModulePage';
import {
    CounterHelp,
    CounterSettingGroup,
    createCounterSettingsValidator,
    defaultLocation,
    hasPaymentScenario,
} from './shared';
import LocationFieldArray from './shared/LocationFieldArray';
import UnavailableTimeRange from './shared/UnavailableTimeRange';
import useModuleOptions from './shared/useModuleOptions';
import { ModuleImplementation } from './types';

type MobilityModuleImplementation = ModuleImplementation<MobilityModuleSpecsFragment, MobilityModuleInitialSettings>;

const { Option } = Select;
const validator = validators.compose(
    validators.requiredNonEmptyString('displayName'),
    validators.requiredString('customerModuleId'),
    validators.requiredString('vehicleModuleId'),
    validators.requiredString('agreementsModuleId'),
    createCounterSettingsValidator('bookingsCounter'),
    validators.requiredNumber('minimumAdvancedBooking'),
    validators.requiredNumber('durationBeforeNextBooking'),
    validators.requiredNumber('amendmentCutOff'),
    validators.requiredArray('scenarios', true),
    validators.requiredBoolean('showResetKYCButton'),

    validators.only(values => hasPaymentScenario(values.scenarios), validators.requiredString('paymentSettingsId')),
    validators.only(values => values.externalModelInfo === true, validators.requiredString('baseUrl')),

    validators.requiredBoolean('signing.isEnabled'),
    validators.only(values => values.signing.isEnabled === true, validators.requiredString('signing.moduleId')),

    validators.forEach(
        'locations',
        validators.compose(
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { address, url, phone, email } = value;

                return !!address || !!url || !!phone || !!email;
            }, validators.requiredString('name')),
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { name, url, phone, email } = value;

                return !!name || !!url || !!phone || !!email;
            }, validators.requiredString('address')),
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { name, address, phone, email } = value;

                return !!name || !!address || !!phone || !!email;
            }, validators.requiredString('url')),
            validators.only((values, error, { prefix }) => {
                const value = get(prefix, values);
                const { name, address, url, email } = value;

                return !!name || !!address || !!url || !!email;
            }, validators.requiredString('phone')),
            validators.only(
                (values, error, { prefix }) => {
                    const value = get(prefix, values);
                    const { name, address, url, phone } = value;

                    return !!name || !!address || !!url || !!phone;
                },
                validators.compose(validators.requiredString('email'), validators.validEmail('email'))
            )
        )
    ),
    validators.unavailableTimeRange('unavailableTimeRange'),
    validators.requiredNumber('availableNumberOfBookingRange.value')
);

export const Form: MobilityModuleImplementation['FormComponent'] = ({ company }) => {
    const { t } = useTranslation(['moduleDetails', 'mobilityModuleDetails']);
    const options = useModuleOptions(company, {
        addNoneOptions: { promoCodes: true, payment: true, giftVouchers: true },
    });
    const { yesNoSwitch } = useSystemSwitchData();
    const { dayOfWeek } = useSystemOptions();
    const { values, setFieldValue } = useFormikContext<MobilityModuleInitialSettings>();
    const externalModelInfoTooltip = useExternalModelInfoTooltip();
    const rentalDisclaimerToolTip = useTooltip('rentalDisclaimer');
    const rentalRequirementToolTip = useTooltip('rentalRequirement');

    const { scenarios } = values;
    const hasPaymentField = useMemo(() => hasPaymentScenario(scenarios), [scenarios]);

    const scenarioOptions = useMemo(
        () => [
            {
                value: ApplicationScenario.Booking,
                label: t('moduleDetails:explanations.scenarios.Booking.label'),
                disabled: true,
            },
            {
                value: ApplicationScenario.Payment,
                label: t('moduleDetails:explanations.scenarios.Payment.label'),
            },
        ],
        [t]
    );

    useEffect(() => {
        if (!values.sendEmailToCustomer) {
            setFieldValue('sendPDFToCustomer', false);
        }
    }, [setFieldValue, values.sendEmailToCustomer]);

    return (
        <>
            <Row gutter={10}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('moduleDetails:fields.applicationScenario', { returnObjects: true })}
                        mode="multiple"
                        name="scenarios"
                        options={scenarioOptions}
                        showSearch
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('mobilityModuleDetails:fields.minimumAdvancedBooking', { returnObjects: true })}
                        addonAfter={t('mobilityModuleDetails:suffix.daysBefore')}
                        name="minimumAdvancedBooking"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('mobilityModuleDetails:fields.durationBeforeNextBooking', { returnObjects: true })}
                        addonAfter={t('mobilityModuleDetails:suffix.hours')}
                        name="durationBeforeNextBooking"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('mobilityModuleDetails:fields.amendmentCutOff', { returnObjects: true })}
                        addonAfter={t('mobilityModuleDetails:suffix.daysBefore')}
                        name="amendmentCutOff"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <DealershipTranslatedTextAreaField
                        {...t('mobilityModuleDetails:fields.rentalDisclaimer', { returnObjects: true })}
                        name="rentalDisclaimer"
                        tooltip={rentalDisclaimerToolTip}
                    />
                </Col>
                <Col md={8} xs={24}>
                    <DealershipTranslatedMarkdownField
                        {...t('mobilityModuleDetails:fields.rentalRequirement', { returnObjects: true })}
                        name="rentalRequirement"
                        tooltip={rentalRequirementToolTip}
                    />
                </Col>
                <Col lg={8} xs={24}>
                    <SwitchField
                        {...t('mobilityModuleDetails:fields.externalModelInfo', { returnObjects: true })}
                        {...yesNoSwitch}
                        name="externalModelInfo"
                        tooltip={externalModelInfoTooltip}
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SelectField
                        {...t('moduleDetails:fields.unavailableDayofWeek', { returnObjects: true })}
                        mode="multiple"
                        name="unavailableDayOfWeek"
                        options={dayOfWeek}
                        showSearch
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputNumberField
                        {...t('mobilityModuleDetails:fields.availableNumberOfBookingRange', { returnObjects: true })}
                        addonAfter={
                            <Select
                                defaultValue={DateTimeUnit.Days}
                                onChange={value => setFieldValue('availableNumberOfBookingRange.unit', value)}
                                value={values.availableNumberOfBookingRange.unit}
                            >
                                <Option value={DateTimeUnit.Days}>{t('common:options.dateTimeUnit.days')}</Option>
                                <Option value={DateTimeUnit.Hours}>{t('common:options.dateTimeUnit.hours')}</Option>
                            </Select>
                        }
                        name="availableNumberOfBookingRange.value"
                        required
                    />
                </Col>
                {values.externalModelInfo && (
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('mobilityModuleDetails:fields.baseUrl', { returnObjects: true })}
                            addonBefore="https://"
                            name="baseUrl"
                            required
                        />
                    </Col>
                )}
                <Col md={8} xs={24}>
                    <SwitchField
                        {...t('mobilityModuleDetails:fields.signing.isEnabled', { returnObjects: true })}
                        name="signing.isEnabled"
                        {...yesNoSwitch}
                        required
                    />
                </Col>
                {values.signing.isEnabled && (
                    <Col md={8} xs={24}>
                        <ModuleSelectField
                            {...t(`mobilityModuleDetails:fields.signing.module`, { returnObjects: true })}
                            companyId={company.id}
                            moduleRole={ModuleRole.Signing}
                            name="signing.moduleId"
                            addNoneOption
                        />
                    </Col>
                )}
                <Col md={8} xs={24}>
                    <SwitchField
                        {...t('mobilityModuleDetails:fields.sendEmailToCustomer', { returnObjects: true })}
                        name="sendEmailToCustomer"
                        {...yesNoSwitch}
                        required
                    />
                </Col>
                {values.sendEmailToCustomer && (
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...t('mobilityModuleDetails:fields.sendPDFToCustomer', { returnObjects: true })}
                            name="sendPDFToCustomer"
                            {...yesNoSwitch}
                            required
                        />
                    </Col>
                )}

                <Col lg={8} xs={24}>
                    <SwitchField
                        {...t('mobilityModuleDetails:fields.persistKYCData', { returnObjects: true })}
                        {...yesNoSwitch}
                        name="persistKYCData"
                    />
                </Col>
                <Col md={8} xs={24}>
                    <SwitchField
                        {...yesNoSwitch}
                        {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                        name="showResetKYCButton"
                    />
                </Col>
            </Row>

            <Divider>{t('mobilityModuleDetails:sections.locations')}</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <LocationFieldArray name="locations" />
            </Space>

            <Divider>{t('moduleDetails:sections.dependencies')}</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                    description={t('moduleDetails:alerts.moduleImmutableDependencies.content')}
                    message={t('moduleDetails:alerts.moduleImmutableDependencies.title')}
                    type="warning"
                    showIcon
                />
                <Row gutter={10}>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.customerModuleId', { returnObjects: true })}
                            name="customerModuleId"
                            options={options.customers}
                            required
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.vehicleModuleId', { returnObjects: true })}
                            name="vehicleModuleId"
                            options={options.vehicles}
                            required
                            showSearch
                        />
                    </Col>
                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.agreementsModuleId', { returnObjects: true })}
                            name="agreementsModuleId"
                            options={options.agreements}
                            required
                            showSearch
                        />
                    </Col>

                    {hasPaymentField && (
                        <Col md={12} xs={24}>
                            <CascaderField
                                {...t('moduleDetails:fields.paymentModuleId', { returnObjects: true })}
                                name="paymentSettingsId"
                                options={options.payment}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={12} xs={24}>
                        <CascaderField
                            {...t('moduleDetails:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>

                    <Col md={12} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.giftVoucherModule', { returnObjects: true })}
                            name="giftVoucherModuleId"
                            options={options.giftVouchers}
                            showSearch
                        />
                    </Col>
                </Row>
            </Space>
            <Divider>{t('moduleDetails:sections.idGenerators')}</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <CounterHelp />
                <Row gutter={10}>
                    <CounterSettingGroup name="bookingsCounter" />
                </Row>
            </Space>

            <Divider>{t('mobilityModuleDetails:sections.unavailableTimeRange')}</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
                <UnavailableTimeRange name="unavailableTimeRange" />
            </Space>
        </>
    );
};

export const emptyTimeSlot = { start: null, end: null };

const implementation: MobilityModuleImplementation = {
    PageComponent: ({ module }: MobilityModulePageProps) => <MobilityModulePage module={module} />,
    getTitle: t => t('moduleDetails:implementations.MobilityModule.className'),
    getValidator: () => validator,
    FormComponent: Form,
    create: async (apolloClient, companyId, values) =>
        apolloClient
            .mutate<CreateMobilityModuleMutation, CreateMobilityModuleMutationVariables>({
                mutation: CreateMobilityModuleDocument,
                variables: {
                    companyId,
                    settings: {
                        ...values,
                        locations: values.locations.filter(location => !!location.name),
                        unavailableTimeRange: values.unavailableTimeRange.filter(
                            range => !(isNil(range.end) && isNil(range.start))
                        ),
                    },
                },
            })
            .then(({ data }) => data.module),
    initialValues: {
        displayName: '',
        customerModuleId: null,
        vehicleModuleId: null,
        agreementsModuleId: null,
        scenarios: [ApplicationScenario.Booking],
        paymentSettingsId: null,
        rentalDisclaimer: {
            defaultValue: { defaultValue: '', overrides: [] },
            overrides: [],
        },
        rentalRequirement: {
            defaultValue: { defaultValue: '', overrides: [] },
            overrides: [],
        },
        locations: [defaultLocation],
        amendmentCutOff: null,
        minimumAdvancedBooking: null,
        durationBeforeNextBooking: 0,
        externalModelInfo: false,
        unavailableTimeRange: [],
        unavailableDayOfWeek: [],
        showResetKYCButton: false,
        availableNumberOfBookingRange: {
            value: 0,
            unit: DateTimeUnit.Days,
        },
        persistKYCData: false,
        bookingCode: {
            defaultValue: '',
            viewable: false,
            overrides: [],
        },
        signing: {
            isEnabled: false,
            moduleId: null,
        },
        sendEmailToCustomer: false,
        sendPDFToCustomer: false,
        homeDelivery: {
            isEnable: false,
            assigneeId: null,
        },
    },
};

export default implementation;
