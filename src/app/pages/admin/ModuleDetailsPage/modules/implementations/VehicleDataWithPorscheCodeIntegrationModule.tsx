import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import {
    VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment,
    VehicleDataWithPorscheCodeIntegrationSettingInput,
} from '../../../../../api';
import {
    CreateVehicleDataWithPorscheCodeIntegrationModuleDocument,
    CreateVehicleDataWithPorscheCodeIntegrationModuleMutation,
    CreateVehicleDataWithPorscheCodeIntegrationModuleMutationVariables,
} from '../../../../../api/mutations/createVehicleDataWithPorscheCodeIntegrationModule';
import { useThemeComponents } from '../../../../../themes/hooks';
import { CompanySpecs } from '../../../AddModulePage/shared';
import VehicleDataWithPorscheCodeIntegrationModulePage from '../../VehicleDataWithPorscheCodeIntegrationModulePage';
// eslint-disable-next-line max-len
import { porscheVehicleDataIntegrationValidator } from '../../VehicleDataWithPorscheCodeIntegrationModulePage/VehicleDataWithPorscheCodeIntegrationModuleDetailsInner';
import { ModuleImplementation } from './types';

type FormValues = VehicleDataWithPorscheCodeIntegrationSettingInput;
type VehicleDataWithPorscheCodeIntegrationImplementation = ModuleImplementation<
    VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment,
    FormValues
>;
type VehicleDataWithPorscheCodeIntegrationPageProps = {
    module: VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment;
};

type FormProps = {
    company: CompanySpecs;
};

const validator = porscheVehicleDataIntegrationValidator;

const Form: VehicleDataWithPorscheCodeIntegrationImplementation['FormComponent'] = ({ company }: FormProps) => {
    const { t } = useTranslation(['moduleDetails']);

    const { FormFields } = useThemeComponents();

    return (
        <Row gutter={10}>
            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.displayName', {
                        returnObjects: true,
                    })}
                    name="displayName"
                    required
                />
            </Col>

            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.authBasePath', { returnObjects: true })}
                    name="authBasePath"
                    required
                />
            </Col>

            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.authClientId', {
                        returnObjects: true,
                    })}
                    name="authClientId"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.authClientSecret', { returnObjects: true })}
                    name="authClientSecret"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.grantType', { returnObjects: true })}
                    name="grantType"
                    required
                />
            </Col>

            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.basePath', { returnObjects: true })}
                    name="basePath"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.porscheClientXId', { returnObjects: true })}
                    name="porscheClientXId"
                    required
                />
            </Col>
            <Col md={8} xs={24}>
                <FormFields.InputField
                    {...t('moduleDetails:fields.porscheClientXSecret', { returnObjects: true })}
                    name="porscheClientXSecret"
                    required
                />
            </Col>
        </Row>
    );
};

const implementation: VehicleDataWithPorscheCodeIntegrationImplementation = {
    PageComponent: ({ module }: VehicleDataWithPorscheCodeIntegrationPageProps) => (
        <VehicleDataWithPorscheCodeIntegrationModulePage module={module} />
    ),
    getTitle: t => t('moduleDetails:implementations.VehicleDataWithPorscheCodeIntegrationModule.className'),
    FormComponent: Form,
    getValidator: () => validator,
    create: async (apolloClient, companyId, values) =>
        apolloClient
            .mutate<
                CreateVehicleDataWithPorscheCodeIntegrationModuleMutation,
                CreateVehicleDataWithPorscheCodeIntegrationModuleMutationVariables
            >({
                mutation: CreateVehicleDataWithPorscheCodeIntegrationModuleDocument,
                variables: {
                    companyId,
                    setting: values,
                },
            })
            .then(({ data }) => data.module),
    initialValues: {
        displayName: '',
    },
};

export default implementation;
