import { Validator } from '@amille/simple-validators';
import { ApolloClient } from '@apollo/client';
import { TFunction } from 'i18next';
import { ModuleSpecsFragment } from '../../../../../api/fragments/ModuleSpecs';
import { ValidatorContext } from '../../../../../utilities/useValidator';
import { CompanySpecs } from '../../../AddModulePage/shared';

export type ModuleImplementation<ModuleSpecs, FormValues> = {
    getTitle: (t: TFunction) => string;
    PageComponent: React.FC<{ module: ModuleSpecs }> | null;
    FormComponent: React.FC<{ company: CompanySpecs }>;
    getValidator: () => Validator<{ company: CompanySpecs } & ValidatorContext>;
    create: (
        apolloClient: ApolloClient<any>,
        companyId: string,
        values: FormValues,
        companyTimeZone?: string
    ) => Promise<ModuleSpecsFragment>;
    initialValues?: Partial<FormValues> | null;
};
