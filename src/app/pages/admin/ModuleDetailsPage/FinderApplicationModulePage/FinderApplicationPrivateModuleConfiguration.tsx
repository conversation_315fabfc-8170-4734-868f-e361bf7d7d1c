/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { pick, uniqBy } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FinderApplicationPrivateModuleSpecsFragment } from '../../../../api/fragments/FinderApplicationPrivateModuleSpecs';
import {
    UpdateFinderApplicationPrivateModuleDocument,
    UpdateFinderApplicationPrivateModuleMutation,
    UpdateFinderApplicationPrivateModuleMutationVariables,
} from '../../../../api/mutations/updateFinderApplicationPrivateModule';
import { FinderApplicationPrivateModuleUpdateSettings, PreferenceValue } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import { getFinancingApplicationMarket } from '../StandardApplicationModulePage/StandardApplicationModuleMainDetails';
import { hasAppointmentScenario, hasPaymentScenario } from '../modules/implementations/shared';
import {
    getDefaultDealershipPaymentSettingValue,
    processDealershipPaymentSettingInput,
} from '../modules/implementations/shared/getDealershipPaymentSetting';
import CounterSettingDetails from '../shared/CounterSettingDetails';
import { getValidator } from './FinderApplicationModuleConfiguration';
import FinderApplicationModuleConfigurationForm from './FinderApplicationModuleConfigurationForm';
import { defaultDiscalimers } from './shared';
import {
    Disclaimer,
    FinderApplicationModuleCounterKey,
    type FinderApplicationPrivateModuleUpdateFormValues,
} from './type';

const counterFields: FinderApplicationModuleCounterKey[] = [
    'leadCounter',
    'applicationCounter',
    'reservationCounter',
    'appointmentCounter',
    'insuranceCounter',
];

type FinderApplicationModuleConfigurationProps = {
    module: FinderApplicationPrivateModuleSpecsFragment;
};

const FinderApplicationModuleConfiguration = ({ module }: FinderApplicationModuleConfigurationProps) => {
    const { t } = useTranslation('finderApplicationModuleDetails');
    const apolloClient = useApolloClient();
    const validate = useValidator(getValidator(false));

    const initialValues: FinderApplicationPrivateModuleUpdateFormValues = useMemo(
        () => ({
            ...pick(
                [
                    'displayName',
                    'testDrive',
                    'tradeIn',
                    'isTradeInAmountVisible',
                    'scenarios',
                    'depositAmount',
                    'myInfoSettingId',
                    'liveChatSettingId',
                    'priceDisclaimer',
                    'reservationInstructions',
                    'promoCodeModuleId',
                    'market',
                    'showResetKYCButton',
                    'showFromValueOnVehicleDetails',
                    'bankDisplayPreference',
                    'finderVehicleConditions',
                    'porscheApprovedInfo',
                    'displayAppointmentDatepicker',
                    'appointmentModuleId',
                    'insurerDisplayPreference',
                    'insurerIds',
                    'insuranceModuleId',
                    'financingPreference',
                    'isInsuranceOptional',
                    'displayFinanceCalculator',
                    'showInsuranceCalculator',
                    'isOcrEnabled',
                    'showVisitModelPageButton',
                    'modelPageUrl',
                    'reservationPeriod',
                    'skipForDeposit',
                    'showRemoteFlowButtonInKYCPage',
                    'hasDealerOptions',
                    'includeDealerOptionsForFinancing',
                    'flexibleDiscount',
                    'bankIds',
                    'capModuleId',
                    'isSearchCapCustomerOptional',
                    'capPrequalification',
                    'leadOrigin',
                    'leadMedium',
                    'leadCampaignId',
                    'useBankDisclaimers',
                    'useInsurerDisclaimers',
                    'allowSaveDraftAndReserveStock',
                    ...counterFields,
                ],
                module
            ),
            paymentSetting: hasPaymentScenario(module.scenarios)
                ? getDefaultDealershipPaymentSettingValue(module.paymentSetting)
                : null,
            disclaimers: !module.disclaimers
                ? defaultDiscalimers
                : uniqBy(
                      'type',
                      [
                          module.disclaimers.financingDisclaimer &&
                              ({
                                  type: 'financing',
                                  value: module.disclaimers.financingDisclaimer,
                              } as Disclaimer),
                          module.disclaimers.insuranceDisclaimer &&
                              ({
                                  type: 'insurance',
                                  value: module.disclaimers.insuranceDisclaimer,
                              } as Disclaimer),
                          ...defaultDiscalimers,
                      ].filter(Boolean)
                  ),
            marketType: getFinancingApplicationMarket(module.marketType),
            bankModuleId: module?.bankModule?.id,
        }),
        [module]
    );

    const onSubmit = useHandleError(
        async (values: FinderApplicationPrivateModuleUpdateFormValues) => {
            const { scenarios } = values;
            message.loading({
                content: t('finderApplicationModuleDetails:messages.submitting'),
                key: 'primary',
                duration: 0,
            });

            const settings: FinderApplicationPrivateModuleUpdateSettings = {
                ...pick(
                    [
                        'displayName',
                        'tradeIn',
                        'isTradeInAmountVisible',
                        'scenarios',
                        'myInfoSettingId',
                        'liveChatSettingId',
                        'priceDisclaimer',
                        'reservationInstructions',
                        'promoCodeModuleId',
                        'market',
                        'marketType',
                        'bankModuleId',
                        'showResetKYCButton',
                        'showFromValueOnVehicleDetails',
                        'bankDisplayPreference',
                        'finderVehicleConditions',
                        'porscheApprovedInfo',
                        'displayAppointmentDatepicker',
                        'appointmentModuleId',
                        'insurerDisplayPreference',
                        'insurerIds',
                        'insuranceModuleId',
                        'financingPreference',
                        'isInsuranceOptional',
                        'displayFinanceCalculator',
                        'showInsuranceCalculator',
                        'isOcrEnabled',
                        'showVisitModelPageButton',
                        'modelPageUrl',
                        'reservationPeriod',
                        'skipForDeposit',
                        'showRemoteFlowButtonInKYCPage',
                        'hasDealerOptions',
                        'includeDealerOptionsForFinancing',
                        'flexibleDiscount',
                        'bankIds',
                        'capModuleId',
                        'isSearchCapCustomerOptional',
                        'capPrequalification',
                        'leadOrigin',
                        'leadMedium',
                        'leadCampaignId',
                        'useBankDisclaimers',
                        'useInsurerDisclaimers',
                        'allowSaveDraftAndReserveStock',
                    ],
                    values
                ),
                disclaimers: {
                    financingDisclaimer: values.disclaimers.find(disclaimer => disclaimer.type === 'financing')?.value,
                    insuranceDisclaimer: values.disclaimers.find(disclaimer => disclaimer.type === 'insurance')?.value,
                },
                testDrive: hasAppointmentScenario(scenarios),
                depositAmount: null,
                bankModuleId:
                    values.displayFinanceCalculator === PreferenceValue.Yes ||
                    values.displayFinanceCalculator === PreferenceValue.Optional
                        ? values.bankModuleId
                        : null,
                capModuleId: values.capModuleId || null,
            };

            if (hasPaymentScenario(scenarios)) {
                // assign value if deposit
                settings.paymentSetting = processDealershipPaymentSettingInput(values.paymentSetting);
                settings.depositAmount = values.depositAmount;
            }

            await apolloClient
                .mutate<
                    UpdateFinderApplicationPrivateModuleMutation,
                    UpdateFinderApplicationPrivateModuleMutationVariables
                >({
                    mutation: UpdateFinderApplicationPrivateModuleDocument,
                    variables: { moduleId: module.id, settings },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            message.success({
                content: t('finderApplicationModuleDetails:messages.success'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateForm" name="updateForm" onSubmitCapture={handleSubmit}>
                    <FinderApplicationModuleConfigurationForm module={module} />
                    <CounterSettingDetails names={counterFields} />
                </Form>
            )}
        </Formik>
    );
};

export default FinderApplicationModuleConfiguration;
