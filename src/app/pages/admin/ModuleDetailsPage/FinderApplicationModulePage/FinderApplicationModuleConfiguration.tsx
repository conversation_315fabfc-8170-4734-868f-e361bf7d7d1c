/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { get, pick, uniqBy } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FinderApplicationPrivateModuleSpecsFragment } from '../../../../api/fragments/FinderApplicationPrivateModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragment } from '../../../../api/fragments/FinderApplicationPublicModuleSpecs';
import {
    UpdateFinderApplicationPublicModuleDocument,
    UpdateFinderApplicationPublicModuleMutation,
    UpdateFinderApplicationPublicModuleMutationVariables,
} from '../../../../api/mutations/updateFinderApplicationModule';
import {
    ApplicationMarket,
    EventLeadOriginType,
    EventMediumType,
    FinderApplicationModuleUpdateSettings,
    ModuleType,
    PreferenceValue,
} from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import { hasValue } from '../../../../utilities/fp';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { getFinancingApplicationMarket } from '../StandardApplicationModulePage/StandardApplicationModuleMainDetails';
import { hasAppointmentScenario, hasPaymentScenario } from '../modules/implementations/shared';
import {
    getDefaultDealershipPaymentSettingValue,
    processDealershipPaymentSettingInput,
} from '../modules/implementations/shared/getDealershipPaymentSetting';
import CounterSettingDetails from '../shared/CounterSettingDetails';
import { newZealandMarketValidator, singaporeMarketValidator } from '../shared/validators';
import FinderApplicationModuleConfigurationForm from './FinderApplicationModuleConfigurationForm';
import { defaultDiscalimers } from './shared';
import { Disclaimer, FinderApplicationModuleConfigurationFormValues, FinderApplicationModuleCounterKey } from './type';

const counterFields: FinderApplicationModuleCounterKey[] = [
    'leadCounter',
    'applicationCounter',
    'reservationCounter',
    'appointmentCounter',
    'insuranceCounter',
];

type FinderModuleType = FinderApplicationPublicModuleSpecsFragment | FinderApplicationPrivateModuleSpecsFragment;

type FinderApplicationModuleConfigurationProps = {
    module: FinderModuleType;
};

const isFinderApplicationModuleUpdateSettings = (obj: any): obj is FinderApplicationModuleUpdateSettings =>
    typeof obj === 'object' && obj !== null;

export const getValidator = (isPublic: boolean) =>
    validators.compose(
        validators.requiredNonEmptyString('displayName'),
        validators.only(
            values => hasPaymentScenario(get('scenarios', values)),
            validators.compose(
                validators.requiredString('paymentSetting.defaultId'),
                validators.requiredNumber('depositAmount.defaultValue')
            )
        ),
        validators.only(
            values => hasAppointmentScenario(values?.scenarios),
            validators.requiredString('appointmentModuleId')
        ),
        validators.only(values => hasValue('bankModuleId')(values), validators.requiredString('market')),
        validators.only(values => {
            const market = get('market', values);
            const marketType = get('marketType', values);

            return market === ApplicationMarket.Singapore && !!marketType;
        }, singaporeMarketValidator),
        validators.only(values => {
            const showInsuranceCalculator = get('showInsuranceCalculator', values);

            return showInsuranceCalculator;
        }, validators.requiredString('insuranceModuleId')),
        validators.only(values => {
            const displayFinanceCalculator = get('displayFinanceCalculator', values);

            return (
                displayFinanceCalculator === PreferenceValue.Yes ||
                displayFinanceCalculator === PreferenceValue.Optional
            );
        }, validators.requiredString('bankModuleId')),
        validators.only(values => {
            const market = get('market', values);
            const marketType = get('marketType', values);

            return market === ApplicationMarket.NewZealand && !!marketType;
        }, newZealandMarketValidator),
        validators.only(values => {
            const showVisitModelPageButton = get('showVisitModelPageButton', values);

            return showVisitModelPageButton === true;
        }, validators.requiredString('modelPageUrl')),
        validators.requiredArray('scenarios', true),
        validators.requiredBoolean('isOcrEnabled'),
        validators.requiredBoolean('tradeIn'),
        validators.only(() => isPublic, validators.requiredString('assignee.defaultId')),
        validators.requiredBoolean('showResetKYCButton'),
        validators.only(
            ({ capModuleId }) => capModuleId,
            validators.compose(
                validators.requiredBoolean('capPrequalification'),
                validators.requiredStringEnum('leadOrigin', EventLeadOriginType),
                validators.requiredStringEnum('leadMedium', EventMediumType),
                validators.only(
                    ({ leadMedium }) => leadMedium !== EventMediumType.Walkin,
                    validators.requiredString('leadCampaignId')
                )
            )
        )
    );

const getInitialPorscheIdIntegrationValues = (module: FinderModuleType) => {
    if (module.__typename === ModuleType.FinderApplicationPublicModule) {
        return {
            isPorscheIdLoginMandatory: module.isPorscheIdLoginMandatory,
            isCustomerDataRetreivalByPorscheId: module.isCustomerDataRetreivalByPorscheId,
        };
    }

    return {};
};

const FinderApplicationModuleConfiguration = ({ module }: FinderApplicationModuleConfigurationProps) => {
    const { t } = useTranslation('finderApplicationModuleDetails');
    const apolloClient = useApolloClient();
    const validate = useValidator(getValidator(module.__typename === 'FinderApplicationPublicModule'));

    const initialValues: FinderApplicationModuleConfigurationFormValues = useMemo(
        () => ({
            ...pick(
                [
                    'displayName',
                    'testDrive',
                    'tradeIn',
                    'isTradeInAmountVisible',
                    'scenarios',
                    'depositAmount',
                    'myInfoSettingId',
                    'liveChatSettingId',
                    'priceDisclaimer',
                    'reservationInstructions',
                    'promoCodeModuleId',
                    'market',
                    'showResetKYCButton',
                    'showFromValueOnVehicleDetails',
                    'bankDisplayPreference',
                    'finderVehicleConditions',
                    'porscheApprovedInfo',
                    'displayAppointmentDatepicker',
                    'appointmentModuleId',
                    'insurerDisplayPreference',
                    'insurerIds',
                    'insuranceModuleId',
                    'financingPreference',
                    'isInsuranceOptional',
                    'displayFinanceCalculator',
                    'showInsuranceCalculator',
                    'isOcrEnabled',
                    'showVisitModelPageButton',
                    'modelPageUrl',
                    'reservationPeriod',
                    'skipForDeposit',
                    'bankIds',
                    'capModuleId',
                    'isSearchCapCustomerOptional',
                    'capPrequalification',
                    'leadOrigin',
                    'leadMedium',
                    'leadCampaignId',
                    'useBankDisclaimers',
                    'useInsurerDisclaimers',
                    ...counterFields,
                ],
                module
            ),
            paymentSetting: hasPaymentScenario(module.scenarios)
                ? getDefaultDealershipPaymentSettingValue(module.paymentSetting)
                : null,
            disclaimers: !module.disclaimers
                ? defaultDiscalimers
                : uniqBy(
                      'type',
                      [
                          module.disclaimers.financingDisclaimer &&
                              ({
                                  type: 'financing',
                                  value: module.disclaimers.financingDisclaimer,
                              } as Disclaimer),
                          module.disclaimers.insuranceDisclaimer &&
                              ({
                                  type: 'insurance',
                                  value: module.disclaimers.insuranceDisclaimer,
                              } as Disclaimer),
                          ...defaultDiscalimers,
                      ].filter(Boolean)
                  ),
            marketType: getFinancingApplicationMarket(module.marketType),
            bankModuleId: module?.bankModule?.id,
            ...(module.__typename === 'FinderApplicationPublicModule' ? { assignee: module.assignee } : null),
            ...getInitialPorscheIdIntegrationValues(module),
        }),
        [module]
    );

    const onSubmit = useHandleError(
        async (values: FinderApplicationModuleConfigurationFormValues) => {
            const { scenarios } = values;
            message.loading({
                content: t('finderApplicationModuleDetails:messages.submitting'),
                key: 'primary',
                duration: 0,
            });

            const settings: FinderApplicationModuleUpdateSettings = {
                ...pick(
                    [
                        'displayName',
                        'tradeIn',
                        'isTradeInAmountVisible',
                        'scenarios',
                        'myInfoSettingId',
                        'liveChatSettingId',
                        'priceDisclaimer',
                        'reservationInstructions',
                        'promoCodeModuleId',
                        'market',
                        'marketType',
                        'bankModuleId',
                        'showResetKYCButton',
                        'showFromValueOnVehicleDetails',
                        'bankDisplayPreference',
                        'finderVehicleConditions',
                        'porscheApprovedInfo',
                        'displayAppointmentDatepicker',
                        'appointmentModuleId',
                        'insurerDisplayPreference',
                        'insurerIds',
                        'insuranceModuleId',
                        'financingPreference',
                        'isInsuranceOptional',
                        'displayFinanceCalculator',
                        'showInsuranceCalculator',
                        'isOcrEnabled',
                        'showVisitModelPageButton',
                        'modelPageUrl',
                        'reservationPeriod',
                        'skipForDeposit',
                        'bankIds',
                        'capModuleId',
                        'isSearchCapCustomerOptional',
                        'capPrequalification',
                        'leadOrigin',
                        'leadMedium',
                        'leadCampaignId',
                        'useBankDisclaimers',
                        'useInsurerDisclaimers',
                    ],
                    values
                ),
                disclaimers: {
                    financingDisclaimer: values.disclaimers.find(disclaimer => disclaimer.type === 'financing')?.value,
                    insuranceDisclaimer: values.disclaimers.find(disclaimer => disclaimer.type === 'insurance')?.value,
                },
                assignee: isFinderApplicationModuleUpdateSettings(values) ? values.assignee : null,
                testDrive: hasAppointmentScenario(scenarios),
                depositAmount: null,
                bankModuleId:
                    values.displayFinanceCalculator === PreferenceValue.Yes ||
                    values.displayFinanceCalculator === PreferenceValue.Optional
                        ? values.bankModuleId
                        : null,
                capModuleId: values.capModuleId || null,
                isPorscheIdLoginMandatory: values.isPorscheIdLoginMandatory,
                isCustomerDataRetreivalByPorscheId: values.isCustomerDataRetreivalByPorscheId,
            };

            if (hasPaymentScenario(scenarios)) {
                // assign value if deposit
                settings.paymentSetting = processDealershipPaymentSettingInput(values.paymentSetting);
                settings.depositAmount = values.depositAmount;
            }

            await apolloClient
                .mutate<
                    UpdateFinderApplicationPublicModuleMutation,
                    UpdateFinderApplicationPublicModuleMutationVariables
                >({
                    mutation: UpdateFinderApplicationPublicModuleDocument,
                    variables: { moduleId: module.id, settings },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            message.success({
                content: t('finderApplicationModuleDetails:messages.success'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateForm" name="updateForm" onSubmitCapture={handleSubmit}>
                    <FinderApplicationModuleConfigurationForm module={module} />
                    <CounterSettingDetails names={counterFields} />
                </Form>
            )}
        </Formik>
    );
};

export default FinderApplicationModuleConfiguration;
