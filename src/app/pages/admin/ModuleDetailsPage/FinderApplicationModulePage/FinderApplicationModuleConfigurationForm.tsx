/* eslint-disable max-len */
import { Col, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { isEqual, isNil, uniqBy } from 'lodash/fp';
import { useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FinderApplicationPrivateModuleSpecsFragment } from '../../../../api/fragments/FinderApplicationPrivateModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragment } from '../../../../api/fragments/FinderApplicationPublicModuleSpecs';
import { EventMediumType, FinderVehicleCondition, ModuleType, PreferenceValue } from '../../../../api/types';
import CascaderField from '../../../../components/fields/CascaderField';
import DealerDepositAmountField from '../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipCascaderField from '../../../../components/fields/DealershipFields/DealershipCascaderField';
import DealershipPriceDisclaimerField from '../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import DealershipSelectField from '../../../../components/fields/DealershipFields/DealershipSelectField';
import DealershipTranslatedMarkdownField from '../../../../components/fields/DealershipFields/DealershipTranslatedMarkdown';
import InputField from '../../../../components/fields/InputField';
import InputNumberField from '../../../../components/fields/InputNumberField';
import SelectField from '../../../../components/fields/SelectField';
import SwitchField from '../../../../components/fields/SwitchField';
import TranslatedMarkdownField from '../../../../components/fields/TranslatedMarkdownField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import { TranslationFieldType } from '../../../../utilities/common';
import useEventOptions from '../../../../utilities/useEventOptions';
import useLeadGenTooltip from '../../../../utilities/useLeadGenTooltip';
import useMyInfoOption from '../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import useTooltip from '../../../../utilities/useTooltip';
import useBankOptions from '../../FinanceProductDetailsPage/shared/useBankOptions';
import useScenarioObserver from '../StandardApplicationModulePage/useScenarioObserver';
import useScenarioOptions from '../StandardApplicationModulePage/useScenarioOptions';
import DisclaimerFieldArray, {
    DisclaimerFieldArrayFieldArrayProps,
} from '../modules/implementations/shared/Disclaimers';
import FlexibleDiscount from '../modules/implementations/shared/FlexibleDiscount';
import MarketTypeGroup from '../modules/implementations/shared/MarketTypeGroup';
import useDisplayPreferenceOptions from '../modules/implementations/shared/useDisplayPreferenceOptions';
import useFinancingPreferenceOptions from '../modules/implementations/shared/useFinancingPreferenceOptions';
import useFinderVehicleConditionOptions from '../modules/implementations/shared/useFinderVehicleConditionOptions';
import useInsurerOptions from '../modules/implementations/shared/useInsurerOptions';
import useModuleOptions from '../modules/implementations/shared/useModuleOptions';
import usePreferenceValueOptions from '../modules/implementations/shared/usePreferenceValues';
import useSalesPersonOptions from '../modules/implementations/shared/useSalesPersonOptions';
import {
    Disclaimer,
    FinderApplicationModuleConfigurationFormValues,
    FinderApplicationPrivateModuleUpdateFormValues,
} from './type';

type DisclaimerTypeOption = DisclaimerFieldArrayFieldArrayProps['disclaimerTypeOptions'][0];

type FormValues = {
    disclaimers: Disclaimer[];
};

/**
 * This updates the disclaimers form value whenever there is a change in disclaimer type options.
 */
export const useUpdateDisclaimers = (disclaimerTypeOptions: DisclaimerTypeOption[]) => {
    const { initialValues, values, setFieldValue } = useFormikContext<FormValues>();
    const prevDisclaimerTypeOptionsRef = useRef(null);

    return useEffect(() => {
        if (!isEqual(disclaimerTypeOptions, prevDisclaimerTypeOptionsRef.current)) {
            prevDisclaimerTypeOptionsRef.current = disclaimerTypeOptions;

            const disclaimers = uniqBy('type', [...values.disclaimers, ...initialValues.disclaimers] as Disclaimer[]);

            const filtered = disclaimers.filter(disclaimer => {
                const found = disclaimerTypeOptions.find(option => option.value === disclaimer.type);

                return found;
            });
            setFieldValue('disclaimers', filtered);
        }
    }, [setFieldValue, initialValues.disclaimers, disclaimerTypeOptions]);
};

export const useDisclaimerTypeOptions = (
    useBankDisclaimers: boolean,
    useInsurerDisclaimers: boolean
): DisclaimerTypeOption[] =>
    useMemo(
        () =>
            [
                !useBankDisclaimers &&
                    ({
                        label: 'Finance',
                        value: 'financing',
                    } as DisclaimerTypeOption),
                !useInsurerDisclaimers &&
                    ({
                        label: 'Insurance',
                        value: 'insurance',
                    } as DisclaimerTypeOption),
            ].filter(Boolean),
        [useBankDisclaimers, useInsurerDisclaimers]
    );

type FinderApplicationModuleConfigurationFormProps = {
    module: FinderApplicationPublicModuleSpecsFragment | FinderApplicationPrivateModuleSpecsFragment;
};

const FinderApplicationModuleConfigurationForm = ({ module }: FinderApplicationModuleConfigurationFormProps) => {
    const { t } = useTranslation(['finderApplicationModuleDetails', 'moduleDetails', 'common']);

    const { yesNoSwitch } = useSystemSwitchData();
    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();

    const { values, setFieldValue } = useFormikContext<FinderApplicationModuleConfigurationFormValues>();
    const options = useModuleOptions(module.company, { addNoneOptions: { promoCodes: true, payment: true } });

    const disclaimerTypeOptions = useDisclaimerTypeOptions(values.useBankDisclaimers, values.useInsurerDisclaimers);
    useUpdateDisclaimers(disclaimerTypeOptions);

    const { eventLeadOriginTypeOptions, eventMediumTypeOptions } = useEventOptions();
    const definedFieldsTooltip = useLeadGenTooltip('definedFields');

    const displayPreferenceOptions = useDisplayPreferenceOptions();
    const preferenceOptions = usePreferenceValueOptions();
    const financingPreferenceOptions = useFinancingPreferenceOptions();
    const { insurerList, loading: insurerLoading } = useInsurerOptions(module.company.id);
    const { options: bankOptions } = useBankOptions();

    const { salesPersonOptions, loading } = useSalesPersonOptions(module.company);

    const reservationPeriodToolTip = useTooltip('reservationPeriodToolTip', 'finderApplicationModuleDetails');

    const salesPersonOptionsWithNone = useMemo(
        () => [
            {
                value: null,
                label: t('common:options.none'),
            },
            ...salesPersonOptions,
        ],
        [salesPersonOptions, t]
    );

    const finderVehicleConditionOptions = useFinderVehicleConditionOptions();

    const { scenarioOptions } = useScenarioOptions();
    const scenario = useScenarioObserver(ModuleType.FinderApplicationPublicModule);

    const isPrivateAccess = useMemo(() => module.__typename === 'FinderApplicationPrivateModule', [module]);

    const hasPorscheIdModule = useMemo(
        () => module.company.modules.some(module => module.__typename === ModuleType.PorscheIdModule),
        [module.company.modules]
    );

    useEffect(() => {
        if (values.isCustomerDataRetreivalByPorscheId && isNil(values.isPorscheIdLoginMandatory)) {
            setFieldValue('isPorscheIdLoginMandatory', true);
        }

        if (!values.isCustomerDataRetreivalByPorscheId) {
            setFieldValue('isPorscheIdLoginMandatory', null);
        }
    }, [setFieldValue, values.isCustomerDataRetreivalByPorscheId, values.isPorscheIdLoginMandatory]);

    useEffect(() => {
        if (!isPrivateAccess) {
            setFieldValue('isSearchCapCustomerOptional', true);
        }
    }, [isPrivateAccess, setFieldValue]);

    return (
        <CollapsibleWrapper defaultActiveKey="finderApplicationModuleDetails">
            <Panel
                key="finderApplicationModuleDetails"
                header={t('finderApplicationModuleDetails:subTitles.mainDetails')}
            >
                <Row gutter={10}>
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('finderApplicationModuleDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.isOcrEnabled', { returnObjects: true })}
                            name="isOcrEnabled"
                        />
                    </Col>
                    {hasMyInfoModule && (
                        <Col md={8} xs={24}>
                            <CascaderField
                                {...t('finderApplicationModuleDetails:fields.myInfoSetting', {
                                    returnObjects: true,
                                })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {options.cap.length ? (
                        <Col md={8} xs={24}>
                            <SelectField
                                allowClear
                                {...t('moduleDetails:fields.capModuleId', { returnObjects: true })}
                                name="capModuleId"
                                onChange={value => {
                                    if (!value || !values.capPrequalification) {
                                        setFieldValue('isSearchCapCustomerOptional', false);
                                        setFieldValue('capPrequalification', false);
                                    }
                                }}
                                options={options.cap}
                                showSearch
                            />
                        </Col>
                    ) : null}

                    {values.capModuleId && (
                        <>
                            {isPrivateAccess && (
                                <Col md={8} xs={24}>
                                    <SwitchField
                                        {...yesNoSwitch}
                                        {...t('moduleDetails:fields.isSearchCapCustomerOptional', {
                                            returnObjects: true,
                                        })}
                                        name="isSearchCapCustomerOptional"
                                    />
                                </Col>
                            )}

                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.prequalification', { returnObjects: true })}
                                    name="capPrequalification"
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.capLeadOrigin', { returnObjects: true })}
                                    name="leadMedium"
                                    options={eventMediumTypeOptions}
                                    tooltip={definedFieldsTooltip}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <SelectField
                                    {...t('moduleDetails:fields.capLeadSource', { returnObjects: true })}
                                    name="leadOrigin"
                                    options={eventLeadOriginTypeOptions}
                                    tooltip={definedFieldsTooltip}
                                    required
                                    showSearch
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <InputField
                                    {...t('moduleDetails:fields.capCampaignId', { returnObjects: true })}
                                    name="leadCampaignId"
                                    required={values.leadMedium !== EventMediumType.Walkin}
                                />
                            </Col>
                        </>
                    )}

                    {module.__typename === 'FinderApplicationPublicModule' && hasPorscheIdModule && (
                        <>
                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...t('moduleDetails:fields.isCustomerDataRetreivalByPorscheId', {
                                        returnObjects: true,
                                    })}
                                    {...yesNoSwitch}
                                    name="isCustomerDataRetreivalByPorscheId"
                                />
                            </Col>
                            {values.isCustomerDataRetreivalByPorscheId && (
                                <Col md={8} xs={24}>
                                    <SwitchField
                                        {...t('moduleDetails:fields.isPorscheIdLoginMandatory', {
                                            returnObjects: true,
                                        })}
                                        {...yesNoSwitch}
                                        name="isPorscheIdLoginMandatory"
                                    />
                                </Col>
                            )}
                        </>
                    )}

                    <Col md={8} xs={24}>
                        <CascaderField
                            {...t('finderApplicationModuleDetails:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('finderApplicationModuleDetails:fields.scenario', { returnObjects: true })}
                            mode="multiple"
                            name="scenarios"
                            options={scenarioOptions}
                            showSearch
                        />
                    </Col>

                    {scenario.hasPayment && (
                        <>
                            <Col md={8} xs={24}>
                                <DealershipCascaderField
                                    {...t('finderApplicationModuleDetails:fields.paymentSetting', {
                                        returnObjects: true,
                                    })}
                                    name="paymentSetting"
                                    options={options?.payment || []}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <DealerDepositAmountField
                                    {...t('finderApplicationModuleDetails:fields.depositAmount', {
                                        returnObjects: true,
                                    })}
                                    name="depositAmount"
                                />
                            </Col>
                        </>
                    )}
                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('finderApplicationModuleDetails:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>

                    {(values.showInsuranceCalculator || scenario.hasInsurance || scenario.hasLeadCapture) && (
                        <Col md={8} xs={24}>
                            <SelectField
                                required
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasInsurance && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurer', { returnObjects: true })}
                                loading={insurerLoading}
                                mode="multiple"
                                name="insurerIds"
                                options={insurerList}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasFinancing && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.bank', { returnObjects: true })}
                                mode="multiple"
                                name="bankIds"
                                options={bankOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                                name="appointmentModuleId"
                                options={options.appointments}
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && values.appointmentModuleId && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.displayAppointmentDatepicker', { returnObjects: true })}
                                name="displayAppointmentDatepicker"
                            />
                        </Col>
                    )}

                    {!module.bankModuleId &&
                        (values.displayFinanceCalculator === PreferenceValue.Yes ||
                            values.displayFinanceCalculator === PreferenceValue.Optional) && (
                            <Col md={8} xs={24}>
                                <SelectField
                                    {...t('finderApplicationModuleDetails:fields.financingModule', {
                                        returnObjects: true,
                                    })}
                                    name="bankModuleId"
                                    options={options.financing}
                                    showSearch
                                />
                            </Col>
                        )}

                    {module.__typename === 'FinderApplicationPublicModule' && (
                        <Col md={8} xs={24}>
                            <DealershipSelectField
                                {...t('finderApplicationModuleDetails:fields.assignee', { returnObjects: true })}
                                companyId={module.companyId}
                                dealerOptions={salesPersonOptionsWithNone}
                                loading={loading}
                                name="assignee"
                                options={salesPersonOptions}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('finderApplicationModuleDetails:fields.tradeIn', { returnObjects: true })}
                            name="tradeIn"
                        />
                    </Col>
                    {values?.tradeIn && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isTradeInAmountVisible', { returnObjects: true })}
                                name="isTradeInAmountVisible"
                            />
                        </Col>
                    )}

                    <MarketTypeGroup name="market" required />

                    <Col md={8} xs={24}>
                        <DealershipTranslatedMarkdownField
                            {...t('moduleDetails:fields.reservationInstructions', { returnObjects: true })}
                            name="reservationInstructions"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <InputNumberField
                            {...t('moduleDetails:fields.reservationPeriod', { returnObjects: true })}
                            addonAfter={t('moduleDetails:suffix.inWorkingDays')}
                            name="reservationPeriod"
                            tooltip={reservationPeriodToolTip}
                            required
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <DealershipPriceDisclaimerField
                            {...t('moduleDetails:fields.priceDisclaimerTitle', { returnObjects: true })}
                            name="priceDisclaimer"
                            isMultipleEntry
                        />
                    </Col>

                    {scenario.hasFinancing && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.financingPreference', { returnObjects: true })}
                                disabled={scenario.hasOnlyFinancing}
                                name="financingPreference"
                                options={financingPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {isPrivateAccess && (
                        <>
                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.hasDealerOptions', { returnObjects: true })}
                                    name="hasDealerOptions"
                                />
                            </Col>

                            {(values as FinderApplicationPrivateModuleUpdateFormValues).hasDealerOptions && (
                                <Col md={8} xs={24}>
                                    <SwitchField
                                        {...yesNoSwitch}
                                        {...t('moduleDetails:fields.includeDealerOptionsForFinancing', {
                                            returnObjects: true,
                                        })}
                                        name="includeDealerOptionsForFinancing"
                                    />
                                </Col>
                            )}
                        </>
                    )}

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.showFinanceCalculator', { returnObjects: true })}
                            disabled={scenario.hasFinancing}
                            name="displayFinanceCalculator"
                            options={preferenceOptions}
                            showSearch
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.useBankDisclaimers', { returnObjects: true })}
                            name="useBankDisclaimers"
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isInsuranceOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyInsurance}
                                name="isInsuranceOptional"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showInsuranceCalculator', { returnObjects: true })}
                            disabled={scenario.hasInsurance}
                            name="showInsuranceCalculator"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.useInsurerDisclaimers', { returnObjects: true })}
                            name="useInsurerDisclaimers"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFromValueOnVehicleDetails', { returnObjects: true })}
                            name="showFromValueOnVehicleDetails"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayPreference', { returnObjects: true })}
                            name="bankDisplayPreference"
                            options={displayPreferenceOptions}
                            showSearch
                        />
                    </Col>

                    {values.insurerIds.length >= 1 && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurerDisplayPreference', { returnObjects: true })}
                                name="insurerDisplayPreference"
                                options={displayPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.finderVehicleCondition', { returnObjects: true })}
                            mode="multiple"
                            name="finderVehicleConditions"
                            options={finderVehicleConditionOptions}
                            showSearch
                        />
                    </Col>
                    {values.finderVehicleConditions.includes(FinderVehicleCondition.PorscheApproved) && (
                        <Col md={8} xs={24}>
                            <TranslatedMarkdownField
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    'moduleDetails:fields.porscheApprovedInfo',
                                    { returnObjects: true }
                                )}
                                name="porscheApprovedInfo"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showVisitModelPageButton', { returnObjects: true })}
                            name="showVisitModelPageButton"
                        />
                    </Col>

                    {values.showVisitModelPageButton && (
                        <Col md={8} xs={24}>
                            <InputField
                                {...t('moduleDetails:fields.modelPageUrl', { returnObjects: true })}
                                name="modelPageUrl"
                                required
                            />
                        </Col>
                    )}

                    {isPrivateAccess && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.showRemoteFlowButtonInKYCPage', {
                                    returnObjects: true,
                                })}
                                name="showRemoteFlowButtonInKYCPage"
                            />
                        </Col>
                    )}

                    {scenario.hasPayment && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.skipForDeposit', { returnObjects: true })}
                                name="skipForDeposit"
                            />
                        </Col>
                    )}

                    {isPrivateAccess && <FlexibleDiscount name="flexibleDiscount" />}

                    {isPrivateAccess && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('finderApplicationModuleDetails:fields.allowSaveDraftAndReserveStock', {
                                    returnObjects: true,
                                })}
                                name="allowSaveDraftAndReserveStock"
                            />
                        </Col>
                    )}

                    {(!values.useBankDisclaimers || !values.useInsurerDisclaimers) && (
                        <Col span={24}>
                            <DisclaimerFieldArray
                                disclaimerTypeOptions={disclaimerTypeOptions}
                                market={values.market}
                                name="disclaimers"
                            />
                        </Col>
                    )}
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default FinderApplicationModuleConfigurationForm;
