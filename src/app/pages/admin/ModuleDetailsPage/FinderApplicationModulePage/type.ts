import { FinderApplicationPrivateModuleSpecsFragment } from '../../../../api/fragments/FinderApplicationPrivateModuleSpecs';
import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments/UploadFileWithPreviewFormData';
import {
    ApplicationScenario,
    DealerDisclaimersConfiguratorInput,
    FinderApplicationModuleReminderEmailInput,
    FinderApplicationModuleSubmitOrderInput,
    FinderApplicationModuleUpdateSettings,
    FinderApplicationPrivateModuleUpdateSettings,
} from '../../../../api/types';

export enum HeaderTabs {
    Vehicles = 'vehicles',
    FinanceProducts = 'financeProducts',
    MainDetails = 'mainDetails',
    EmailContents = 'email',
}

export type EmailOverrideContent = {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
    scenarios: ApplicationScenario[];
    isActive: boolean;
};

export type FinderApplicationEmailWithOverrides<T> = {
    defaultValue: T;
    overrides: (T & EmailOverrideContent)[];
};

export type FinderApplicationModuleConfirmEmailContents = FinderApplicationModuleSubmitOrderInput & {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
};

export type FinderApplicationModuleReminderEmailContents = FinderApplicationModuleReminderEmailInput & {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
};

export type FinderApplicationEmailFormValues = {
    submitOrder: FinderApplicationEmailWithOverrides<FinderApplicationModuleConfirmEmailContents>;
    reminderEmail: FinderApplicationEmailWithOverrides<FinderApplicationModuleReminderEmailContents>;
};

export type FinderApplicationModuleCounterKey =
    | 'applicationCounter'
    | 'reservationCounter'
    | 'leadCounter'
    | 'appointmentCounter'
    | 'insuranceCounter';

export type Disclaimer = {
    type: 'financing' | 'insurance';
    value: DealerDisclaimersConfiguratorInput;
};

export type FinderApplicationPrivateModuleUpdateFormValues = Omit<
    FinderApplicationPrivateModuleUpdateSettings,
    'disclaimers'
> & {
    disclaimers: Disclaimer[];
};

export type FinderApplicationModuleConfigurationFormValues = (
    | Omit<
          FinderApplicationModuleUpdateSettings,
          'disclaimers' | 'isPorscheIdLoginMandatory' | 'isCustomerDataRetreivalByPorscheId'
      >
    | Omit<FinderApplicationPrivateModuleUpdateSettings, 'disclaimers'>
) &
    Partial<Pick<FinderApplicationPrivateModuleSpecsFragment, FinderApplicationModuleCounterKey>> & {
        disclaimers: Disclaimer[];
    } & Partial<
        Pick<FinderApplicationModuleUpdateSettings, 'isPorscheIdLoginMandatory' | 'isCustomerDataRetreivalByPorscheId'>
    >;
