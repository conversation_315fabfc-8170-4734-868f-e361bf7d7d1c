import { useApolloClient } from '@apollo/client';
import { useCallback } from 'react';
import { UploadedFileWithPreview } from '../../../api';
import { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';
import { UploadFileWithPreviewFormDataFragment } from '../../../api/fragments/UploadFileWithPreviewFormData';
import {
    DeleteStockAssetMutation,
    DeleteStockAssetMutationVariables,
    DeleteStockAssetDocument,
} from '../../../api/mutations/deleteStockAsset';
import {
    UploadStockAssetMutationVariables,
    UploadStockAssetDocument,
    UploadStockAssetMutation,
} from '../../../api/mutations/uploadStockAsset';
import { type StockFormValues } from './shared';

const useHandleStockAssets = (initialValues?: { stock: StockInventorySpecsFragment }) => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (stockId: string, values: { stock: StockFormValues }) => {
            if (values.stock.__typename !== 'MobilityStockInventory') {
                return;
            }

            let { images } = values.stock;
            images = images ?? [];

            const deletes = [];

            const imagesToUploadHaveIds = images.filter(
                (image: File | UploadFileWithPreviewFormDataFragment) => !(image instanceof File)
            ) as UploadedFileWithPreview[];

            const imageIdsToDelete = [];

            (initialValues?.stock?.__typename !== 'MobilityStockInventory' || !initialValues?.stock?.images
                ? []
                : initialValues.stock.images
            ).forEach(image => {
                if (image.id && !imagesToUploadHaveIds.find(imageToUpload => imageToUpload?.id === image.id)) {
                    imageIdsToDelete.push(image.id);
                }
            });

            for (const image of images) {
                if (image instanceof File) {
                    // one by one to keep the upload order
                    // eslint-disable-next-line no-await-in-loop
                    await apolloClient.mutate<UploadStockAssetMutation, UploadStockAssetMutationVariables>({
                        mutation: UploadStockAssetDocument,
                        variables: { stockId, upload: image },
                    });
                }
            }

            imageIdsToDelete.forEach(imageId => {
                deletes.push(
                    apolloClient.mutate<DeleteStockAssetMutation, DeleteStockAssetMutationVariables>({
                        mutation: DeleteStockAssetDocument,
                        variables: { stockId, uploadId: imageId },
                    })
                );
            });

            if (deletes.length) {
                await Promise.all(deletes);
            }
        },
        [apolloClient, initialValues]
    );
};

export default useHandleStockAssets;
