import { Button, message, Space, Tabs } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import * as permissionKind from '../../../../shared/permissions';
// eslint-disable-next-line max-len
import { InventoryDetailsWithPermissionsDataFragment } from '../../../api/fragments/InventoryDetailsWithPermissionsData';
import { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';
import {
    DeleteStockInventoryDocument,
    DeleteStockInventoryMutation,
    DeleteStockInventoryMutationVariables,
} from '../../../api/mutations/deleteStockInventory';
import { useUpdateStockInventoryMutation } from '../../../api/mutations/updateStockInventory';
import { GetInventoryDocument } from '../../../api/queries/getInventory';
import { useAccount } from '../../../components/contexts/AccountContextManager';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions, { PermissionValues } from '../../../utilities/hasPermissions';
import useFormattedSimpleVersioning from '../../../utilities/useFormattedSimpleVersioning';
import useHandleError from '../../../utilities/useHandleError';
import useTranslatedString from '../../../utilities/useTranslatedString';
import InventoryMainDetailsSection from '../InventoryDetailsPage/InventoryMainDetailsSection';
import useDeletionConfirm from '../VariantConfiguratorDetailsPage/useDeletionConfirm';
import ActivityLogTab from './ActivityLogTab';
import InventoryBlockPeriodSection from './InventoryBlockPeriodSection';
import InventoryStockDetailsSection from './InventoryStockDetailsForm';
import MobilityDetailsSection from './MobilityDetailsSection';
import useHandleStockAssets from './useHandleStockAssets';

enum TabKeys {
    Stock = 'Stock',
    ActivityLog = 'ActivityLog',
}

type InventoryStockInnerPageProps = {
    inventory: InventoryDetailsWithPermissionsDataFragment;
    stock: StockInventorySpecsFragment;
    stockNo: number;
    permissions: Pick<PermissionValues, 'hasUpdatePermission' | 'hasDeletePermission'>;
};

type InventoryStockFormProps = Omit<InventoryStockInnerPageProps, 'stockNo'>;
const InventoryStockForm = ({ inventory, stock, permissions }: InventoryStockFormProps) => {
    const { t } = useTranslation(['inventoryDetails']);
    const translate = useTranslatedString();
    const handleStockAssets = useHandleStockAssets({ stock });
    const account = useAccount();

    const [hasUpdatePermission, hasMobilityPermissions] = useMemo(
        () => [permissions.hasUpdatePermission, hasPermissions(account.permissions, [permissionKind.viewMobilities])],
        [account.permissions, permissions.hasUpdatePermission]
    );
    const [updateStockInventoryMutation] = useUpdateStockInventoryMutation();
    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: inventory.versioning,
        timeZone: inventory.module.company.timeZone,
    });

    const navigate = useNavigate();

    const onSubmit = useHandleError<{ stock: StockInventorySpecsFragment }>(
        async values => {
            message.loading({
                content: t('inventoryDetails:messages.updateStock.submitting'),
                key: 'primary',
                duration: 0,
            });

            await updateStockInventoryMutation({
                variables: {
                    id: stock.id,
                    inventoryId: inventory.id,
                    settings: {
                        vin: values.stock.vin,
                        reservationStatus:
                            values.stock.__typename === 'ConfiguratorStockInventory'
                                ? values.stock.reservationStatus
                                : null,
                        ...(values.stock.__typename === 'MobilityStockInventory' && {
                            period: values.stock.period,
                            price: values.stock.price,
                            blockPeriod: values.stock.blockPeriod.map(block => omit('period', block)),
                            isActive: values.stock.isActive,
                            mileage: values.stock.mileage,
                        }),
                    },
                },
            }).finally(() => {
                message.destroy('primary');
            });

            // handle stock asset upload/delete
            await handleStockAssets(stock.id, values);

            message.success({
                content: t('inventoryDetails:messages.updateStock.success'),
                key: 'primary',
            });
            navigate(-1);
        },
        [t, updateStockInventoryMutation, stock.id, inventory.id, handleStockAssets, navigate]
    );

    const initialValues = useMemo(
        () => ({
            ...inventory,
            lastModified: updated,
            moduleName: inventory.module.displayName,
            variantName: translate(inventory.variant.name),
            modelName: translate(inventory.model.name),
            subModelName: inventory.subModel?.name ? translate(inventory.subModel?.name) : '-',
            dealerName: inventory.dealer.displayName,
            packageSetting:
                inventory.__typename === 'ConfiguratorInventory'
                    ? {
                          ...inventory.packageSetting,
                          packageName: inventory.packageSetting?.packageName ?? '-',
                      }
                    : undefined,
            stock: {
                ...stock,
                period: stock.__typename === 'ConfiguratorStockInventory' ? undefined : stock.period,
                price: stock.__typename === 'MobilityStockInventory' && stock.price ? stock.price : inventory.price,
                blockPeriod:
                    stock.__typename === 'MobilityStockInventory'
                        ? stock.blockPeriod.map(block => ({
                              ...block,
                              // date
                              period: { __typename: 'Period', start: block.start, end: block.end },
                              // start time
                              start: block.start,
                              // end time
                              end: block.end,
                              comment: block.comment,
                          }))
                        : [],
            },
        }),
        [inventory, stock, translate, updated]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit}>
            {({ handleSubmit }) => (
                <Form id="inventoryDetails" name="inventoryDetails" onSubmitCapture={handleSubmit}>
                    <InventoryMainDetailsSection disabled={!hasUpdatePermission} isShowActive={false} offset={offset} />
                    <InventoryStockDetailsSection disabled={!hasUpdatePermission} offset={offset} />
                    {inventory.__typename === 'MobilityInventory' && (
                        <>
                            {hasMobilityPermissions && (
                                <MobilityDetailsSection stock={stock} vehicleId={inventory.variant.identifier} />
                            )}

                            <InventoryBlockPeriodSection
                                disabled={!hasUpdatePermission}
                                name="stock.blockPeriod"
                                stock={stock}
                            />
                        </>
                    )}
                </Form>
            )}
        </Formik>
    );
};

type TabbedContentsProps = InventoryStockFormProps & {
    currentTab: TabKeys;
};
const TabbedContents = ({ currentTab, inventory, stock, permissions }: TabbedContentsProps) => {
    switch (currentTab) {
        case TabKeys.Stock:
            return <InventoryStockForm inventory={inventory} permissions={permissions} stock={stock} />;

        case TabKeys.ActivityLog:
            return <ActivityLogTab inventoryId={inventory.id} stockId={stock.id} />;

        default:
            return null;
    }
};

const InventoryStockInnerPage = (props: Omit<InventoryStockInnerPageProps, 'permissions'>) => {
    const { inventory, stock, stockNo } = props;

    const { t } = useTranslation(['inventoryDetails']);

    const navigate = useNavigate();

    const deleteConfirmation = useDeletionConfirm<DeleteStockInventoryMutation, DeleteStockInventoryMutationVariables>(
        'inventoryDetails',
        'confirms.deleteStock',
        'messages.deleteStock'
    );

    const deleteStock = useCallback(() => {
        deleteConfirmation(
            {
                mutation: DeleteStockInventoryDocument,
                variables: {
                    id: stock.id,
                    inventoryId: inventory.id,
                },
                refetchQueries: [GetInventoryDocument],
            },
            () => {
                navigate(-1);
            }
        );
    }, [deleteConfirmation, inventory.id, navigate, stock.id]);

    const [currentTab, setCurrentTab] = useState(TabKeys.Stock);
    const onTabChange = useCallback((activeKey: TabKeys) => {
        setCurrentTab(activeKey);
    }, []);

    const footer = (
        <Tabs activeKey={currentTab} onChange={onTabChange}>
            <Tabs.TabPane key={TabKeys.Stock} tab={t('inventoryDetails:tabs.stock.stock')} />
            <Tabs.TabPane key={TabKeys.ActivityLog} tab={t('inventoryDetails:tabs.stock.activityLog')} />
        </Tabs>
    );

    const [hasUpdatePermission, hasDeletePermission] = useMemo(
        () => [
            hasPermissions(inventory.permissions, [permissionKind.updateInventory]),
            hasPermissions(inventory.permissions, [permissionKind.deleteInventory]),
        ],
        [inventory.permissions]
    );

    const stockActions = [
        <Space key="footer" size={8}>
            <Button key="cancel" onClick={() => navigate(-1)}>
                {t('inventoryDetails:actions.cancel')}
            </Button>
            {hasDeletePermission ? (
                <Button key="delete" onClick={deleteStock}>
                    {t('inventoryDetails:actions.delete')}
                </Button>
            ) : null}
            {hasUpdatePermission ? (
                <Button key="save" form="inventoryDetails" htmlType="submit" type="primary">
                    {t('inventoryDetails:actions.save')}
                </Button>
            ) : null}
        </Space>,
    ];
    const actions = currentTab === TabKeys.Stock ? stockActions : null;

    return (
        <ConsolePageWithHeader
            footer={actions}
            header={{ footer }}
            onBack={() => navigate(-1)}
            title={t('inventoryDetails:titles.stock', { stockNo })}
        >
            <TabbedContents
                currentTab={currentTab}
                inventory={inventory}
                permissions={{ hasDeletePermission, hasUpdatePermission }}
                stock={stock}
            />
        </ConsolePageWithHeader>
    );
};

export default InventoryStockInnerPage;
