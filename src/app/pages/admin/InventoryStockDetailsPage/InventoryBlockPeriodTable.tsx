import { DeleteOutlined } from '@ant-design/icons';
import { Table } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../themes/hooks';
import useFormatDate from '../../../utilities/useFormatDate';
import type { InventoryStockFormValues } from './shared';

const StyledIcon = styled.div`
    display: flex;
    justify-content: center;
    height: 100%;
    align-items: center;
`;
const InventoryBlockPeriodTable = ({ disabled = false }: { disabled?: boolean }) => {
    const { t } = useTranslation('inventoryDetails');
    const { Table: StyledTable } = useThemeComponents();
    const { values, setFieldValue } = useFormikContext<InventoryStockFormValues>();

    const stock = useMemo(() => {
        if (values.stock.__typename === 'ConfiguratorStockInventory') {
            return null;
        }

        return values.stock;
    }, [values.stock]);

    const formatDate = useFormatDate();
    const company = useCompany(true);

    const removeBlockPeriod = useCallback(
        index => {
            const updatedArray = stock.blockPeriod.filter((period, blockPeriodIndex) => index !== blockPeriodIndex);

            setFieldValue('stock.blockPeriod', updatedArray);
        },
        [setFieldValue, stock.blockPeriod]
    );

    return (
        <StyledTable
            dataSource={stock.blockPeriod}
            pagination={false}
            rowKey={(row, index: number) => `blockPeriod_${index.toString()}`}
        >
            <Table.Column
                dataIndex="start"
                render={(value, record) => formatDate({ date: value, timeZone: company?.timeZone, withOffset: true })}
                title={t('inventoryDetails:period.startTime.label')}
            />
            <Table.Column
                dataIndex="end"
                render={(value, record) => formatDate({ date: value, timeZone: company?.timeZone, withOffset: true })}
                title={t('inventoryDetails:period.endTime.label')}
            />
            <Table.Column dataIndex="comment" title={t('inventoryDetails:period.comment.label')} />
            <Table.Column
                key="actions"
                align="center"
                render={(value, record, index) =>
                    !disabled && (
                        <StyledIcon>
                            <DeleteOutlined
                                onClick={() => removeBlockPeriod(index)}
                                style={{ color: 'var(--ant-primary-color)' }}
                            />
                        </StyledIcon>
                    )
                }
                title={t('common:actions.action')}
                width={100}
            />
        </StyledTable>
    );
};

export default InventoryBlockPeriodTable;
