import { message } from 'antd';
import dayjs from 'dayjs';
import { Formik, FormikErrors } from 'formik';
import { FormEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StockInventorySpecsFragment } from '../../../../api/fragments/StockInventorySpecs';
import Form from '../../../../components/fields/Form';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { type InventoryStockFormValues } from '../shared';
import BlockPeriodForm from './BlockPeriodForm';
import { type BlockPeriodFormValues } from './shared';

export type BlockPeriodModalProps = {
    visible: boolean;
    close: () => void;
    values: InventoryStockFormValues;
    vehicleId: string;
    setFieldValue: (
        field: string,
        value: any,
        shouldValidate?: boolean
    ) => Promise<void | FormikErrors<InventoryStockFormValues>>;
    stock: StockInventorySpecsFragment;
};

const emptyBlockPeriod = {
    comment: '',
    period: { start: null, end: null },
    start: null,
    end: null,
};

const BlockPeriodModal = ({
    visible,
    close,
    values: parentValues,
    setFieldValue: parentSetFieldValue,
    vehicleId,
    stock,
}: BlockPeriodModalProps) => {
    const { t } = useTranslation('inventoryDetails');
    const { Modal } = useThemeComponents();

    const initialValue = useMemo(
        (): BlockPeriodFormValues => ({
            comment: '',
            period: { start: null, end: null },
            start: null,
            end: null,
        }),
        []
    );

    const addBlockPeriod = useHandleError<BlockPeriodFormValues>(
        async (values, helpers) => {
            if (parentValues.stock.__typename !== 'MobilityStockInventory') {
                throw new Error('mobilityStock not supported');
            }

            const concatStart = dayjs(values.period.start)
                .set('hour', dayjs(values.start).hour())
                .set('minute', dayjs(values.start).minute());

            const concatEnd = dayjs(values.period.end)
                .set('hour', dayjs(values.end).hour())
                .set('minute', dayjs(values.end).minute());

            parentSetFieldValue('stock.blockPeriod', [
                ...parentValues.stock.blockPeriod,
                {
                    comment: values.comment,
                    start: concatStart.toDate(),
                    end: concatEnd.toDate(),
                },
            ]);

            // reset the values after adding to the block period
            helpers.setValues({ comment: '', start: null, end: null, period: null });

            message.success({
                content: t('inventoryDetails:messages.blockPeriod.addBlockPeriod'),
                key: 'primary',
            });

            close();
        },
        [close, parentSetFieldValue, parentValues.stock, t]
    );

    const validate = useValidator(
        validators.compose(
            validators.requiredString('comment'),
            validators.custom('period', (value, values, errors, context) => {
                const { start, end } = value ?? {};
                const { start: startTime, end: endTime } = values;

                if (start && end && startTime && endTime) {
                    const concatStart = dayjs(start)
                        .set('hour', dayjs(startTime).hour())
                        .set('minute', dayjs(startTime).minute());

                    const concatEnd = dayjs(end)
                        .set('hour', dayjs(endTime).hour())
                        .set('minute', dayjs(endTime).minute());

                    if (concatStart.isValid() && concatEnd.isValid() && concatEnd.isAfter(concatStart)) {
                        return null;
                    }
                }

                return context.defaultMessages.requiredValue;
            })
        )
    );

    return (
        <Formik initialValues={initialValue} onSubmit={addBlockPeriod} validate={validate}>
            {({ handleSubmit, setValues }) => (
                <Modal
                    okButtonProps={{ htmlType: 'submit', form: 'blockPeriodForm' }}
                    onCancel={() => {
                        close();
                        setValues(emptyBlockPeriod);
                    }}
                    onOk={ev => handleSubmit(ev as unknown as FormEvent<HTMLFormElement>)}
                    open={visible}
                    title={t('inventoryDetails:modal.title')}
                >
                    <Form id="blockPeriodForm" name="blockPeriodForm" onSubmitCapture={handleSubmit}>
                        <BlockPeriodForm parentValues={parentValues} stock={stock} vehicleId={vehicleId} />
                    </Form>
                </Modal>
            )}
        </Formik>
    );
};

export const useBlockPeriodModal = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (
            values: InventoryStockFormValues,
            setFieldValue: (
                field: string,
                value: any,
                shouldValidate?: boolean
            ) => Promise<void | FormikErrors<InventoryStockFormValues>>,
            vehicleId: string,
            stock: StockInventorySpecsFragment
        ) => (
            <BlockPeriodModal
                close={actions.close}
                setFieldValue={setFieldValue}
                stock={stock}
                values={values}
                vehicleId={vehicleId}
                visible={visible}
            />
        ),
    };
};

export default BlockPeriodModal;
