import { Col } from 'antd';
import { PickerTimeProps } from 'antd/es/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { StockInventorySpecsFragment } from '../../../../api/fragments/StockInventorySpecs';
import { useThemeComponents } from '../../../../themes/hooks';
import { computeDefaultDisableTimeTimePicker } from '../../../portal/MobilityApplicationEntrypoint/helper';
import DatePickerType from '../../../portal/MobilityApplicationEntrypoint/shared';
import { type BlockPeriodFormValues } from './shared';

type BlockPeriodTimePickerProps = {
    typeTimePicker: DatePickerType;
    disabledTimeSlot: Record<number, number[]>;
    reservedTimeSlotRange: Record<string, Record<number, number[]>>;
    stock: Extract<StockInventorySpecsFragment, { __typename: 'MobilityStockInventory' }>;
    durationNextBooking: number;
    name: string;
    offset?: string;
};

const BlockPeriodTimePicker = ({
    typeTimePicker,
    disabledTimeSlot,
    reservedTimeSlotRange,
    stock,
    durationNextBooking,
    name,
    offset,
}: BlockPeriodTimePickerProps) => {
    const { t } = useTranslation(['inventoryDetails', 'common']);
    const { FormFields } = useThemeComponents();

    const { values, setFieldValue } = useFormikContext<BlockPeriodFormValues>();
    const disabledTime: PickerTimeProps<Dayjs>['disabledTime'] = useCallback(
        value =>
            computeDefaultDisableTimeTimePicker(
                values.period,
                values.start,
                values.end,
                typeTimePicker,
                disabledTimeSlot,
                reservedTimeSlotRange,
                stock,
                durationNextBooking
            ),
        [disabledTimeSlot, durationNextBooking, reservedTimeSlotRange, stock, typeTimePicker, values]
    );

    const onOpenChange = useCallback(
        open => {
            if (!open) {
                if (!isNil(values.start)) {
                    const concatStart = dayjs(values.period.start)
                        .set('hour', dayjs(values.start).hour())
                        .set('minute', dayjs(values.start).minute());
                    const period = {
                        ...values.period,
                        start: concatStart,
                    };
                    setFieldValue('period', period);
                }
                if (!isNil(values.end)) {
                    const concatEnd = dayjs(values.period.end)
                        .set('hour', dayjs(values.end).hour())
                        .set('minute', dayjs(values.end).minute());
                    const period = {
                        ...values.period,
                        end: concatEnd,
                    };

                    setFieldValue('period', period);
                }
            }
        },
        [setFieldValue, values.end, values.period, values.start]
    );

    return (
        <Col span={24}>
            <FormFields.TimePickerField
                disabledTime={disabledTime}
                format={t('common:formats.mobilityTimePicker')}
                name={name}
                onOpenChange={onOpenChange}
                {...t(`inventoryDetails:modal.${typeTimePicker === DatePickerType.Start ? 'start' : 'end'}`, {
                    returnObjects: true,
                })}
                minuteStep={15}
                offset={offset}
                showNow={false}
                hideDisabledOptions
                required
            />
        </Col>
    );
};

export default BlockPeriodTimePicker;
