import { Col } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useFormikContext } from 'formik';
import { merge } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { StockInventorySpecsFragment } from '../../../../api/fragments/StockInventorySpecs';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    calculateAvailaleHoursForNextBooking,
    calculateDisabledInventoryStockBlockPeriod,
    calculateDisabledTimeRange,
} from '../../../portal/MobilityApplicationEntrypoint/CarDetailsPage/BookingForm/RentalFields/shared';
import { computeDisabledDateDatePicker } from '../../../portal/MobilityApplicationEntrypoint/helper';
import DatePickerType from '../../../portal/MobilityApplicationEntrypoint/shared';
import { useInventory } from '../../InventoryDetailsPage/InventoryContext';
import { type InventoryStockFormValues } from '../shared';
import BlockPeriodTimePicker from './BlockPeriodTimePicker';
import { type BlockPeriodFormValues } from './shared';

type BlockPeriodFormProps = {
    vehicleId: string;
    stock: StockInventorySpecsFragment;
    parentValues: InventoryStockFormValues;
};

const BlockPeriodForm = ({ vehicleId, stock, parentValues }: BlockPeriodFormProps) => {
    const { t } = useTranslation(['inventoryDetails', 'common']);
    const { FormFields } = useThemeComponents();

    const { values, setFieldValue } = useFormikContext<BlockPeriodFormValues>();

    const inventory = useInventory();
    if (parentValues.stock.__typename !== 'MobilityStockInventory') {
        return null;
    }
    const { module } = inventory;
    if (module.__typename !== 'MobilityModule') {
        return null;
    }
    if (stock.__typename !== 'MobilityStockInventory') {
        return null;
    }
    const {
        availableNumberOfBookingRange,
        minimumAdvancedBooking,
        unavailableDayOfWeek,
        unavailableTimeRange,
        durationBeforeNextBooking,
    } = module;
    const disabledTimeSlot = calculateDisabledTimeRange(unavailableTimeRange);

    const reservedRange = calculateAvailaleHoursForNextBooking(
        stock.reservations,
        durationBeforeNextBooking,
        minimumAdvancedBooking,

        disabledTimeSlot
    );

    const offset = useMemo(() => dayjs().tz(module.company.timeZone).format('Z'), []);
    const blockPeriodsRange = calculateDisabledInventoryStockBlockPeriod(
        parentValues.stock.blockPeriod,
        durationBeforeNextBooking,
        minimumAdvancedBooking,

        disabledTimeSlot
    );

    const reservedTimeSlotRange = useMemo(
        () => merge(reservedRange, blockPeriodsRange),
        [blockPeriodsRange, reservedRange]
    );

    const onCalendarChange = useCallback(
        (value, dateStrings, info) => {
            const [start, end] = value ?? [];
            const rentalPeriod = {
                start: start ? dayjs(start).toDate() : undefined,
                end: end ? dayjs(end).toDate() : undefined,
            };

            setFieldValue('period', rentalPeriod);
        },
        [setFieldValue]
    );

    const disabledDate: RangePickerProps<Dayjs>['disabledDate'] = useCallback(
        value =>
            computeDisabledDateDatePicker(
                value,
                values.period,
                availableNumberOfBookingRange,
                unavailableDayOfWeek,
                unavailableTimeRange,
                durationBeforeNextBooking,
                stock,
                reservedTimeSlotRange
            ),
        [
            availableNumberOfBookingRange,
            durationBeforeNextBooking,
            reservedTimeSlotRange,
            stock,
            unavailableDayOfWeek,
            unavailableTimeRange,
            values,
        ]
    );

    return (
        <>
            <Col span={24}>
                <FormFields.RangePickerField
                    defaultValue={null}
                    disabled={[false, false]}
                    disabledDate={disabledDate}
                    label={t('inventoryDetails:modal.period.label')}
                    name="period"
                    onCalendarChange={onCalendarChange}
                    showTime={false}
                    readOnly
                    required
                />
            </Col>

            <BlockPeriodTimePicker
                disabledTimeSlot={disabledTimeSlot}
                durationNextBooking={durationBeforeNextBooking}
                name="start"
                offset={offset}
                reservedTimeSlotRange={reservedTimeSlotRange}
                stock={stock}
                typeTimePicker={DatePickerType.Start}
            />

            <BlockPeriodTimePicker
                disabledTimeSlot={disabledTimeSlot}
                durationNextBooking={durationBeforeNextBooking}
                name="end"
                offset={offset}
                reservedTimeSlotRange={reservedTimeSlotRange}
                stock={stock}
                typeTimePicker={DatePickerType.End}
            />

            <Col span={24}>
                <FormFields.InputField
                    name="comment"
                    {...t('inventoryDetails:modal.comment', { returnObjects: true })}
                    required
                />
            </Col>
        </>
    );
};

export default BlockPeriodForm;
