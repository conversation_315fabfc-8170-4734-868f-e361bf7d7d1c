import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';
import { useThemeComponents } from '../../../themes/hooks';
import { useInventory } from '../InventoryDetailsPage/InventoryContext';
import InventoryBlockPeriodTable from './InventoryBlockPeriodTable';
import { useBlockPeriodModal } from './modal/BlockPeriodModal';
import { type InventoryStockFormValues } from './shared';

type InventoryBlockPeriodSectionProps = {
    name: string;
    stock: StockInventorySpecsFragment;
    disabled?: boolean;
};
const StyledAddButton = styled(Button)`
    margin-top: 10px;
`;

const InventoryBlockPeriodSection = ({ name, stock, disabled = false }: InventoryBlockPeriodSectionProps) => {
    const { t } = useTranslation('inventoryDetails');
    const { Card } = useThemeComponents();
    const inventory = useInventory();

    const { values, setFieldValue } = useFormikContext<InventoryStockFormValues>();
    const blockPeriod = useBlockPeriodModal();

    return (
        <>
            {blockPeriod.render(values, setFieldValue, inventory.variant.identifier, stock)}
            <Card title={t('inventoryDetails:sections.blockPeriod')}>
                <InventoryBlockPeriodTable disabled={disabled} />
                {!disabled && (
                    <StyledAddButton icon={<PlusOutlined />} onClick={blockPeriod.open} type="link">
                        {t('inventoryDetails:actions.addBlockPeriod')}
                    </StyledAddButton>
                )}
            </Card>
        </>
    );
};

export default InventoryBlockPeriodSection;
