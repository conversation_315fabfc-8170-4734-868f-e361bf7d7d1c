import { LockOutlined } from '@ant-design/icons';
import { Space, Typography, Button, notification, Col, Row } from 'antd';
import Dayjs from 'dayjs';
import durationPlugin from 'dayjs/plugin/duration';
import { Formik, Form } from 'formik';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useChangePasswordMutation } from '../../../api/mutations/changePassword';
import PasswordRequirementList from '../../../components/PasswordRequirementList';
import { useAccount } from '../../../components/contexts/AccountContextManager';
import PasswordField from '../../../components/fields/PasswordField';
import requirements from '../../../utilities/passwordRequirement';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import validators from '../../../utilities/validators';

const { Title } = Typography;
const colSpan = { lg: 12, md: 16, xs: 18 };

// ensure the duration plugin is here
Dayjs.extend(durationPlugin);

const UserSelfPasswordSettings = () => {
    const user = useAccount();

    const [showForm, setShowForm] = useState(false);

    const isNearExpiration = Dayjs(user.passwordExpiresAt).subtract(10, 'day').isBefore(new Date());

    if (showForm) {
        return <PasswordForm goToIntroduction={() => setShowForm(false)} />;
    }

    return <Introduction goToForm={() => setShowForm(true)} isNearExpiration={isNearExpiration} />;
};

export default UserSelfPasswordSettings;

const passwordValidator = validators.compose(
    validators.requiredString('previousPassword'),
    validators.requiredString('newPassword'),
    validators.requiredString('newPasswordRepeat'),
    validators.validPasswordFormat('newPassword'),
    validators.matchPassword('newPasswordRepeat', 'newPassword')
);

type PasswordFormProps = {
    goToIntroduction: () => void;
};

type FormValues = {
    previousPassword: string;
    newPassword: string;
    newPasswordRepeat: string;
};

const PasswordForm = ({ goToIntroduction }: PasswordFormProps) => {
    const { t } = useTranslation('userSelf');
    const [mutation] = useChangePasswordMutation();
    const validate = useValidator(passwordValidator);

    const validatePassword = useCallback(
        value =>
            requirements(t).map(({ description, regex }) => ({
                description,
                isChecked: regex.test(value),
            })),
        [t]
    );

    const onSubmit = useHandleError(
        async ({ previousPassword, newPassword }: FormValues) => {
            // display a notification
            const { data } = await mutation({ variables: { previousPassword, newPassword } });
            if (data?.changePassword) {
                notification.success({
                    message: t('userSelf:passwordSettings.successMessage.title'),
                    description: t('userSelf:passwordSettings.successMessage.description'),
                });
                // go back to the introduction
                goToIntroduction();
            }
        },
        [goToIntroduction, t, mutation]
    );

    return (
        <Formik<FormValues>
            initialValues={{ previousPassword: '', newPassword: '', newPasswordRepeat: '' }}
            onSubmit={onSubmit}
            validate={validate}
        >
            {({ isSubmitting, handleSubmit, values, isValid }) => (
                <Form onSubmitCapture={handleSubmit}>
                    <Row justify="start">
                        <Col {...colSpan} style={{ paddingRight: '20px' }}>
                            <PasswordField
                                name="previousPassword"
                                prefix={<LockOutlined />}
                                {...t('userSelf:passwordSettings.fields.previousPassword', { returnObjects: true })}
                            />
                            <PasswordField
                                name="newPassword"
                                prefix={<LockOutlined />}
                                {...t('userSelf:passwordSettings.fields.newPassword', { returnObjects: true })}
                            />
                            <PasswordField
                                name="newPasswordRepeat"
                                prefix={<LockOutlined />}
                                {...t('userSelf:passwordSettings.fields.newPasswordRepeat', { returnObjects: true })}
                            />
                        </Col>
                        <Col>
                            <Title level={5}>{t('userSelf:passwordSettings.requirements.title')}</Title>
                            <PasswordRequirementList requirements={validatePassword(values.newPassword)} />
                        </Col>
                    </Row>
                    <div style={{ textAlign: 'right' }}>
                        <Space>
                            <Button onClick={goToIntroduction} type="dashed">
                                {t('userSelf:passwordSettings.cancelButton')}
                            </Button>
                            <Button disabled={!isValid} htmlType="submit" loading={isSubmitting} type="primary">
                                {t('userSelf:passwordSettings.submitButton')}
                            </Button>
                        </Space>
                    </div>
                </Form>
            )}
        </Formik>
    );
};

type IntroductionProps = {
    goToForm: () => void;
    isNearExpiration: boolean;
};

const Introduction = ({ goToForm, isNearExpiration }: IntroductionProps) => {
    const { t } = useTranslation(['userSelf', 'common']);
    const user = useAccount();

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Typography.Paragraph>
                {t('userSelf:passwordSettings.introductionText', {
                    date: t('common:formats.date', { date: new Date(user.passwordExpiresAt) }),
                })}
            </Typography.Paragraph>
            <div style={{ textAlign: 'right' }}>
                <Button ghost={!isNearExpiration} onClick={goToForm} type="primary">
                    {t('userSelf:passwordSettings.changeButton')}
                </Button>
            </div>
        </Space>
    );
};
