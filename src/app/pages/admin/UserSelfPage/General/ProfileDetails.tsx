import { useApolloClient } from '@apollo/client';
import { Row, Col, Button, message } from 'antd';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { allowedExtensions } from '../../../../../server/utils/extensions';
import * as api from '../../../../api';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import Form from '../../../../components/fields/Form';
import InputField from '../../../../components/fields/InputField';
import PhoneAndPrefixField from '../../../../components/fields/PhoneAndPrefixField';
import SingleUploadField from '../../../../components/fields/SingleUploadField';
import breakpoints from '../../../../utilities/breakpoints';
import { hasValue } from '../../../../utilities/fp';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { type UserFormValues } from './shared';
import useHandleUserAssets from './useHandleUserAssets';

const colSpan = { lg: 8, xs: 24 };

const ButtonContainer = styled(Col)`
    text-align: right;
    button {
        @media (max-width: ${breakpoints.md}) {
            width: 100%;
        }
    }
`;

const formValidator = validators.compose(
    validators.requiredString('displayName'),
    validators.requiredString('email'),
    validators.validEmail('email'),
    validators.only(
        values => hasValue('mobile.value')(values) || hasValue('mobile.prefix')(values),
        validators.compose(
            validators.validPhone('mobile.value', 'mobile.prefix'),
            validators.requiredNumber('mobile.prefix')
        )
    )
);

const ProfileDetails = () => {
    const user = useAccount();
    const { t } = useTranslation(['userSelf']);
    const company = useCompany(true);
    const companyId = company?.id;

    const initialValues = useMemo(
        (): UserFormValues => ({
            displayName: user.displayName,
            email: user.email,
            mobile: user.mobile,
            profileImage: user.profileImage,
            alias: user.alias,
        }),
        [user]
    );

    const apolloClient = useApolloClient();
    const handleUserAssets = useHandleUserAssets();

    const onSubmit = useHandleError<UserFormValues>(
        async values => {
            const { profileImage, ...data } = values;
            // submitting message
            message.loading({
                content: t('userSelf:general.messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // submit update
            await apolloClient
                .mutate<api.UpdateAccountMutation, api.UpdateAccountMutationVariables>({
                    mutation: api.UpdateAccountDocument,
                    variables: { id: user.id, data, companyId },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // handle user asset upload/delete
            await handleUserAssets(user.id, values, user);

            message.success({
                content: t('userSelf:general.messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, user, handleUserAssets, t, companyId]
    );

    const validate = useValidator(formValidator);

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ isSubmitting, handleSubmit }) => (
                <Form onSubmitCapture={handleSubmit}>
                    <Row gutter={[16, 16]}>
                        <Col {...colSpan}>
                            <InputField
                                {...t('userSelf:general.fields.displayName', {
                                    returnObjects: true,
                                })}
                                name="displayName"
                                required
                            />
                        </Col>
                        <Col {...colSpan}>
                            <InputField
                                {...t('userSelf:general.fields.email', {
                                    returnObjects: true,
                                })}
                                name="email"
                                required
                            />
                        </Col>
                        <Col {...colSpan}>
                            <PhoneAndPrefixField
                                {...t('userSelf:general.fields.mobile', {
                                    returnObjects: true,
                                })}
                                name="mobile"
                            />
                        </Col>
                        <Col {...colSpan}>
                            <SingleUploadField
                                {...t('userSelf:general.fields.profileImage', {
                                    returnObjects: true,
                                })}
                                extensions={allowedExtensions.image}
                                name="profileImage"
                            />
                        </Col>
                        <Col {...colSpan}>
                            <InputField
                                {...t('userSelf:general.fields.alias', {
                                    returnObjects: true,
                                })}
                                name="alias"
                            />
                        </Col>

                        <ButtonContainer xs={{ span: 24, offset: 0 }} xxl={{ span: 12, offset: 12 }}>
                            <Button htmlType="submit" loading={isSubmitting} type="primary">
                                {t('userSelf:general.submitButton')}
                            </Button>
                        </ButtonContainer>
                    </Row>
                </Form>
            )}
        </Formik>
    );
};

export default ProfileDetails;
