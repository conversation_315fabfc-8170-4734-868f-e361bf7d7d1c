import { useApolloClient } from '@apollo/client';
import { isObject } from 'lodash/fp';
import { useCallback } from 'react';
import * as api from '../../../../api';
import { UserAsset } from '../../../../api/types';
import { type UserFormValues } from './shared';

const useHandleUserAssets = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        (userId: string, values: UserFormValues, initialValues?: api.CurrentUserDataFragment) => {
            const { profileImage } = values;
            const uploads = [];
            if (profileImage instanceof File) {
                // upload a new file
                uploads.push(
                    apolloClient.mutate<api.UploadUserAssetMutation, api.UploadUserAssetMutationVariables>({
                        mutation: api.UploadUserAssetDocument,
                        variables: { userId, assetType: UserAsset.ProfileImage, upload: profileImage },
                    })
                );
            } else if (profileImage === null && isObject(initialValues.profileImage)) {
                // delete existing file
                uploads.push(
                    apolloClient.mutate<api.DeleteUserAssetMutation, api.DeleteUserAssetMutationVariables>({
                        mutation: api.DeleteUserAssetDocument,
                        variables: { userId, assetType: UserAsset.ProfileImage },
                    })
                );
            }

            if (uploads.length) {
                return Promise.all(uploads);
            }

            return Promise.resolve([]);
        },
        [apolloClient]
    );
};

export default useHandleUserAssets;
