import { useApolloClient } from '@apollo/client';
import { Button, Grid, message, Modal } from 'antd';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import getApolloErrors from '../../../../server/utils/getApolloErrors';
import { UserGroupOptionsDataFragment } from '../../../api';
import { RoleListDataFragment } from '../../../api/fragments/RoleListData';
import { UserSpecsFragment } from '../../../api/fragments/UserSpecs';
import {
    ResendActivationLinkDocument,
    ResendActivationLinkMutation,
    ResendActivationLinkMutationVariables,
} from '../../../api/mutations/resendActivationLink';
import { UpdateUserDocument, UpdateUserMutation, UpdateUserMutationVariables } from '../../../api/mutations/updateUser';
import { UserSettings } from '../../../api/types';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../components/contexts/shared';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import useFormattedSimpleVersioning from '../../../utilities/useFormattedSimpleVersioning';
import useHandleError from '../../../utilities/useHandleError';
import { filteredRolesAndUserGroupsByCompanyContext } from '../../shared/filterDataByCompany';
import { UserFormValues, useUserFormValidator } from './UserForm';
import UserDetailForm from './UserForm/UserDetailForm';

export type UserDetailsInnerPageProps = {
    user: UserSpecsFragment;
    disabled?: boolean;
    allowedRoles: RoleListDataFragment[];
    allowedUserGroups: UserGroupOptionsDataFragment[];
};

const UserDetailsInnerPage = ({
    user,
    disabled = false,
    allowedRoles,
    allowedUserGroups,
}: UserDetailsInnerPageProps) => {
    const { t } = useTranslation(['userDetails']);
    const company = useCompany(true);
    const userId = user.id;
    const { roles, userGroups } = user;

    const { filteredRoles, filteredUserGroups, roleIdsNotAllowed, userGroupIdsNotAllowed } =
        filteredRolesAndUserGroupsByCompanyContext(roles, userGroups, allowedRoles, allowedUserGroups);

    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: user.versioning,
        timeZone: company?.timeZone,
    });
    const screens = Grid.useBreakpoint();

    const apolloClient = useApolloClient();

    const router = useRouter(false);

    const navigate = useNavigate();
    const onSubmit = useHandleError<UserFormValues>(
        async settings => {
            const { roleIds, userGroupIds, ...others } = settings;
            const updatedSettings: UserSettings = {
                ...others,
                roleIds: [...roleIds, ...roleIdsNotAllowed],
                userGroupIds: [...userGroupIds, ...userGroupIdsNotAllowed],
            };
            // submitting message
            message.loading({
                content: t('userDetails:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // submit update
            await apolloClient
                .mutate<UpdateUserMutation, UpdateUserMutationVariables>({
                    mutation: UpdateUserDocument,
                    variables: { id: userId, settings: updatedSettings, companyId: company?.id },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('userDetails:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [roleIdsNotAllowed, userGroupIdsNotAllowed, t, apolloClient, userId, company]
    );

    const onResendActivation = useCallback(async () => {
        try {
            message.loading({
                content: t('userDetails:messages.resendActivationSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient
                .mutate<ResendActivationLinkMutation, ResendActivationLinkMutationVariables>({
                    mutation: ResendActivationLinkDocument,
                    variables: { userId, companyId: router?.companyId },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            Modal.success({
                className: 'static-modal',
                content: t('userDetails:messages.resendActivationSuccessful'),
                onOk: () => navigate('/admin/accesses/users'),
            });
        } catch (error) {
            const apolloErrors = getApolloErrors(error);

            if (apolloErrors?.$root) {
                message.error(apolloErrors?.$root);
            }
        }
    }, [t, apolloClient, userId, navigate, router?.companyId]);

    const initialValues = useMemo(
        (): UserFormValues => ({
            ...pick(['displayName', 'email', 'mobile', 'alias', 'isActive'], user),
            roleIds: filteredRoles.map(({ id }) => id),
            userGroupIds: filteredUserGroups.map(({ id }) => id),
        }),
        [filteredRoles, filteredUserGroups, user]
    );

    const validate = useUserFormValidator();

    return (
        <ConsolePageWithHeader
            footer={[
                !user?.lastSignedIn && (
                    <Button key="resend" onClick={onResendActivation}>
                        {t('userDetails:actions.resendActivation')}
                    </Button>
                ),
                !disabled && (
                    <Button key="submit" form="userForm" htmlType="submit" type="primary">
                        {t('userDetails:actions.update')}
                    </Button>
                ),
            ]}
            onBack={() => navigate('/admin/accesses/users')}
            title={screens.md ? t('userDetails:title', { name: initialValues.displayName }) : initialValues.displayName}
        >
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} enableReinitialize>
                {({ handleSubmit }) => (
                    <Form id="userForm" name="userForm" onSubmitCapture={handleSubmit}>
                        <UserDetailForm disabled={disabled} offset={offset} updated={updated} user={user} />
                    </Form>
                )}
            </Formik>
        </ConsolePageWithHeader>
    );
};

export default UserDetailsInnerPage;
