import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useEffect } from 'react';
import { type FinanceProductFormValues, SettingsKey } from './typings';

export const generalViewableAndEditableDefaults = { isViewable: false, isEditable: false };
export const interestRateViewableAndEditableDefaults = { isViewable: false, isEditable: false };

const useViewableAndEditable = (
    settingsKey: SettingsKey,
    use: boolean,
    table: boolean,
    isViewable: boolean | null | undefined,
    isEditable: boolean | null | undefined,
    defaults: {
        isViewable: boolean | undefined;
        isEditable?: boolean | undefined;
    }
) => {
    const { setFieldValue } = useFormikContext<FinanceProductFormValues>();

    const { isViewable: defaultIsViewable, isEditable: defaultIsEditable } = defaults;
    useEffect(() => {
        if (use) {
            if (isNil(isViewable)) {
                setFieldValue(`${settingsKey}.isViewable`, defaultIsViewable);
            }

            // assign default if there is default provided
            if (isNil(isEditable) && !isNil(defaults.isEditable)) {
                setFieldValue(`${settingsKey}.isEditable`, defaultIsEditable);
            }

            if ((table || isViewable === false) && isEditable !== false && !isNil(defaults.isEditable)) {
                setFieldValue(`${settingsKey}.isEditable`, false);
            }
        }
    }, [
        settingsKey,
        use,
        table,
        isViewable,
        isEditable,
        setFieldValue,
        defaultIsViewable,
        defaultIsEditable,
        defaults.isEditable,
    ]);
};

export default useViewableAndEditable;
