import { useFormikContext } from 'formik';
import { filter, flow, get, omit, trim, uniq } from 'lodash/fp';
import { useMemo } from 'react';
import { useGetFinderVehicleOptionsQuery } from '../../../../api/queries/getFinderVehicleOptions';
import { useGetLocalVariantsOptionsQuery } from '../../../../api/queries/getLocalVariantsOptions';
import hasVehicleParameterFilter from '../FinanceProductForm/helpers/hasVehicleParameterFilter';
import { type FinanceProductFormValues } from './typings';
import useBankOptions from './useBankOptions';

export const getFinderVehicleFilter = (
    vehicleReferenceParameters: FinanceProductFormValues['vehicleReferenceParameters']
) => {
    const variantReferences: string = get('finderVehicle.variants', vehicleReferenceParameters) || '';

    // clean data by trimming values
    // and removing empty spaces
    const variants = flow([variants => variants.map(variant => trim(variant)), filter(Boolean)])(
        variantReferences.split(',')
    );

    return {
        ...omit(
            ['variants', '__typename', 'mileage.__typename', 'modelYear.__typename'],
            vehicleReferenceParameters.finderVehicle
        ),
        vehicleIds: variants,
    };
};

export const getVariantFilter = (vehicleReferenceParameters: FinanceProductFormValues['vehicleReferenceParameters']) =>
    omit(['__typename'], vehicleReferenceParameters.variant);

const useVehicleSuiteIdsFromParameters = () => {
    const { values } = useFormikContext<FinanceProductFormValues>();

    const { items } = useBankOptions();
    const bank = useMemo(() => items.find(({ id }) => values.bankId === id), [items, values.bankId]);
    const { vehicleReferenceParameters } = values;

    const finderVehicleFilter = useMemo(
        () => getFinderVehicleFilter(vehicleReferenceParameters),
        [vehicleReferenceParameters]
    );

    const variantFilter = useMemo(() => getVariantFilter(vehicleReferenceParameters), [vehicleReferenceParameters]);

    const { data: variantsFromParametersData, loading: variantParametersLoading } = useGetLocalVariantsOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyId: bank?.module?.companyId,
                ...variantFilter,
            },
        },
        // skip if filter is empty
        skip: !hasVehicleParameterFilter(variantFilter),
    });

    const { data: finderVehicleIdsFromParametersData, loading: finderVehicleParametersLoading } =
        useGetFinderVehicleOptionsQuery({
            fetchPolicy: 'cache-and-network',
            variables: {
                filter: {
                    companyId: bank?.module?.companyId,
                    ...finderVehicleFilter,
                },
            },
            // skip if filter is empty
            skip: !hasVehicleParameterFilter(finderVehicleFilter),
        });

    return useMemo(
        (): { suiteIds: string[]; isLoading: boolean } => ({
            suiteIds: uniq([
                ...(variantsFromParametersData?.list?.items || []).map(variant => variant.versioning.suiteId),
                ...(finderVehicleIdsFromParametersData?.list?.items || []).map(
                    finderVehicle => finderVehicle.versioning.suiteId
                ),
            ]),
            isLoading: finderVehicleParametersLoading || variantParametersLoading,
        }),
        [
            finderVehicleIdsFromParametersData?.list?.items,
            finderVehicleParametersLoading,
            variantParametersLoading,
            variantsFromParametersData?.list?.items,
        ]
    );
};

export default useVehicleSuiteIdsFromParameters;
