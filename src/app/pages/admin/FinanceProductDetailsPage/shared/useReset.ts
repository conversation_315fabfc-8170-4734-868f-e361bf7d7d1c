import { FormikHelpers } from 'formik';
import { useEffect } from 'react';
import { FinanceProductType } from '../../../../api/types';
import { type FinanceProductFormValues, SettingsKey } from './typings';

export const all: SettingsKey[] = [
    'term',
    'downPayment',
    'loan',
    'interestRate',
    'lease',
    'deposit',
    'licensePlateFee',
    'commission',
    'monthlyPaymentFixedInterestRate',
    'licenseAndFuelTax',
    'displacement',
    'insuranceFee',
    'taxLoss',
    'balloon',
    'balloonGfv',
    'residualValue',
    'residualValue.averageMileage',
];

export const settings = {
    hirePurchase: ['term', 'downPayment', 'loan', 'interestRate'] as SettingsKey[],
    hirePurchaseWithBalloon: ['term', 'downPayment', 'loan', 'interestRate', 'balloon'] as SettingsKey[],
    hirePurchaseWithBalloonGFV: ['term', 'downPayment', 'loan', 'interestRate', 'balloonGfv'] as SettingsKey[],
    lease: ['term', 'lease', 'deposit'],
    deferredPrincipal: ['term', 'downPayment', 'loan', 'interestRate'] as SettingsKey[],
    ucclLeasing: [
        'term',
        'downPayment',
        'loan',
        'interestRate',
        'licensePlateFee',
        'commission',
        'monthlyPaymentFixedInterestRate',
        'licenseAndFuelTax',
        'displacement',
        'insuranceFee',
        'taxLoss',
    ] as SettingsKey[],
    leasePurchase: [
        'term',
        'downPayment',
        'loan',
        'interestRate',
        'residualValue',
        'residualValue.averageMileage',
    ] as SettingsKey[],
};

const useReset = (
    type: FinanceProductType,
    setFieldValue: FormikHelpers<FinanceProductFormValues>['setFieldValue']
) => {
    useEffect(() => {
        let reset: SettingsKey[] | undefined;

        switch (type) {
            case FinanceProductType.HirePurchase:
                reset = all.filter(key => !settings.hirePurchase.includes(key));
                break;

            case FinanceProductType.HirePurchaseWithBalloon:
                reset = all.filter(key => !settings.hirePurchaseWithBalloon.includes(key));
                break;

            case FinanceProductType.HirePurchaseWithBalloonGfv:
                reset = all.filter(key => !settings.hirePurchaseWithBalloonGFV.includes(key));
                break;

            case FinanceProductType.Lease:
                reset = all.filter(key => !settings.lease.includes(key));
                break;

            case FinanceProductType.DeferredPrincipal:
                reset = all.filter(key => !settings.deferredPrincipal.includes(key));
                break;

            case FinanceProductType.UcclLeasing:
                reset = all.filter(key => !settings.ucclLeasing.includes(key));
                break;

            case FinanceProductType.LeasePurchase:
                reset = all.filter(key => !settings.leasePurchase.includes(key));
                break;
        }

        if (reset) {
            reset.forEach(key => {
                setFieldValue(`${key}`, undefined);
            });
        }
    }, [type, setFieldValue]);
};

export default useReset;
