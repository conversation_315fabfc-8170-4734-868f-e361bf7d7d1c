import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useEffect, useMemo } from 'react';
import { type FinanceProductFormValues, SettingsKey } from './typings';

const useTable = (
    settingsKey: SettingsKey,
    use: boolean,
    table: Array<any> | null | undefined,
    initialTable?: Array<any> | null,
    tableKey?: string
) => {
    const { setFieldValue } = useFormikContext<FinanceProductFormValues>();

    const usedInitialTable = useMemo(() => initialTable ?? [], [initialTable]);
    const usedTableKey = useMemo(() => tableKey ?? 'table', [tableKey]);

    useEffect(() => {
        if (use && isNil(table)) {
            setFieldValue(`${settingsKey}.${usedTableKey}`, usedInitialTable);
        }
    }, [usedTableKey, usedInitialTable, settingsKey, use, table, setFieldValue]);
};

export default useTable;
