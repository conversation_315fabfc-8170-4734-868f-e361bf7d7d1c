import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useGetLocalVariantsOptionsQuery } from '../../../../api/queries/getLocalVariantsOptions';
import { type FinanceProductFormValues } from './typings';
import useBankOptions from './useBankOptions';

export type VariantOption = { id: string; name: string; identifier: string };

const useSelectedVariants = (variantSuiteIds: string[]) => {
    const { items } = useBankOptions();
    const { values } = useFormikContext<FinanceProductFormValues>();
    const bank = useMemo(() => items.find(({ id }) => values.bankId === id), [items, values.bankId]);

    const { data } = useGetLocalVariantsOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyId: bank?.module?.companyId,
            },
        },
    });

    return useMemo<VariantOption[]>(() => {
        const variants = data?.list?.items || [];

        if (!variantSuiteIds?.length || !variants?.length) {
            return [];
        }

        return variantSuiteIds
            .map(variantSuiteId => {
                const variant = variants.find(
                    variant => variant !== undefined && variant.versioning.suiteId === variantSuiteId
                );

                if (variant) {
                    return {
                        id: variant.versioning.suiteId,
                        name: variant.name.defaultValue,
                        identifier: variant.identifier,
                    };
                }

                return null;
            })
            .filter(Boolean);
    }, [data, variantSuiteIds]);
};

export default useSelectedVariants;
