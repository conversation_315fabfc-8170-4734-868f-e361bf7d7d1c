import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useEffect } from 'react';
import { type FinanceProductFormValues, SettingsKey } from './typings';

const useType = (
    settingsKey: SettingsKey,
    use: boolean,
    type: string | null | undefined,
    defaultType: 'range' | 'table' | 'fixed' = 'range'
) => {
    const { setFieldValue } = useFormikContext<FinanceProductFormValues>();

    useEffect(() => {
        if (use && isNil(type)) {
            setFieldValue(`${settingsKey}.type`, defaultType);
        }
    }, [settingsKey, use, type, defaultType, setFieldValue]);
};

export default useType;
