import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { FinanceProductType } from '../../../../api/types';
import { type FinanceProductFormValues, SettingsKey } from './typings';
import { settings } from './useReset';

const useVisible = () => {
    const { values } = useFormikContext<FinanceProductFormValues>();
    const type = values?.type;

    return useMemo<{ [key in SettingsKey]?: boolean }>(() => {
        let result = {};

        switch (type) {
            case FinanceProductType.HirePurchase:
                result = Object.fromEntries(settings.hirePurchase.map(key => [key, true]));
                break;

            case FinanceProductType.HirePurchaseWithBalloon:
                result = Object.fromEntries(settings.hirePurchaseWithBalloon.map(key => [key, true]));
                break;

            case FinanceProductType.HirePurchaseWithBalloonGfv:
                result = Object.fromEntries(settings.hirePurchaseWithBalloonGFV.map(key => [key, true]));
                break;

            case FinanceProductType.Lease:
                result = Object.fromEntries(settings.lease.map(key => [key, true]));
                break;

            case FinanceProductType.DeferredPrincipal:
                result = Object.fromEntries(settings.deferredPrincipal.map(key => [key, true]));
                break;

            case FinanceProductType.UcclLeasing:
                result = Object.fromEntries(settings.ucclLeasing.map(key => [key, true]));
                break;

            case FinanceProductType.LeasePurchase:
                result = Object.fromEntries(settings.leasePurchase.map(key => [key, true]));
                break;
        }

        return result;
    }, [type]);
};

export default useVisible;
