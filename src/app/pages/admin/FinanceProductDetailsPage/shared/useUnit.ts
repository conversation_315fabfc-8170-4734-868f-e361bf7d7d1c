import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useEffect } from 'react';
import { AmountUnit } from '../../../../api/types';
import { type FinanceProductFormValues, SettingsKey } from './typings';

const defaultUnit = AmountUnit.Percentage;
const useUnit = (settingsKey: SettingsKey, use: boolean, unit: AmountUnit, unitKey: string = 'unit') => {
    const { setFieldValue } = useFormikContext<FinanceProductFormValues>();

    useEffect(() => {
        if (use && isNil(unit)) {
            setFieldValue(`${settingsKey}.${unitKey}`, defaultUnit);
        }
    }, [settingsKey, use, unit, setFieldValue, unitKey]);
};

export default useUnit;
