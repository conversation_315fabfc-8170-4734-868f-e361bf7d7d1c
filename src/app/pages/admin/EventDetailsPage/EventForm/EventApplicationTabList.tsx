import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import { useLocation } from 'react-router-dom';
import urljoin from 'url-join';
import { ApplicationStage, LeadStageOption } from '../../../../api/types';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import useGetEventDocumentDetailsEndpointPrefix from '../../../../utilities/useGetEventDocumentDetailsEndpoint';
import ApplicationList from '../../../shared/ApplicationList';
import { useApplicationColumns } from '../../../shared/ApplicationList/helpers';
import LeadList from '../../../shared/LeadList';
import { useLeadColumns } from '../../../shared/LeadList/helpers';

export enum ListKind {
    Applications = 'applications',
    Leads = 'lead',
}

type EventApplicationTabListProps = {
    listKind: ListKind.Applications;
    stage: ApplicationStage;
    setLoadedApplicationIds: React.Dispatch<React.SetStateAction<string[]>>;
    setSelectedApplications: React.Dispatch<React.SetStateAction<string[]>>;
};

type EventLeadTabListProps = {
    listKind: ListKind.Leads;
    setLoadedLeadIds: React.Dispatch<React.SetStateAction<string[]>>;
    setSelectedLeads: React.Dispatch<React.SetStateAction<string[]>>;
};

export type EventApplicationOrLeadTabListProps = {
    eventId: string;
} & (EventApplicationTabListProps | EventLeadTabListProps);

const getKindProps = (props: EventApplicationOrLeadTabListProps) => {
    switch (props.listKind) {
        case ListKind.Applications:
            return {
                stage: props.stage,
                setLoadedApplicationIds: props.setLoadedApplicationIds,
                setSelectedApplications: props.setSelectedApplications,
            };

        case ListKind.Leads:
            return { setLoadedLeadIds: props.setLoadedLeadIds, setSelectedLeads: props.setSelectedLeads };

        default:
            throw new Error('List kind is not available');
    }
};

const EventApplicationTabList = (props: EventApplicationOrLeadTabListProps) => {
    const eventPageLocation = useLocation();
    const { dealerIds } = useMultipleDealerIds();
    const { eventId, listKind } = props;

    const { stage, setLoadedApplicationIds, setSelectedApplications, setLoadedLeadIds, setSelectedLeads } =
        getKindProps(props);

    const applicationColumns = useApplicationColumns(stage, { hasModule: false });
    const leadColumns = useLeadColumns({ hasModule: false });

    const navigate = useNavigate();
    const getPrefix = useGetEventDocumentDetailsEndpointPrefix(stage, listKind === ListKind.Leads);

    const onRow = useCallback(
        id => {
            const prefix = getPrefix();
            if (getPrefix()) {
                navigate(urljoin(prefix, id), { state: { previousEndpoint: eventPageLocation.pathname } });
            }
        },
        [eventPageLocation.pathname, navigate, getPrefix]
    );

    const onLeadRow = useCallback(
        (isLead: boolean, id: string) => {
            const prefix = getPrefix(isLead);
            if (prefix) {
                navigate(urljoin(prefix, id), { state: { previousEndpoint: eventPageLocation.pathname } });
            }
        },
        [eventPageLocation.pathname, navigate, getPrefix]
    );

    if (listKind === ListKind.Applications && !stage) {
        return null;
    }

    return listKind === ListKind.Leads ? (
        <LeadList
            dealerIds={dealerIds}
            desiredColumns={leadColumns}
            eventId={eventId}
            isAllowSelection={false}
            leadStage={LeadStageOption.LeadAndContact}
            onRow={onLeadRow}
            setLoadedLeadIds={setLoadedLeadIds}
            setSelectedLead={setSelectedLeads}
        />
    ) : (
        <ApplicationList
            eventId={eventId}
            isAllowSelection={false}
            onRow={onRow}
            setLoadedApplicationIds={setLoadedApplicationIds}
            setSelectedApplication={setSelectedApplications}
            showColumns={applicationColumns}
            stage={stage}
        />
    );
};

export default EventApplicationTabList;
