import { useTranslation } from 'react-i18next';
import { MaintenanceUpdateFragment } from '../../../api/fragments/MaintenanceUpdate';
import { UploadFileWithPreviewFormDataFragment } from '../../../api/fragments/UploadFileWithPreviewFormData';
import { MaintenanceModuleSettings, ModuleType } from '../../../api/types';
import LoadingElement from '../../../components/LoadingElement';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import NoItemResult from '../../../components/results/NoItemResult';
import NotFoundResult from '../../../components/results/NotFoundResult';
import MaintenanceDetailsInnerPage from './MaintenanceDetailsInnerPage';

export type FormValues = MaintenanceModuleSettings & {
    image?: File | UploadFileWithPreviewFormDataFragment;
};

export type MaintenanceModulePageProps = { module: MaintenanceUpdateFragment };

const MaintenanceDetailsPage = () => {
    const { t } = useTranslation(['maintenance']);
    const company = useCompany(true);

    const module = company?.activeMaintenanceModule;

    if (!company) {
        return <NoItemResult subTitle={`${t('maintenance:noCompanySelected')}`} />;
    }

    if (!module) {
        return <LoadingElement />;
    }

    if (!module || module.__typename !== ModuleType.MaintenanceModule) {
        return <NotFoundResult />;
    }

    return <MaintenanceDetailsInnerPage module={module} />;
};

export default MaintenanceDetailsPage;
