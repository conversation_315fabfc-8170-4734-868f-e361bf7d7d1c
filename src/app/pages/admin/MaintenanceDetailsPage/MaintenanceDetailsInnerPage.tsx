import { Button, Tabs, message } from 'antd';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { isNil, omit, pick } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MaintenanceUpdateFragment } from '../../../api/fragments/MaintenanceUpdate';
import { UploadFileWithPreviewFormDataFragment } from '../../../api/fragments/UploadFileWithPreviewFormData';
import { useUpdateMaintenanceModuleDetailsMutation } from '../../../api/mutations/updateMaintenanceModuleDetails';
import { MaintenanceDetailsSettings } from '../../../api/types';
import { useCompanyContext } from '../../../components/contexts/CompanyContextManager';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import useFormattedSimpleVersioning from '../../../utilities/useFormattedSimpleVersioning';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import validators from '../../../utilities/validators';
import MaintenanceDetailsForm from './MaintenanceDetailsForm';
import { type SubmittedFormValues } from './shared';
import useUploadMaintainenceBannerImage from './useUploadMaintainenceBannerImage';

export type InitialFormValues = Omit<MaintenanceDetailsSettings, 'warningBefore'> & {
    image?: File | UploadFileWithPreviewFormDataFragment;
    warningBefore: String;
    startTime: string | Date;
    endTime: string | Date;
};

export type MaintenanceModulePageProps = { module: MaintenanceUpdateFragment };

export enum MaintenanceActiveTabKey {
    Details = 'details',
}

const formValidator = validators.compose(
    validators.requiresPeriod('period'),
    validators.requiredBoolean('isActive'),
    validators.requiredUploadFile('image'),
    validators.validTimeStringFormat('warningBefore'),
    validators.requiredString('pageTitle.defaultValue')
);

const timeUnits = {
    w: 604800, // weeks to seconds
    d: 86400, // days to seconds
    h: 3600, // hours to seconds
    m: 60, // minutes to seconds
};

function convertDurationToSeconds(durationString) {
    // Verify the format of the input

    return durationString.split(' ').reduce((total, segment) => {
        const unit = segment.slice(-1);
        const value = parseInt(segment.slice(0, -1), 10);

        return total + value * timeUnits[unit];
    }, 0);
}

function convertSecondsToDuration(seconds) {
    let remainingSeconds = seconds;
    let durationString = '';

    Object.entries(timeUnits).forEach(([unit, unitSeconds]) => {
        if (remainingSeconds >= unitSeconds) {
            const value = Math.floor(remainingSeconds / unitSeconds);
            durationString += `${value}${unit} `;
            remainingSeconds -= value * unitSeconds;
        }
    });

    return durationString.trim();
}

const MaintenanceDetailsInnerPage = ({ module }: MaintenanceModulePageProps) => {
    const { t } = useTranslation(['maintenance', 'moduleDetails', 'common']);

    const handleUploadImage = useUploadMaintainenceBannerImage();
    const [currentTab, setCurrentTab] = useState(MaintenanceActiveTabKey.Details);

    const { timeZonesByModuleId } = useCompanyContext();
    const timeZone = timeZonesByModuleId[module.id];

    const initialValues = useMemo(
        (): InitialFormValues => ({
            ...pick(['image'], module),
            pageTitle: isNil(module.pageTitle) ? { defaultValue: '', overrides: [] } : module.pageTitle,
            pageContent: isNil(module.pageContent) ? { defaultValue: '', overrides: [] } : module.pageContent,
            warningBefore: convertSecondsToDuration(module.warningBefore),
            isActive: module?.isActive ? module.isActive : false,
            period: module.period,
            startTime: dayjs(module.period.start).tz(timeZone).toDate(),
            endTime: dayjs(module.period.end).tz(timeZone).toDate(),
        }),

        [module, timeZone]
    );

    const [updateMaintenance] = useUpdateMaintenanceModuleDetailsMutation();

    const validate = useValidator(formValidator);

    const onSubmit = useHandleError<SubmittedFormValues>(
        async values => {
            const newPeriod = {
                start: dayjs(values.period.start)
                    .set('hour', dayjs(values.startTime).tz(timeZone).hour())
                    .set('minute', dayjs(values.startTime).tz(timeZone).minute())
                    .set('second', 0)
                    .tz(timeZone)
                    .toDate(),
                end: dayjs(values.period.end)
                    .set('hour', dayjs(values.endTime).tz(timeZone).hour())
                    .set('minute', dayjs(values.endTime).tz(timeZone).minute())
                    .set('second', 0)
                    .tz(timeZone)
                    .toDate(),
            };

            // submitting message
            message.loading({
                content: t('maintenance:messages.updateMaintainenceDetailsSubmitting'),
                key: 'primary',
                duration: 0,
            });
            const newValues = omit(['startTime', 'endTime'], values);

            // Convert warningBefore to seconds
            const warningBeforeInSeconds = convertDurationToSeconds(values.warningBefore);

            // submit creation

            await updateMaintenance({
                variables: {
                    settings: {
                        ...pick(['pageTitle', 'pageContent', 'period', 'isActive'], newValues),
                        warningBefore: warningBeforeInSeconds,
                        period: newPeriod,
                    },
                    moduleId: module.id,
                },
            });

            await handleUploadImage(module.id, values);

            // inform about success
            message.success({
                content: t('maintenance:messages.updateMaintainenceDetailsSuccessful'),
                key: 'primary',
            });
        },
        [timeZone, module.id, updateMaintenance, t, handleUploadImage]
    );

    const { updated, offset } = useFormattedSimpleVersioning({ versioning: module.versioning, timeZone });

    const footer = useMemo(
        () => (
            <Button key="submit" form="updateMainDetails" htmlType="submit" type="primary">
                {t('maintenance:actions.update')}
            </Button>
        ),
        [t]
    );

    const onTabChange = useCallback(
        (activeKey: MaintenanceActiveTabKey) => {
            setCurrentTab(activeKey);
        },
        [setCurrentTab]
    );

    const headers = (
        <Tabs activeKey={currentTab} onChange={onTabChange}>
            <Tabs.TabPane key={MaintenanceActiveTabKey.Details} tab={t('maintenance:tabs.details')} />
        </Tabs>
    );

    return (
        <ConsolePageWithHeader footer={[footer]} header={{ footer: headers }} title={t('maintenance:title')}>
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="updateMainDetails" name="updateMainDetails" onSubmitCapture={handleSubmit}>
                        <MaintenanceDetailsForm offset={offset} updated={updated} />
                    </Form>
                )}
            </Formik>
        </ConsolePageWithHeader>
    );
};

export default MaintenanceDetailsInnerPage;
