import { useApolloClient } from '@apollo/client';
import { isObject } from 'lodash/fp';
import { useCallback } from 'react';
import { BannerDataFragment } from '../../../api/fragments/BannerData';
import {
    DeleteMaintenanceModuleAssetDocument,
    DeleteMaintenanceModuleAssetMutation,
    DeleteMaintenanceModuleAssetMutationVariables,
} from '../../../api/mutations/deleteMaintenanceModuleAsset';
import {
    UploadMaintenanceModuleAssetDocument,
    UploadMaintenanceModuleAssetMutation,
    UploadMaintenanceModuleAssetMutationVariables,
} from '../../../api/mutations/uploadMaintenanceModuleAsset';
import { ModuleAsset } from '../../../api/types';
import { type SubmittedFormValues } from './shared';

const useUploadMaintainenceBannerImage = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        (moduleId: string, values: SubmittedFormValues, initialValues?: BannerDataFragment) => {
            if (!values.image) {
                throw new Error('Please upload an image');
            }

            const { image: bannerImage } = values;
            const uploads = [];

            if (bannerImage instanceof File) {
                uploads.push(
                    apolloClient.mutate<
                        UploadMaintenanceModuleAssetMutation,
                        UploadMaintenanceModuleAssetMutationVariables
                    >({
                        mutation: UploadMaintenanceModuleAssetDocument,
                        variables: {
                            moduleId,
                            assetType: ModuleAsset.Image,
                            upload: bannerImage,
                        },
                    })
                );
            } else if (bannerImage === null && isObject(initialValues?.bannerImage)) {
                uploads.push(
                    apolloClient.mutate<
                        DeleteMaintenanceModuleAssetMutation,
                        DeleteMaintenanceModuleAssetMutationVariables
                    >({
                        mutation: DeleteMaintenanceModuleAssetDocument,
                        variables: {
                            moduleId,
                            assetType: ModuleAsset.Image,
                        },
                    })
                );
            }

            if (uploads.length) {
                return Promise.all(uploads);
            }

            return Promise.resolve([]);
        },
        [apolloClient]
    );
};

export default useUploadMaintainenceBannerImage;
