import { head, isEmpty } from 'lodash/fp';
import { useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { CurrentUserDataFragment } from '../../../api/fragments/CurrentUserData';
import TokenExpirationController from '../../../components/TokenExpirationController';
import { useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../components/contexts/shared';
import LoginLayout from '../../../layouts/LoginLayout';
import LoginContent from '../../../layouts/LoginLayout/LoginContent';
import { useRuntimeConfig } from '../../../runtimeConfig';
import AuthenticateStep, { FormValues } from './AuthenticateStep';
import PasswordExpiredStep from './PasswordExpiredStep';
import SmsOTPStep from './SmsOTPStep';
import TOTPStep from './TOTPStep';
import TokenExpired from './TokenExpired';

type State =
    | { step: 'authenticate' }
    | { step: 'totp'; token: string }
    | { step: 'passwordExpired'; token: string }
    | { step: 'smsOtp'; token: string; values: FormValues };

type Action =
    | { type: 'moveToTOTP'; token: string }
    | { type: 'moveToSmsOTP'; token: string; values: FormValues }
    | { type: 'moveToPasswordExpired'; token: string }
    | { type: 'reset' };

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'moveToPasswordExpired':
            return { step: 'passwordExpired', token: action.token };

        case 'moveToTOTP':
            return { step: 'totp', token: action.token };

        case 'moveToSmsOTP':
            return { step: 'smsOtp', token: action.token, values: action.values };

        case 'reset':
            return { step: 'authenticate' };

        default:
            return state;
    }
};

export type ActionHandlers = {
    completeAuthentication: (token: string, user: CurrentUserDataFragment) => Promise<void>;
    moveToTOTP: (token: string) => void;
    moveToSmsOTP: (token: string, values: FormValues) => void;
    moveToPasswordExpired: (token: string) => void;
    goBackToAuthenticate: () => void;
};

const SignInPage = () => {
    const { t } = useTranslation(['signInPage']);
    const locationState = useLocation().state as { nextPage?: string; queryParams?: string };
    const requestedNextPage = locationState?.nextPage;
    const requestedNextPageQueryParams = locationState?.queryParams;
    const { login } = useAccountContext();
    const navigate = useNavigate();
    const { router } = useRuntimeConfig();
    const { withAdmin, id: routerId } = router;
    const routerContext = useRouter();

    const [state, dispatch] = useReducer(reducer, { step: 'authenticate' });

    const actions = useMemo(
        (): ActionHandlers => ({
            completeAuthentication: async (token: string, user: CurrentUserDataFragment) => {
                // update session
                login(token, user);

                if (routerContext?.refetch) {
                    // refetch routes to update permissions based on the last logged in user
                    await routerContext.refetch();
                }

                // identify what is the next page
                let nextPage = requestedNextPage;

                if (!nextPage) {
                    // Getting the first avaialable endpoint on CI
                    // Refer to https://appvantage.atlassian.net/browse/AN-2359
                    if (routerId && routerContext?.endpoints?.length > 0) {
                        nextPage = head(routerContext.endpoints).pathname;
                    } else if (withAdmin) {
                        nextPage = '/admin';
                    } else {
                        nextPage = '/';
                    }
                }

                const nextPageRedirection =
                    requestedNextPage && !isEmpty(requestedNextPageQueryParams)
                        ? `${nextPage}${requestedNextPageQueryParams}`
                        : nextPage;

                // then redirect to the page the user wanted to see
                navigate(nextPageRedirection, { replace: true });
            },
            moveToTOTP: (token: string) => dispatch({ type: 'moveToTOTP', token }),
            moveToSmsOTP: (token: string, values: FormValues) => dispatch({ type: 'moveToSmsOTP', token, values }),
            moveToPasswordExpired: (token: string) => dispatch({ type: 'moveToPasswordExpired', token }),
            goBackToAuthenticate: () => dispatch({ type: 'reset' }),
        }),
        [login, routerContext, requestedNextPage, navigate, requestedNextPageQueryParams, routerId, withAdmin]
    );

    const stepElement = (() => {
        const tokenExpiredFallback = (
            <LoginContent title={t('signInPage:authenticateStep.title')}>
                <TokenExpired onLinkClick={actions.goBackToAuthenticate} />
            </LoginContent>
        );

        switch (state.step) {
            case 'authenticate':
                return <AuthenticateStep actions={actions} />;

            case 'totp':
                return (
                    <TokenExpirationController fallback={tokenExpiredFallback} token={state.token}>
                        <TOTPStep actions={actions} token={state.token} />
                    </TokenExpirationController>
                );

            case 'smsOtp':
                return (
                    <TokenExpirationController fallback={tokenExpiredFallback} token={state.token}>
                        <SmsOTPStep actions={actions} token={state.token} values={state.values} />
                    </TokenExpirationController>
                );

            case 'passwordExpired':
                return (
                    <TokenExpirationController fallback={tokenExpiredFallback} token={state.token}>
                        <PasswordExpiredStep actions={actions} token={state.token} />
                    </TokenExpirationController>
                );

            default:
                return null;
        }
    })();

    return <LoginLayout>{stepElement}</LoginLayout>;
};

export default SignInPage;
