import { UserOutlined } from '@ant-design/icons';
import { message, Space, Typography } from 'antd';
import { Form, Formik, useField } from 'formik';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useApplyForPasswordChangeMutation } from '../../../api/mutations/applyForPasswordChange';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../components/contexts/shared';
import InputField from '../../../components/fields/InputField';
import LoginContent from '../../../layouts/LoginLayout/LoginContent';
import { useThemeComponents } from '../../../themes/hooks';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import validators from '../../../utilities/validators';

const Paragraph = styled(Typography.Paragraph)`
    text-align: center;
`;

type FormValues = { email: string };

const formValidator = validators.validEmail('email');

type RequestNewPasswordFieldsProps = {
    isSubmitting: boolean;
    isValid: boolean;
};
const RequestNewPasswordFields = ({ isSubmitting, isValid }: RequestNewPasswordFieldsProps) => {
    const { t } = useTranslation(['signInPage']);
    const [{ value: email }, , { setValue: setEmail }] = useField('email');
    const {
        Button,
        FormFields: { InputField },
    } = useThemeComponents();
    useEffect(() => {
        setEmail(email.trim());
    }, [email, setEmail]);

    return (
        <>
            <InputField
                {...t('signInPage:requestNewPassword.fields.email', { returnObjects: true })}
                name="email"
                prefix={<UserOutlined />}
            />
            <Button disabled={!isValid} htmlType="submit" loading={isSubmitting} size="large" type="primary" block>
                {t('signInPage:requestNewPassword.submitBtn')}
            </Button>
        </>
    );
};

const RequestNewPassword = () => {
    const { t } = useTranslation(['signInPage']);
    const navigate = useNavigate();
    const state = useLocation().state as { email?: string };
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [mutation] = useApplyForPasswordChangeMutation();
    const validate = useValidator(formValidator);
    const router = useRouter(false);
    const { Button } = useThemeComponents();

    const onSubmit = useHandleError(
        async ({ email }: FormValues) => {
            message.loading({
                type: 'loading',
                content: t('signInPage:requestNewPassword.submittingMessage'),
                key: 'primary',
                duration: 0,
            });
            const { data } = await mutation({
                variables: { email, companyId: router?.companyId },
            }).finally(() => {
                message.destroy('primary');
            });

            setIsSubmitted(data?.applyForPasswordChange);
        },
        [router?.companyId, mutation, t]
    );

    const element = isSubmitted ? (
        <Space direction="vertical">
            <Paragraph>{t('signInPage:requestNewPassword.successMessage')}</Paragraph>
            <Button
                htmlType="button"
                onClick={() => {
                    navigate('../signIn');
                }}
                size="large"
                type="primary"
                block
            >
                {t('signInPage:requestNewPassword.authenticateLink')}
            </Button>
        </Space>
    ) : (
        <Formik initialValues={{ email: state?.email || '' }} onSubmit={onSubmit} validate={validate}>
            {({ isSubmitting, isValid }) => (
                <Form>
                    <RequestNewPasswordFields isSubmitting={isSubmitting} isValid={isValid} />
                </Form>
            )}
        </Formik>
    );

    return <LoginContent title={t('signInPage:requestNewPassword.title')}>{element}</LoginContent>;
};

export default RequestNewPassword;
