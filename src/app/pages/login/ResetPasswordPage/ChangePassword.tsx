import { LockOutlined } from '@ant-design/icons';
import { Al<PERSON>, Space, message, Form as AntdForm } from 'antd';
import Title from 'antd/lib/typography/Title';
import { Formik, Form, FormikValues, useField } from 'formik';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useChangePasswordFromTokenMutation } from '../../../api/mutations/changePasswordFromToken';
import PasswordRequirementList from '../../../components/PasswordRequirementList';
import LoginContent from '../../../layouts/LoginLayout/LoginContent';
import { useThemeComponents } from '../../../themes/hooks';
import requirements from '../../../utilities/passwordRequirement';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import validators from '../../../utilities/validators';

type FormValues = { password: string; passwordRepeat: string };

const initialValues = { password: '', passwordRepeat: '' };

const passwordValidator = validators.compose(
    validators.requiredString('password'),
    validators.requiredString('passwordRepeat'),
    validators.validPasswordFormat('password'),
    validators.matchPassword('passwordRepeat', 'password')
);

export enum TranslationType {
    NewPassword = 'newPassword',
    SetupPassword = 'setupPassword',
}
export type ChangePasswordProps = { token: string; translationType: TranslationType };
type ChangePasswordFieldsProps = {
    isSubmitting: boolean;
    translationType: TranslationType;
    values: FormikValues;
};

const ChangePasswordFields = ({ isSubmitting, values, translationType }: ChangePasswordFieldsProps) => {
    const { t } = useTranslation(['signInPage', 'userSelf']);
    const {
        Button,
        FormFields: { PasswordField },
    } = useThemeComponents();

    const [{ value: password }, , { setValue: setPassword }] = useField('password');
    const [{ value: passwordRepeat }, , { setValue: setPasswordRepeat }] = useField('passwordRepeat');

    useEffect(() => {
        setPassword(password.trim());
        setPasswordRepeat(passwordRepeat.trim());
    }, [password, passwordRepeat, setPassword, setPasswordRepeat]);

    const validatePassword = useCallback(
        value =>
            requirements(t).map(({ description, regex }) => ({
                description,
                isChecked: regex.test(value),
            })),
        [t]
    );

    const description = useMemo(
        () =>
            translationType === TranslationType.SetupPassword ? (
                <AntdForm.Item>
                    <Alert description={t('signInPage:setupPassword.description')} type="info" showIcon />
                </AntdForm.Item>
            ) : null,
        [translationType, t]
    );

    return (
        <>
            {description}
            <PasswordField
                {...t(`signInPage:${translationType}.fields.password`, { returnObjects: true })}
                name="password"
                prefix={<LockOutlined />}
            />
            <PasswordField
                {...t(`signInPage:${translationType}.fields.passwordRepeat`, { returnObjects: true })}
                name="passwordRepeat"
                prefix={<LockOutlined />}
            />
            <Space direction="vertical" size="middle">
                <div>
                    <Title level={5}>{t('userSelf:passwordSettings.requirements.title')}</Title>
                    <PasswordRequirementList requirements={validatePassword(values.password)} />
                </div>
                <Button htmlType="submit" loading={isSubmitting} type="primary" block>
                    {t(`signInPage:${translationType}.submitBtn`)}
                </Button>
            </Space>
        </>
    );
};

const ChangePassword = ({ token, translationType }: ChangePasswordProps) => {
    const { t } = useTranslation(['signInPage', 'userSelf']);
    const navigate = useNavigate();
    const validate = useValidator(passwordValidator);
    const { Button } = useThemeComponents();

    const [isSuccessful, setIsSuccessful] = useState(false);
    const [mutation] = useChangePasswordFromTokenMutation();

    const onSubmit = useHandleError(
        async ({ password }: FormValues) => {
            message.loading({
                type: 'loading',
                content: t(`signInPage:${translationType}.submittingMessage`),
                key: 'primary',
                duration: 0,
            });

            const { data } = await mutation({ variables: { token, password } }).finally(() => {
                message.destroy('primary');
            });

            setIsSuccessful(data?.changePasswordFromToken);
        },
        [mutation, setIsSuccessful, token, translationType, t]
    );

    const element = isSuccessful ? (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Alert description={t(`signInPage:${translationType}.successMessage`)} type="success" showIcon />
            <Button
                htmlType="button"
                onClick={() => {
                    navigate('../signIn', { state: { noWebAuthn: true } });
                }}
                type="dashed"
                block
            >
                {t(`signInPage:${translationType}.authenticateLink`)}
            </Button>
        </Space>
    ) : (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ isSubmitting, values }) => (
                <Form>
                    <ChangePasswordFields
                        isSubmitting={isSubmitting}
                        translationType={translationType}
                        values={values}
                    />
                </Form>
            )}
        </Formik>
    );

    return <LoginContent title={t(`signInPage:${translationType}.title`)}>{element}</LoginContent>;
};

export default ChangePassword;
