import { intersection } from 'lodash/fp';
import { useMemo } from 'react';
import { DebugJourneyDataFragment } from '../../../api/fragments/DebugJourneyData';
import type { FinderVehicleDataFragment } from '../../../api/fragments/FinderVehicleData';
import {
    ApplicationStage,
    FinderVehicleCondition,
    PreferenceValue,
    type FinderApplicationPublicModule,
} from '../../../api/types';

export type FinderApplicationState = Extract<
    DebugJourneyDataFragment['application'],
    { __typename: 'FinderApplication' }
>;

// Although preowned not listed in graphql vehicle type
// it's defined in AFC finder details, so include it
const preownedConditions = ['classic', 'used', 'porsche_approved', 'preowned'];

export const getShouldShowFinanceCalculator = (
    displayFinanceCalculator: FinderApplicationPublicModule['displayFinanceCalculator']
) => {
    if (displayFinanceCalculator === PreferenceValue.Yes || displayFinanceCalculator === PreferenceValue.Optional) {
        return true;
    }

    return false;
};

export const useIsVehicleAvailableForApplication = (
    previousApplication: FinderApplicationState | null,
    vehicle: FinderVehicleDataFragment
) =>
    useMemo(() => {
        if (!previousApplication) {
            return !vehicle.isDeleted && vehicle.isActive;
        }

        if (previousApplication.__typename !== 'FinderApplication') {
            throw new Error('invalid application type');
        }

        if (
            intersection(
                [ApplicationStage.Financing, ApplicationStage.Insurance, ApplicationStage.Reservation],
                previousApplication.stages
            ).length
        ) {
            return true;
        }

        return !vehicle.isDeleted && vehicle.isActive;
    }, [previousApplication, vehicle.isActive, vehicle.isDeleted]);

export const getFinderVehicleCondition = (finder?: FinderVehicleDataFragment) => {
    if (!finder) {
        return null;
    }

    const condition = finder.listing.vehicle.condition.value?.toLowerCase();
    const porscheApproved = finder.listing.warranty?.porscheApproved;

    if (condition === 'new') {
        return FinderVehicleCondition.New;
    }

    if (preownedConditions.includes(condition)) {
        return porscheApproved ? FinderVehicleCondition.PorscheApproved : FinderVehicleCondition.Preowned;
    }

    return null;
};
