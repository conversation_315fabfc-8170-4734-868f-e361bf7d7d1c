/* eslint-disable max-len */
import { Route, Routes } from 'react-router-dom';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import PorscheNotFound from '../../../components/results/PorscheNotFound';
import { useThemeComponents } from '../../../themes/hooks';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import SessionRevokedPage from '../SessionRevokedPage';
import CustomerValidationPage from '../StandardApplicationEntrypoint/CustomerValidationPage';
import TestDriveJourney from '../TestDriveEntrypoint/Journey';
import { FinderApplicationProvider } from './FinderApplicationContext';
import FinderJourneyPath from './FinderJourneyPath';
import { FinderLeadProvider } from './FinderLeadContext';
import Journey from './Journey';
import ThankYouPage from './ThankYou';

export type FinderApplicationPublicAccessEntrypointProps = {
    endpoint: FinderApplicationPublicAccessEntrypointContextDataFragment;
};

const FinderApplicationPublicAccessEntrypoint = ({ endpoint }: FinderApplicationPublicAccessEntrypointProps) => {
    const { FinderLayout } = useThemeComponents();
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <FinderLeadProvider>
                <Routes>
                    <Route
                        key="appliedFinancingFromDetails"
                        element={<CustomerValidationPage layout={FinderLayout} />}
                        path="remote/authorize"
                    />
                    <Route
                        key="finderRoot"
                        element={<FinderApplicationProvider endpoint={endpoint} />}
                        path=":listingId"
                    >
                        <Route key="offer" element={<FinderJourneyPath endpoint={endpoint} />} index />
                        <Route key="testdrive" element={<TestDriveJourney endpoint={endpoint} />} path="testdrive" />
                        <Route key="apply" element={<Journey endpoint={endpoint} />} path="apply" />
                        <Route key="sessionRevoked" element={<SessionRevokedPage />} path="sessionRevoked" />
                        <Route key="thankyou" element={<ThankYouPage />} path="thankyou" />
                        <Route key="404" element={<PorscheNotFound listingUrl={endpoint.listingUrl} />} path="*" />
                    </Route>
                    <Route key="404" element={<PorscheNotFound listingUrl={endpoint.listingUrl} />} path="*" />
                </Routes>
            </FinderLeadProvider>
        </>
    );
};

export default FinderApplicationPublicAccessEntrypoint;
