/* eslint-disable max-len */
import { Col, Row, Space } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import urljoin from 'url-join';
import { StartTestDriveMutation } from '../../../../api/mutations/startTestDrive';
import { ApplicationStage } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getApplicationIdentifier } from '../../../../utilities/application';
import useTestDriveStartModal from '../../../shared/ApplicationDetailsPage/generic/TestDriveModal/TestDriveStartedModal';
import FullJourneySteps from '../../../shared/JourneyPage/FullJourneySteps';
import BookingDeposit from '../../../shared/ThankYou/BookingDeposit';
import DealerInfo from '../../../shared/ThankYou/DealerInfo';
import SubmittedContent from '../../../shared/ThankYou/SubmittedContent';
import VehicleDetails from '../../../shared/ThankYou/VehicleDetails';
import useDealerSpecificPaymentSetting from '../../../shared/useDealerSpecificPaymentSetting';
import VehicleInfo from '../ApplicantKYC/VehicleInfo';
import { useFinderApplicationContext } from '../FinderApplicationContext';
import { usePersistFinderJourneyValues } from '../OfferPage/FinderVehicleDetailsPage/usePersistFinderJourneyValues';
import { JourneyStage, MaybeJourneyState } from '../types';
import VisitModelPageButton from './VisitModelPageButton';

type ThankYouContentProps = {
    state: MaybeJourneyState;
};

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };
const colSpanLeftV3 = { lg: 9, md: 12, sm: 24, xs: 24 };
const colSpanRightV3 = { lg: 10, md: 12, sm: 24, xs: 24 };

const ThankYouContent = ({ state }: ThankYouContentProps) => {
    const { t } = useTranslation(['finderJourney', 'paymentDetails']);
    const navigate = useNavigate();
    const router = useRouter(false);
    const { FinderLayout, Button } = useThemeComponents();
    const { layout } = useRouter();

    const { application } = state;
    const { vehicle, dealer, endpoint, module } = useFinderApplicationContext();
    const { remove: removePersistedJourneyValues } = usePersistFinderJourneyValues();

    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    const onFinishModal = useCallback(
        (data: StartTestDriveMutation) => {
            navigate('../testdrive', { state: { token: data?.result?.token, stage: JourneyStage.Initialize } });
        },
        [navigate]
    );

    useEffect(() => {
        removePersistedJourneyValues();
    }, [removePersistedJourneyValues]);

    const [startTestDrive, renderTestDriveModal] = useTestDriveStartModal(
        {
            __typename: 'FinderApplication',
            id: application.id,
            vehicle:
                application.vehicle?.__typename === 'FinderVehicle'
                    ? {
                          __typename: application.vehicle.__typename,
                          id: application.vehicle.id,
                          moduleId: application.vehicle.moduleId,
                          listing: application.vehicle.listing,
                      }
                    : undefined,
            appointmentStage: application.appointmentStage,
            draftFlow: application.draftFlow,
            moduleId: module.id,
            dealer: application.dealer,
            vehicleId: application.vehicle.__typename === 'FinderVehicle' ? application.vehicle.id : undefined,
        },
        ApplicationStage.Appointment,
        onFinishModal
    );

    const { withCustomerDevice, bank, lead: latestLead } = application;

    const testDriveJourney =
        module.appointmentModule?.__typename === 'AppointmentModule' &&
        module.appointmentModule?.hasTestDriveProcess &&
        application.draftFlow.isAppointmentCompleted;

    const identifier = useMemo(
        () =>
            getApplicationIdentifier(application, [
                ApplicationStage.Financing,
                ApplicationStage.Reservation,
                ApplicationStage.Lead,
            ]),
        [application]
    );

    const launchpadEndpoint = useMemo(() => {
        if (!router?.endpoints?.length || !latestLead) {
            return null;
        }

        const launchpadModuleEndpoint = router.endpoints.find(endpoint => {
            if (endpoint.__typename === 'LaunchPadApplicationEntrypoint') {
                return endpoint.launchPadApplicationModule.id === latestLead.moduleId;
            }

            return false;
        });

        return launchpadModuleEndpoint
            ? urljoin(launchpadModuleEndpoint.pathname, 'leads', latestLead.versioning.suiteId)
            : null;
    }, [latestLead, router]);

    const showStartTestDriveButton =
        testDriveJourney &&
        endpoint.__typename === 'FinderApplicationEntrypoint' &&
        !isNil(application.draftFlow.isTestDriveProcessStarted) &&
        !application.draftFlow.isTestDriveProcessStarted &&
        isNil(application.appointmentStage?.checkOutTime);

    const onButtonClick = useCallback(async () => {
        if (launchpadEndpoint && latestLead?.__typename === 'LaunchpadLead') {
            navigate(`/${launchpadEndpoint}`, {
                state: { completedExternalJourney: true },
            });

            return;
        }

        if (showStartTestDriveButton) {
            startTestDrive();
        } else {
            window.open(vehicle.url, '_blank');
        }
    }, [latestLead, launchpadEndpoint, navigate, showStartTestDriveButton, startTestDrive, vehicle.url]);

    const { paymentSetting, depositAmount } = useDealerSpecificPaymentSetting(module, dealer.id);

    const { pageTitle, pageSubtitle, pageDescription, contactText, reference, primaryButtonText } = useMemo(() => {
        const primaryButtonText = (() => {
            if (latestLead && latestLead.__typename === 'LaunchpadLead') {
                return t('finderJourney:thankYou.buttons.viewAllActivities');
            }

            if (showStartTestDriveButton) {
                return t('finderJourney:thankYou.buttons.testDriveJourney');
            }

            return t('finderJourney:thankYou.buttons.continueBrowsing');
        })();

        if (withCustomerDevice) {
            return {
                pageTitle: t('finderJourney:thankYou.titles.withCustomerDevice'),
                pageSubtitle: t('finderJourney:thankYou.subTitles.withCustomerDevice'),
                pageDescription: t('finderJourney:thankYou.descriptions.reserveSuccessfulWithoutEmail'),
                contactText: bank?.remoteFlowAcknowledgmentInfo,
                reference: undefined,
                primaryButtonText,
            };
        }

        return {
            pageTitle: v3LayoutType
                ? t('finderJourney:thankYou.titles.defaultV3')
                : t('finderJourney:thankYou.titles.default'),
            pageSubtitle: t('finderJourney:thankYou.subTitles.reserveSuccessful'),
            pageDescription: t('finderJourney:thankYou.descriptions.reserveSuccessfulWithEmail'),
            contactText: undefined,
            reference: t('finderJourney:thankYou.reference', {
                reference: identifier,
            }),
            primaryButtonText,
        };
    }, [withCustomerDevice, t, identifier, latestLead, showStartTestDriveButton, bank?.remoteFlowAcknowledgmentInfo]);

    return (
        <FinderLayout headerHeadingAlign="center" title={pageTitle}>
            <BasicProLayoutContainer>
                <FullJourneySteps
                    stages={state?.stages}
                    style={v3LayoutType ? { paddingTop: 0, paddingBottom: '36px' } : undefined}
                />
                <Row gutter={[24, 24]} justify="center" style={{ marginBottom: '24px' }}>
                    <Col {...(v3LayoutType ? colSpanLeftV3 : colSpan)}>
                        <Space direction="vertical" size={24} style={{ width: '100%' }}>
                            <VehicleDetails
                                state={state}
                                vehicleSection={<VehicleInfo application={state.application} />}
                            />
                            {depositAmount && paymentSetting && dealer.integrationDetails.assortment && (
                                <BookingDeposit depositAmount={depositAmount} />
                            )}
                        </Space>
                    </Col>
                    <Col {...(v3LayoutType ? colSpanRightV3 : colSpan)}>
                        <Space
                            className={v3LayoutType && 'v3-layout-card'}
                            direction="vertical"
                            size={v3LayoutType ? 36 : 32}
                            style={{ width: '100%' }}
                        >
                            <SubmittedContent
                                description={pageDescription}
                                reference={reference}
                                subDescription={contactText}
                                title={pageSubtitle}
                            />
                            <Space direction="vertical" size={24} style={{ width: '100%' }}>
                                <DealerInfo dealer={dealer} />
                                <Button onClick={onButtonClick} type="primary" block>
                                    {primaryButtonText}
                                </Button>
                                <VisitModelPageButton />
                            </Space>
                        </Space>
                    </Col>
                </Row>
                {renderTestDriveModal()}
            </BasicProLayoutContainer>
        </FinderLayout>
    );
};

export default ThankYouContent;
