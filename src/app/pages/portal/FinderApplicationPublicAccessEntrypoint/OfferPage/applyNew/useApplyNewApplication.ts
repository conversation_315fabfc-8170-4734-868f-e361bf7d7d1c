import { useApolloClient } from '@apollo/client';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../../api/queries/getApplicationJourney';
import { useThemeComponents } from '../../../../../themes/hooks';
import getApolloErrors from '../../../../../utilities/getApolloErrors';
import { useParseJWTPayload } from '../../../../../utilities/parseJWTPayload';
import { useFinderApplicationContext } from '../../FinderApplicationContext';
import type { FinderApplicationState } from '../../shared';

type TokenPayload = {
    applicationModuleId: { $oid: string };
    origin: 'draft' | 'remote-applicant' | 'remote-guarantor';
};

const useApplyNewApplication = (): [boolean, boolean, FinderApplicationState | null] => {
    const state = useLocation().state as { token?: string };

    if (!state?.token) {
        return [false, false, null];
    }

    const apolloClient = useApolloClient();
    const { notification } = useThemeComponents();
    const { module } = useFinderApplicationContext();
    const [application, setApplication] = useState<FinderApplicationState | null>(null);
    const [error, setError] = useState<Error | null>(null);

    const { applicationModuleId } = useParseJWTPayload<TokenPayload>(state.token);
    const isValid = applicationModuleId.$oid === module.id;

    useEffect(() => {
        apolloClient
            .query<GetApplicationJourneyQuery, GetApplicationJourneyQueryVariables>({
                query: GetApplicationJourneyDocument,
                fetchPolicy: 'no-cache',
                variables: { token: state.token, refreshToken: true },
            })
            .then(result => {
                if (result.data.result.application.__typename === 'FinderApplication') {
                    setApplication(result.data.result.application);
                }
            })
            .catch(error => {
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors !== null) {
                    const { $root: rootError } = apolloErrors;

                    if (rootError) {
                        notification.error(rootError);
                    }
                } else {
                    console.error(error);
                }

                setError(error);
            });
    }, [apolloClient, state?.token, notification]);

    if (!isValid || !!error || (application && application?.__typename !== 'FinderApplication')) {
        // things are not as expected

        return [true, false, null];
    }

    return [true, true, application];
};

export default useApplyNewApplication;
