/* eslint-disable max-len */
import { Divider, Typography } from 'antd';
import styled, { css } from 'styled-components';
import { DealerListDataFragment } from '../../../../../api/fragments/DealerListData';
import { FinderVehicleDataFragment } from '../../../../../api/fragments/FinderVehicleData';
import { CompanyTheme } from '../../../../../api/types';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import ConditionText from './ConditionText';

const StyledConditionInfo = styled.div<{
    companyTheme?: CompanyTheme;
    v3LayoutType?: boolean;
}>`
    ${props =>
        props.companyTheme === CompanyTheme.PorscheV3 &&
        css`
            font-size: 16px;
        `}
    margin-bottom: ${props => (props.v3LayoutType ? '0' : '32px')};
`;

const { Text } = Typography;

type ConditionInfoProps = {
    vehicle: FinderVehicleDataFragment;
    module: any;
    dealer: DealerListDataFragment;
    isPublicAccess: boolean;
};

const ConditionInfo = ({ vehicle, module, dealer, isPublicAccess }: ConditionInfoProps) => {
    const { theme } = useThemeComponents();
    const { layout } = useRouter();

    return (
        <StyledConditionInfo companyTheme={theme} v3LayoutType={layout.__typename === 'PorscheV3Layout'}>
            <ConditionText module={module} vehicle={vehicle} />
            {isPublicAccess && (
                <>
                    <Divider type="vertical" />
                    <Text>{dealer.displayName}</Text>
                </>
            )}
        </StyledConditionInfo>
    );
};

export default ConditionInfo;
