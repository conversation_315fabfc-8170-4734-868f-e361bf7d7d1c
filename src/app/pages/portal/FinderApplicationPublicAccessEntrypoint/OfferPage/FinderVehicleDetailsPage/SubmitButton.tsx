import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import breakpoints from '../../../../../utilities/breakpoints';

type SubmitButtonProps = {
    disabled: boolean;
};

const FooterButtonContainer = styled.div`
    display: block;
    ${css`
        & .ant-btn {
            min-width: fit-content;

            &:last-child {
                margin-top: 18px;
                margin-bottom: 18px;
                @media screen and (min-width: ${breakpoints.md}) {
                    margin-right: 36px;
                }
            }
        }
    `}
`;

const SubmitButton = ({ disabled }: SubmitButtonProps) => {
    const { t } = useTranslation(['carDetails']);
    const { layout } = useRouter();

    const { Button } = useThemeComponents();

    return (
        <FooterButtonContainer>
            <Button
                key="submit"
                disabled={disabled}
                form="calculatorForm"
                htmlType="submit"
                porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                type="primary"
            >
                {t('carDetails:actions.next')}
            </Button>
        </FooterButtonContainer>
    );
};

export default SubmitButton;
