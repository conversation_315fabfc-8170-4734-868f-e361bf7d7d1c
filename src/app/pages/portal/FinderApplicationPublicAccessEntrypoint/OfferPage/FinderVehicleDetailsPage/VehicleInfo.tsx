import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { FinderVehicleDataFragment } from '../../../../../api/fragments/FinderVehicleData';
import { useRouter } from '../../../../../components/contexts/shared';
import breakpoints from '../../../../../utilities/breakpoints';
import usePublic from '../../../../../utilities/usePublic';
import { DynamicText, FinderCockpitImage, ImageBox, ImageWrapper, SpaceImage } from '../../ApplicantKYC/VehicleInfo';

type VehicleInfoProps = {
    vehicle: FinderVehicleDataFragment;
};

const LayoutGrid = styled.div<{ v3LayoutType?: boolean }>`
    display: grid;
    column-gap: clamp(16px, 12px + 1.25vw, 36px);
    grid-template-columns: ${props => (props.v3LayoutType ? 'auto' : '1fr 1fr')};
    margin-bottom: 48px;

    @media (max-width: ${breakpoints.xl}) {
        grid-template-columns: 1fr;
    }

    ${props =>
        props.v3LayoutType &&
        css`
            margin-top: 24px;
        `}
`;

const LayoutGridItem = styled.div`
    display: flex;
    flex-direction: column;
`;

const TextInfoGrid = styled.div`
    display: grid;
    column-gap: clamp(16px, 12px + 1.25vw, 36px);
    grid-template-columns: 1fr 1fr;
`;

const TextInfoGridItem = styled.div<{ v3LayoutType?: boolean }>`
    border-bottom: 1px solid rgb(216, 216, 219);
    display: flex;
    flex-direction: column;
    padding: ${props => (props.v3LayoutType ? '16px 0px' : '12px 0px')};
`;

const SpaceImageItem = styled(SpaceImage)<{ v3LayoutType?: boolean }>`
    padding: ${props => (props.v3LayoutType ? '16px 0px' : '12px 0px')};
    &:first-child {
        padding-top: 0;
    }
    border-bottom: 1px solid rgb(216, 216, 219);
`;

const VehicleInfo = ({ vehicle }: VehicleInfoProps) => {
    const { t } = useTranslation(['finderJourney']);
    const { layout } = useRouter();

    const transmissionManual = usePublic('icons/manual.jpg');
    const transmissionAuto = usePublic('icons/automatic.jpg');

    const {
        interior,
        transmission,
        mileage,
        firstRegistrationDate,
        numberOfPreviousOwners,
        power,
        accidentFree,
        engineType,
    } = vehicle.listing.vehicle;

    const seatStitchColor = interior?.color?.seatStitchingColorHexCode ?? '#ffffff';
    const seatColor = interior?.color?.seatColorHexCode ?? '#ffffff';
    const cockpitStitchColor = interior?.color?.cockpitStitchingColorHexCode ?? '#ffffff';
    const cockpitColor = interior?.color?.cockpitColorHexCode ?? '#ffffff';

    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    return (
        <LayoutGrid className={v3LayoutType ? 'v3-layout-card-small' : ''} v3LayoutType={v3LayoutType}>
            <LayoutGridItem>
                <SpaceImageItem size={v3LayoutType ? 16 : 8} v3LayoutType={v3LayoutType}>
                    <ImageWrapper v3LayoutType={v3LayoutType}>
                        <ImageBox
                            $color={vehicle.listing.vehicle.exteriorColor.value}
                            $height={v3LayoutType ? '70px' : '44px'}
                            $width={v3LayoutType ? '70px' : '60px'}
                            v3LayoutType={v3LayoutType}
                        />
                    </ImageWrapper>
                    <div>
                        <DynamicText v3LayoutType={v3LayoutType} isLabel>
                            {t('finderJourney:vehicleInterest.fields.exteriorColor')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} isValue>
                            {vehicle.listing.vehicle.exteriorColor?.name?.localize}
                        </DynamicText>
                    </div>
                </SpaceImageItem>
                <SpaceImageItem size={v3LayoutType ? 16 : 8} v3LayoutType={v3LayoutType}>
                    <ImageWrapper v3LayoutType={v3LayoutType}>
                        <ImageBox
                            $height={v3LayoutType ? '70px' : '44px'}
                            $width={v3LayoutType ? '70px' : '60px'}
                            style={{ display: 'flex' }}
                            v3LayoutType={v3LayoutType}
                        >
                            <FinderCockpitImage
                                cockpitColor={cockpitColor}
                                cockpitStitchColor={cockpitStitchColor}
                                seatColor={seatColor}
                                seatStitchColor={seatStitchColor}
                                v3LayoutType={v3LayoutType}
                            />
                        </ImageBox>
                    </ImageWrapper>
                    <div>
                        <DynamicText v3LayoutType={v3LayoutType} isLabel>
                            {t('finderJourney:vehicleInterest.fields.interiorColor')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} isValue>
                            {interior.name.localize}
                        </DynamicText>
                    </div>
                </SpaceImageItem>
                <SpaceImageItem size={v3LayoutType ? 16 : 8} v3LayoutType={v3LayoutType}>
                    <ImageWrapper v3LayoutType={v3LayoutType}>
                        <ImageBox
                            $height={v3LayoutType ? '70px' : '44px'}
                            $width={v3LayoutType ? '70px' : '60px'}
                            as="img"
                            src={
                                transmission?.localize?.toUpperCase() === 'MANUAL'
                                    ? transmissionManual
                                    : transmissionAuto
                            }
                            v3LayoutType={v3LayoutType}
                        />
                    </ImageWrapper>
                    <div>
                        <DynamicText v3LayoutType={v3LayoutType} isLabel>
                            {t('finderJourney:vehicleInterest.fields.transmission')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} isValue>
                            {transmission.localize}
                        </DynamicText>
                    </div>
                </SpaceImageItem>
            </LayoutGridItem>
            <LayoutGridItem>
                <TextInfoGrid>
                    <TextInfoGridItem v3LayoutType={v3LayoutType}>
                        <DynamicText spacing={v3LayoutType ? 4 : 0} v3LayoutType={v3LayoutType} invert isLabel>
                            {t('finderJourney:vehicleInterest.fields.mileage')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} invert isValue>
                            {mileage?.localize}
                        </DynamicText>
                    </TextInfoGridItem>
                    <TextInfoGridItem v3LayoutType={v3LayoutType}>
                        <DynamicText spacing={v3LayoutType ? 4 : 0} v3LayoutType={v3LayoutType} invert isLabel>
                            {t('finderJourney:vehicleInterest.fields.power')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} invert isValue>
                            {`${power?.kilowatt?.localize ?? '-'}/${power?.horsepower?.localize ?? '-'}`}
                        </DynamicText>
                    </TextInfoGridItem>
                    <TextInfoGridItem v3LayoutType={v3LayoutType}>
                        <DynamicText spacing={v3LayoutType ? 4 : 0} v3LayoutType={v3LayoutType} invert isLabel>
                            {t('finderJourney:vehicleInterest.fields.firstRegistration')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} invert isValue>
                            {firstRegistrationDate?.localize}
                        </DynamicText>
                    </TextInfoGridItem>
                    <TextInfoGridItem v3LayoutType={v3LayoutType}>
                        <DynamicText spacing={v3LayoutType ? 4 : 0} v3LayoutType={v3LayoutType} invert isLabel>
                            {t('finderJourney:vehicleInterest.fields.engineType')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} invert isValue>
                            {engineType?.localize}
                        </DynamicText>
                    </TextInfoGridItem>
                    <TextInfoGridItem v3LayoutType={v3LayoutType}>
                        <DynamicText spacing={v3LayoutType ? 4 : 0} v3LayoutType={v3LayoutType} invert isLabel>
                            {t('finderJourney:vehicleInterest.fields.previousOwners')}
                        </DynamicText>
                        <DynamicText v3LayoutType={v3LayoutType} invert isValue>
                            {numberOfPreviousOwners ?? '0'}
                        </DynamicText>
                    </TextInfoGridItem>
                    {!isNil(accidentFree) && (
                        <TextInfoGridItem v3LayoutType={v3LayoutType}>
                            <DynamicText spacing={v3LayoutType ? 4 : 0} v3LayoutType={v3LayoutType} invert isLabel>
                                {t('finderJourney:vehicleInterest.fields.accidentHistory')}
                            </DynamicText>
                            <DynamicText v3LayoutType={v3LayoutType} invert isValue>
                                {accidentFree
                                    ? t('finderJourney:vehicleInterest.options.accidentHistory.yes')
                                    : t('finderJourney:vehicleInterest.options.accidentHistory.no')}
                            </DynamicText>
                        </TextInfoGridItem>
                    )}
                </TextInfoGrid>
            </LayoutGridItem>
        </LayoutGrid>
    );
};

export default VehicleInfo;
