import { PTag } from '@porsche-design-system/components-react';
import { Typography } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { FinderVehicleDataFragment } from '../../../../../api/fragments/FinderVehicleData';
import { FinderVehicleCondition } from '../../../../../api/types';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import renderMarkdown from '../../../../../utilities/renderMarkdown';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import { FinderModule } from '../../FinderApplicationContext';
import { getFinderVehicleCondition } from '../../shared';

const { Text, Paragraph } = Typography;

const StyledText = styled(Text)<{ underline: boolean }>`
    ${props => (props.underline ? 'cursor: pointer;' : '')}
`;

type ConditionTextProps = {
    vehicle: FinderVehicleDataFragment;
    module: FinderModule;
};

const ConditionText = ({ vehicle, module }: ConditionTextProps) => {
    const { t } = useTranslation(['moduleDetails']);
    const translate = useTranslatedString();
    const { layout } = useRouter();

    const condition = getFinderVehicleCondition(vehicle);
    const { Modal } = useThemeComponents();

    const isPorscheApprovedClickable =
        condition === FinderVehicleCondition.PorscheApproved && !!translate(module.porscheApprovedInfo);
    const [visible, setVisible] = useState(false);
    const onOpen = () => {
        if (isPorscheApprovedClickable) {
            setVisible(true);
        }
    };
    const onClose = () => {
        setVisible(false);
    };

    const content = t(
        `moduleDetails:explanations.finderVehicleCondition.${condition.replace(
            condition[0],
            condition[0].toLowerCase()
        )}`
    );

    return (
        <>
            {layout.__typename === 'PorscheV3Layout' ? (
                <PTag color="background-base">
                    {isPorscheApprovedClickable ? (
                        <button onClick={onOpen} type="button">
                            {content}
                        </button>
                    ) : (
                        content
                    )}
                </PTag>
            ) : (
                <StyledText onClick={onOpen} underline={isPorscheApprovedClickable}>
                    {content}
                </StyledText>
            )}
            <Modal
                footer={null}
                onCancel={onClose}
                open={visible}
                title={t('moduleDetails:explanations.finderVehicleCondition.porscheApproved')}
                width={480}
                centered
            >
                <Paragraph>{renderMarkdown(translate(module.porscheApprovedInfo))}</Paragraph>
            </Modal>
        </>
    );
};
export default ConditionText;
