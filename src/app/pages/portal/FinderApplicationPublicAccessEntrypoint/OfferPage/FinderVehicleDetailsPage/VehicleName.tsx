import { DownOutlined } from '@ant-design/icons';
import { PHeading } from '@porsche-design-system/components-react';
import { Typography } from 'antd';
import { groupBy, head, noop, uniq } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
// eslint-disable-next-line max-len
import { FinderApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/FinderApplicationEntrypointContextData';
// eslint-disable-next-line max-len
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { FinderVehicleDataFragment } from '../../../../../api/fragments/FinderVehicleData';
import SelectVehicleModalPure from '../../../../../calculator/GridCalculator/SelectVehicleModalPure';
import { useRouter } from '../../../../../components/contexts/shared';
import useTranslatedString, { TranslatedString } from '../../../../../utilities/useTranslatedString';
import NameLink from '../../../../shared/CIPage/VehicleNameLink';
import { useFinderApplicationContext } from '../../FinderApplicationContext';
import type { FinderApplicationState } from '../../shared';

const { Title } = Typography;

const Name = styled(Title)`
    text-align: initial;
    margin-bottom: 0 !important;
`;

const NLink = styled(NameLink)`
    &.ant-btn-link {
        margin-bottom: 0;
    }
`;

export type VehicleNameProps = {
    endpoint:
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
    vehicle: FinderVehicleDataFragment;
    applyNewApplication?: FinderApplicationState | null;
    disableVehicleSelection?: boolean;
};

const PublicVehicleName = ({
    vehicle,
    translatedString,
}: Pick<VehicleNameProps, 'vehicle'> & { translatedString: TranslatedString }) => (
    <Name level={1}>{translatedString(vehicle.name)}</Name>
);

type SelectVehicleModalProps = Pick<VehicleNameProps, 'vehicle'> & {
    translatedString: TranslatedString;
    modalVisible: boolean;
    setModalVisible: (visible: boolean) => void;
};
type VehicleInfo = {
    name: string;
    model: string;
    identifier: string;
};
type Option = {
    value: string;
    label: string;
};
export const SelectVehicleModal = ({
    modalVisible,
    setModalVisible,
    vehicle,
    translatedString,
}: SelectVehicleModalProps) => {
    const initial = useMemo<VehicleInfo>(
        () => ({
            name: translatedString(vehicle.name),
            model: vehicle.listing?.vehicle?.modelSeries?.localize,
            identifier: vehicle.listing?.id,
        }),
        [vehicle, translatedString]
    );
    // state to track current selected vehicle
    const [selected, setSelected] = useState<VehicleInfo>(initial);

    const { availableVehicles } = useFinderApplicationContext();
    const [vehicles, vehiclesByModel, modelOptions] = useMemo<
        [VehicleInfo[], Record<string, VehicleInfo[]>, Option[]]
    >(() => {
        const result = availableVehicles.map(({ name, listing }) => ({
            name: translatedString(name),
            model: listing?.vehicle?.modelSeries?.localize,
            identifier: listing?.id,
        }));

        if (!availableVehicles.some(({ id }) => id === vehicle.id)) {
            result.push({
                name: translatedString(vehicle.name),
                model: vehicle.listing?.vehicle?.modelSeries?.localize,
                identifier: vehicle.listing?.id,
            });
        }

        return [
            result,
            groupBy('model', result),
            uniq(result.map(({ model }) => model)).map(model => ({ label: model, value: model })),
        ];
    }, [availableVehicles, vehicle, translatedString]);

    const vehicleOptions = useMemo(
        () =>
            (vehiclesByModel[selected?.model] ?? []).map(({ name, identifier }) => ({
                label: name,
                value: identifier,
            })),
        [selected?.model, vehiclesByModel]
    );

    const handleCancel = useCallback(() => {
        setModalVisible(false);
    }, [setModalVisible]);

    const handleOnModelChange = useCallback(
        (value: string) => {
            const first = head(vehiclesByModel[value]);
            if (first) {
                setSelected(first);
            }
        },
        [vehiclesByModel]
    );

    const handleOnVehicleChange = useCallback(
        (value: string) => {
            const found = vehicles.find(({ identifier }) => identifier === value);
            if (found) {
                setSelected(found);
            }
        },
        [vehicles]
    );

    const navigate = useNavigate();
    const handleOnSelect = useCallback(() => {
        if (selected?.identifier) {
            navigate(`../${selected.identifier}`);
        }
    }, [selected?.identifier, navigate]);

    return (
        <SelectVehicleModalPure
            handleCancel={handleCancel}
            handleOnModelChange={handleOnModelChange}
            handleOnSelect={handleOnSelect}
            handleOnSubModelChange={noop}
            handleOnVehicleChange={handleOnVehicleChange}
            modalVisible={modalVisible}
            modelId={selected.model}
            modelOptions={modelOptions}
            subModelId=""
            subModelOptions={[]}
            vehicleId={selected.identifier}
            vehicleOptions={vehicleOptions}
        />
    );
};

const PrivateVehicleName = ({
    vehicle,
    translatedString,
    disableVehicleSelection = false,
}: Pick<VehicleNameProps, 'vehicle'> & {
    translatedString: TranslatedString;
    disableVehicleSelection?: boolean;
}) => {
    const { layout } = useRouter();
    const [modalVisible, setModalVisible] = useState<boolean>(false);

    const vehicleName = translatedString(vehicle.name);

    if (disableVehicleSelection) {
        return (
            <div>
                {layout?.__typename === 'PorscheV3Layout' ? (
                    <PHeading size="xx-large">{vehicleName}</PHeading>
                ) : (
                    <Name level={1}>{vehicleName}</Name>
                )}
            </div>
        );
    }

    return (
        <>
            <NLink onClick={() => setModalVisible(true)} type="link" block>
                <Name level={1}>{vehicleName}</Name>
                <DownOutlined style={{ fontSize: '0.875rem', position: 'relative', top: '3px' }} />
            </NLink>
            <SelectVehicleModal
                modalVisible={modalVisible}
                setModalVisible={setModalVisible}
                translatedString={translatedString}
                vehicle={vehicle}
            />
        </>
    );
};

const VehicleName = ({ vehicle, endpoint, applyNewApplication, disableVehicleSelection = false }: VehicleNameProps) => {
    const translatedString = useTranslatedString();

    if (endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint' || applyNewApplication) {
        return <PublicVehicleName translatedString={translatedString} vehicle={vehicle} />;
    }

    return (
        <PrivateVehicleName
            disableVehicleSelection={disableVehicleSelection}
            translatedString={translatedString}
            vehicle={vehicle}
        />
    );
};

export default VehicleName;
