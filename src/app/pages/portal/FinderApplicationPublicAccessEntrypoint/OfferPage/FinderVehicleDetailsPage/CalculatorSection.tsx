/* eslint-disable max-len */
import { useFormikContext } from 'formik';
import { isEmpty } from 'lodash/fp';
import { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { DealerListDataFragment } from '../../../../../api/fragments/DealerListData';
import { FinderApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/FinderApplicationEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { FinderVehicleDataFragment } from '../../../../../api/fragments/FinderVehicleData';
import {
    ApplicationMarket,
    FinancingPreferenceValue,
    PreferenceValue,
    StandardApplicationConfiguration,
} from '../../../../../api/types';
import InsuranceGridCalculator from '../../../../../calculator/Implementations/InsuranceGridCalculator';
import { CalculatorContext } from '../../../../../calculator/computing';
import { BankFieldContext } from '../../../../../calculator/computing/defaultFields/bankField';
import { InsurerFieldContext } from '../../../../../calculator/computing/defaultFields/insurerField';
import type { CalculatorFormValues } from '../../../../../calculator/form/types';
import { GenericCalculatorValues } from '../../../../../calculator/types';
import ParagraphEllipsis from '../../../../../components/ParagraphEllipsis';
import { useRouter } from '../../../../../components/contexts/shared';
import CheckboxField from '../../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../../themes/hooks';
import { fillDynamicDisclaimerTextValues } from '../../../../../utilities/journeys/calculator';
import { promoCodeValue } from '../../../../../utilities/promoCode';
import renderMarkdown from '../../../../../utilities/renderMarkdown';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import useDefaultMarketTypeValue from '../../../../../utilities/useDefaultMarketTypeValue';
import usePlaceholderValues from '../../../../../utilities/usePlaceholderValues';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import {
    hasFinancingScenario,
    hasNoFinancingScenario,
} from '../../../../admin/ModuleDetailsPage/modules/implementations/shared';
import PromoCodeField from '../../../../shared/CIPage/PromoCode';
import TradeIn from '../../../../shared/CIPage/TradeIn';
import { CheckboxContainer, Price, PriceContainer, StyledInfoCircleFilled } from '../../../../shared/CIPage/ui';
import useDealerSpecificPaymentSetting from '../../../../shared/useDealerSpecificPaymentSetting';
import useValidPromoCode from '../../../../shared/useValidPromoCode';
import { useFinderApplicationContext } from '../../FinderApplicationContext';
import { getShouldShowFinanceCalculator, type FinderApplicationState } from '../../shared';
import { ApplicationScenarioConfig, OfferPageActionStateProps } from '../../types';
import useApplyNewCondition from '../applyNew/useApplyNewCondition';
import ApplyFinance from './ApplyFinance';
import ConditionInfo from './ConditionInfo';
import VehicleName from './VehicleName';
import { DepositContainer, DepositLabel, DepositValue, SectionContainer, VehicleBriefContainer } from './ui';

const PriceDisclaimerContainer = styled.div`
    margin: 10px 0px;
    line-height: 1.3;
    margin-top: 10px;
    opacity: 0.8;
`;

type CalculatorSectionProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    dealer: DealerListDataFragment;
    endpoint:
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
    applicationScenario: ApplicationScenarioConfig;
    initialApplicationConfiguration?: StandardApplicationConfiguration;
    initialCalculatorValues: Partial<GenericCalculatorValues>;
    vehicle: FinderVehicleDataFragment;
    applyNewApplication?: FinderApplicationState | null;
} & OfferPageActionStateProps;

const CalculatorSection = ({
    actions,
    calculatorContext,
    dealer,
    endpoint,
    applicationScenario,
    initialApplicationConfiguration,
    initialCalculatorValues,
    state,
    vehicle,
    applyNewApplication,
}: CalculatorSectionProps) => {
    const { formatAmountWithCurrency } = useCompanyFormats();
    const { t } = useTranslation(['calculators', 'finder', 'paymentDetails', 'finderPrivate']);
    const translatedString = useTranslatedString();
    const validPromoCode = useMemo(() => !isEmpty(state.promoCode), [state.promoCode]);

    const [showCarPriceModal, setShowCarPriceModal] = useState(false);

    const { Checkbox, Tooltip, theme, WarningMessageBox } = useThemeComponents();
    const { layout } = useRouter();

    const { module } = useFinderApplicationContext();

    const { priceDisclaimer } = module;

    const { values: calculatorValues } = calculatorContext;
    const price = calculatorValues.carPriceAfterDiscount;

    const promoAmount = validPromoCode ? promoCodeValue(state.promoCode, calculatorValues.totalPrice) : 0;

    const [isCoeEnabled, coeAmount] = useMemo(() => {
        switch (initialCalculatorValues.market) {
            case ApplicationMarket.Singapore:
                return [true, initialCalculatorValues.coe];

            default:
                return [false, 0];
        }
    }, [initialCalculatorValues]);

    const totalPrice = coeAmount ? price + coeAmount - promoAmount : price - promoAmount;

    const { availableFinanceProducts, selectedBank } = calculatorContext.getFieldContext<BankFieldContext>('bank');

    const { availableInsuranceProducts } = calculatorContext.getFieldContext<InsurerFieldContext>('insurerId');

    // Intentionally froze selected bank, so it won't rerender on bank change
    const initialSelectedBank = useRef(selectedBank);

    const marketTypeValue = useDefaultMarketTypeValue(dealer.id, initialSelectedBank.current);
    const placeholders = usePlaceholderValues({ module, marketTypeValue });

    const usedPriceDisclaimer = useMemo(() => {
        const priceDisclaimerFromDealer = priceDisclaimer?.overrides?.find(
            ({ dealerId }) => dealerId === dealer.id
        )?.value;

        const priceDisclaimerData = priceDisclaimerFromDealer?.length
            ? priceDisclaimerFromDealer
            : priceDisclaimer?.defaultValue;

        return priceDisclaimerData?.length
            ? priceDisclaimerData.map(disclaimer => translatedString(disclaimer, placeholders))
            : null;
    }, [dealer.id, priceDisclaimer, translatedString, placeholders]);

    const hasValidPromoCode = useValidPromoCode({
        dealerId: dealer.id,
        variantSuiteId: vehicle.versioning.suiteId,
        promoCodeModuleId: module.promoCodeModule?.id,
        applicationModuleId: module.id,
    });

    const isPublicAccess = module.__typename === 'FinderApplicationPublicModule';

    const { values } = useFormikContext<CalculatorFormValues>();

    const showNoFPWarning =
        availableFinanceProducts.length <= 0 &&
        ((hasFinancingScenario(module.scenarios) &&
            module.financingPreference === FinancingPreferenceValue.Mandatory) ||
            (hasNoFinancingScenario(module.scenarios) && module.displayFinanceCalculator === PreferenceValue.Yes));

    const isFinancingSectionBigBottom = useMemo(
        () =>
            !(applicationScenario.withInsurance || module.showInsuranceCalculator) &&
            !module.tradeIn &&
            !module.testDrive &&
            module.displayFinanceCalculator !== PreferenceValue.Optional
                ? true
                : values.configuration.withFinancing,
        [
            applicationScenario.withInsurance,
            module.displayFinanceCalculator,
            module.showInsuranceCalculator,
            module.testDrive,
            module.tradeIn,
            values.configuration.withFinancing,
        ]
    );

    const isInsuranceSectionBigBottom = useMemo(
        () =>
            !module.tradeIn && !module.testDrive && module.displayFinanceCalculator !== PreferenceValue.Optional
                ? true
                : values.configuration.withInsurance,
        [module.displayFinanceCalculator, module.testDrive, module.tradeIn, values.configuration.withInsurance]
    );

    const { canApplyFinancing, canApplyRequestFinancing, canApplyInsurance, canApplyAppointment } =
        useApplyNewCondition(applyNewApplication, module);

    const isFinancingSectionVisible = useMemo(
        () => (applyNewApplication && (canApplyFinancing || canApplyRequestFinancing)) || !applyNewApplication,
        [applyNewApplication, canApplyFinancing, canApplyRequestFinancing]
    );

    const isInsuranceSectionVisible = useMemo(
        () => (applyNewApplication && canApplyInsurance) || !applyNewApplication,
        [applyNewApplication, canApplyInsurance]
    );

    const isAppointmentSectionVisible = useMemo(
        () => (applyNewApplication && canApplyAppointment) || !applyNewApplication,
        [applyNewApplication, canApplyAppointment]
    );

    const { paymentSetting, depositAmount } = useDealerSpecificPaymentSetting(module, dealer.id);
    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    return (
        <div
            className={v3LayoutType ? 'v3-layout-card-small' : ''}
            style={v3LayoutType ? { marginBottom: '140px' } : {}}
        >
            <VehicleBriefContainer>
                {!v3LayoutType && (
                    <>
                        <VehicleName applyNewApplication={applyNewApplication} endpoint={endpoint} vehicle={vehicle} />
                        <ConditionInfo
                            dealer={dealer}
                            isPublicAccess={isPublicAccess}
                            module={module}
                            vehicle={vehicle}
                        />
                    </>
                )}
                <PriceContainer>
                    <Price
                        className="carPrice"
                        clickable={!isPublicAccess}
                        companyTheme={theme}
                        isPromoValid={validPromoCode}
                        onClick={() => !isPublicAccess && setShowCarPriceModal(true)}
                        promo={promoAmount}
                    >
                        <span>{t('finder:offerPage.price.label')}</span>
                        <span>{formatAmountWithCurrency(price)}</span>
                    </Price>
                    {promoAmount && validPromoCode ? (
                        <Price companyTheme={theme} isCoeEnabled={isCoeEnabled}>
                            <span>
                                {t(
                                    isCoeEnabled
                                        ? 'finder:offerPage.priceWithoutCoe.label'
                                        : 'finder:offerCarPriceFieldModalPage.finalPrice.label'
                                )}
                            </span>
                            <span>{price && formatAmountWithCurrency(price - promoAmount)}</span>
                        </Price>
                    ) : null}
                    {coeAmount ? (
                        <Price companyTheme={theme} isCoeEnabled={isCoeEnabled}>
                            <span>{t('finder:offerPage.estimatedCoe.label')}</span>
                            <span>{formatAmountWithCurrency(coeAmount)}</span>
                        </Price>
                    ) : null}
                    {coeAmount ? (
                        <Price
                            className="totalPrice"
                            companyTheme={theme}
                            fieldKey="priceIncludeCoe"
                            isCoeEnabled={isCoeEnabled}
                        >
                            <span>{t('finder:offerPage.priceIncludeCoe.label')}</span>
                            <span>{totalPrice && formatAmountWithCurrency(totalPrice)}</span>
                        </Price>
                    ) : null}
                    {usedPriceDisclaimer &&
                        usedPriceDisclaimer.map(disclaimer => (
                            <PriceDisclaimerContainer>
                                <ParagraphEllipsis
                                    collapseText={t('calculators:readLess')}
                                    expandText={t('calculators:readMore')}
                                    row={1}
                                >
                                    {renderMarkdown(
                                        fillDynamicDisclaimerTextValues(disclaimer, {
                                            totalPrice: formatAmountWithCurrency(totalPrice),
                                        })
                                    )}
                                </ParagraphEllipsis>
                            </PriceDisclaimerContainer>
                        ))}
                </PriceContainer>
            </VehicleBriefContainer>
            {isFinancingSectionVisible && (
                <>
                    {showNoFPWarning && (
                        <SectionContainer isCheckboxChecked>
                            <WarningMessageBox
                                description={t(`calculators:notification.noFinanceProducts.description`)}
                                message={t(`calculators:notification.noFinanceProducts.title`)}
                            />
                        </SectionContainer>
                    )}

                    {availableFinanceProducts.length > 0 && (
                        <SectionContainer
                            hidden={
                                !(
                                    applicationScenario.withFinancing ||
                                    getShouldShowFinanceCalculator(module.displayFinanceCalculator)
                                )
                            }
                            isCheckboxChecked={isFinancingSectionBigBottom}
                        >
                            <ApplyFinance
                                actions={actions}
                                applicationScenario={applicationScenario}
                                calculatorContext={calculatorContext}
                                dealerId={dealer.id}
                                finderApplicationModule={module}
                                initialFinancingConfiguration={
                                    initialApplicationConfiguration.withFinancing ||
                                    (module.displayFinanceCalculator === PreferenceValue.Yes &&
                                        module.financingPreference !== FinancingPreferenceValue.Request) ||
                                    module.financingPreference === FinancingPreferenceValue.Request
                                }
                                setShowCarPriceModal={setShowCarPriceModal}
                                showCarPriceModal={showCarPriceModal}
                                state={state}
                                totalPrice={totalPrice}
                                vehicle={vehicle}
                            />
                        </SectionContainer>
                    )}
                </>
            )}
            {isInsuranceSectionVisible && availableInsuranceProducts.length > 0 && (
                <SectionContainer
                    hidden={!(applicationScenario.withInsurance || module.showInsuranceCalculator)}
                    isCheckboxChecked={isInsuranceSectionBigBottom}
                >
                    <InsuranceGridCalculator
                        applicationModule={module}
                        calculatorContext={calculatorContext}
                        dealerId={dealer.id}
                        hideCheckbox={!applicationScenario.isInsuranceOptional}
                        initialInsuranceConfiguration={
                            initialApplicationConfiguration.withInsurance || module.showInsuranceCalculator
                        }
                        isPublicAccess={endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint'}
                    />
                </SectionContainer>
            )}
            {isFinancingSectionVisible && (
                <>
                    <SectionContainer
                        hidden={!module.tradeIn}
                        isCheckboxChecked={
                            !module.testDrive && module.displayFinanceCalculator !== PreferenceValue.Optional
                                ? true
                                : values.configuration.tradeIn
                        }
                    >
                        <TradeIn
                            calculatorContext={calculatorContext}
                            disabledCheckbox={applyNewApplication && applyNewApplication.configuration.tradeIn}
                            isTradeInAmountVisible={module.isTradeInAmountVisible}
                            moduleType={module.__typename}
                            state={state}
                        />
                    </SectionContainer>
                    {availableFinanceProducts.length > 0 && (
                        <SectionContainer
                            hidden={module.displayFinanceCalculator !== PreferenceValue.Optional}
                            isCheckboxChecked={!module.testDrive}
                        >
                            <CheckboxContainer hasPrice={false} hasTooltip>
                                <CheckboxField
                                    customComponent={Checkbox}
                                    name="configuration.requestForFinancing"
                                    onClick={() => actions.setShowFinanceCalculator(!state.showFinanceCalculator)}
                                >
                                    {t('finder:offerPage.displayFinanceCalculator.label')}
                                </CheckboxField>
                                <Tooltip title={t('finder:offerPage.displayFinanceCalculator.tooltip')}>
                                    <StyledInfoCircleFilled />
                                </Tooltip>
                            </CheckboxContainer>
                        </SectionContainer>
                    )}
                </>
            )}
            {isAppointmentSectionVisible && (
                <SectionContainer hidden={!module.testDrive} isCheckboxChecked>
                    <CheckboxContainer hasPrice={false} hasTooltip={false}>
                        <CheckboxField customComponent={Checkbox} defaultChecked={false} name="configuration.testDrive">
                            {module.__typename === 'FinderApplicationPrivateModule'
                                ? t('finderPrivate:offerPage.bookTestDrive.label')
                                : t('finder:offerPage.bookTestDrive.label')}

                            <Tooltip
                                placement="top"
                                title={
                                    module.__typename === 'FinderApplicationPrivateModule'
                                        ? t('finderPrivate:offerPage.bookTestDrive.tooltip')
                                        : t('finder:offerPage.bookTestDrive.tooltip')
                                }
                            >
                                <StyledInfoCircleFilled />
                            </Tooltip>
                        </CheckboxField>
                    </CheckboxContainer>
                </SectionContainer>
            )}
            {!applyNewApplication && (
                <SectionContainer hidden={!hasValidPromoCode} isCheckboxChecked>
                    <PromoCodeField
                        key="promoCode"
                        applicationModuleId={module.id}
                        calculatorContext={calculatorContext}
                        promoCode={state.promoCode}
                        promoCodeError={state.promoCodeError}
                        promoCodeModuleId={module.promoCodeModule?.id}
                        setPromoCode={actions.setPromoCode}
                        setPromoCodeError={actions.setPromoCodeError}
                    />
                </SectionContainer>
            )}
            {(!applyNewApplication || !applyNewApplication?.deposit) && (
                <SectionContainer
                    hidden={
                        !depositAmount ||
                        (paymentSetting &&
                            paymentSetting.__typename === 'PorschePaymentSetting' &&
                            !dealer.integrationDetails.assortment)
                    }
                    v3LayoutType={v3LayoutType}
                    isCheckboxChecked
                >
                    <DepositContainer>
                        <DepositLabel>{t('finder:offerPage.bookingDeposit.label')}</DepositLabel>
                        <DepositValue>{formatAmountWithCurrency(depositAmount)}</DepositValue>
                    </DepositContainer>
                </SectionContainer>
            )}
        </div>
    );
};

export default CalculatorSection;
