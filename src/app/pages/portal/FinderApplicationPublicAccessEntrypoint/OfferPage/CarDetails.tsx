/* eslint-disable max-len */
import { LeftOutlined } from '@ant-design/icons';
import { PButtonPure } from '@porsche-design-system/components-react';
import { Col, Row, Grid as AntdGrid } from 'antd';
import dayjs from 'dayjs';
import { isEmpty, isNil } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FinderVehicleDataFragment } from '../../../../api/fragments/FinderVehicleData';
import {
    FinancingPreferenceValue,
    FinderVehicleCondition,
    InsuranceProductSortingField,
    PreferenceValue,
    SortingOrder,
} from '../../../../api/types';
import { GenericCalculatorProvider } from '../../../../calculator/CalculatorProvider';
import { defaultInsuranceAge } from '../../../../calculator/computing/insuranceFields/dateOfBirthField';
import CalculatorForm from '../../../../calculator/form/CalculatorForm';
import getInsuranceCalculatorValueFromApplication from '../../../../calculator/getInsuranceCalculatorValueFromApplication';
import { GenericCalculatorValues } from '../../../../calculator/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    getCalculatorValuesFromApplication,
    getInitialCalculatorValues,
} from '../../../../utilities/journeys/calculator';
import useDefaultMarketTypeValue from '../../../../utilities/useDefaultMarketTypeValue';
import useInitialValue from '../../../../utilities/useInitialValue';
import useInsurancingValueFromLead from '../../../../utilities/useInsurancingValueFromLead';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
    hasNoFinancingScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useLocalInsuranceProducts from '../../../shared/CIPage/useLocalInsuranceProduct';
import getIsDealerOptionsVisible, {
    getIncludeDealerOptionsForFinancing,
} from '../../../shared/getIsDealerOptionsVisible';
import getIsFlexibleDiscountEnabled from '../../../shared/getIsFlexibleDiscountEnabled';
import ChangeVehicleTrigger from '../../StandardApplicationEntrypoint/CarDetailsPage/CalculatorStage/CalculatorPage/ChangeVehicleTrigger';
import { HeaderButtonContainer } from '../../StandardApplicationEntrypoint/CarListingPage/CarListingPageInner';
import { useFinderApplicationContext } from '../FinderApplicationContext';
import { useIsVehicleAvailableForApplication } from '../shared';
import type { FinderApplicationState } from '../shared';
import { ApplicationScenarioConfig, JourneyState, OfferPageActionStateProps } from '../types';
import CalculatorSection from './FinderVehicleDetailsPage/CalculatorSection';
import ConditionInfo from './FinderVehicleDetailsPage/ConditionInfo';
import ReservationInstructions from './FinderVehicleDetailsPage/ReservationInstructions';
import SubmitButton from './FinderVehicleDetailsPage/SubmitButton';
import VehicleImage from './FinderVehicleDetailsPage/VehicleImage';
import VehicleInfo from './FinderVehicleDetailsPage/VehicleInfo';
import VehicleName, { SelectVehicleModal } from './FinderVehicleDetailsPage/VehicleName';
import { usePersistFinderJourneyValues } from './FinderVehicleDetailsPage/usePersistFinderJourneyValues';
import useApplyNewCondition from './applyNew/useApplyNewCondition';

const StyledChangeVehicleTriggerWrapper = styled.div`
    margin-top: 16px;
`;

export type CarDetailsPropsType = {
    applicationScenario: ApplicationScenarioConfig;
    initialApplication?: JourneyState['application'];
    applyNewApplication?: FinderApplicationState | null;
    canApplyForFinancing: boolean;
    hasFinancing: boolean;
} & OfferPageActionStateProps;

const withFinancingValidation = (
    financingPreference: FinancingPreferenceValue,
    displayFinanceCalculator: PreferenceValue,
    hasFinancing: boolean,
    vehicle: FinderVehicleDataFragment
) => {
    const isLTAEnabled =
        vehicle.module.__typename === 'FinderVehicleManagementModule' && vehicle.module.setting.allowLTA;

    if (hasFinancing && financingPreference !== FinancingPreferenceValue.Request) {
        if (financingPreference === FinancingPreferenceValue.Optional) {
            // in Singapore Finder vehicle
            if (isLTAEnabled) {
                if (!isNil(vehicle.lta) && vehicle.setting.isInspected) {
                    return true;
                }

                return false;
            }

            return true;
        }

        return displayFinanceCalculator !== PreferenceValue.No;
    }

    return false;
};

// Need further check with promo since it's being used by some components other that PromoCode itself
const CarDetails = ({
    actions,
    applicationScenario,
    state,
    initialApplication,
    applyNewApplication,
    canApplyForFinancing,
    hasFinancing,
}: CarDetailsPropsType) => {
    const screens = AntdGrid.useBreakpoint();
    const { t } = useTranslation(['carDetails', 'finder']);
    const navigate = useNavigate();
    const router = useRouter();

    const { FinderLayout } = useThemeComponents();
    const translatedString = useTranslatedString();

    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const { promoCode } = state;
    const { setPromoCode } = actions;

    const [isValid, setIsValid] = useState<boolean>(false);
    const [modalVisible, setModalVisible] = useState<boolean>(false);

    const { persistedValue: persistedJourneyValues, transformPersistedCalculatorValues } =
        usePersistFinderJourneyValues();

    // load dependencies
    const { availableFinanceProducts, endpoint, vehicle, dealer, condition, module, lead } =
        useFinderApplicationContext();

    const { bankDisplayPreference, insurerDisplayPreference, marketType, promoCodeModule, scenarios } = module;

    const defaultDateOfBirth = useMemo(() => dayjs().subtract(defaultInsuranceAge, 'year'), []);

    const { canApplyInsurance } = useApplyNewCondition(applyNewApplication, module);
    const insuranceCalculatorValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (!hasInsuranceScenario(scenarios) || (applyNewApplication && !canApplyInsurance)) {
            return {};
        }

        return {
            dateOfBirth:
                endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint' ? undefined : defaultDateOfBirth,
            isInsuranceEnabled: true,
            noClaimDiscount: 50,
            yearsOfDriving: 8,
            dateOfRegistration:
                condition === FinderVehicleCondition.New
                    ? dayjs()
                    : dayjs(vehicle.listing.vehicle.firstRegistrationDate?.value),
        };
    }, [
        applyNewApplication,
        condition,
        defaultDateOfBirth,
        endpoint.__typename,
        scenarios,
        canApplyInsurance,
        vehicle.listing.vehicle.firstRegistrationDate?.value,
    ]);

    const onBack = useCallback(() => {
        if (endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint') {
            window.open(vehicle.url, '_self');
        } else {
            navigate('..', { state });
        }
    }, [endpoint.__typename, vehicle.url, navigate, state]);

    const initialConfiguration = initialApplication?.configuration ??
        persistedJourneyValues?.calculatorValues?.configuration ?? {
            withFinancing:
                applicationScenario.withFinancing ||
                (withFinancingValidation(
                    module.financingPreference,
                    module.displayFinanceCalculator,
                    hasFinancing,
                    vehicle
                ) &&
                    availableFinanceProducts.length > 0), // If optional, by default we open the finance calculator
            withInsurance: applicationScenario.withInsurance ?? module?.showInsuranceCalculator ?? false,
            tradeIn: false,
            testDrive: false,
            requestForFinancing:
                (initialApplication?.configuration?.requestForFinancing ??
                    module?.financingPreference === FinancingPreferenceValue.Request) &&
                availableFinanceProducts.length > 0,
        };

    const insurancingValueFromLead = useInsurancingValueFromLead({ lead, defaultDateOfBirth });
    const marketTypeValue = useDefaultMarketTypeValue(dealer.id);
    const calculatorsValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (initialApplication) {
            return {
                ...getCalculatorValuesFromApplication(marketType, marketTypeValue, initialApplication),
                ...(initialApplication?.insurancing
                    ? getInsuranceCalculatorValueFromApplication(initialApplication?.insurancing)
                    : {}),
                isFinancingEnabled: initialApplication?.configuration?.withFinancing ?? false,
                isInsuranceEnabled: initialApplication?.configuration?.withInsurance ?? false,
            };
        }

        if (!isEmpty(persistedJourneyValues?.calculatorValues?.calculator)) {
            return transformPersistedCalculatorValues(persistedJourneyValues);
        }

        return {
            ...getInitialCalculatorValues(marketTypeValue, marketType),
            ...insuranceCalculatorValues,
            ...(lead && insurancingValueFromLead),
            vehicle: vehicle.id,
            isFinancingEnabled: initialConfiguration.withFinancing,
        };
    }, [
        initialApplication,
        persistedJourneyValues,
        marketTypeValue,
        marketType,
        insuranceCalculatorValues,
        vehicle.id,
        initialConfiguration.withFinancing,
        transformPersistedCalculatorValues,
        lead,
        insurancingValueFromLead,
    ]);

    const { insuranceProducts, loading } = useLocalInsuranceProducts({
        module,
        sort: { field: InsuranceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds: [dealer.id],
    });

    const initialCalculatorValues = useInitialValue<Partial<GenericCalculatorValues>>(calculatorsValues);
    const promoCodeViewable = !!promoCodeModule;
    const [isSubmitting, setIsSubmitting] = useState(false);

    const hasFinancingCalculator = useMemo(
        () => applicationScenario.withFinancing || state.showFinanceCalculator,
        [applicationScenario.withFinancing, state.showFinanceCalculator]
    );

    const isFinancingOptional =
        (hasFinancingScenario(module.scenarios) &&
            (module.financingPreference === FinancingPreferenceValue.Request ||
                module.financingPreference === FinancingPreferenceValue.Optional)) ||
        (hasNoFinancingScenario(module.scenarios) && module.displayFinanceCalculator === PreferenceValue.Optional);

    const vehicleIsAvailableForApplication = useIsVehicleAvailableForApplication(applyNewApplication, vehicle);

    const isSubmitButtonDisabled = useMemo(
        () => !isValid || isSubmitting || !vehicleIsAvailableForApplication,
        [isSubmitting, isValid, vehicleIsAvailableForApplication]
    );

    if (loading) {
        return <PortalLoadingElement />;
    }

    const isV3LayoutType = router?.layout?.__typename === 'PorscheV3Layout';

    return (
        <GenericCalculatorProvider
            bankDisplayPreference={bankDisplayPreference}
            companyId={module.company.id}
            dealerId={dealer.id}
            financeProducts={availableFinanceProducts}
            hasFinancingCalculator={hasFinancingCalculator}
            includeDealerOptionsForFinancing={getIncludeDealerOptionsForFinancing(module)}
            initialValues={initialCalculatorValues}
            insuranceProducts={insuranceProducts}
            insurerDisplayPreference={insurerDisplayPreference}
            isDealerOptionsVisible={getIsDealerOptionsVisible(module)}
            isFinancingOptional={isFinancingOptional}
            isFlexibleDiscountEnabled={getIsFlexibleDiscountEnabled(module)}
            isPublicAccess={endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint'}
            marketType={marketType}
            promoCode={promoCode}
            promoCodeViewable={promoCodeViewable}
            vehicles={[vehicle]}
        >
            {calculatorContext => (
                <CalculatorForm
                    applyNewApplication={applyNewApplication}
                    calculatorContext={calculatorContext}
                    dealerId={dealer.id}
                    endpoint={endpoint}
                    initialConfiguration={initialConfiguration}
                    module={module}
                    setIsSubmitting={setIsSubmitting}
                    setIsValid={setIsValid}
                    setPromoCode={setPromoCode}
                >
                    <FormAutoTouch />
                    <FinderLayout
                        backIcon={
                            !applyNewApplication &&
                            !lead &&
                            (router.layout?.__typename === 'PorscheV3Layout' ? (
                                <PButtonPure icon="arrow-left" size="xx-small">
                                    {t('carDetails:actions.backToList')}
                                </PButtonPure>
                            ) : (
                                <>
                                    <LeftOutlined />
                                    {endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint'
                                        ? t('finder:offerPage.actions.backToPorscheFinder')
                                        : t('carDetails:actions.back')}
                                </>
                            ))
                        }
                        content={
                            isV3LayoutType && (
                                <>
                                    <ConditionInfo
                                        dealer={dealer}
                                        isPublicAccess={
                                            endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint'
                                        }
                                        module={module}
                                        vehicle={vehicle}
                                    />
                                    {screens.xs && (
                                        <StyledChangeVehicleTriggerWrapper>
                                            <ChangeVehicleTrigger
                                                allowChangeVehicle={!applyNewApplication}
                                                setModalVisible={setModalVisible}
                                                variant="secondary"
                                            />
                                        </StyledChangeVehicleTriggerWrapper>
                                    )}
                                </>
                            )
                        }
                        extra={
                            <HeaderButtonContainer>
                                {isV3LayoutType && !screens.xs && (
                                    <ChangeVehicleTrigger
                                        allowChangeVehicle={!applyNewApplication}
                                        setModalVisible={setModalVisible}
                                        variant="secondary"
                                    />
                                )}
                            </HeaderButtonContainer>
                        }
                        footer={[<SubmitButton disabled={isSubmitButtonDisabled} />]}
                        onBack={onBack}
                        style={{ height: isV3LayoutType ? '100%' : basicLayoutHeight }}
                        title={
                            isV3LayoutType && (
                                <VehicleName
                                    applyNewApplication={applyNewApplication}
                                    endpoint={endpoint}
                                    vehicle={vehicle}
                                    disableVehicleSelection
                                />
                            )
                        }
                        hasFooterBar
                    >
                        <BasicProLayoutContainer>
                            <Row gutter={isV3LayoutType ? [24, 24] : 40}>
                                <Col md={14} xs={24}>
                                    <ReservationInstructions />
                                    <VehicleImage vehicle={vehicle} />
                                    <VehicleInfo vehicle={vehicle} />
                                </Col>
                                <Col md={10} xs={24}>
                                    <CalculatorSection
                                        actions={actions}
                                        applicationScenario={applicationScenario}
                                        applyNewApplication={applyNewApplication}
                                        calculatorContext={calculatorContext}
                                        dealer={dealer}
                                        endpoint={endpoint}
                                        initialApplicationConfiguration={initialConfiguration}
                                        initialCalculatorValues={initialCalculatorValues}
                                        state={state}
                                        vehicle={vehicle}
                                    />
                                </Col>
                            </Row>
                        </BasicProLayoutContainer>
                    </FinderLayout>
                    <SelectVehicleModal
                        modalVisible={modalVisible}
                        setModalVisible={setModalVisible}
                        translatedString={translatedString}
                        vehicle={vehicle}
                    />
                </CalculatorForm>
            )}
        </GenericCalculatorProvider>
    );
};

export default CarDetails;
