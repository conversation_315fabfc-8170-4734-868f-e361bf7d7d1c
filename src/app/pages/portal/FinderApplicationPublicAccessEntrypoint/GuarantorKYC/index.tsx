/* eslint-disable max-len */
import { Col, Row, Space } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { FinderApplicationEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import { CustomerKind, LocalCustomerManagementModule } from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import useMyInfo from '../../../../components/MyInfo/useMyInfo';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import Form from '../../../../components/fields/Form';
import Ocr, { useOcrDetectedHandler, useUploadOcrFiles } from '../../../../components/ocr';
import { withOcrFilesManagerContext } from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { getApplicantAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import CustomerDetails from '../../../shared/JourneyPage/CustomerDetails';
import { getInitUploadDocuments } from '../../../shared/JourneyPage/CustomerDetails/shared';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import refineCustomerValues from '../../../shared/refineCustomerValues';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useRefineKycPresets from '../../../shared/useRefineKycPresets';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import ConsentsAndDeclarations from '../../ConfiguratorApplicationEntrypoint/ApplicantKYCPage/ConsentsAndDeclarations';
import DealerInfo from '../../ConfiguratorApplicationEntrypoint/DealerInfo';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import JourneyToolbar from '../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import { Backdrop } from '../../StandardApplicationEntrypoint/styledComponents';
import FinderBookingDeposit from '../ApplicantKYC/BookingDeposit';
import VehicleInterest from '../ApplicantKYC/VehicleInterest';
import { useFinderApplicationContext } from '../FinderApplicationContext';
import { FinderKYCJourneyValues, JourneyAction, JourneyState } from '../types';

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

type ApplicantKYCProps = {
    state?: JourneyState;
    dispatch?: Dispatch<JourneyAction>;
    endpoint:
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
};

type ApplicantKYCInnerProps = ApplicantKYCProps & {
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    applicationAgreements: ApplicationAgreementDataFragment[];
    setPrefill: Dispatch<SetStateAction<boolean>>;
} & UploadDocumentProp;

const GuarantorKYCInner = ({
    state,
    kycPresets,
    kycExtraSettings,
    applicationAgreements,
    setPrefill,
    uploadDocument,
    removeDocument,
}: ApplicantKYCInnerProps) => {
    const { application } = state;
    const { endpoint, module } = useFinderApplicationContext();
    const router = useRouter();

    const [isLoading, setIsLoading] = useState<boolean>(false);

    const { isSubmitting, handleSubmit, validateForm, submitForm, values } = useFormikContext<FinderKYCJourneyValues>();

    const { myInfoSettingId, isOcrEnabled } = module;

    const myInfo = useMyInfo({
        isEnabled: !!myInfoSettingId,
    });

    const onOcrDetected = useOcrDetectedHandler(kycPresets);

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const user = useAccount(true);
    const showCommentsToBank = user && application?.bank?.showCommentsField;
    const showCommentsToInsurer = user && application?.insurer?.showCommentsField;

    return (
        <>
            <FormAutoTouch />
            <Form id="kycJourneyForm" name="kycJourneyForm" onSubmitCapture={handleSubmit}>
                <Row gutter={[24, 24]}>
                    <Col {...leftColSpan}>
                        <Space direction="vertical" size={24}>
                            <VehicleInterest application={application} />
                            <FinderBookingDeposit />
                            <DealerInfo dealer={application.dealer} />
                            {myInfo.render({
                                getMyInfoVariables: () => ({
                                    routerId: router.id,
                                    endpointId: endpoint.id,
                                    applicationId: application.id,
                                    customerKind: CustomerKind.Guarantor,
                                    withTradeIn: values.configuration.tradeIn,
                                    withTestDrive: values.configuration.testDrive,
                                }),
                                getTradeInValues: () => ({
                                    withTradeIn: values.configuration.tradeIn,
                                    withTestDrive: values.configuration.testDrive,
                                    name: 'tradeInVehicle',
                                }),
                                onLoading: setIsLoading,
                            })}
                            {!myInfo.withMyInfo && isOcrEnabled && <Ocr onDetected={onOcrDetected} />}
                        </Space>
                    </Col>
                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            stage={state.stage}
                            stages={state.stages}
                        >
                            <Row gutter={[24, 24]}>
                                <Col span={24}>
                                    <CustomerDetails
                                        customerKind={CustomerKind.Guarantor}
                                        hasGuarantorPreset={false}
                                        hasUploadDocuments={application.bank?.hasUploadDocuments}
                                        hasVSOUpload={false}
                                        kycExtraSettings={kycExtraSettings}
                                        kycPresets={kycPresets}
                                        removeDocument={removeDocument}
                                        setPrefill={setPrefill}
                                        showCommentsToInsurer={showCommentsToInsurer}
                                        showRemarks={showCommentsToBank}
                                        showTabs={false}
                                        showTitle={false}
                                        uploadDocument={uploadDocument}
                                        withFinancing={application.configuration.withFinancing}
                                    />
                                </Col>
                                <Col span={24}>
                                    <ConsentsAndDeclarations applicationAgreements={applicationAgreements} />
                                </Col>
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>
            {isLoading && <Backdrop />}
            <JourneyToolbar>
                <NextButton key="nextButton" disabled={isSubmitting} onSubmit={onSubmit} />
            </JourneyToolbar>
        </>
    );
};

const GuarantorKYC = ({ state, dispatch, endpoint }: ApplicantKYCProps) => {
    const { t } = useTranslation(['customerDetails']);

    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();
    const [prefill, setPrefill] = useState<boolean>(false);

    const { token, application } = state;
    const { guarantorAgreements, guarantorKYC, guarantor, configuration, tradeInVehicle } = application;

    const agreementsKYC = guarantorAgreements.map(agreement => ({
        ...agreement,
        isAgreed: false,
    }));
    const applicantKycAgreements = useMemo(() => getApplicantAgreements(agreementsKYC), [agreementsKYC]);

    const agreementsValidator = useAgreementsValidator(applicantKycAgreements, 'agreements');
    const agreements = useAgreementsValues(applicantKycAgreements);

    const applicantKycFields = useMemo(() => getApplicantKyc(guarantorKYC), [guarantorKYC]);
    const kycPresets = useRefineKycPresets(applicantKycFields);

    const kycExtraSettings = useMemo(() => {
        if (application?.module?.__typename === 'FinderApplicationPublicModule') {
            return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? application.module.customerModule.extraSettings
                : null;
        }

        return null;
    }, [application]);

    const isEditableField = useMemo(() => (guarantor ? guarantor.fields : []), [guarantor]);
    const applicants = useMemo(
        () => ({ fields: getInitialValues(isEditableField, kycPresets) }),
        [isEditableField, kycPresets]
    );
    const { notification } = useThemeComponents();

    const user = useAccount(true);

    const showCommentsToBank = user && application?.bank?.showCommentsField;
    const showCommentsToInsurer = user && application?.insurer?.showCommentsField;

    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );

    const moduleCountryCode = useMemo(
        () =>
            (application.module.__typename === 'FinderApplicationPublicModule' ||
                application.module.__typename === 'FinderApplicationPrivateModule') &&
            application.module.company.countryCode,
        [application.module.__typename]
    );
    // TODO: As per AN-966, finder does not have `proceed with customer`, phase 2 will have it
    const applicantsValidator = useKYCFormValidator({
        field: kycPresets,
        extraSettings: kycExtraSettings,
        moduleCountryCode,
        prefix: 'customer.fields',
        saveDraft: false,
    });

    const uploadOcrFiles = useUploadOcrFiles();
    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const validations = useMemo(
        () =>
            validators.compose(
                applicantsValidator,
                agreementsValidator,
                validators.only(
                    () => showUploadDocument,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Guarantor}`)
                )
            ),
        [agreementsValidator, applicantsValidator, showUploadDocument]
    );

    const validate = useValidator(validations, { prefill });

    const initialValues: FinderKYCJourneyValues = useMemo(
        () => ({
            agreements,
            customer: applicants,
            configuration,
            remarks: application?.remarks ?? '',
            commentsToInsurer: application?.commentsToInsurer ?? '',
            tradeInVehicle,
            isCorporateCustomer: false,
            hasGuarantor: false,
            prefill: false,
            uploadDocuments: getInitUploadDocuments(application.documents, showUploadDocument, CustomerKind.Guarantor),
        }),
        [agreements, applicants, application.documents, configuration, showUploadDocument, tradeInVehicle]
    );

    const onSubmit = useHandleError(
        async (values: FinderKYCJourneyValues) => {
            notification.loading({
                content: t('customerDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const { applicantFields } = refineCustomerValues(values.customer.fields);

            const submitAgreementKYC = await submitAgreements(
                state.token,
                values.agreements,
                CustomerKind.Guarantor,
                false
            );

            if (showCommentsToBank || showCommentsToInsurer) {
                // we wait to update remarks first
                await updateApplicationFields(submitAgreementKYC.token, values.remarks, values.commentsToInsurer);
                // then we later call the KYC
                // as calling submit customer will immediately call the next step and will have not the remarks
            }

            const submitApplicantKYC =
                await // TODO: As per AN-966, finder does not have `proceed with customer`, phase 2 will have it
                submitCustomerDetails({
                    token: submitAgreementKYC.token,
                    fields: applicantFields,
                    customerKind: CustomerKind.Guarantor,
                    sameCorrespondenceAddress: values.prefill,
                });

            await uploadOcrFiles(submitApplicantKYC.token);

            notification.destroy('primary');

            if (submitApplicantKYC.__typename === 'GiftVoucherJourneyContext') {
                throw new Error('unsupported journey context');
            }

            if (submitApplicantKYC.application.__typename !== 'FinderApplication') {
                throw new Error('unexpected type');
            }

            dispatch({
                type: 'next',
                token: submitApplicantKYC.token,
                application: submitApplicantKYC.application,
            });
        },
        [
            notification,
            t,
            state.token,
            submitAgreements,
            submitCustomerDetails,
            showCommentsToBank,
            showCommentsToInsurer,
            updateApplicationFields,
            uploadOcrFiles,
            dispatch,
        ]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            <GuarantorKYCInner
                applicationAgreements={applicantKycAgreements}
                dispatch={dispatch}
                endpoint={endpoint}
                kycExtraSettings={kycExtraSettings}
                kycPresets={kycPresets}
                removeDocument={removeDocument}
                setPrefill={setPrefill}
                state={state}
                uploadDocument={uploadDocument}
            />
        </Formik>
    );
};

export default withOcrFilesManagerContext(GuarantorKYC);
