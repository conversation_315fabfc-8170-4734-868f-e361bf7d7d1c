/* eslint-disable max-len */
import { Formik } from 'formik';
import { isEmpty, isNil, sumBy } from 'lodash/fp';
import { Dispatch, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FinderApplicationEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { useUpdateFinderApplicationJourneyMutation } from '../../../../api/mutations/updateFinderApplicationJourney';
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import { CustomerKind } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useUploadOcrFiles } from '../../../../components/ocr';
import { withOcrFilesManagerContext } from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues, getKycInitialValuesFromInsurance } from '../../../../utilities/kycPresets';
import useInsuranceKyc from '../../../../utilities/kycPresets/useInsuranceKyc';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { hasAppointmentScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { getInitUploadDocuments } from '../../../shared/JourneyPage/CustomerDetails/shared';
import useKYCData from '../../../shared/JourneyPage/CustomerDetails/useKYCData';
import {
    useGetInitialQuotationValues,
    useQuotationValidator,
    useSubmitApplicationQuotation,
} from '../../../shared/JourneyPage/QuotationDetails';
import refineCustomerValues from '../../../shared/refineCustomerValues';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useRefineKycPresets from '../../../shared/useRefineKycPresets';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import { retrieveAppointmentModule } from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import {
    submitVisitAppointmentOrTestDrive,
    useAppointmentSubmissions,
} from '../../StandardApplicationEntrypoint/AppointmentPage/shared';
import { useFirstAvailableSlot } from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentAvailability';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import { usePersistFinderJourneyValues } from '../OfferPage/FinderVehicleDetailsPage/usePersistFinderJourneyValues';
import { FinderKYCJourneyValues, JourneyAction, JourneyState } from '../types';
import ApplicantKYCInner from './ApplicantKYCInner';

type ApplicantKYCProps = {
    state?: JourneyState;
    dispatch?: Dispatch<JourneyAction>;
    endpoint:
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
};

const ApplicantKYC = ({ state, dispatch, endpoint }: ApplicantKYCProps) => {
    const { t } = useTranslation(['customerDetails']);

    const [updateFinderJourney] = useUpdateFinderApplicationJourneyMutation();
    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();

    const { token, application } = state;
    const {
        applicantAgreements,
        applicantKYC,
        corporateAgreements,
        corporateKYC,
        applicant,
        configuration,
        tradeInVehicle,
        guarantorKYC,
        draftFlow,
    } = application;
    const [isCorporate, setIsCorporate] = useState<boolean>(application.applicant.__typename === 'CorporateCustomer');
    const [prefill, setPrefill] = useState<boolean>(false);
    const [saveDraft, setSaveDraft] = useState(false);

    const { persistedValue, save: persistJourneyValues } = usePersistFinderJourneyValues();

    const { kycAgreements, kycFields } = useKYCData(
        isCorporate,
        applicantKYC,
        corporateKYC,
        applicantAgreements,
        corporateAgreements
    );

    const isApplyingFromDetails = useMemo(
        () =>
            (application.configuration.withFinancing && application.configuration.requestForFinancing) ||
            !isNil(draftFlow.isApplyingForFinanceCompleted) ||
            !isNil(draftFlow.isApplyingForInsuranceCompleted) ||
            !isNil(draftFlow.isApplyingForReservationCompleted),
        [
            application.configuration.requestForFinancing,
            application.configuration.withFinancing,
            draftFlow.isApplyingForFinanceCompleted,
            draftFlow.isApplyingForInsuranceCompleted,
            draftFlow.isApplyingForReservationCompleted,
        ]
    );

    const isApplyingFromApplyNew = useMemo(
        () =>
            application.draftFlow.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application.draftFlow.isApplyNewForInsuranceReceived ||
                !application.draftFlow.isApplyNewForAppointmentReceived),
        [
            application.draftFlow.isReceived,
            application.draftFlow.isApplyNewForAppointmentReceived,
            application.draftFlow.isApplyNewForFinancingReceived,
            application.draftFlow.isApplyNewForInsuranceReceived,
        ]
    );

    const applicantKycAgreements = useMemo(
        () =>
            kycAgreements.map(agreement => ({
                ...agreement,
                isAgreed: isApplyingFromDetails || isApplyingFromApplyNew ? agreement.isAgreed : false,
            })),
        [isApplyingFromApplyNew, isApplyingFromDetails, kycAgreements]
    );

    const agreementsValidator = useAgreementsValidator(applicantKycAgreements, 'agreements');
    const agreements = useAgreementsValues(applicantKycAgreements);

    const kycPresets = useRefineKycPresets(kycFields);

    const kycExtraSettings = useMemo(() => {
        switch (application?.module?.__typename) {
            case 'FinderApplicationPrivateModule':
            case 'FinderApplicationPublicModule':
                return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                    ? application.module.customerModule.extraSettings
                    : null;

            default:
                return null;
        }
    }, [application]);

    const { applicationModule, moduleCountryCode, moduleTimeZone } = useMemo(() => {
        const finderApplicationModule =
            application.module.__typename === 'FinderApplicationPrivateModule' ||
            application.module.__typename === 'FinderApplicationPublicModule'
                ? application.module
                : null;

        return {
            applicationModule: finderApplicationModule,
            moduleCountryCode: finderApplicationModule?.company.countryCode,
            moduleTimeZone: finderApplicationModule?.company.timeZone,
        };
    }, [application.module]);

    const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(applicationModule);

    const { data: bookedListing, loading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: isEmpty(appointmentModule?.id),
    });
    const bookedAppointmentTimeSlots = useMemo(
        () => bookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [bookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const { data: visitBookedListing, loading: visitLoading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: visitAppointmentModule?.id,
        },
        skip: isEmpty(visitAppointmentModule?.id),
    });
    const bookedVisitAppointmentTimeSlots = useMemo(
        () => visitBookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [visitBookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const isEditableField = useMemo(() => (applicant ? applicant.fields : []), [applicant]);
    const applicants = useMemo(
        () => ({
            fields: {
                ...getInitialValues(isEditableField, kycPresets),
                ...(!isCorporate && getKycInitialValuesFromInsurance(application.insurancing, kycPresets)),
            },
        }),
        [isEditableField, kycPresets, isCorporate, application.insurancing]
    );
    const { notification } = useThemeComponents();

    const user = useAccount(true);

    const showCommentsToBank = user && application?.bank?.showCommentsField;
    const showCommentsToInsurer = user && application?.insurer?.showCommentsField;

    const showResetKYCButton = useMemo(
        () => applicationModule?.showResetKYCButton,
        [applicationModule?.showResetKYCButton]
    );

    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );

    // TODO: As per AN-966, finder does not have `proceed with customer`, phase 2 will have it
    const applicantsValidator = useKYCFormValidator({
        field: kycPresets,
        extraSettings: kycExtraSettings,
        moduleCountryCode,
        prefix: 'customer.fields',
        saveDraft,
        bankProvider: application.bank?.integration.provider,
    });

    const uploadOcrFiles = useUploadOcrFiles();

    const quotationValidator = useQuotationValidator(
        application.bank?.integration.__typename === 'EnbdBankIntegration' && application.configuration.withFinancing,
        sumBy('amount', application.financing?.dealerOptions ?? []),
        'quotation'
    );

    const validations = useMemo(
        () =>
            validators.compose(
                applicantsValidator,
                agreementsValidator,
                validators.only(() => !saveDraft, quotationValidator),
                validators.only(
                    () => showUploadDocument && !isCorporate,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Local}`)
                ),
                validators.only(
                    () => showUploadDocument && isCorporate,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Corporate}`)
                ),
                validators.only(
                    (values, error, { prefix }) =>
                        values.configuration.testDrive &&
                        hasAppointmentScenario(applicationModule.scenarios) &&
                        applicationModule?.appointmentModule &&
                        applicationModule?.displayAppointmentDatepicker &&
                        !values.appointment?.useCurrentDateTime,
                    validators.compose(
                        validators.requiredDate('appointment.date'),
                        validators.requiredString('appointment.time')
                    )
                ),
                validators.only(
                    values => !!values.configuration.isAffinAutoFinanceCentreRequired,
                    validators.compose(validators.requiredString('financing.affinAutoFinanceCentre'))
                )
            ),
        [
            agreementsValidator,
            applicantsValidator,
            isCorporate,
            quotationValidator,
            saveDraft,
            showUploadDocument,
            applicationModule,
        ]
    );

    const submitQuotation = useSubmitApplicationQuotation();

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);
    const { submitAppointment, submitVisitAppointment } = useAppointmentSubmissions();
    const validate = useValidator(validations, { prefill });

    const getInitialQuotationValues = useGetInitialQuotationValues();
    const datePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedAppointmentTimeSlots,
        moduleTimeZone,
        application.appointmentStage?.bookingTimeSlot?.slot
    );

    const visitDatePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedVisitAppointmentTimeSlots,
        moduleTimeZone,
        application.visitAppointmentStage?.bookingTimeSlot?.slot
    );

    const initialValues: FinderKYCJourneyValues = useMemo(
        () => ({
            agreements,
            customer: applicants,
            configuration,
            remarks: application?.remarks ?? '',
            commentsToInsurer: application?.commentsToInsurer ?? '',
            tradeInVehicle,
            isCorporateCustomer: false,
            hasGuarantor: false,
            prefill: false,
            quotation: getInitialQuotationValues({
                bank: application.bank,
                financing: application.financing,
                isFinancingEnabled: application.configuration.withFinancing,
            }),
            appointment: {
                date: datePrefill?.date,
                time: datePrefill?.firstSlot?.value,
                useCurrentDateTime: false,
            },
            uploadDocuments: getInitUploadDocuments(
                application.documents,
                showUploadDocument,
                application.applicant.kind
            ),
            ...persistedValue?.kyc,
        }),
        [
            agreements,
            applicants,
            configuration,
            application?.remarks,
            application?.commentsToInsurer,
            application?.bank,
            application?.financing,
            application?.configuration.withFinancing,
            application?.documents,
            application?.applicant?.kind,
            tradeInVehicle,
            getInitialQuotationValues,
            datePrefill?.date,
            datePrefill?.firstSlot?.value,
            showUploadDocument,
            persistedValue?.kyc,
        ]
    );

    // Added insurance kyc handler in KYC Page
    const { birthdayConfirmation } = useInsuranceKyc(application.configuration.withInsurance, {
        createdAt: application.versioning?.createdAt,
    });

    const onSubmit = useHandleError(
        async ({ appointment, ...values }: FinderKYCJourneyValues) => {
            birthdayConfirmation(
                applicants.fields.Birthday?.value,
                values.customer.fields.Birthday?.value,
                async () => {
                    notification.loading({
                        content: t('customerDetails:messages.creationSubmitting'),
                        duration: 0,
                        key: 'primary',
                    });

                    const { data } = await updateFinderJourney({
                        variables: {
                            token: state.token,
                            tradeInVehicle: values.tradeInVehicle.filter(({ isSelected }) => isSelected),
                            customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                            financing: values.financing,
                            saveDraft,
                        },
                    });

                    const updatedJourney = data.result;

                    const updatedToken = await submitQuotation(updatedJourney.token, values);

                    const { applicantFields } = refineCustomerValues(values.customer.fields);
                    const submitAgreementKYC = await submitAgreements(
                        updatedToken,
                        values.agreements,
                        isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                        values.hasGuarantor
                    );

                    if (showCommentsToBank || showCommentsToInsurer) {
                        // we wait to update remarks first
                        await updateApplicationFields(
                            submitAgreementKYC.token,
                            values.remarks,
                            values.commentsToInsurer
                        );
                        // then we later call the KYC
                        // as calling submit customer will immediately call the next step and will have not the remarks
                    }

                    const submitApplicantKYC = // TODO: As per AN-966, finder does not have `proceed with customer`, phase 2 will have it
                        await submitCustomerDetails({
                            token: submitAgreementKYC.token,
                            fields: applicantFields,
                            customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                            sameCorrespondenceAddress: values.prefill,
                            capValues:
                                applicationModule.__typename === 'FinderApplicationPrivateModule'
                                    ? values.capValues
                                    : null,
                            customerCiamId: values.customerCiamId,
                            saveDraft,
                        });

                    const finalResult =
                        values.configuration.testDrive &&
                        applicationModule?.displayAppointmentDatepicker &&
                        !application.draftFlow.isAppointmentCompleted
                            ? await submitVisitAppointmentOrTestDrive(
                                  submitAppointment,
                                  submitVisitAppointment,
                                  submitApplicantKYC.token,
                                  appointment,
                                  applicationModule?.company.timeZone,
                                  appointmentModule,
                                  applicationModule?.displayAppointmentDatepicker
                              )
                            : submitApplicantKYC;

                    await uploadOcrFiles(finalResult.token);

                    notification.destroy('primary');

                    if (finalResult.__typename === 'GiftVoucherJourneyContext') {
                        throw new Error('unsupported journey context');
                    }

                    if (finalResult.application.__typename !== 'FinderApplication') {
                        throw new Error('unexpected type');
                    }

                    if (!saveDraft) {
                        dispatch({
                            type: 'next',
                            token: finalResult.token,
                            application:
                                finalResult.application.__typename === 'FinderApplication' && finalResult.application,
                        });
                    } else {
                        notification.success({
                            content: t('customerDetails:messages.draftSaved'),
                            key: 'secondary',
                        });
                    }
                }
            );
        },
        [
            birthdayConfirmation,
            applicants.fields.Birthday?.value,
            notification,
            t,
            updateFinderJourney,
            state.token,
            isCorporate,
            saveDraft,
            submitQuotation,
            submitAgreements,
            showCommentsToBank,
            showCommentsToInsurer,
            submitCustomerDetails,
            applicationModule.__typename,
            applicationModule?.displayAppointmentDatepicker,
            applicationModule?.company.timeZone,
            application.draftFlow.isAppointmentCompleted,
            submitAppointment,
            appointmentModule,
            uploadOcrFiles,
            dispatch,
            updateApplicationFields,
            submitVisitAppointment,
        ]
    );

    if (loading) {
        return <PortalLoadingElement />;
    }

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            <ApplicantKYCInner
                applicationAgreements={applicantKycAgreements}
                dispatch={dispatch}
                endpoint={endpoint}
                hasGuarantorPreset={guarantorKYC.length > 0}
                isCorporate={isCorporate}
                kycExtraSettings={kycExtraSettings}
                kycPresets={kycPresets}
                removeDocument={removeDocument}
                setIsCorporate={setIsCorporate}
                setPrefill={setPrefill}
                setSaveDraft={setSaveDraft}
                showResetButton={showResetKYCButton}
                showTabs={corporateKYC.length > 0}
                showUploadDocument={showUploadDocument}
                state={state}
                uploadDocument={uploadDocument}
            />
        </Formik>
    );
};

export default withOcrFilesManagerContext(ApplicantKYC);
