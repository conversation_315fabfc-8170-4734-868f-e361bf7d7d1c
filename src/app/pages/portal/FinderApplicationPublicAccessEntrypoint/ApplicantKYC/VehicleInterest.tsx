import { PHeading, PText } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../components/contexts/shared';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import type { FinderApplicationState } from '../shared';
import VehicleInfo from './VehicleInfo';

export type VehicleInterestProps = {
    application: FinderApplicationState;
};

/**
 * Porsche finder vehicle interest
 */
const VehicleInterest = ({ application }: VehicleInterestProps) => {
    const { t } = useTranslation('finderJourney');
    const { layout } = useRouter();

    return (
        <div className={layout?.__typename === 'PorscheV3Layout' ? 'v3-layout-card' : ''}>
            {layout?.__typename === 'PorscheV3Layout' ? (
                <PHeading size="medium" style={{ marginBottom: '21px' }}>
                    {t('finderJourney:applicantKyc.vehicleDetails.titleV3')}
                </PHeading>
            ) : (
                <Title size="large">{t('finderJourney:applicantKyc.vehicleDetails.title')}</Title>
            )}
            <VehicleInfo application={application} />
        </div>
    );
};

export default VehicleInterest;
