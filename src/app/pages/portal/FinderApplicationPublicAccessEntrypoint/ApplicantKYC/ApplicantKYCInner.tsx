/* eslint-disable max-len */
import { Col, Row, Space } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { FinderApplicationEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import {
    CompanyTheme,
    CustomerKind,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
    PorscheIdData,
} from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import useMyInfo from '../../../../components/MyInfo/useMyInfo';
import MandatoryPorscheIDLogin from '../../../../components/PorscheID/MandatoryPorscheIDLogin';
import getKYCDataFromPorscheId from '../../../../components/PorscheID/getKYCDataFromPorscheId';
import { getPorscheIDConfigByApplicationJourney } from '../../../../components/PorscheID/getPorscheIDConfig';
import { SelectedCapValues, ViewState } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import Form from '../../../../components/fields/Form';
import Ocr, { useOcrDetectedHandler } from '../../../../components/ocr';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import { hasAppointmentScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import { SearchCapCustomerContextManager } from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/ContextManager';
import getKYCDataFromCap, {
    getCurrentVehicleFromCap,
} from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/getKYCDataFromCap';
import useCapSearchCustomerAction from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/useCapSearchCustomerAction';
import CustomerDetails from '../../../shared/JourneyPage/CustomerDetails';
import AffinAutoFinanceCentreSelect from '../../../shared/JourneyPage/CustomerDetails/AffinAutoFinanceCentreSelect';
import ResetKYCButtonPorsche from '../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import { QuotationDetails } from '../../../shared/JourneyPage/QuotationDetails';
import ProceedWithCustomerButton from '../../../shared/ProceedWithCustomerButton';
import ConsentsAndDeclarations from '../../ConfiguratorApplicationEntrypoint/ApplicantKYCPage/ConsentsAndDeclarations';
import DealerInfo from '../../ConfiguratorApplicationEntrypoint/DealerInfo';
import AppointmentDetailsSection, {
    AppointmentType,
} from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import { SectionDivider } from '../../StandardApplicationEntrypoint/KYCPage/shared';
import useProceedWithCustomerDeviceButton from '../../StandardApplicationEntrypoint/KYCPage/useProceedWithCustomerDeviceButton';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import { Backdrop, StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import { useFinderApplicationContext } from '../FinderApplicationContext';
import { usePersistFinderJourneyValues } from '../OfferPage/FinderVehicleDetailsPage/usePersistFinderJourneyValues';
import { FinderKYCJourneyValues, JourneyAction, JourneyStage, JourneyState } from '../types';
import SaveDraftButton, { canSaveDraft } from './SaveDraftButton';
import VehicleInterest from './VehicleInterest';

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

type ApplicantKYCInnerProps = {
    state: JourneyState;
    dispatch: Dispatch<JourneyAction>;
    endpoint:
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
    kycPresets: KycFieldSpecsFragment[];
    applicationAgreements: ApplicationAgreementDataFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    isCorporate: boolean;
    setIsCorporate?: Dispatch<SetStateAction<boolean>>;
    showTabs: boolean;
    showUploadDocument: boolean;
    hasGuarantorPreset: boolean;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    showResetButton?: boolean;
    setSaveDraft: (value: boolean) => void;
} & UploadDocumentProp;

const ApplicantKYCInner = ({
    state,
    kycPresets,
    kycExtraSettings,
    applicationAgreements,
    isCorporate,
    setIsCorporate,
    showTabs,
    hasGuarantorPreset,
    setPrefill,
    showResetButton = false,
    dispatch,
    setSaveDraft,
    uploadDocument,
    removeDocument,
}: ApplicantKYCInnerProps) => {
    const { application } = state;
    const { theme } = useThemeComponents();
    const { endpoint, module, dealer, lead } = useFinderApplicationContext();
    const router = useRouter();
    const user = useAccount(true);

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isPorscheIDFetchLoading, setIsPorscheIDFetchLoading] = useState<boolean>(false);
    const [capModalHasError, setCapModalHasError] = useState(false);

    const {
        isSubmitting,
        handleSubmit,
        validateForm,
        submitForm,
        values,
        initialValues,
        resetForm,
        setFieldValue,
        setValues,
    } = useFormikContext<FinderKYCJourneyValues>();

    const { myInfoSettingId, isOcrEnabled, insurers } = module;

    const myInfo = useMyInfo({
        isEnabled: !!myInfoSettingId,
    });

    const { save: persistJourneyValue, persistedValue } = usePersistFinderJourneyValues();

    useEffect(() => {
        persistJourneyValue({
            ...persistedValue,
            kyc: values,
        });
    }, [persistedValue, persistJourneyValue, values]);

    const onOcrDetected = useOcrDetectedHandler(kycPresets);

    const onSaveDraft = useCallback(async () => {
        try {
            setSaveDraft(true);
            await validateForm();
            await submitForm();
        } finally {
            setSaveDraft(false);
        }
    }, [submitForm, validateForm, setSaveDraft]);

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const isApplyingFromDetails = useMemo(
        () =>
            (application.configuration.withFinancing && application.configuration.requestForFinancing) ||
            !isNil(application.draftFlow.isApplyingForFinanceCompleted) ||
            !isNil(application.draftFlow.isApplyingForInsuranceCompleted) ||
            !isNil(application.draftFlow.isApplyingForReservationCompleted),
        [
            application.configuration.requestForFinancing,
            application.configuration.withFinancing,
            application.draftFlow.isApplyingForFinanceCompleted,
            application.draftFlow.isApplyingForInsuranceCompleted,
            application.draftFlow.isApplyingForReservationCompleted,
        ]
    );

    const { proceedWithCustomerButton, proceedWithCustomer } = useProceedWithCustomerDeviceButton(
        kycPresets,
        state,
        dispatch
    );

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    const { capModuleId, isSearchCapOptional } = useMemo(() => {
        if (
            application.module.__typename === 'FinderApplicationPrivateModule' ||
            application.module.__typename === 'FinderApplicationPublicModule'
        ) {
            return {
                capModuleId: application.module.capModuleId,
                isSearchCapOptional: application.module.isSearchCapCustomerOptional,
            };
        }

        return {
            capModuleId: null,
            isSearchCapOptional: null,
        };
    }, [application]);

    const { showSearchCapCustomerButton, immediateOpenCapCustomerSearch } = useCapSearchCustomerAction({
        kycHasCompleted:
            application.draftFlow?.isApplicantKYCCompleted ||
            (application.module.__typename === 'FinderApplicationPrivateModule' &&
                application.applicant?.fields?.length > 0),
        requireLogin: application.module.__typename === 'FinderApplicationPrivateModule',
        isCapEnabled: !!capModuleId,
        isOptional: isSearchCapOptional,
        hasLeadGuid: !!values.capValues?.leadGuid,
        capModalHasError,
        isLead: !!lead,
    });

    const { porscheIdIntegrationEnabled, isPorscheIdLoginMandatory } = useMemo(
        () => getPorscheIDConfigByApplicationJourney(application),
        [application]
    );

    const isApplyingFromApplyNew = useMemo(
        () =>
            application.draftFlow.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application.draftFlow.isApplyNewForInsuranceReceived ||
                !application.draftFlow.isApplyNewForAppointmentReceived),
        [
            application.draftFlow.isReceived,
            application.draftFlow.isApplyNewForAppointmentReceived,
            application.draftFlow.isApplyNewForFinancingReceived,
            application.draftFlow.isApplyNewForInsuranceReceived,
        ]
    );

    const isAppointmentSectionVisible = useMemo(
        () =>
            values.configuration.testDrive &&
            hasAppointmentScenario(module.scenarios) &&
            module.appointmentModule &&
            module.displayAppointmentDatepicker,
        [
            module.appointmentModule,
            module.displayAppointmentDatepicker,
            module.scenarios,
            values.configuration.testDrive,
        ]
    );

    const showCommentsToBank = user && application?.bank?.showCommentsField;

    const showCommentsToInsurer = useMemo(() => {
        if (!user) {
            return false;
        }

        const insurer = insurers.find(insurer => insurer.id === application?.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [user, insurers, application?.insurancing?.insurerId]);

    const getTradeInValues = useCallback(
        () => ({
            withTradeIn: values.configuration.tradeIn,
            withTestDrive: values.configuration.testDrive,
            name: 'tradeInVehicle',
        }),
        [values.configuration.testDrive, values.configuration.tradeIn]
    );

    const getMyInfoVariables = useCallback(
        () => ({
            routerId: router.id,
            endpointId: endpoint.id,
            applicationId: application.id,
            customerKind: CustomerKind.Local,
            withTradeIn: values.configuration.tradeIn,
            withTestDrive: values.configuration.testDrive,
        }),
        [application.id, endpoint.id, router.id, values.configuration.testDrive, values.configuration.tradeIn]
    );

    const onCapValuesChanged = useCallback(
        (capValues: SelectedCapValues) => {
            switch (capValues.selectedValue) {
                case ViewState.BusinessPartner: {
                    const { selectedBusinessPartner } = capValues;

                    if (selectedBusinessPartner.currentVehicle && values.tradeInVehicle?.length) {
                        setFieldValue(
                            'tradeInVehicle.0',
                            getCurrentVehicleFromCap(selectedBusinessPartner, values.tradeInVehicle)
                        );
                    }

                    setFieldValue(
                        'customer.fields',
                        getKYCDataFromCap(selectedBusinessPartner, kycPresets, values.customer)
                    );
                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                    });

                    break;
                }

                default: {
                    const { selectedBusinessPartner, selectedLead } = capValues;

                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                        leadGuid: selectedLead?.leadGuid,
                        leadId: selectedLead?.leadId,
                    });
                }
            }
        },
        [kycPresets, setFieldValue, values.customer, values.tradeInVehicle]
    );

    const onCapSearchErrorConfirmed = useCallback(() => {
        setCapModalHasError(true);
    }, []);

    const hasCurrentVehiclePreset = useMemo(() => {
        const keys = kycPresets.map(preset => preset.key);

        return keys.includes(LocalCustomerFieldKey.CurrentVehicleVin);
    }, [kycPresets]);

    const onPorscheIDCustomerFetched = useCallback(
        (porscheIdData: PorscheIdData) => {
            getKYCDataFromPorscheId(porscheIdData, setValues, hasCurrentVehiclePreset);

            if (state?.stage === JourneyStage.PorscheIdLoginRegister) {
                dispatch({
                    type: 'goTo',
                    stage: JourneyStage.ApplicantKYC,
                });
            }
        },
        [dispatch, setValues, state?.stage, hasCurrentVehiclePreset]
    );

    useEffect(() => {
        if (
            isPorscheIdLoginMandatory &&
            values.customerCiamId &&
            state?.stage === JourneyStage.PorscheIdLoginRegister
        ) {
            dispatch({
                type: 'goTo',
                stage: JourneyStage.ApplicantKYC,
            });
        }
    }, [dispatch, isPorscheIdLoginMandatory, state?.stage, values.customerCiamId]);

    const v3LayoutType = router?.layout?.__typename === 'PorscheV3Layout';

    return (
        <SearchCapCustomerContextManager
            applicationModuleId={module.id}
            applicationRequireLogin={module.__typename === 'FinderApplicationPrivateModule'}
            capModuleId={module.capModuleId}
            dealerId={dealer.id}
            kycHasCompleted={application.draftFlow?.isApplicantKYCCompleted}
            lead={application.lead}
            onCapModalErrorConfirmed={onCapSearchErrorConfirmed}
            onCapValuesChanged={onCapValuesChanged}
        >
            <FormAutoTouch />
            <Form id="kycJourneyForm" name="kycJourneyForm" onSubmitCapture={handleSubmit}>
                <Row gutter={[24, 24]}>
                    <Col {...leftColSpan}>
                        <Space direction="vertical" size={40}>
                            <Space direction="vertical" size={v3LayoutType ? 24 : 40}>
                                <VehicleInterest application={application} />
                                {application.configuration.isAffinAutoFinanceCentreRequired && (
                                    <AffinAutoFinanceCentreSelect />
                                )}
                                {isAppointmentSectionVisible && (
                                    <AppointmentDetailsSection
                                        application={application}
                                        applicationModule={module}
                                        appointmentType={AppointmentType.Appointment}
                                        showSkipValidation={module.__typename === 'FinderApplicationPrivateModule'}
                                    />
                                )}
                                {!v3LayoutType && <DealerInfo dealer={application.dealer} />}
                                {myInfo.render({
                                    getMyInfoVariables,
                                    getTradeInValues,
                                    onLoading: setIsLoading,
                                    topDivider: (
                                        <Col span={24}>
                                            <SectionDivider />
                                        </Col>
                                    ),
                                })}
                                {!myInfo.withMyInfo && isOcrEnabled && <Ocr onDetected={onOcrDetected} />}
                            </Space>
                            {v3LayoutType && <DealerInfo dealer={application.dealer} />}
                        </Space>
                    </Col>
                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            extra={
                                proceedWithCustomerButton && (
                                    <ProceedWithCustomerButton onClick={() => proceedWithCustomer(values)} />
                                )
                            }
                            isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                            stage={state.stage}
                            stages={state.stages}
                            withCapSearchButton={showSearchCapCustomerButton}
                        >
                            <Row gutter={[24, 24]}>
                                {state.stage === JourneyStage.PorscheIdLoginRegister && (
                                    <MandatoryPorscheIDLogin
                                        applicationId={application.id}
                                        endpointId={endpoint.id}
                                        onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                                        routerId={router.id}
                                        setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                                    />
                                )}
                                {state.stage === JourneyStage.ApplicantKYC && (
                                    <>
                                        <Col span={24}>
                                            <CustomerDetails
                                                applicationId={application.id}
                                                customerKind={isCorporate ? CustomerKind.Corporate : CustomerKind.Local}
                                                endpointId={endpoint.id}
                                                hasGuarantorPreset={hasGuarantorPreset}
                                                hasUploadDocuments={application.bank?.hasUploadDocuments}
                                                hasVSOUpload={false}
                                                immediateOpenCapCustomerSearch={immediateOpenCapCustomerSearch}
                                                isApplyingFromApplyNew={isApplyingFromApplyNew}
                                                isApplyingFromDetails={isApplyingFromDetails}
                                                isGuarantorCompleted={application.draftFlow.isGuarantorCompleted}
                                                isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                                                kycExtraSettings={kycExtraSettings}
                                                kycPresets={kycPresets}
                                                onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                                                porscheIdIntegrationEnabled={porscheIdIntegrationEnabled}
                                                removeDocument={removeDocument}
                                                resetFormHandler={resetFormHandler}
                                                routerId={router.id}
                                                setIsCorporate={setIsCorporate}
                                                setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                                                setPrefill={setPrefill}
                                                showCommentsToInsurer={showCommentsToInsurer}
                                                showRemarks={showCommentsToBank}
                                                showResetButton={showResetButton && !myInfo.withMyInfo}
                                                showTabs={showTabs}
                                                showTitle={false}
                                                uploadDocument={uploadDocument}
                                                withFinancing={application.configuration.withFinancing}
                                            />
                                        </Col>
                                        <Col span={24}>
                                            <ConsentsAndDeclarations applicationAgreements={applicationAgreements} />
                                        </Col>
                                        {values.quotation?.source && (
                                            <Col span={24}>
                                                <QuotationDetails source={values.quotation.source} />
                                            </Col>
                                        )}
                                    </>
                                )}
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>
            {(isLoading || isPorscheIDFetchLoading) && <Backdrop />}
            <StyledJourneyToolbar>
                {showResetButton &&
                    !myInfo.withMyInfo &&
                    (theme === CompanyTheme.Porsche || theme === CompanyTheme.PorscheV3) && (
                        <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                    )}
                {canSaveDraft(application) && (
                    <SaveDraftButton
                        applicantKYC={kycPresets}
                        bankProvider={application.bank?.integration.provider}
                        kycExtraSettings={kycExtraSettings}
                        moduleCountryCode={module.company.countryCode}
                        onSaveDraft={onSaveDraft}
                        values={values}
                    />
                )}
                <NextButton
                    key="nextButton"
                    disabled={isSubmitting || (isPorscheIdLoginMandatory && !values.customerCiamId)}
                    onSubmit={onSubmit}
                />
            </StyledJourneyToolbar>
        </SearchCapCustomerContextManager>
    );
};

export default ApplicantKYCInner;
