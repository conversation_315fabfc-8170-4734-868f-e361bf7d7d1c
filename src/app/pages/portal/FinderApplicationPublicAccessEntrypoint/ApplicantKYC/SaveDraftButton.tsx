import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { KycField, LocalCustomerManagementModule } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import type { FinderApplicationState } from '../shared';
import type { FinderKYCJourneyValues } from '../types';

export const canSaveDraft = (application: FinderApplicationState) =>
    application.module.__typename === 'FinderApplicationPrivateModule' &&
    application.module.allowSaveDraftAndReserveStock &&
    !application.withCustomerDevice &&
    (!application.draftFlow?.isReceived ||
        (application.draftFlow?.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application?.draftFlow.isApplyNewForInsuranceReceived)));

const SaveDraftButton = ({
    values,
    onSaveDraft,
    applicantKYC,
    kycExtraSettings,
    moduleCountryCode,
    bankProvider,
}: {
    values: FinderKYCJourneyValues;
    onSaveDraft: () => void;
    applicantKYC: Array<KycField>;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    moduleCountryCode: string;
    bankProvider?: string;
}) => {
    const { t } = useTranslation('customerDetails');
    const { Button } = useThemeComponents();
    const { layout } = useRouter();

    const applicantsValidator = useKYCFormValidator({
        field: applicantKYC,
        extraSettings: kycExtraSettings,
        moduleCountryCode,
        prefix: 'customer.fields',
        saveDraft: true,
        bankProvider,
    });

    const disabled = useMemo(() => !isNil(applicantsValidator.validate(values)), [applicantsValidator, values]);

    return (
        <Button
            key="saveDraft"
            disabled={disabled}
            htmlType="button"
            onClick={onSaveDraft}
            porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
            type="tertiary"
        >
            {t('customerDetails:saveDraftButton')}
        </Button>
    );
};

export default SaveDraftButton;
