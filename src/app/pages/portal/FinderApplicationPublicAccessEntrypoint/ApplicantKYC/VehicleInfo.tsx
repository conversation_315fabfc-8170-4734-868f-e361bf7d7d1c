import { componentsReady, PIcon, PText } from '@porsche-design-system/components-react';
import { Col, Divider, Image, Row, Space, Typography } from 'antd';
import { isNil } from 'lodash/fp';
import { useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useRouter } from '../../../../components/contexts/shared';
import CollapsibleWrapper, { Panel, CollapseProps } from '../../../../components/wrappers/CollapsibleWrapper';
import breakpoints from '../../../../utilities/breakpoints';
import { roundUpWithPrecision } from '../../../../utilities/rounding';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import usePublic from '../../../../utilities/usePublic';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { useFinderApplicationContext } from '../FinderApplicationContext';
import type { FinderApplicationState } from '../shared';

const BaseVehicleCollapsible = styled(CollapsibleWrapper)`
    &.ant-collapse-icon-position-end > .ant-collapse-item > .ant-collapse-header {
        padding: 0;
        border-radius: var(--card-border-radius, initial);
    }
    .ant-collapse-content-box {
        padding-left: 16px;
        padding-right: 16px;
    }
`;

const StandardVehicleCollapsible = styled(BaseVehicleCollapsible)`
    &.ant-collapse-icon-position-end > .ant-collapse-item > .ant-collapse-header {
        border: 1px solid ${({ theme }) => theme.antd['border-color-base']};
        border-bottom: 0;

        .ant-collapse-arrow {
            right: 8px;
        }
    }
    .ant-collapse-content-box {
        border: 1px solid ${({ theme }) => theme.antd['border-color-base']};
        border-top: 0;
    }
    &.ant-collapse > .ant-collapse-item {
        border-bottom: 1px solid ${({ theme }) => theme.antd['border-color-base']};
    }
    .ant-collapse-content {
        border-top: 1px solid ${({ theme }) => theme.antd['border-color-base']};
    }
`;

const V3VehicleCollapsible = styled(BaseVehicleCollapsible)`
    &.ant-collapse-icon-position-end > .ant-collapse-item > .ant-collapse-header {
        border: none;

        .ant-collapse-arrow {
            right: -10px;
        }
    }
    .ant-collapse-content-box {
        border: none;
        padding: 56px 0 0 0;
    }
    &.ant-collapse > .ant-collapse-item {
        border-bottom: none;

        &.ant-collapse-item-active .ant-collapse-header {
            &::after {
                content: '';
                display: block;
                position: absolute;
                bottom: -32px;
                left: -32px;
                right: -32px;
                height: 1px;
                background-color: ${({ theme }) => theme.antd['border-color-base']};
            }
        }
    }
    .ant-collapse-content {
        border-top: none;
    }
`;

const VehicleCollapsible = ({ v3LayoutType, ...props }: CollapseProps & { v3LayoutType: boolean }) => {
    const CollapsibleComponent = v3LayoutType ? V3VehicleCollapsible : StandardVehicleCollapsible;

    return <CollapsibleComponent {...props} />;
};

export const ImageWrapper = styled.div<{ v3LayoutType: boolean }>`
    padding: 2px;
    border-radius: ${props => (props.v3LayoutType ? '8px' : '0')};
    border: 1px solid ${({ theme }) => theme.antd['border-color-base']};
`;

export const ImageBox = styled.div<{ $color?: string; $width?: string; $height?: string; v3LayoutType: boolean }>`
    width: ${({ $width, v3LayoutType }) => $width ?? (v3LayoutType ? '70px' : '60px')};
    height: ${({ $height, v3LayoutType }) => $height ?? (v3LayoutType ? '70px' : '60px')};
    position: relative;
    border-radius: ${props => (props.v3LayoutType ? '6px' : '0')};
    ${({ $color }) => $color && `background-color: ${$color};`}
`;

const Splitter = styled(Divider)`
    margin: 0;
    border-color: ${({ theme }) => theme.antd['border-color-base']};
`;

const SpaceContainer = styled(Space)`
    width: 100%;

    & > .ant-space-item {
        line-height: 1;
    }
`;

export const SpaceImage = styled(Space)`
    width: 100%;
`;

export const ValueText = styled(Typography.Text)`
    display: block;
    font-weight: 900;
    font-size: 16px;
    line-height: ${({ theme }) => theme.antd['line-height-base']};
`;

export const LabelText = styled(Typography.Text)<{ spacing?: number }>`
    display: block;
    font-weight: 400;
    font-size: 16px;
    line-height: ${({ theme }) => theme.antd['line-height-base']};
    margin-bottom: ${props => props.spacing}px;
`;

const SmallItalicText = styled(Typography.Text)`
    display: block;
    font-size: 12px;
    font-style: italic;
    font-weight: 400;
    line-height: ${({ theme }) => theme.antd['line-height-base']};
`;

type DynamicTextProps = {
    children: React.ReactNode;
    isLabel?: boolean;
    isValue?: boolean;
    invert?: boolean;
    v3LayoutType: boolean;
    spacing?: number;
};

export const DynamicText = ({
    children,
    isLabel,
    isValue,
    v3LayoutType,
    invert = false,
    spacing = 0,
}: DynamicTextProps) => {
    if (v3LayoutType) {
        let weight: 'regular' | 'bold' = 'regular';
        if (invert) {
            weight = isLabel ? 'regular' : 'bold';
        } else {
            weight = isLabel ? 'bold' : 'regular';
        }

        return (
            <PText style={{ marginBottom: `${spacing}px` }} weight={weight}>
                {children}
            </PText>
        );
    }

    if (isLabel) {
        return <LabelText spacing={spacing}>{children}</LabelText>;
    }
    if (isValue) {
        return <ValueText>{children}</ValueText>;
    }

    return <Typography.Text>{children}</Typography.Text>;
};

const VehicleImageContainer = styled.div<{ v3LayoutType: boolean }>`
    & .ant-image {
        width: 100%;
        & > img {
            aspect-ratio: 16/9;
            object-fit: cover;
            border-radius: ${props => (props.v3LayoutType ? '12px' : 0)};
        }
    }
`;

const useTextAlignStyle = (wrapperRef: React.RefObject<HTMLDivElement>) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const pText = wrapper.querySelector('p-text');
            const shadowRoot = pText?.shadowRoot;
            if (!shadowRoot) {
                return;
            }

            const style = document.createElement('style');
            style.textContent = `
                @media screen and (max-width: ${breakpoints.md}) {
                    :host .root {
                        text-align: right;
                    }
                }
            `;
            shadowRoot.appendChild(style);
        });
    }, [wrapperRef]);
};

const DescriptionText = ({ children, ...props }: DynamicTextProps) => {
    const wrapperRef = useRef<HTMLDivElement>(null);
    useTextAlignStyle(wrapperRef);

    return (
        <div ref={wrapperRef}>
            <DynamicText {...props}>{children}</DynamicText>
        </div>
    );
};

type EqualGridProps = {
    title?: React.ReactNode;
    description?: React.ReactNode;
    lg?: number;
    v3LayoutType: boolean;
};

const EqualGrid = ({ title, description, lg = 12, v3LayoutType }: EqualGridProps) => (
    <Row gutter={16}>
        <Col lg={lg} xs={14}>
            <DynamicText v3LayoutType={v3LayoutType} invert isLabel>
                {title}
            </DynamicText>
        </Col>
        <Col lg={lg} xs={10}>
            <DescriptionText v3LayoutType={v3LayoutType} invert isValue>
                {description ?? '-'}
            </DescriptionText>
        </Col>
    </Row>
);

type VehicleInfoProps = {
    application: FinderApplicationState;
};

type FinderCockpitImageProps = {
    seatStitchColor: string;
    seatColor: string;
    cockpitStitchColor: string;
    cockpitColor: string;
    v3LayoutType?: boolean;
};

export const FinderCockpitImage = ({
    seatStitchColor,
    seatColor,
    cockpitStitchColor,
    cockpitColor,
    v3LayoutType,
}: FinderCockpitImageProps) => (
    <>
        <svg
            style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                left: '0px',
                top: '0px',
            }}
            viewBox="0 0 48 48"
        >
            <rect fill={seatStitchColor} height="11" rx="2" width="2" x="15" y="-1" />
            <rect fill={seatStitchColor} height="11" rx="2" width="2" x="15" y="12" />
            <rect fill={seatStitchColor} height="11" rx="2" width="2" x="15" y="25" />
            <rect fill={seatStitchColor} height="11" rx="2" width="2" x="15" y="38" />
            <rect fill={cockpitStitchColor} height="11" rx="2" width="2" x="31" y="-1" />
            <rect fill={cockpitStitchColor} height="11" rx="2" width="2" x="31" y="12" />
            <rect fill={cockpitStitchColor} height="11" rx="2" width="2" x="31" y="25" />
            <rect fill={cockpitStitchColor} height="11" rx="2" width="2" x="31" y="38" />
        </svg>
        <div
            style={{
                height: '100%',
                width: '50%',
                borderTopLeftRadius: v3LayoutType ? '6px' : '0',
                borderBottomLeftRadius: v3LayoutType ? '6px' : '0',
                backgroundColor: seatColor,
            }}
        />
        <div
            style={{
                height: '100%',
                width: '50%',
                borderTopRightRadius: v3LayoutType ? '6px' : '0',
                borderBottomRightRadius: v3LayoutType ? '6px' : '0',
                backgroundColor: cockpitColor,
            }}
        />
    </>
);

const VehicleInfo = ({ application }: VehicleInfoProps) => {
    const { t } = useTranslation(['finderJourney', 'common', 'calculators']);
    const translatedString = useTranslatedString();
    const formats = useCompanyFormats();

    const { vehicle } = useFinderApplicationContext();
    const { layout } = useRouter();

    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    const {
        interior,
        images,
        transmission,
        mileage,
        firstRegistrationDate,
        numberOfPreviousOwners,
        power,
        accidentFree,
        engineType,
    } = vehicle.listing.vehicle;

    const transmissionManual = usePublic('icons/manual.jpg');
    const transmissionAuto = usePublic('icons/automatic.jpg');

    const image = useMemo(() => images.edges[0].node.variants[0], [images.edges]);

    const seatStitchColor = interior?.color?.seatStitchingColorHexCode ?? '#ffffff';
    const seatColor = interior?.color?.seatColorHexCode ?? '#ffffff';
    const cockpitStitchColor = interior?.color?.cockpitStitchingColorHexCode ?? '#ffffff';
    const cockpitColor = interior?.color?.cockpitColorHexCode ?? '#ffffff';

    const vehicleName = translatedString(vehicle.name);
    const totalPrice = formats.formatAmountWithCurrency(
        application.financing?.totalPrice ?? vehicle.listing.price.value
    );

    const monthlyPaymentText =
        application.configuration.withFinancing && application.financing?.monthlyInstalment
            ? t('calculators:monthlyPaymentInfo', {
                  value: formats.formatAmountWithCurrency(
                      roundUpWithPrecision(application.financing.monthlyInstalment?.[0]?.amount, formats.amountDecimals)
                  ),
              })
            : null;

    const expandIcon = v3LayoutType
        ? ({ isActive }) => <PIcon name={isActive ? 'arrow-head-up' : 'arrow-head-down'} />
        : undefined;

    return (
        <VehicleCollapsible
            expandIcon={expandIcon}
            mode="default"
            v3LayoutType={v3LayoutType}
            isCompact
            useTransparentBackground
        >
            <Panel
                key="vehicleInfo"
                header={
                    <Row gutter={16} style={{ alignItems: 'center' }}>
                        <Col span={v3LayoutType ? 11 : 12}>
                            <VehicleImageContainer v3LayoutType={v3LayoutType}>
                                <Image preview={false} src={image.url} />
                            </VehicleImageContainer>
                        </Col>
                        <Col span={v3LayoutType ? 13 : 12}>
                            <div style={{ paddingRight: 16 }}>
                                {v3LayoutType ? (
                                    <Space direction="vertical" size={8}>
                                        <PText size="medium" weight="bold">
                                            {vehicleName}
                                        </PText>
                                        <PText color="contrast-high" size="small">
                                            {totalPrice}
                                        </PText>
                                    </Space>
                                ) : (
                                    <>
                                        <ValueText>{vehicleName}</ValueText>
                                        <LabelText>{totalPrice}</LabelText>
                                    </>
                                )}
                                {monthlyPaymentText &&
                                    (v3LayoutType ? (
                                        <PText color="contrast-medium" size="x-small">
                                            {monthlyPaymentText}
                                        </PText>
                                    ) : (
                                        <SmallItalicText>{monthlyPaymentText}</SmallItalicText>
                                    ))}
                            </div>
                        </Col>
                    </Row>
                }
            >
                <SpaceContainer
                    direction="vertical"
                    size={v3LayoutType ? [0, 16] : [0, 12]}
                    split={v3LayoutType ? null : <Splitter />}
                    style={{ width: '100%' }}
                >
                    <SpaceImage>
                        <ImageWrapper v3LayoutType={v3LayoutType}>
                            <ImageBox
                                $color={vehicle.listing.vehicle.exteriorColor.value}
                                v3LayoutType={v3LayoutType}
                            />
                        </ImageWrapper>
                        <div>
                            <DynamicText v3LayoutType={v3LayoutType} isLabel>
                                {t('finderJourney:vehicleInterest.fields.exteriorColor')}
                            </DynamicText>
                            <DynamicText v3LayoutType={v3LayoutType} isValue>
                                {vehicle.listing.vehicle.exteriorColor?.name?.localize}
                            </DynamicText>
                        </div>
                    </SpaceImage>

                    <SpaceImage>
                        <ImageWrapper v3LayoutType={v3LayoutType}>
                            <ImageBox style={{ display: 'flex' }} v3LayoutType={v3LayoutType}>
                                <FinderCockpitImage
                                    cockpitColor={cockpitColor}
                                    cockpitStitchColor={cockpitStitchColor}
                                    seatColor={seatColor}
                                    seatStitchColor={seatStitchColor}
                                    v3LayoutType={v3LayoutType}
                                />
                            </ImageBox>
                        </ImageWrapper>
                        <div>
                            <DynamicText v3LayoutType={v3LayoutType} isLabel>
                                {t('finderJourney:vehicleInterest.fields.interiorColor')}
                            </DynamicText>
                            <DynamicText v3LayoutType={v3LayoutType} isValue>
                                {interior.name.localize}
                            </DynamicText>
                        </div>
                    </SpaceImage>

                    <SpaceImage>
                        <ImageWrapper v3LayoutType={v3LayoutType}>
                            <ImageBox
                                as="img"
                                src={
                                    transmission?.localize?.toUpperCase() === 'MANUAL'
                                        ? transmissionManual
                                        : transmissionAuto
                                }
                                v3LayoutType={v3LayoutType}
                            />
                        </ImageWrapper>
                        <div>
                            <DynamicText v3LayoutType={v3LayoutType} isLabel>
                                {t('finderJourney:vehicleInterest.fields.transmission')}
                            </DynamicText>
                            <DynamicText v3LayoutType={v3LayoutType} isValue>
                                {transmission.localize}
                            </DynamicText>
                        </div>
                    </SpaceImage>

                    <SpaceContainer
                        direction="vertical"
                        size={v3LayoutType ? [0, 8] : [0, 12]}
                        split={<Splitter />}
                        style={{ width: '100%', marginTop: v3LayoutType ? 8 : 0 }}
                    >
                        <EqualGrid
                            description={mileage?.localize}
                            title={t('finderJourney:vehicleInterest.fields.mileage')}
                            v3LayoutType={v3LayoutType}
                        />
                        <EqualGrid
                            description={firstRegistrationDate?.localize}
                            title={t('finderJourney:vehicleInterest.fields.firstRegistration')}
                            v3LayoutType={v3LayoutType}
                        />
                        <EqualGrid
                            description={numberOfPreviousOwners}
                            title={t('finderJourney:vehicleInterest.fields.previousOwners')}
                            v3LayoutType={v3LayoutType}
                        />
                        <EqualGrid
                            description={`${power?.kilowatt?.localize ?? '-'}/${power?.horsepower?.localize ?? '-'}`}
                            title={t('finderJourney:vehicleInterest.fields.power')}
                            v3LayoutType={v3LayoutType}
                        />
                        {!isNil(accidentFree) && (
                            <EqualGrid
                                description={
                                    accidentFree
                                        ? t('finderJourney:vehicleInterest.options.accidentHistory.yes')
                                        : t('finderJourney:vehicleInterest.options.accidentHistory.no')
                                }
                                title={t('finderJourney:vehicleInterest.fields.accidentHistory')}
                                v3LayoutType={v3LayoutType}
                            />
                        )}
                        <EqualGrid
                            description={engineType?.localize}
                            title={t('finderJourney:vehicleInterest.fields.engineType')}
                            v3LayoutType={v3LayoutType}
                        />
                    </SpaceContainer>
                </SpaceContainer>
            </Panel>
        </VehicleCollapsible>
    );
};

export default VehicleInfo;
