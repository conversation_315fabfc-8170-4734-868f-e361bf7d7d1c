import { CtsModuleSettingDataFragment } from '../../../api/fragments/CtsModuleSettingData';
import { PromoCodeDataFragment } from '../../../api/fragments/PromoCodeData';
import {
    AffinAutoFinanceCentre,
    CapValuesInput,
    CustomerKind,
    FinderApplicationConfigurationPayload,
} from '../../../api/types';
import { KYCPresetFormFields } from '../../../utilities/kycPresets';
import { AgreementValues } from '../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { QuotationFormValues } from '../../shared/JourneyPage/QuotationDetails/types';
import { AppointmentValues } from '../StandardApplicationEntrypoint/AppointmentPage/appointmentValues';
import { JourneyStage, State, Action } from '../StandardApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from './shared';

export type JourneyLocationState = {
    token: string;
    stage?: JourneyStage;
};

export type MaybeJourneyLocationState = JourneyLocationState | null | undefined;

export type JourneyState = State<FinderApplicationState> & { ctsSetting?: CtsModuleSettingDataFragment };
export type JourneyAction = Action<FinderApplicationState>;
export type MaybeJourneyState = JourneyState | null | undefined;

export type FinderApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type FinderKYCJourneyValues = QuotationFormValues & {
    agreements: AgreementValues;
    customer: FinderApplicantFormValues;
    configuration: FinderApplicationConfigurationPayload;
    isCorporateCustomer: boolean;
    hasGuarantor: boolean;

    tradeInVehicle?: FinderApplicationState['tradeInVehicle'];
    /** Depends on application's bank settings */
    remarks?: string;
    /** Depends on application's insurer settings */
    commentsToInsurer?: string;

    uploadDocuments?: {
        [CustomerKind.Guarantor]?: File[];
        [CustomerKind.Local]?: File[];
        [CustomerKind.Corporate]?: File[];
    };
    prefill: boolean;
    appointment?: AppointmentValues;

    financing?: {
        affinAutoFinanceCentre?: AffinAutoFinanceCentre;
    };

    capValues?: CapValuesInput;
    customerCiamId?: string;
};

export type ApplicationScenarioConfig = {
    testDrive: boolean;
    withFinancing: boolean;
    withInsurance: boolean;
    withPayment?: boolean;
    withLeadCapture?: boolean;
    isFinancingOptional: boolean;
    isInsuranceOptional: boolean;
};

export type OfferPageStateProps = {
    promoCodeInput: string;
    promoCodeError: string;
    promoCode: PromoCodeDataFragment | null;
    showFinanceCalculator: boolean;
};

type OfferPageSetPromoCodeAction = { type: 'setPromoCode'; promoCode: PromoCodeDataFragment | null };
type OfferPageSetPromoCodeInputAction = { type: 'setPromoCodeInput'; promoCodeInput: string };
type OfferPageSetPromoCodeErrorAction = { type: 'setPromoCodeError'; promoCodeError: string };
type OfferPageSetShowFinanceCalculatorAction = { type: 'setShowFinanceCalculator'; showFinanceCalculator: boolean };

export type OfferPageActions =
    | OfferPageSetPromoCodeAction
    | OfferPageSetPromoCodeInputAction
    | OfferPageSetPromoCodeErrorAction
    | OfferPageSetShowFinanceCalculatorAction;

export type OfferPageActionsProps = {
    setPromoCode: (promoCode: PromoCodeDataFragment | null) => void;
    setPromoCodeInput: (promoCodeInput: string) => void;
    setPromoCodeError: (promoCodeError: string) => void;
    setShowFinanceCalculator: (showFinanceCalculator: boolean) => void;
};

export type OfferPageActionStateProps = {
    actions: OfferPageActionsProps;
    state: OfferPageStateProps;
};

export { JourneyStage };
