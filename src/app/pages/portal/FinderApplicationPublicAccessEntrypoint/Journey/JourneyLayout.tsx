import React, { Dispatch, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import JourneySteps from '../../../shared/JourneyPage/JourneySteps';
import { JourneyStepsProvider } from '../../../shared/JourneyPage/JourneyStepsContext';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../shared';
import useFinderLayout from '../useFinderLayout';

type JourneyLayoutProps = PropsWithChildren & {
    state: State<FinderApplicationState>;
    dispatch: Dispatch<Action<FinderApplicationState>>;
};

const JourneyLayout = ({ state, dispatch, children }: JourneyLayoutProps) => {
    const { application, stage: currentStage } = state;

    const { t } = useTranslation(['customerDetails']);
    const { BackButton, FinderLayout } = useThemeComponents();
    const { title, onBack, shouldShowBackIcon, useJourneyLayout } = useFinderLayout(
        dispatch,
        application,
        currentStage
    );

    if (!useJourneyLayout) {
        // eslint-disable-next-line react/jsx-no-useless-fragment
        return <>{children}</>;
    }

    return (
        <FinderLayout
            backIcon={
                shouldShowBackIcon ? <BackButton type="link">{t('customerDetails:backButton')}</BackButton> : null
            }
            onBack={onBack}
            title={title}
            hasFooterBar
        >
            <BasicProLayoutContainer>
                <JourneyStepsProvider
                    value={{
                        onBack,
                        shouldAllowBack: shouldShowBackIcon,
                    }}
                >
                    <JourneySteps currentStage={state.stage} stages={state?.stages} />
                    {children}
                </JourneyStepsProvider>
            </BasicProLayoutContainer>
        </FinderLayout>
    );
};

export default JourneyLayout;
