/* eslint-disable max-len */
import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { FinderApplicationEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationEntrypointContextData';
import { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/FinderApplicationPublicAccessEntrypointContextData';
import { useExtendFinderVehicleExpiryMutation } from '../../../../api/mutations/extendFinderVehicleExpiry';
import { useReleaseFinderVehicleExpiryMutation } from '../../../../api/mutations/releaseFinderVehicleExpiry';
import { ApplicationSigningPurpose } from '../../../../api/types';
import ApplicationSessionModal from '../../../../components/ApplicationSessionExpireModal';
import PorscheNotFound from '../../../../components/results/PorscheNotFound';
import { useThemeComponents } from '../../../../themes/hooks';
import useApplicationConcurrencyRedirect from '../../../../utilities/useApplicationConcurrencyRedirect';
import RedirectAndReplace from '../../../shared/RedirectAndReplace';
import AdyenPaymentPage from '../../StandardApplicationEntrypoint/AdyenPaymentPage';
import ConfirmationPage from '../../StandardApplicationEntrypoint/ConfirmationPage';
import FiservPaymentPage from '../../StandardApplicationEntrypoint/FiservPaymentPage';
import {
    NamiralRedirectPage,
    NamirialPage,
    NamirialRejectPage,
    NamirialTimeoutPage,
} from '../../StandardApplicationEntrypoint/NamirialPage';
import OTPPage from '../../StandardApplicationEntrypoint/OTPPage';
import PayGatePaymentPage from '../../StandardApplicationEntrypoint/PayGatePaymentPage';
import PorschePaymentPage from '../../StandardApplicationEntrypoint/PorschePaymentPage';
import TtbPaymentPage from '../../StandardApplicationEntrypoint/TtbPaymentPage';
import { mayProceedToThanksYou } from '../../StandardApplicationEntrypoint/shared/util';
import ApplicantKYC from '../ApplicantKYC';
import GuarantorKYC from '../GuarantorKYC';
import type { FinderApplicationState } from '../shared';
import { JourneyStage } from '../types';
import InitializeStage from './InitializeStage';
import JourneyLayout from './JourneyLayout';
import { reducer } from './reducer';

export type JourneyControllerProps = {
    initialToken: string;
    initialApplication: FinderApplicationState;
    initialStage: JourneyStage;
    journeyStages: JourneyStage[];
    endpoint:
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
};

const JourneyController = ({
    initialToken,
    initialApplication,
    initialStage,
    endpoint,
    journeyStages,
}: JourneyControllerProps) => {
    const initialState = useRef({
        token: initialToken,
        stage: initialStage,
        application: initialApplication,
        stages: journeyStages,
    });

    const navigate = useNavigate();

    const [state, dispatch] = useReducer(reducer, initialState.current);

    const locationState = useLocation().state as {
        token?: string;
        myInfoAuthorizationCode?: string;
        linkId?: string;
        porscheIdAuthorizationCode?: string;
    };

    const { myInfoAuthorizationCode, linkId, porscheIdAuthorizationCode } = locationState;

    useApplicationConcurrencyRedirect(initialToken);

    const { FinderLayout } = useThemeComponents();

    const [extendFinderVehicle] = useExtendFinderVehicleExpiryMutation();
    const [releaseFinderVehicle] = useReleaseFinderVehicleExpiryMutation();

    const extendSession = useCallback(async () => {
        const { data } = await extendFinderVehicle({ variables: { token: state.token } });

        if (data?.result?.application.__typename !== 'FinderApplication') {
            throw new Error('Unexpected application type');
        }

        dispatch({
            type: 'refresh',
            token: data.result.token,
            application: data.result.application,
        });
    }, [extendFinderVehicle, state.token]);

    const endSession = useCallback(async () => {
        await releaseFinderVehicle({ variables: { token: state.token } });
    }, [releaseFinderVehicle, state.token]);

    useEffect(() => {
        // ensure we locally persist the latest token as well
        navigate('.', {
            state: { myInfoAuthorizationCode, token: state.token, linkId, porscheIdAuthorizationCode },
            replace: true,
        });
    }, [state.token, navigate, myInfoAuthorizationCode, linkId, porscheIdAuthorizationCode]);

    const journeyContent = useMemo(() => {
        switch (state.stage) {
            case JourneyStage.Initialize:
                return <InitializeStage dispatch={dispatch} state={state} />;

            case JourneyStage.PorscheIdLoginRegister:
            case JourneyStage.ApplicantKYC:
                return <ApplicantKYC dispatch={dispatch} endpoint={endpoint} state={state} />;

            case JourneyStage.GuarantorKYC:
                return <GuarantorKYC dispatch={dispatch} endpoint={endpoint} state={state} />;

            case JourneyStage.Deposit:
                switch (state.application.deposit?.__typename) {
                    case 'ApplicationAdyenDeposit':
                        return (
                            <AdyenPaymentPage
                                CustomLayout={FinderLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationPorscheDeposit':
                        return (
                            <PorschePaymentPage
                                CustomLayout={FinderLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationFiservDeposit':
                        return (
                            <FiservPaymentPage
                                CustomLayout={FinderLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationPayGateDeposit':
                        return (
                            <PayGatePaymentPage
                                CustomLayout={FinderLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationTtbDeposit':
                        return (
                            <TtbPaymentPage
                                CustomLayout={FinderLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    default:
                        return <PorscheNotFound listingUrl={endpoint.listingUrl} />;
                }

            case JourneyStage.Otp:
                return (
                    <OTPPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Finance}
                        shouldIncludeLayout={false}
                        state={state}
                    />
                );

            case JourneyStage.InsuranceOtp:
                return (
                    <OTPPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Insurance}
                        shouldIncludeLayout={false}
                        state={state}
                    />
                );

            case JourneyStage.Namirial:
            case JourneyStage.GuarantorNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.Finance} state={state} />;

            case JourneyStage.NamirialRedirect:
            case JourneyStage.GuarantorNamirialRedirect:
                return (
                    <NamiralRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Finance}
                        state={state}
                    />
                );

            case JourneyStage.InsuranceNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.Insurance} state={state} />;

            case JourneyStage.InsuranceNamirialRedirect:
                return (
                    <NamiralRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Insurance}
                        state={state}
                    />
                );

            case JourneyStage.NamirialReject:
            case JourneyStage.GuarantorNamirialReject:
                return <NamirialRejectPage />;

            case JourneyStage.NamirialTimeout:
            case JourneyStage.GuarantorNamirialTimeout:
                return <NamirialTimeoutPage />;

            case JourneyStage.ConfirmEmailSend:
                return <ConfirmationPage />;

            case JourneyStage.Unknown:
            default:
                return <PorscheNotFound listingUrl={endpoint.listingUrl} />;
        }
    }, [FinderLayout, endpoint, state]);

    const canProceedToThankYou = mayProceedToThanksYou(state.application);

    if (canProceedToThankYou) {
        return <RedirectAndReplace path={{ pathname: '../thankyou' }} state={state} />;
    }

    return (
        <>
            <ApplicationSessionModal
                endSession={endSession}
                expireAt={state.application.expireAt}
                extendSession={extendSession}
            />
            <JourneyLayout dispatch={dispatch} state={state}>
                {journeyContent}
            </JourneyLayout>
        </>
    );
};

export default JourneyController;
