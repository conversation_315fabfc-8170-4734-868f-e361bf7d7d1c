import { PText } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useRouter } from '../../../../../components/contexts/shared';
// eslint-disable-next-line max-len
import CarSelectionNoResult from '../../../StandardApplicationEntrypoint/CarListingPage/CarSelection/CarSelectionNoResult';
import { CarSelectionContainer, Title } from '../../../StandardApplicationEntrypoint/CarListingPage/CarSelection/ui';
import { useCarListingState } from '../CarListingStateProvider';
import CarCard from './CarCard';

const CarSelection = () => {
    const {
        state: { variants, openFilterDrawer, total },
        meta: { leadId: leadSuiteId },
    } = useCarListingState();

    const { t } = useTranslation(['carList']);
    const navigate = useNavigate();
    const { layout } = useRouter();

    const onClickHandler = useCallback(
        (variantId: string) => {
            navigate(`./${variantId}`, leadSuiteId ? { state: { leadId: leadSuiteId } } : undefined);
        },
        [leadSuiteId, navigate]
    );

    if (variants.length === 0) {
        return (
            <CarSelectionContainer $showFilter={openFilterDrawer}>
                <CarSelectionNoResult />
            </CarSelectionContainer>
        );
    }

    const isPorscheV3Layout = layout.__typename === 'PorscheV3Layout';
    const VehicleCount = isPorscheV3Layout ? (
        <PText color="contrast-medium" style={{ marginBottom: '36px' }}>
            {t('carList:noOfAvailable.vehiclePorschev3', { count: total })}
        </PText>
    ) : (
        <Title level={5}>{t('carList:noOfAvailable.vehicle', { count: total })}</Title>
    );

    return (
        <CarSelectionContainer $showFilter={openFilterDrawer}>
            {VehicleCount}
            <Row gutter={[24, isPorscheV3Layout ? 36 : 40]} style={{ marginTop: '15px' }}>
                {variants.map(variant => (
                    <Col key={variant.id} sm={openFilterDrawer ? 24 : 12} xl={openFilterDrawer ? 8 : 6} xs={24}>
                        <CarCard onClick={() => onClickHandler(`./${variant?.listing?.id}`)} variant={variant} />
                    </Col>
                ))}
            </Row>
        </CarSelectionContainer>
    );
};

export default CarSelection;
