import { PHeading, PText } from '@porsche-design-system/components-react';
import { CardProps } from 'antd';
import { last } from 'lodash/fp';
import { useMemo } from 'react';
import { FinderVehicleSelectionDataFragment } from '../../../../../api/fragments';
import { useRouter } from '../../../../../components/contexts/shared';
import { roundUpWithPrecision } from '../../../../../utilities/rounding';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import usePublic from '../../../../../utilities/usePublic';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import { StyledVehicleCard, StyledVehicleCardMeta } from '../../../../shared/CIPage/carListingUI';

export type CarCardProps = {
    variant: FinderVehicleSelectionDataFragment;
    onClick?: () => void;
} & CardProps;

const CarCard = ({ variant, onClick, ...props }: CarCardProps) => {
    const translate = useTranslatedString();
    const { formatAmountWithCurrency, amountDecimals } = useCompanyFormats();
    const fallbackSrc = usePublic('empty.svg');
    const { layout } = useRouter();
    const image = useMemo(
        () => last(variant?.listing?.vehicle?.images?.edges?.[0]?.node?.variants).url || fallbackSrc,
        [fallbackSrc, variant?.listing?.vehicle?.images]
    );

    const title = translate(variant.name);

    const description =
        `${formatAmountWithCurrency(variant.listing?.price.value)} ` +
        `(${formatAmountWithCurrency(roundUpWithPrecision(variant.monthlyInstalment, amountDecimals))}/month)`;

    const VehicleCardMeta =
        layout.__typename === 'PorscheV3Layout' ? (
            <>
                <PHeading color="primary" size="medium" style={{ marginBottom: '4px' }}>
                    {title}
                </PHeading>
                <PText color="contrast-medium">{description}</PText>
            </>
        ) : (
            <StyledVehicleCardMeta description={description} title={title} />
        );

    return (
        <StyledVehicleCard
            key={variant.id}
            bordered={false}
            cover={<img alt={title} src={image} />}
            hoverable={false}
            onClick={onClick}
            {...props}
        >
            {VehicleCardMeta}
        </StyledVehicleCard>
    );
};

export default CarCard;
