import { PHeading } from '@porsche-design-system/components-react';
import { Grid, Typography } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from 'styled-components';
import { LayoutType } from '../../../../api/types';
import { useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
// eslint-disable-next-line max-len
import { usePersistFinderJourneyValues } from '../../FinderApplicationPublicAccessEntrypoint/OfferPage/FinderVehicleDetailsPage/usePersistFinderJourneyValues';
import CarListingPageDisclaimer from '../../StandardApplicationEntrypoint/CarListingPage/CarListingPageDisclaimer';
import CarFilterOnLargeScreen from './CarFilter/CarFilterOnLargeScreen';
import CarFilterOnSmallScreen from './CarFilter/CarFilterOnSmallScreen';
import { useCarListingState } from './CarListingStateProvider';
import CarSelection from './CarSelection';

const InfiniteScrollContainer = styled.div`
    width: 100%;
`;

const Container = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0;
    margin: 0;
    min-height: calc(100vh - 246px);

    @media screen and (min-width: ${breakpoints.md}) {
        flex-direction: row;
        justify-content: space-between;
        margin: ${props => (props.theme.layoutType === LayoutType.PorscheV3 ? '0' : '0 36px')};
    }
`;

const CarListingInner = () => {
    const { t } = useTranslation('carList');
    const { FinderLayout } = useThemeComponents();
    const { layout } = useRouter();

    const { remove: removePersistedFinderJourneyValue } = usePersistFinderJourneyValues();

    useEffect(() => {
        removePersistedFinderJourneyValue();
    }, [removePersistedFinderJourneyValue]);

    const {
        state: { total, variants, page },
        actions,
        meta: { endpoint },
    } = useCarListingState();

    const { dealerId } = useSingleDealerId();

    const next = useCallback(() => {
        actions.setPage(page + 1);
    }, [actions, page]);

    const screens = Grid.useBreakpoint();

    const isPorscheV3Layout = layout.__typename === 'PorscheV3Layout';
    const Title = isPorscheV3Layout ? (
        <PHeading size="xx-large">{t('carList:titlePorscheV3')}</PHeading>
    ) : (
        <Typography.Title level={5}>{t('carList:title')}</Typography.Title>
    );

    return (
        <FinderLayout headerBottomMargin="small" title={Title}>
            {!isNil(screens.md) && !screens.md && <CarFilterOnSmallScreen />}
            <Container>
                <InfiniteScrollContainer>
                    <InfiniteScroll
                        dataLength={variants.length}
                        hasMore={variants.length < total}
                        loader={null}
                        next={next}
                        scrollThreshold={0.8}
                        style={{ overflow: 'hidden' }}
                    >
                        <CarSelection />
                    </InfiniteScroll>
                </InfiniteScrollContainer>
                {screens.md && <CarFilterOnLargeScreen />}
            </Container>
            <CarListingPageDisclaimer applicationModule={endpoint.finderApplicationModules[0]} dealerIds={[dealerId]} />
        </FinderLayout>
    );
};

export default CarListingInner;
