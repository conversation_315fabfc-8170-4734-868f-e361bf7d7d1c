/* eslint-disable max-len */
import { Route, Routes } from 'react-router-dom';
import { FinderApplicationEntrypointContextDataFragment } from '../../../api/fragments/FinderApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import PorscheNotFound from '../../../components/results/PorscheNotFound';
import { useThemeComponents } from '../../../themes/hooks';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import { FinderApplicationProvider } from '../FinderApplicationPublicAccessEntrypoint/FinderApplicationContext';
import { FinderLeadProvider } from '../FinderApplicationPublicAccessEntrypoint/FinderLeadContext';
import Journey from '../FinderApplicationPublicAccessEntrypoint/Journey';
import ThankYouPage from '../FinderApplicationPublicAccessEntrypoint/ThankYou';
import SessionRevokedPage from '../SessionRevokedPage';
import CustomerValidationPage from '../StandardApplicationEntrypoint/CustomerValidationPage';
import TestDriveJourney from '../TestDriveEntrypoint/Journey';

export type FinderApplicationEntrypointProps = {
    endpoint: FinderApplicationEntrypointContextDataFragment;
};

const RemoteAccessEntryPoint = ({ endpoint }: FinderApplicationEntrypointProps) => {
    const { FinderLayout } = useThemeComponents();

    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <FinderLeadProvider>
                <Routes>
                    <Route
                        key="authorize"
                        element={<CustomerValidationPage layout={FinderLayout} />}
                        path="authorize"
                    />
                    <Route
                        key="finderRoot"
                        element={<FinderApplicationProvider endpoint={endpoint} isRemote />}
                        path=":listingId"
                    >
                        <Route key="apply" element={<Journey endpoint={endpoint} />} path="apply" />
                        <Route key="testdrive" element={<TestDriveJourney endpoint={endpoint} />} path="testdrive" />
                        <Route key="thankyou" element={<ThankYouPage />} path="thankyou" />
                        <Route key="sessionRevoked" element={<SessionRevokedPage />} path="sessionRevoked" />
                    </Route>
                    <Route key="404" element={<PorscheNotFound listingUrl={endpoint.listingUrl} />} path="*" />
                </Routes>
            </FinderLeadProvider>
        </>
    );
};

export default RemoteAccessEntryPoint;
