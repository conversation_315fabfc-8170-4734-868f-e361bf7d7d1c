/* eslint-disable max-len */
import { Route, Routes } from 'react-router-dom';
import { FinderApplicationEntrypointContextDataFragment } from '../../../api/fragments/FinderApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import PrivateAccessEntryPoint from './PrivateAccessEntryPoint';
import RemoteAccessEntryPoint from './RemoteAccessEntryPoint';

export type FinderApplicationEntrypointProps = {
    endpoint: FinderApplicationEntrypointContextDataFragment;
};

const FinderApplicationEntrypoint = ({ endpoint }: FinderApplicationEntrypointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key="public" element={<RemoteAccessEntryPoint endpoint={endpoint} />} path="remote/*" />
                <Route key="private" element={<PrivateAccessEntryPoint endpoint={endpoint} />} path="*" />
            </Routes>
        </>
    );
};

export default FinderApplicationEntrypoint;
