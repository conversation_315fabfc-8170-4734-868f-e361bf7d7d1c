import { useMemo } from 'react';
import { Route, Routes } from 'react-router-dom';
import { WebpageEndpointContextDataFragment } from '../../../api/fragments/WebpageEndpointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import RedirectAndReplace from '../../shared/RedirectAndReplace';
import NotFoundPage from '../NotFoundPage';
import WebPage from './Webpage';

export type WebpageEntrypointProps = {
    endpoint: WebpageEndpointContextDataFragment;
};

const MobilityWebpageEndpoint = ({ endpoint }: WebpageEntrypointProps) => {
    const homePath = useMemo(
        () => endpoint.webpagePaths.find(({ webpageId }) => webpageId === endpoint.webPageId)?.webpageSubpath ?? '404',
        [endpoint]
    );

    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.title}`} />
            <Routes>
                {endpoint.webpagePaths.map(webpagePath => (
                    <Route
                        key="urlSlug"
                        element={<WebPage webpageId={webpagePath.webpageId} />}
                        path={webpagePath.webpageSubpath}
                    />
                ))}
                <Route
                    key="homePage"
                    element={<RedirectAndReplace key="homePage" path={homePath} state={null} />}
                    path=""
                />
                <Route key="404" element={<NotFoundPage />} path="*" />,
            </Routes>
        </>
    );
};

export default MobilityWebpageEndpoint;
