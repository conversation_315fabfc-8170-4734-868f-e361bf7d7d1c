import React, { useReducer } from 'react';
import { useGetApplicationAuditTrailsQuery } from '../../../../api/queries';
import { pageReducer } from '../../../../components/PaginatedTable';
import ActivityLogs from '../ui/ActivityLogs';
import { useLaunchpadApplicationDetailsContext } from '../utils/LaunchpadApplicationDetailsContext';

const ShowroomVisitActivityLogs = () => {
    const { application } = useLaunchpadApplicationDetailsContext();
    const [state, dispatch] = useReducer(pageReducer, { page: 1, pageSize: 10 });

    const query = useGetApplicationAuditTrailsQuery({
        variables: {
            applicationId: application?.id,
            pagination: { limit: state.pageSize, offset: (state.page - 1) * state.pageSize },
        },
        fetchPolicy: 'network-only',
        skip: !application,
        notifyOnNetworkStatusChange: true,
    });

    if (!application) {
        return null;
    }

    return (
        <ActivityLogs
            agreements={application.showroomVisitAgreements}
            dispatch={dispatch}
            entityId={application.id}
            queryResult={query}
            state={state}
            timezone={application.module.company.timeZone}
        />
    );
};

export default ShowroomVisitActivityLogs;
