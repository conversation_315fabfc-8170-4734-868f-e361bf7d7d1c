import type { QueryResult } from '@apollo/client';
import React, { Dispatch, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import type { GetApplicationAuditTrailsQuery } from '../../../../api/queries/getApplicationAuditTrails';
import type { ListLeadAuditTrailsQuery } from '../../../../api/queries/listLeadAuditTrails';
import LoadingElement from '../../../../components/LoadingElement';
import type { PageState, PageAction } from '../../../../components/PaginatedTable';
import InternalErrorResult from '../../../../components/results/InternalErrorResult';
import ActivityLogTable from '../../../shared/ActivityLog/ActivityLogTable';
import type { ConsentModalState } from '../../../shared/ActivityLog/ApplicationActivityLog';
import ConsentAndDeclarationModal from '../../../shared/ActivityLog/ConsentAndDeclarationModal';
import { ContentItem, ContentItemTitle } from './ContentItem';

export type ActivityLogsProps = {
    entityId: string;
    timezone: string;
    agreements: ApplicationAgreementDataFragment[];
    queryResult: QueryResult<ListLeadAuditTrailsQuery> | QueryResult<GetApplicationAuditTrailsQuery>;
    state: PageState;
    dispatch: Dispatch<PageAction>;
};

const ActivityLogs = ({ entityId, timezone, agreements, queryResult, state, dispatch }: ActivityLogsProps) => {
    const { t } = useTranslation(['launchpadLeadDetails']);

    const [consentModalState, setConsentModalState] = useState<ConsentModalState>({
        open: false,
        targetConsentId: undefined,
    });

    const { loading, error, data } = queryResult;

    if (loading) {
        return <LoadingElement />;
    }

    if (!entityId || (!loading && error)) {
        return <InternalErrorResult />;
    }

    return (
        <ContentItem>
            <ContentItemTitle>{t('launchpadLeadDetails:form.title.activityLogs')}</ContentItemTitle>
            <ActivityLogTable
                count={data?.result?.count ?? 0}
                data={data?.result?.items ?? []}
                dispatch={dispatch}
                showCnDModalHandler={(consentId: string) =>
                    setConsentModalState({ open: true, targetConsentId: consentId, source: 'applicant' })
                }
                state={state}
                timezone={timezone}
                porscheV3StyleOverride
            />
            <ConsentAndDeclarationModal
                applicantAgreements={agreements}
                guarantorAgreements={[]}
                onClose={() =>
                    setConsentModalState({
                        open: false,
                        targetConsentId: undefined,
                        source: undefined,
                    })
                }
                open={consentModalState.open}
                source={consentModalState.source}
                targetConsentId={consentModalState.targetConsentId ?? ''}
            />
        </ContentItem>
    );
};

export default ActivityLogs;
