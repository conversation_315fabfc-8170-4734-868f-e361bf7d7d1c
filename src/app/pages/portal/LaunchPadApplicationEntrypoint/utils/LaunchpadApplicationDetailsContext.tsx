import { ApolloQueryResult } from '@apollo/client';
import { createContext, useContext, useMemo } from 'react';
import * as permissionKind from '../../../../../shared/permissions';
import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { GetApplicationQuery } from '../../../../api/queries/getApplication';
import hasPermissions from '../../../../utilities/hasPermissions';

type LaunchpadApplicationDataFragment = Extract<ApplicationDataFragment, { __typename: 'LaunchpadApplication' }>;

type LaunchpadApplicationDetailsContextProps = {
    application: LaunchpadApplicationDataFragment;
    permissions: {
        hasUpdatePermission: boolean;
        hasViewPermission: boolean;
        hasCreateTestDrivePermission: boolean;
    };
    refetch?: (variables?: Partial<{ id: string }>) => Promise<ApolloQueryResult<GetApplicationQuery>>;
};

const LaunchpadApplicationDetailsContext = createContext<LaunchpadApplicationDetailsContextProps>(null);

export const useLaunchpadApplicationDetailsContext = () => {
    const context = useContext(LaunchpadApplicationDetailsContext);

    if (context === null) {
        throw new Error(
            'useLaunchpadApplicationDetailsContext must be used within a LaunchpadApplicationDetailsProvider'
        );
    }

    return context;
};

export const LaunchpadApplicationDetailsContextProvider = ({
    application,
    refetch,
    children,
}: {
    application: LaunchpadApplicationDataFragment;
    refetch?: (variables?: Partial<{ id: string }>) => Promise<ApolloQueryResult<GetApplicationQuery>>;
    children: React.ReactNode;
}) => {
    const value = useMemo(
        () => ({
            application,
            permissions: {
                hasUpdatePermission: hasPermissions(application.permissions, [permissionKind.updateApplication]),
                hasViewPermission: hasPermissions(application.permissions, [permissionKind.viewApplications]),
                hasCreateTestDrivePermission: hasPermissions(application.permissions, [permissionKind.createTestDrive]),
            },
            refetch,
        }),
        [application, refetch]
    );

    return (
        <LaunchpadApplicationDetailsContext.Provider value={value}>
            {children}
        </LaunchpadApplicationDetailsContext.Provider>
    );
};
