import React, { useReducer } from 'react';
import { useListLeadAuditTrailsQuery } from '../../../../api';
import { pageReducer } from '../../../../components/PaginatedTable';
import ActivityLogs from '../ui/ActivityLogs';
import { useLeadDetailsContext } from './LeadDetailsContext';

const LeadActivityLogs = () => {
    const { lead } = useLeadDetailsContext();
    const [state, dispatch] = useReducer(pageReducer, { page: 1, pageSize: 10 });

    const query = useListLeadAuditTrailsQuery({
        variables: {
            leadId: lead?.id,
            pagination: { limit: state.pageSize, offset: (state.page - 1) * state.pageSize },
        },
        fetchPolicy: 'network-only',
        skip: !lead,
        notifyOnNetworkStatusChange: true,
    });

    if (!lead) {
        return null;
    }

    return (
        <ActivityLogs
            agreements={lead.customerAgreements}
            dispatch={dispatch}
            entityId={lead.id}
            queryResult={query}
            state={state}
            timezone={lead.module.company.timeZone}
        />
    );
};

export default LeadActivityLogs;
