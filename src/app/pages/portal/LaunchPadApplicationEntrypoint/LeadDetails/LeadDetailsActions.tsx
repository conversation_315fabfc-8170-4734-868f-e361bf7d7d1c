import { Col, Row } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { useMeasure } from 'react-use';
import urljoin from 'url-join';
// eslint-disable-next-line max-len
import { LaunchPadApplicationEntrypointContextDataFragment } from '../../../../api/fragments/LaunchPadApplicationEntrypointContextData';
import { LaunchPadModuleSpecsFragment } from '../../../../api/fragments/LaunchPadModuleSpecs';
import { LeadListEndpointContextDataFragment } from '../../../../api/fragments/LeadListEndpointContextData';
import { ApplicationStage, LaunchPadModule, LeadStatus } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import { useAppointmentModal } from '../../../../components/leads/appointmentModal';
import { useFollowUpLeadModal } from '../../../../components/leads/followUpModal';
import { useIntentAndAssignModal } from '../../../../components/leads/intentAndAssignModal';
import { useQualifyModal } from '../../../../components/leads/qualifyModal';
import { useShowroomVisitModal } from '../../../../components/leads/showroomVisitModal';
import { useTradeInModal } from '../../../../components/leads/tradeInModal';
import useCheckCapIntegrationIsOn from '../../../../utilities/useCheckCapIntegrationIsOn';
import usePublic from '../../../../utilities/usePublic';
import useSalesOfferModal from '../CreateSalesOffer/useSalesOfferModal';
import ActionButton from '../ui/ActionButton';
import { ContentItem } from '../ui/ContentItem';
import useLaunchpadActionColSpan from '../utils/useLaunchpadActionColSpan';
import useLeadVehicleDetails from '../utils/useLeadVehicleDetails';
import { useLeadDetailsContext } from './LeadDetailsContext';

type LeadDetailsActionProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    dealerId: string;
    endpoint: LaunchPadApplicationEntrypointContextDataFragment | LeadListEndpointContextDataFragment;
};

const LeadDetailsActions = ({ launchpadModule, dealerId, endpoint }: LeadDetailsActionProps) => {
    const { t } = useTranslation(['launchpadLeadDetails']);
    const thumbsUpIcon = usePublic('icons/thumbs-up.svg');
    const thumbsDownIcon = usePublic('icons/thumbs-down.svg');
    const navigate = useNavigate();
    const router = useRouter(false);
    const [ref, { width }] = useMeasure();

    const { lead, permissions, refetchLead } = useLeadDetailsContext();

    const colSpan = useLaunchpadActionColSpan(width);

    const company = useCompany();

    const qualifyModal = useQualifyModal({ launchpadModule, lead, dealerId, refetchLead });

    const followUpModal = useFollowUpLeadModal({
        launchpadModule,
        lead,
        onCloseAction: () => refetchLead(),
    });

    const onIntentAndAssignModalClose = useCallback(
        (isSaved: boolean) => {
            if (isSaved) {
                refetchLead();
            }
        },
        [refetchLead]
    );

    const intentAndAssignModal = useIntentAndAssignModal({
        launchpadModule,
        lead,
        dealerId,
        onClose: onIntentAndAssignModalClose,
    });

    const appointmentModal = useAppointmentModal({
        launchpadModule,
        endpointId: endpoint.id,
        lead,
        dealerId,
        countryCode: company.countryCode,
        timeZone: company.timeZone,
    });

    const showroomVisitModal = useShowroomVisitModal({
        launchpadModule,
        endpointId: endpoint.id,
        lead,
        timeZone: company.timeZone,
    });

    const tradeInModal = useTradeInModal({
        endpointId: endpoint.id,
        launchpadModuleId: launchpadModule.id,
        lead,
        refetchLead,
    });
    const createSalesOfferModal = useSalesOfferModal({
        salesOfferModuleId:
            endpoint.__typename === 'LaunchPadApplicationEntrypoint'
                ? endpoint.launchPadApplicationModule.salesOfferModuleId
                : (
                      endpoint.applicationModules.find(
                          module => module.__typename === 'LaunchPadModule'
                      ) as LaunchPadModule
                  )?.salesOfferModuleId,
        leadSuiteId: lead.versioning.suiteId,
        refetchLead,
    });

    const isFeatureUsed = useMemo(
        () => ({
            isTradeInApplied: lead.applications.find(application =>
                application.stages.includes(ApplicationStage.TradeIn)
            ),
            // F&I should always can create new application
            isFnIApplied: false,
            isSalesOfferApplied: lead.__typename === 'LaunchpadLead' && !isNil(lead?.salesOffer),
        }),
        [lead]
    );

    const salesOfferId = useMemo(
        () => (lead.__typename === 'LaunchpadLead' && !isNil(lead.salesOffer) ? lead.salesOffer?.id : null),
        [lead]
    );

    const financeAndInsuranceCalculatorEndpoint = useMemo(() => {
        if (!router?.endpoints?.length) {
            return null;
        }

        const launchpadFinanceAndInsuranceCalculatorModuleEndpoint = router.endpoints.find(endpoint => {
            if (endpoint.__typename === 'StandardApplicationEntrypoint') {
                return endpoint.applicationModule.id === launchpadModule.financeAndInsuranceCalculator;
            }

            return false;
        });

        if (launchpadFinanceAndInsuranceCalculatorModuleEndpoint && lead.vehicle) {
            return urljoin(launchpadFinanceAndInsuranceCalculatorModuleEndpoint.pathname, 'details', lead.vehicle.id);
        }

        return null;
    }, [launchpadModule, lead, router]);

    const vehicleInfo = useLeadVehicleDetails(lead);

    const navigateToApplicationEntryPoint = useCallback(
        (endpoint: string, extraState?: Record<string, any>) => {
            navigate(`/${endpoint}`, {
                state: { leadId: lead.versioning.suiteId, dealerId: lead.dealerId, ...extraState },
            });
        },
        [lead.dealerId, lead.versioning.suiteId, navigate]
    );

    const capIntegrationIsOn = useCheckCapIntegrationIsOn({ lead });

    if (lead.status === LeadStatus.SubmittingToCap) {
        return null;
    }

    if (!lead.isLead) {
        if (!permissions.hasUpdatePermission) {
            return null;
        }

        return (
            <ContentItem>
                <Row ref={ref} gutter={[24, 24]}>
                    {permissions.hasUpdatePermission && (
                        <>
                            <Col span={colSpan}>
                                <ActionButton
                                    description={t('launchpadLeadDetails:actions.intent&Assign.description')}
                                    icon="list"
                                    label={t('launchpadLeadDetails:actions.intent&Assign.label')}
                                    onClick={() => intentAndAssignModal.open()}
                                />
                            </Col>
                            {intentAndAssignModal.render()}
                        </>
                    )}

                    {permissions.hasUpdatePermission &&
                        lead.status === LeadStatus.PendingQualify &&
                        capIntegrationIsOn && (
                            <>
                                <Col span={colSpan}>
                                    <ActionButton
                                        description={t('launchpadLeadDetails:actions.qualify.description')}
                                        iconSource={thumbsUpIcon}
                                        label={t('launchpadLeadDetails:actions.qualify.label')}
                                        onClick={() => qualifyModal.qualify()}
                                    />
                                </Col>
                                <Col span={colSpan}>
                                    <ActionButton
                                        description={t('launchpadLeadDetails:actions.unqualify.description')}
                                        iconSource={thumbsDownIcon}
                                        label={t('launchpadLeadDetails:actions.unqualify.label')}
                                        onClick={() => qualifyModal.unqualify()}
                                    />
                                </Col>
                                {qualifyModal.render()}
                            </>
                        )}
                </Row>
            </ContentItem>
        );
    }

    return (
        <ContentItem>
            <Row ref={ref} gutter={[24, 24]}>
                {permissions.hasCreateShowroomVisitPermission && launchpadModule.visitAppointmentModuleId && (
                    <Col span={colSpan}>
                        <ActionButton
                            description={t('launchpadLeadDetails:actions.showroomVisit.description')}
                            icon="car"
                            label={t('launchpadLeadDetails:actions.showroomVisit.label')}
                            onClick={() => showroomVisitModal.createShowroomVisit()}
                        />
                        {showroomVisitModal.render()}
                    </Col>
                )}
                {permissions.hasCreateTestDrivePermission && launchpadModule.appointmentModuleId && (
                    <>
                        <Col span={colSpan}>
                            <ActionButton
                                description={t('launchpadLeadDetails:actions.testDrive.description')}
                                icon="calendar"
                                label={t('launchpadLeadDetails:actions.testDrive.label')}
                                onClick={() => appointmentModal.createTestDrive()}
                            />
                        </Col>
                        {appointmentModal.render()}
                    </>
                )}
                {permissions.hasCreateFinanceAndInsurancePermission &&
                    launchpadModule.financeAndInsuranceCalculator &&
                    financeAndInsuranceCalculatorEndpoint && (
                        <Col span={colSpan}>
                            <ActionButton
                                description={t('launchpadLeadDetails:actions.f&iCalculator.description')}
                                icon="calculator"
                                label={t('launchpadLeadDetails:actions.f&iCalculator.label')}
                                onClick={() => navigateToApplicationEntryPoint(financeAndInsuranceCalculatorEndpoint)}
                            />
                        </Col>
                    )}
                {permissions.hasCreateFinderApplicationFromLeadPermission &&
                    launchpadModule.finderAssignedStockEntrypoint && (
                        <Col span={colSpan}>
                            <ActionButton
                                description={t('launchpadLeadDetails:actions.assignStock.description')}
                                icon="garage"
                                label={t('launchpadLeadDetails:actions.assignStock.label')}
                                onClick={() =>
                                    navigateToApplicationEntryPoint(
                                        launchpadModule.finderAssignedStockEntrypoint.pathname,
                                        vehicleInfo
                                    )
                                }
                            />
                        </Col>
                    )}
                {permissions.hasCreateTradeInPermission && launchpadModule.salesManager && (
                    <>
                        <Col span={colSpan}>
                            <ActionButton
                                description={t('launchpadLeadDetails:actions.tradeIn.description')}
                                icon="switch"
                                label={t('launchpadLeadDetails:actions.tradeIn.label')}
                                onClick={() =>
                                    isNil(isFeatureUsed.isTradeInApplied)
                                        ? tradeInModal.open()
                                        : navigate(`tradeIn/${isFeatureUsed.isTradeInApplied.versioning.suiteId}`)
                                }
                            />
                        </Col>
                        {tradeInModal.render()}
                    </>
                )}
                {permissions.hasCreateSalesOfferPermission && launchpadModule.salesOfferModuleId && (
                    <Col span={colSpan}>
                        <ActionButton
                            description={t('launchpadLeadDetails:actions.salesOffer.description')}
                            icon="purchase"
                            label={t('launchpadLeadDetails:actions.salesOffer.label')}
                            onClick={() =>
                                !isFeatureUsed.isSalesOfferApplied
                                    ? createSalesOfferModal.open()
                                    : navigate(`salesOffer/${salesOfferId}`)
                            }
                        />
                        {createSalesOfferModal.render()}
                    </Col>
                )}

                {permissions.hasCreateFollowUpPermission && (
                    <>
                        <Col span={colSpan}>
                            <ActionButton
                                description={t('launchpadLeadDetails:actions.followUp.description')}
                                icon="chat"
                                label={t('launchpadLeadDetails:actions.followUp.label')}
                                onClick={() => followUpModal.open()}
                            />
                        </Col>
                        {followUpModal.render()}
                    </>
                )}
            </Row>
        </ContentItem>
    );
};

export default LeadDetailsActions;
