import { createContext, useContext, useMemo } from 'react';
import * as permissionKind from '../../../../../shared/permissions';
import {
    LaunchPadApplicationEntrypointContextDataFragment,
    LeadListEndpointContextDataFragment,
    LeadDataFragment,
} from '../../../../api';
import hasPermissions from '../../../../utilities/hasPermissions';

type LeadDetailsContextProps = {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment | LeadListEndpointContextDataFragment;
    lead: LeadDataFragment;
    permissions: {
        hasUpdatePermission: boolean;
        hasViewPermission: boolean;
        hasCreateTestDrivePermission: boolean;
        hasCreateShowroomVisitPermission: boolean;
        hasCreateFollowUpPermission: boolean;
        hasCreateFinanceAndInsurancePermission: boolean;
        hasCreateTradeInPermission: boolean;
        hasCreateFinderApplicationFromLeadPermission: boolean;
        hasCreateSalesOfferPermission: boolean;
    };
    refetchLead: () => void;
};

const LeadDetailsContext = createContext<LeadDetailsContextProps>(null);

export const useLeadDetailsContext = () => {
    const context = useContext(LeadDetailsContext);

    if (context === null) {
        throw new Error('useLeadDetailsContext must be used within a LeadDetailsProvider');
    }

    return context;
};

export const LeadDetailsContextProvider = ({
    endpoint,
    lead,
    children,
    refetchLead,
}: {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment | LeadListEndpointContextDataFragment;
    lead: LeadDataFragment;
    children: React.ReactNode;
    refetchLead?: () => void;
}) => {
    const value = useMemo(
        () => ({
            endpoint,
            lead,
            permissions: {
                hasUpdatePermission: hasPermissions(lead.permissions, [
                    !lead.isLead ? permissionKind.updateContact : permissionKind.updateLead,
                ]),
                hasViewPermission: hasPermissions(lead.permissions, [
                    !lead.isLead ? permissionKind.viewContact : permissionKind.viewLeads,
                ]),
                hasCreateTestDrivePermission: hasPermissions(lead.permissions, [permissionKind.createTestDrive]),
                hasCreateShowroomVisitPermission: hasPermissions(lead.permissions, [
                    permissionKind.createShowroomVisit,
                ]),
                hasCreateFollowUpPermission: hasPermissions(lead.permissions, [permissionKind.createFollowUp]),
                hasCreateFinanceAndInsurancePermission:
                    hasPermissions(lead.permissions, [permissionKind.updateLead]) &&
                    hasPermissions(lead.permissions, [permissionKind.createFinanceAndInsuranceApplication]),
                hasCreateTradeInPermission: hasPermissions(lead.permissions, [permissionKind.createTradeInApplication]),
                hasCreateFinderApplicationFromLeadPermission:
                    hasPermissions(lead.permissions, [permissionKind.updateLead]) &&
                    hasPermissions(lead.permissions, [permissionKind.createFinderApplicationFromLead]),
                hasCreateSalesOfferPermission: hasPermissions(lead.permissions, [permissionKind.createSalesOffer]),
            },
            refetchLead,
        }),
        [lead, refetchLead, endpoint]
    );

    return <LeadDetailsContext.Provider value={value}>{children}</LeadDetailsContext.Provider>;
};
