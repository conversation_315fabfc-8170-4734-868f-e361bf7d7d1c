import { Route, Routes } from 'react-router-dom';
// eslint-disable-next-line max-len
import { LaunchPadApplicationEntrypointContextDataFragment } from '../../../api/fragments/LaunchPadApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import withPrivateAccess from '../../../utilities/withPrivateAccess';
import MetaData from '../../shared/MetaData';
import NotFoundPage from '../NotFoundPage';
import TestDriveJourney from '../TestDriveEntrypoint/Journey';
import AppointmentDetailsPage from './AppointmentDetails';
import LaunchpadDashboard from './Dashboard/LaunchpadDashboard';
import LeadDetailsPage from './LeadDetails/LeadDetailsPage';
import SalesOfferDetails from './SalesOfferDetails';
import ShowroomVisitDetailsPage from './ShowroomVisitDetails';
import TradeInDetails from './TradeInDetails';

export type LaunchPadApplicationEntrypointProps = {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment;
};

const LaunchPadApplicationEntrypoint = ({ endpoint }: LaunchPadApplicationEntrypointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key={`dashboard-${endpoint.id}`} element={<LaunchpadDashboard endpoint={endpoint} />} path="" />
                <Route
                    key={`lead-details-${endpoint.id}`}
                    element={<LeadDetailsPage endpoint={endpoint} />}
                    path="leads/:id"
                />

                <Route
                    key={`lead-details-${endpoint.id}-trade-in`}
                    element={<TradeInDetails endpoint={endpoint} />}
                    path="leads/:id/tradein/:tradeInId"
                />

                <Route
                    key={`lead-details-${endpoint.id}-sales-offer`}
                    element={<SalesOfferDetails endpoint={endpoint} />}
                    path="leads/:id/salesOffer/:salesOfferId"
                />

                <Route
                    key={`appointment-details-${endpoint.id}`}
                    element={<AppointmentDetailsPage endpoint={endpoint} />}
                    path="appointment/:id"
                />

                <Route
                    key={`showroom-visit-details-${endpoint.id}`}
                    element={<ShowroomVisitDetailsPage endpoint={endpoint} />}
                    path="showroomvisit/:id"
                />
                <Route key="testDrive" element={<TestDriveJourney endpoint={endpoint} />} path="testDrive" />
                <Route key="404" element={<NotFoundPage />} path="*" />
            </Routes>
        </>
    );
};

export default withPrivateAccess(LaunchPadApplicationEntrypoint);
