import { ApolloError } from '@apollo/client';
import { Col, Row } from 'antd';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useUpdateMainDetailsSalesOfferMutation } from '../../../../../../api';
import { useCompany } from '../../../../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../../../../themes/hooks';
import useCompanyFormats from '../../../../../../utilities/useCompanyFormats';
import { withCommasForNumberString } from '../../../../../../utilities/useFormats';
import useHandleError from '../../../../../../utilities/useHandleError';
import useSalesOfferOptions from '../../../../../../utilities/useSalesOfferOptions';
import useValidator from '../../../../../../utilities/useValidator';
import validators from '../../../../../../utilities/validators';
import { ButtonContainer, StyledForm } from '../../../LeadDetails/LeadDetailsInner';
import { defaultFieldsColSpan } from '../../../LeadDetails/ui';
import { ContentItem, ContentItemTitle } from '../../../ui/ContentItem';
import { consolidateConfigurator } from '../Vehicle/Configurator';
import { extractSalesOfferFromLaunchpadLead } from '../shared';
import type { MainDetailsValues } from './typings';

const StyledRow = styled(Row)`
    .ant-form-item {
        margin-bottom: 0;
    }
`;

const MainDetails = () => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails', 'launchpadLeadDetails']);
    const { currency } = useCompany();

    const [mutation] = useUpdateMainDetailsSalesOfferMutation();
    const { formatAmount } = useCompanyFormats();
    const {
        Button,
        FormFields: { DatePickerField, TextAreaField, InputNumberField, DisplayField, SelectField },
        notification,
    } = useThemeComponents();

    const validation = useValidator(
        validators.compose(
            validators.requiredDate('estimatedDeliveryDate'),
            validators.requiredNumber('optionsSubsidy'),
            validators.requiredNumber('coe'),
            validators.requiredNumber('consecutiveBidsNo'),
            validators.requiredString('coeCategory')
        )
    );

    const { id, vehicle, tradeInApplication, tradeIn, mainDetails, vsaSerialNumber } =
        extractSalesOfferFromLaunchpadLead();

    const { vehiclePrice } = consolidateConfigurator(vehicle.porscheVehicleData);

    const initialValues: MainDetailsValues = useMemo(
        () => ({
            allPrice: vehiclePrice + vehicle.localFittedOptions.reduce((acc, option) => acc + option.price, 0),
            vehicleTradeInValue: tradeIn.isEnabled ? tradeInApplication?.tradeInVehicle?.[0]?.price : undefined,
            optionsSubsidy: mainDetails.optionsSubsidy,
            estimatedDeliveryDate: mainDetails.estimatedDeliveryDate
                ? dayjs(mainDetails.estimatedDeliveryDate).toDate()
                : null,
            coe: mainDetails.coeAmount || 0,
            consecutiveBidsNo: mainDetails.numberConsecutiveBids,
            remarks: mainDetails.remarks || '',
            coeCategory: mainDetails.coeCategory,
            vsaSerialNumber,
        }),
        [
            vehiclePrice,
            vehicle.localFittedOptions,
            tradeIn.isEnabled,
            tradeInApplication?.tradeInVehicle,
            mainDetails.optionsSubsidy,
            mainDetails.estimatedDeliveryDate,
            mainDetails.coeAmount,
            mainDetails.numberConsecutiveBids,
            mainDetails.remarks,
            mainDetails.coeCategory,
            vsaSerialNumber,
        ]
    );

    const onSubmit = useHandleError<MainDetailsValues>(
        async values => {
            try {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.updatingSalesOfferMainDetails'),
                    duration: 0,
                    key: 'primary',
                });

                const { data } = await mutation({
                    variables: {
                        id,
                        mainDetails: {
                            estimatedDeliveryDate: values.estimatedDeliveryDate,
                            optionsSubsidy: values.optionsSubsidy,
                            coeAmount: values.coe,
                            numberConsecutiveBids: values.consecutiveBidsNo,
                            coeCategory: values.coeCategory,
                            remarks: values.remarks,
                        },
                    },
                });

                notification.destroy('primary');

                if (data) {
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.updateSalesOfferMainDetailsSuccessful'),
                        key: 'secondary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            }
        },
        [id, mutation, notification, t]
    );

    const { salesOfferCoeCategories } = useSalesOfferOptions();

    return (
        <Formik<MainDetailsValues>
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={validation}
            enableReinitialize
        >
            {({ handleSubmit, values }) => (
                <StyledForm
                    id="salesOfferMainDetailsForm"
                    name="salesOfferMainDetailsForm"
                    onSubmitCapture={handleSubmit}
                >
                    <ContentItem>
                        <ContentItemTitle>
                            {t('launchpadSalesOfferDetails:sections.mainDetails.title')}
                        </ContentItemTitle>
                        <StyledRow gutter={[16, 16]}>
                            <Col {...defaultFieldsColSpan}>
                                <DisplayField
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.allPrice', {
                                        returnObjects: true,
                                    })}
                                    addonBefore={currency}
                                    value={formatAmount(values.allPrice)}
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <DisplayField
                                    {...t(
                                        'launchpadSalesOfferDetails:sections.mainDetails.fields.vehicleTradeInValue',
                                        {
                                            returnObjects: true,
                                        }
                                    )}
                                    addonBefore={currency}
                                    value={formatAmount(values.vehicleTradeInValue)}
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <InputNumberField
                                    name="optionsSubsidy"
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.optionsSubsidy', {
                                        returnObjects: true,
                                    })}
                                    addonBefore={currency}
                                    formatter={value => withCommasForNumberString(value as string)}
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <DatePickerField
                                    name="estimatedDeliveryDate"
                                    {...t(
                                        'launchpadSalesOfferDetails:sections.mainDetails.fields.estimatedDeliveryDate',
                                        {
                                            returnObjects: true,
                                        }
                                    )}
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <InputNumberField
                                    formatter={value => withCommasForNumberString(value as string)}
                                    name="coe"
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.coe', {
                                        returnObjects: true,
                                    })}
                                    addonBefore={currency}
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <SelectField
                                    name="coeCategory"
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.coeCategory', {
                                        returnObjects: true,
                                    })}
                                    options={salesOfferCoeCategories}
                                    required
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <InputNumberField
                                    name="consecutiveBidsNo"
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.consecutiveBidsNo', {
                                        returnObjects: true,
                                    })}
                                    max={99}
                                    maxLength={2}
                                />
                            </Col>
                            <Col {...defaultFieldsColSpan}>
                                <DisplayField
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.vsaSerialNumber', {
                                        returnObjects: true,
                                    })}
                                    value={values.vsaSerialNumber}
                                />
                            </Col>
                            <Col xs={24}>
                                <TextAreaField
                                    name="remarks"
                                    {...t('launchpadSalesOfferDetails:sections.mainDetails.fields.remarks', {
                                        returnObjects: true,
                                    })}
                                />
                            </Col>
                        </StyledRow>
                    </ContentItem>
                    <ButtonContainer>
                        <Button form="salesOfferMainDetailsForm" htmlType="submit" type="primary">
                            {t('common:actions.save')}
                        </Button>
                    </ButtonContainer>
                </StyledForm>
            )}
        </Formik>
    );
};
export default MainDetails;
