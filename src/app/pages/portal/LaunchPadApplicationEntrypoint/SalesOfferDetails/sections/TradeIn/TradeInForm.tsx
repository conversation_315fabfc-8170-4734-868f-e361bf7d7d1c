import { ApolloError } from '@apollo/client';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { LocalCustomerFieldSource, useUpdateTradeInSalesOfferMutation } from '../../../../../../api';
import { useLanguage } from '../../../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../../../themes/hooks';
import useHandleError from '../../../../../../utilities/useHandleError';
import useValidator from '../../../../../../utilities/useValidator';
import validators from '../../../../../../utilities/validators';
import { useLeadDetailsContext } from '../../../LeadDetails/LeadDetailsContext';
import { StyledForm } from '../../../LeadDetails/LeadDetailsInner';
import { ContentItem, ContentItemTitle } from '../../../ui/ContentItem';
import { extractSalesOfferFromLaunchpadLead } from '../shared';
import TradeInFormActions from './TradeInFormActions';
import type { TradeInFormValues } from './typings';

interface TradeInFormProps {
    children: ReactNode;
    defaultValues: TradeInFormValues;
    isUpdate: boolean;
}

const TradeInForm = ({ children, defaultValues, isUpdate }: TradeInFormProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);

    const { notification } = useThemeComponents();

    const [mutation] = useUpdateTradeInSalesOfferMutation();
    const { id, tradeInApplication } = extractSalesOfferFromLaunchpadLead();

    const { endpoint } = useLeadDetailsContext();
    const { currentLanguageId } = useLanguage();
    const onSubmit = useHandleError<TradeInFormValues>(
        async values => {
            try {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.updatingTradeInSalesOffer'),
                    duration: 0,
                    key: 'primary',
                });

                const { data } = await mutation({
                    variables: {
                        id,
                        endpointId: endpoint.id,
                        languageId: currentLanguageId,
                        tradeIn: {
                            isEnabled: values.isEditable,
                            applicationSuiteId: tradeInApplication?.versioning.suiteId,
                            tradeInVehicle: values.isEditable
                                ? {
                                      ...omit('isEditable', values),
                                      yearOfManufacture: values.yearOfManufacture
                                          ? parseInt(dayjs(values.yearOfManufacture).format('YYYY'), 10)
                                          : null,
                                      isSelected: false,
                                      source: LocalCustomerFieldSource.UserInput,
                                  }
                                : null,
                        },
                    },
                });

                notification.destroy('primary');

                if (data) {
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.updateTradeInSalesOfferSuccessful'),
                        key: 'secondary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            }
        },
        [notification, t, mutation, id, endpoint.id, currentLanguageId, tradeInApplication?.versioning.suiteId]
    );

    const validation = useValidator(
        validators.only(
            value => value.isEnabled,
            validators.compose(
                validators.requiredString('registrationNumber'),
                validators.requiredNonEmptyString('ownerIdType'),
                validators.requiredString('ownerId')
            )
        )
    );

    return (
        <Formik<TradeInFormValues>
            initialValues={defaultValues}
            onSubmit={onSubmit}
            validate={validation}
            enableReinitialize
        >
            {({ handleSubmit, values, errors }) => (
                <StyledForm id="salesOfferTradeIn" name="salesOfferTradeIn" onSubmitCapture={handleSubmit}>
                    <ContentItem>
                        <ContentItemTitle>
                            {t('launchpadSalesOfferDetails:sections.tradeinDetails.title')}
                        </ContentItemTitle>
                        {children}
                    </ContentItem>

                    <TradeInFormActions defaultValues={defaultValues} isUpdate={isUpdate} />
                </StyledForm>
            )}
        </Formik>
    );
};
export default TradeInForm;
