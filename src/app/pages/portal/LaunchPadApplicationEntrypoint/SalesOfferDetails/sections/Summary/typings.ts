import { ApplicationStatus, SalesOfferFeatureKind, SalesOfferFeatureStatus } from '../../../../../../api';

export type SummaryOffer = {
    kind: SalesOfferFeatureKind;
    displayName: string;
    status: SalesOfferFeatureStatus | ApplicationStatus | null;
    latestDate: Date;
    disabled?: boolean;
    isSelected?: boolean;
};

export type PreOfferForm = {
    offers: SummaryOffer[];
    selectAll: boolean;
};
