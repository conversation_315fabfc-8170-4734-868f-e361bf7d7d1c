import { ApolloError } from '@apollo/client';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { useSendSalesOfferMutation } from '../../../../../../api';
import { useLanguage } from '../../../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../../../themes/hooks';
import useHandleError from '../../../../../../utilities/useHandleError';
import { useLeadDetailsContext } from '../../../LeadDetails/LeadDetailsContext';
import { ButtonContainer, StyledForm } from '../../../LeadDetails/LeadDetailsInner';
import { ContentItem } from '../../../ui/ContentItem';
import { extractSalesOfferFromLaunchpadLead } from '../shared';
import PreOfferInnerForm from './PreOfferInnerForm';
import { usePreOffers } from './shared';
import type { PreOfferForm } from './typings';

const PreOffer = () => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const { Button, notification } = useThemeComponents();

    const preOffers = usePreOffers();
    const initialValues = {
        offers: preOffers,
        selectAll: false,
    };

    const { currentLanguageId } = useLanguage();
    const { endpoint } = useLeadDetailsContext();
    const [mutation] = useSendSalesOfferMutation();
    const { id } = extractSalesOfferFromLaunchpadLead();
    const submit = useHandleError(
        async (values: PreOfferForm) => {
            try {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.sendingPreOffer'),
                    duration: 0,
                    key: 'primary',
                });

                const featureKinds = values.offers.filter(o => !o.disabled && o.isSelected).map(o => o.kind);
                const { data } = await mutation({
                    variables: {
                        id,
                        endpointId: endpoint.id,
                        featureKinds,
                        languageId: currentLanguageId,
                    },
                });

                notification.destroy('primary');

                if (data) {
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.sentPreOfferSuccessful'),
                        key: 'secondary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            }
        },
        [id, mutation, notification, t, endpoint.id, currentLanguageId]
    );

    return (
        <Formik<PreOfferForm> initialValues={initialValues} onSubmit={submit} enableReinitialize>
            {({ handleSubmit, values }) => (
                <StyledForm id="summaryForm" name="summaryForm" onSubmitCapture={handleSubmit}>
                    <ContentItem>
                        <PreOfferInnerForm />
                        <ButtonContainer>
                            <Button
                                customV3Style={{
                                    height: '40px',
                                    padding: '6px 27px 0px 27px',
                                }}
                                disabled={!values.offers.some(o => !o.disabled && o.isSelected)}
                                form="summaryForm"
                                htmlType="submit"
                                type="primary"
                            >
                                {t('launchpadSalesOfferDetails:sections.summaryDetails.actions.send')}
                            </Button>
                        </ButtonContainer>
                    </ContentItem>
                </StyledForm>
            )}
        </Formik>
    );
};

export default PreOffer;
