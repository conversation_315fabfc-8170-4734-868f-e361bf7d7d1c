import { PText, PIcon } from '@porsche-design-system/components-react';
import { themeLightContrastLow } from '@porsche-design-system/components-react/styles';
import { Tag } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { SalesOfferFeatureKind } from '../../../../../../api';
import { useThemeComponents } from '../../../../../../themes/hooks';
import usePublic from '../../../../../../utilities/usePublic';
import { getStatusLabel, getStatusStyle } from './shared';
import { SummaryOffer } from './typings';

const SummaryRow = styled.div<{ isFirst?: boolean; isOffer?: boolean }>`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid ${themeLightContrastLow};
    ${({ isOffer }) => (isOffer ? `padding: 9px 0;` : 'padding: 13px 0;')}
    ${({ isFirst }) => (isFirst ? `border-top: 1px solid ${themeLightContrastLow};` : null)}
`;

const FirstColumn = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1 1 0;
    gap: 16px;
    @media (min-width: 620px) {
        flex-direction: row;
        align-items: center;
    }
`;

const FirstSubColumn = styled.div<{ isStatus?: boolean }>`
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1 1 0;
    ${({ isStatus }) => (isStatus ? `padding-left: 34px;` : null)}
`;

const LastColumn = styled.div`
    display: flex;
    flex-direction: row;
    min-width: 88px;
`;

const CheckboxFieldStyled = styled.div`
    .ant-form-item {
        margin-bottom: 0px;
        padding-left: 52px;
    }
`;

interface OfferRowProps {
    type: 'offer' | 'preOffer';
    item: SummaryOffer;
    index: number;
    onSend?: (kind: SalesOfferFeatureKind) => void;
}
const OfferRow = ({ type, item, onSend, index }: OfferRowProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails', 'applicationList']);
    const {
        Button,
        FormFields: { CheckboxField },
    } = useThemeComponents();
    const fileIcon = usePublic('file.svg');

    return (
        <SummaryRow isFirst={index === 0} isOffer={type === 'offer'}>
            <FirstColumn>
                <FirstSubColumn>
                    <PIcon size="xx-small" source={fileIcon} style={{ margin: '0 7px' }} />
                    <PText size="small">{item.displayName}</PText>
                </FirstSubColumn>
                {item.status && (
                    <FirstSubColumn isStatus>
                        <Tag
                            style={{
                                ...getStatusStyle(item.status),
                                padding: '4px 8px',
                                borderRadius: '4px',
                                margin: '0px',
                            }}
                        >
                            <PText size="x-small">
                                {getStatusLabel(item.status, t)} ({dayjs(item.latestDate).format('D MMM YYYY')})
                            </PText>
                        </Tag>
                    </FirstSubColumn>
                )}
            </FirstColumn>
            {type === 'offer' && (
                <LastColumn>
                    <Button
                        customV3Style={{
                            height: '40px',
                            padding: '6px 27px 0px 27px',
                        }}
                        disabled={item.disabled}
                        onClick={() => onSend?.(item.kind)}
                        type="primary"
                    >
                        {t('launchpadSalesOfferDetails:sections.summaryDetails.actions.send')}
                    </Button>
                </LastColumn>
            )}
            {type === 'preOffer' && (
                <LastColumn>
                    <CheckboxFieldStyled>
                        <CheckboxField disabled={item.disabled} name={`offers[${index}].isSelected`} />
                    </CheckboxFieldStyled>
                </LastColumn>
            )}
        </SummaryRow>
    );
};

export default OfferRow;
