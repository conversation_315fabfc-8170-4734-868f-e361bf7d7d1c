import { PText } from '@porsche-design-system/components-react';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useFormikContext } from 'formik';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { ContentItemTitle } from '../../../ui/ContentItem';
import OfferRow from './OfferRow';
import type { PreOfferForm } from './typings';

const TitleRow = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .ant-form-item {
        margin-bottom: 0px;
    }
`;

const TitleCheckbox = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
`;

const PreOfferInnerForm = () => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const {
        FormFields: { CheckboxField },
    } = useThemeComponents();

    const { values, setFieldValue } = useFormikContext<PreOfferForm>();
    const [isIndeterminate, setIsIndeterminate] = useState(true);

    // we only handle the indeterminate and select all state when the offer is enabled
    useEffect(() => {
        const enabledOffers = values.offers.filter(i => !i.disabled);
        if (!enabledOffers.every(o => o.isSelected) && enabledOffers.some(o => o.isSelected)) {
            setIsIndeterminate(true);
        } else {
            setIsIndeterminate(false);
        }

        if (enabledOffers.every(o => o.isSelected)) {
            setFieldValue('selectAll', true);
        } else if (enabledOffers.every(o => !o.isSelected)) {
            setFieldValue('selectAll', false);
        }
    }, [setFieldValue, values.offers]);

    const onSelectAllChange = useCallback(
        (e: CheckboxChangeEvent) => {
            const isSelected = isIndeterminate ? true : e.target.checked;
            values.offers.forEach((offer, index) => {
                if (!offer.disabled) {
                    setFieldValue(`offers[${index}].isSelected`, isSelected);
                }
            });
        },
        [isIndeterminate, setFieldValue, values.offers]
    );

    return (
        <>
            <TitleRow>
                <ContentItemTitle>
                    {t('launchpadSalesOfferDetails:sections.summaryDetails.preOfferTitle')}
                </ContentItemTitle>
                <TitleCheckbox>
                    <PText size="small">{t('launchpadSalesOfferDetails:sections.summaryDetails.selectAll')}</PText>
                    <CheckboxField
                        disabled={values.offers.every(o => o.disabled)}
                        indeterminate={isIndeterminate}
                        name="selectAll"
                        onChange={onSelectAllChange}
                    />
                </TitleCheckbox>
            </TitleRow>
            <div>
                {values.offers.map((item, index) => (
                    <OfferRow key={item.kind} index={index} item={item} type="preOffer" />
                ))}
            </div>
        </>
    );
};

export default PreOfferInnerForm;
