import { useApolloClient } from '@apollo/client';
import { ApolloError } from 'apollo-server-core';
import { FormikHelpers, FormikProps } from 'formik';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import {
    CreateSalesOfferDocument,
    type CreateSalesOfferMutation,
    type CreateSalesOfferMutationVariables,
} from '../../../../api/mutations/createSalesOffer';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import SalesOfferForm, { SalesOfferModalFormikValues } from './SalesOfferForm';

type SalesOfferModalProps = {
    onClose: () => void;
    visible: boolean;
    salesOfferModuleId: string;
    leadSuiteId: string;
    refetchLead?: () => void;
};

const SalesOfferModal = ({ onClose, visible, salesOfferModuleId, leadSuiteId, refetchLead }: SalesOfferModalProps) => {
    const apolloClient = useApolloClient();
    const { t } = useTranslation(['salesOfferDetails']);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const formRef = useRef<FormikProps<SalesOfferModalFormikValues>>(null);
    const navigate = useNavigate();
    const { Modal, Button, notification } = useThemeComponents();

    const handleClose = useCallback(() => {
        formRef.current?.resetForm();
        onClose?.();
    }, [onClose]);

    const handleSubmit = useHandleError(
        async (values: SalesOfferModalFormikValues, { resetForm }: FormikHelpers<SalesOfferModalFormikValues>) => {
            try {
                setIsSubmitting(true);
                notification.loading({
                    content: t('salesOfferDetails:modal.messages.submitting'),
                    duration: 0,
                    key: 'primary',
                });

                const { data, errors } = await apolloClient.mutate<
                    CreateSalesOfferMutation,
                    CreateSalesOfferMutationVariables
                >({
                    mutation: CreateSalesOfferDocument,
                    variables: {
                        porscheCode: values.porscheCode,
                        salesOfferModuleId,
                        leadSuiteId,
                    },
                });

                if (!errors) {
                    notification.success({
                        content: t('salesOfferDetails:modal.messages.success'),
                        key: 'primary',
                    });
                    resetForm();
                    refetchLead?.();
                    onClose();
                }

                // Redirect to the sales offer details page once success
                navigate(`/leads/${leadSuiteId}/salesOffer/${data.createSalesOffer.id}`);
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error({ content: error.graphQLErrors[0].message });
                } else {
                    console.error(error);
                }
            } finally {
                notification.destroy('primary');
                setIsSubmitting(false);
            }
        },
        [notification, t, apolloClient, salesOfferModuleId, leadSuiteId, navigate, onClose, refetchLead]
    );

    const footerButton = useMemo(
        () => [
            <Button
                key="submit"
                disabled={isSubmitting}
                form="createSalesOfferModalForm"
                htmlType="submit"
                loading={isSubmitting}
                type="primary"
                block
            >
                {t('salesOfferDetails:modal.buttons.retrieve')}
            </Button>,
            <Button key="cancel" disabled={isSubmitting} onClick={handleClose} type="tertiary" block>
                {t('salesOfferDetails:modal.buttons.cancel')}
            </Button>,
        ],
        [Button, handleClose, isSubmitting, t]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButton}
            onCancel={onClose}
            open={visible}
            title={t('salesOfferDetails:modal.title')}
            centered
            destroyOnClose
        >
            <SalesOfferForm formRef={formRef} onSubmit={handleSubmit} />
        </Modal>
    );
};

export default SalesOfferModal;
