import { Row, Col } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useMeasure } from 'react-use';
import { LaunchPadModuleSpecsFragment } from '../../../../api/fragments/LaunchPadModuleSpecs';
import { useNewContactModal } from '../../../../components/leads/newContactModal';
import ActionButton from '../ui/ActionButton';
import { ContentItem } from '../ui/ContentItem';
import useLaunchpadActionColSpan from '../utils/useLaunchpadActionColSpan';
import { useLaunchpadContext } from './LaunchpadContext';

type LaunchpadDashboardActionsProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    dealerId: string;
    countryCode: string;
    endpointId: string;
};

const LaunchpadDashboardActions = ({
    launchpadModule,
    dealerId,
    countryCode,
    endpointId,
}: LaunchpadDashboardActionsProps) => {
    const { t } = useTranslation(['launchpadLeadList']);
    const newContactModal = useNewContactModal({ launchpadModule, dealerId, countryCode, endpointId });
    const { permissions } = useLaunchpadContext();
    const [ref, { width }] = useMeasure<HTMLDivElement>();
    const colSpan = useLaunchpadActionColSpan(width);

    const hasAnyPermission = useMemo(() => Object.values(permissions).some(value => value), [permissions]);

    if (!hasAnyPermission) {
        return null;
    }

    return (
        <ContentItem>
            <Row ref={ref} gutter={[24, 24]}>
                {permissions.hasCreateContactPermission && (
                    <Col span={colSpan}>
                        <ActionButton
                            description={t('launchpadLeadList:actions.createContact.description')}
                            icon="search"
                            label={t('launchpadLeadList:actions.createContact.label')}
                            onClick={() => newContactModal.open()}
                        />
                    </Col>
                )}
                {
                    // TODO: to be added as the feature scale
                    /* {permissions.hasCreateFollowUpPermission && (
                        <Col {...colSpan}>
                            <ActionButton
                                icon="refresh"
                                {...t('launchpadLeadList:actions.leadsInProcess', { returnObjects: true })}
                            />
                        </Col>
                    )}
                    {permissions.hasCreateShowroomVisitPermission && (
                        <Col {...colSpan}>
                            <ActionButton
                                icon="card"
                                {...t('launchpadLeadList:actions.reservation', { returnObjects: true })}
                            />
                        </Col>
                    )}
                    {permissions.hasCreateTestDrivePermission && (
                        <Col {...colSpan}>
                            <ActionButton
                                icon="calendar"
                                {...t('launchpadLeadList:actions.testDriveBookings', { returnObjects: true })}
                            />
                        </Col>
                    )} */
                }
            </Row>
            {newContactModal.render()}
        </ContentItem>
    );
};

export default LaunchpadDashboardActions;
