import {
    headingXLargeStyle,
    textXSmallStyle,
    themeLightContrastMedium,
    spacingStaticMedium,
} from '@porsche-design-system/components-react/styles';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useListLeadsQuery, LeadSortingField, LeadFilteringRule, SortingOrder } from '../../../../api';
import { LaunchPadApplicationEntrypointContextDataFragment, LeadListDataFragment } from '../../../../api/fragments';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import { useListCIReducer } from '../../../shared/CIPage/useListCIReducer';
import ContentContainer from '../ui/ContentContainer';
import { LaunchpadContextProvider } from './LaunchpadContext';
import LaunchpadDashboardActions from './LaunchpadDashboardActions';
import LeadList from './LeadList';

const PageHeader = styled.h2({ ...headingXLargeStyle, margin: 0 });

const PageDescription = styled.p({ ...textXSmallStyle, color: themeLightContrastMedium, margin: 0 });

const Wrapper = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${spacingStaticMedium};
`;

type Props = {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment;
};

const LaunchpadDashboard = ({ endpoint }: Props) => {
    const { t } = useTranslation(['launchpadLeadList']);
    const company = useCompany();
    const { dealerId, dealerOptions } = useSingleDealerId();
    const [accumulatedLeads, setAccumulatedLeads] = useState<LeadListDataFragment[]>([]);
    const [state, dispatch] = useListCIReducer<LeadSortingField, LeadFilteringRule>({
        filter: { companyIds: [company.id], moduleIds: [endpoint.launchPadApplicationModule.id] },
        sort: { field: LeadSortingField.UpdatedAt, order: SortingOrder.Desc },
        page: 1,
        pageSize: 10,
    });

    const { filter } = state;

    const { data } = useListLeadsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: state.filter,
            pagination: { offset: (state.page - 1) * state.pageSize, limit: state.pageSize },
            sort: state.sort,
        },
        onCompleted: newData => {
            if (newData?.list?.items) {
                setAccumulatedLeads(prev => [...prev, ...newData.list.items]);
            }
        },
        skip: dealerOptions.length === 0,
    });

    useEffect(() => {
        setAccumulatedLeads([]);
        dispatch({
            type: 'setFilter',
            filteringRule: {
                ...filter,
                dealerIds: [dealerId],
            },
        });
    }, [dealerId, dispatch]);

    const onNextPage = useCallback(() => dispatch({ type: 'setPage', page: state.page + 1 }), [dispatch, state.page]);

    return (
        <LaunchpadContextProvider endpoint={endpoint}>
            <ContentContainer>
                <Wrapper>
                    <LaunchpadDashboardActions
                        countryCode={company.countryCode}
                        dealerId={dealerId}
                        endpointId={endpoint.id}
                        launchpadModule={endpoint.launchPadApplicationModule}
                    />
                    <div>
                        <PageHeader>{t('launchpadLeadList:title.latestContactAndLeads')}</PageHeader>
                        <PageDescription>
                            {t('launchpadLeadList:listCount', { count: data?.list?.count ?? 0 })}
                        </PageDescription>
                    </div>
                    <LeadList
                        leads={accumulatedLeads}
                        onNextPage={onNextPage}
                        total={data?.list?.count ?? 0}
                        showLeadType
                    />
                </Wrapper>
            </ContentContainer>
        </LaunchpadContextProvider>
    );
};

export default LaunchpadDashboard;
