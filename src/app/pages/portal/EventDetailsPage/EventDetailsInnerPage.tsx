import { DownloadOutlined } from '@ant-design/icons';
import { Formik } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import * as permissionKind from '../../../../shared/permissions';
import { EventDataFragment } from '../../../api/fragments/EventData';
import {
    ApplicationStage,
    ContentRefinementSourceAction,
    ContentRefinementSourceKind,
    LeadStageOption,
} from '../../../api/types';
import { useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import GlobalStateContextProvider from '../../../components/contexts/GenericStateOnPageContextManager';
import { ExportModalInput, useExportFormatModal } from '../../../components/fields/DownloadModal/ExportFormatModal';
import { usePasswordModal } from '../../../components/fields/DownloadModal/PasswordModal';
import Form from '../../../components/fields/Form';
import { useThemeComponents } from '../../../themes/hooks';
import { ExportFormat, exportEventsApplication, exportEventsLead } from '../../../utilities/export';
import hasPermissions from '../../../utilities/hasPermissions';
import EventDetailTabPanes from '../../admin/EventDetailsPage/EventDetailTabPanes';
import EventDetailsTabs from '../../admin/EventDetailsPage/EventDetailsTabs';
import { useEventFormValidator } from '../../admin/EventDetailsPage/EventForm/shared';
import { useEventInitialValues, useEventUpdateSubmission } from '../../admin/EventDetailsPage/utils';

export enum Tab {
    MainDetails = 'MainDetails',
    CustomerInformation = 'CustomerInformation',
    Reservations = 'Reservations',
    Leads = 'Leads',
    Appointments = 'Appointments',
}

export type EventDetailsInnerPageProps = {
    event: EventDataFragment;
    disabled?: boolean;
};

const EventDetailsInnerPage = ({ event, disabled = false }: EventDetailsInnerPageProps) => {
    const { t } = useTranslation(['eventDetails']);
    const { Button } = useThemeComponents();

    const [currentTab, setCurrentTab] = useState<Tab>(Tab.MainDetails);
    const [loadedAppointmentIds, setLoadedAppointmentIds] = useState<string[]>([]);
    const [selectedAppointments, setSelectedAppointments] = useState<string[]>([]);
    const [loadedReservationIds, setLoadedReservationIds] = useState<string[]>([]);
    const [loadedLeadIds, setLoadedLeadIds] = useState<string[]>([]);
    const [selectedReservations, setSelectedReservations] = useState<string[]>([]);
    const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
    const [onDownloadLoading, setOnDownloadLoading] = useState(false);

    const passwordModal = usePasswordModal();
    const exportFormatModal = useExportFormatModal({
        initialFormat: { format: ExportFormat.CapFormat },
        dateRangeVisible: true,
    });

    const { id: eventId, identifier } = event;

    const { token } = useAccountContext();

    const { BasicProPageWithHeader } = useThemeComponents();
    const refineContentParams = useMemo(
        () => ({
            hasPermissionToRefineContent: hasPermissions(event.permissions, [permissionKind.updateEvent]),
            source: {
                kind: ContentRefinementSourceKind.Event,
                action: ContentRefinementSourceAction.Update,
                identifier: eventId,
            },
        }),
        [event.permissions, eventId]
    );

    const navigate = useNavigate();

    const goBack = useCallback(() => {
        navigate('..');
    }, [navigate]);

    const { dealerIds } = useMultipleDealerIds();

    const company = useMemo(() => {
        if (event.module.__typename === 'EventApplicationModule') {
            return event.module.company;
        }

        throw new Error('Invalid module');
    }, [event]);

    const downloadHandler = useCallback(
        async ({ format, period }: ExportModalInput) => {
            setOnDownloadLoading(true);
            const getExportInfoByTab = (tab: Tab) => {
                switch (tab) {
                    case Tab.Reservations:
                        return { stage: ApplicationStage.Reservation, applicationIds: selectedReservations };

                    case Tab.Leads:
                        return { leadIds: selectedLeads, period };

                    case Tab.Appointments:
                        return { stage: ApplicationStage.Appointment, applicationIds: selectedAppointments };

                    default:
                        throw new Error('Invalid tab for downloading applications');
                }
            };

            const { stage, applicationIds, leadIds } = getExportInfoByTab(currentTab);

            const baseExportValues = {
                eventId,
                dealerIds,
                company,
                token,
                identifier,
            };

            const password =
                currentTab === Tab.Leads
                    ? await exportEventsLead({
                          ...baseExportValues,
                          leadIds,
                          stage: LeadStageOption.LeadAndContact,
                          format,
                          period,
                      })
                    : await exportEventsApplication({
                          ...baseExportValues,
                          applicationIds,
                          stage,
                      });

            setOnDownloadLoading(false);
            exportFormatModal.close();

            if (password && typeof password === 'string') {
                passwordModal.open(password);
            }
        },
        [
            currentTab,
            eventId,
            dealerIds,
            company,
            token,
            identifier,
            exportFormatModal,
            selectedReservations,
            selectedLeads,
            selectedAppointments,
            passwordModal,
        ]
    );

    const onDownloadApplicationClick = useCallback(() => {
        if (currentTab === Tab.Leads) {
            exportFormatModal.open();
        } else {
            downloadHandler({});
        }
    }, [currentTab, downloadHandler, exportFormatModal]);

    const extra = useMemo(() => {
        switch (currentTab) {
            case Tab.Reservations:
                return loadedReservationIds.length ? (
                    <Button
                        disabled={!selectedReservations.length}
                        icon={<DownloadOutlined />}
                        loading={onDownloadLoading}
                        onClick={onDownloadApplicationClick}
                        type="primary"
                    >
                        {t('eventDetails:actions.download')}
                    </Button>
                ) : null;

            case Tab.Leads:
                return loadedLeadIds.length ? (
                    <Button
                        icon={<DownloadOutlined />}
                        loading={onDownloadLoading}
                        onClick={onDownloadApplicationClick}
                        type="primary"
                    >
                        {t('eventDetails:actions.download')}
                    </Button>
                ) : null;

            case Tab.Appointments:
                return loadedAppointmentIds.length ? (
                    <Button
                        disabled={!selectedAppointments.length}
                        icon={<DownloadOutlined />}
                        loading={onDownloadLoading}
                        onClick={onDownloadApplicationClick}
                        type="primary"
                    >
                        {t('eventDetails:actions.download')}
                    </Button>
                ) : null;

            default:
                return null;
        }
    }, [
        Button,
        currentTab,
        loadedAppointmentIds.length,
        loadedLeadIds.length,
        loadedReservationIds.length,
        onDownloadApplicationClick,
        onDownloadLoading,
        selectedAppointments.length,
        selectedReservations.length,
        t,
    ]);

    const validate = useEventFormValidator(event.firstRouterPath);
    const onSubmit = useEventUpdateSubmission(event, goBack);
    const initialValues = useEventInitialValues(event);

    return (
        <GlobalStateContextProvider refineContent={refineContentParams}>
            {passwordModal.render()}
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="eventForm" name="eventForm" onSubmitCapture={handleSubmit}>
                        <BasicProPageWithHeader
                            extra={extra}
                            onBack={goBack}
                            style={{ background: '#fff', height: '100%' }}
                            title={event.displayName}
                        >
                            <EventDetailsTabs
                                activeTab={currentTab}
                                event={event}
                                pageType="Portal"
                                setActiveTab={setCurrentTab}
                            />
                            <EventDetailTabPanes
                                activeTab={currentTab}
                                disabled={disabled}
                                event={event}
                                goBack={goBack}
                                pageType="Portal"
                                setLoadedAppointmentIds={setLoadedAppointmentIds}
                                setLoadedLeadIds={setLoadedLeadIds}
                                setLoadedReservationIds={setLoadedReservationIds}
                                setSelectedAppointments={setSelectedAppointments}
                                setSelectedLeads={setSelectedLeads}
                                setSelectedReservations={setSelectedReservations}
                            />
                        </BasicProPageWithHeader>
                    </Form>
                )}
            </Formik>

            {exportFormatModal.render({
                title: t('eventDetails:modals.downloadApplication.title'),
                description: t('eventDetails:modals.downloadApplication.description'),
                onDownload: downloadHandler,
            })}
        </GlobalStateContextProvider>
    );
};

export default EventDetailsInnerPage;
