/* eslint-disable max-len */
import { isEmpty } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useParams } from 'react-router';
import {
    ApplicationStage,
    LeadStageOption,
    useGetLeadQuery,
    useGetReferenceApplicationListEndpointsQuery,
} from '../../../../api';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { LeadDetailsExtraContext } from '../../../../components/contexts/LeadDetailsExtraContext';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import LeadForm from '../../../admin/LeadDetailsPage/LeadForm';
import { LeadFormApplication } from '../../../admin/LeadDetailsPage/shared';
import {
    eventPortalPath,
    ReferenceApplicationProvider,
    ReferencePathPrefixes,
} from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import NotFoundPage from '../../NotFoundPage';

type PortalLeadDetailsInnerProps = {
    lead: LeadFormApplication;
};

const PortalLeadDetailsInner = ({ lead }: PortalLeadDetailsInnerProps) => {
    const router = useRouter(true);

    const { data, loading } = useGetReferenceApplicationListEndpointsQuery({
        variables: {
            filter: {
                applicationModuleId: lead.moduleId,
                stage: ApplicationStage.Lead,
                routerId: router.id,
            },
        },
    });

    const getEventEndpoint = useMemo(() => {
        const prefix = router.endpoints.find(
            item =>
                item.__typename === 'EventApplicationEntrypoint' &&
                lead.__typename === 'EventLead' &&
                item.eventApplicationModule?.id === lead.moduleId
        )?.pathname;

        return eventPortalPath(prefix);
    }, [router.endpoints, lead]);

    const leadEndpoint = useMemo(
        () =>
            router?.endpoints?.find(
                item =>
                    item.__typename === 'LeadListEndpoint' &&
                    [LeadStageOption.Lead, LeadStageOption.LeadAndContact].includes(item.leadStage)
            )?.pathname,
        [router.endpoints]
    );

    const contactEndpoint = useMemo(
        () =>
            router?.endpoints?.find(
                item =>
                    item.__typename === 'LeadListEndpoint' &&
                    [LeadStageOption.Contact, LeadStageOption.LeadAndContact].includes(item.leadStage)
            )?.pathname,
        [router.endpoints]
    );

    const referencePaths = useMemo((): ReferencePathPrefixes => {
        if (!data?.endpoints) {
            return {};
        }

        const stages = data.endpoints.reduce((acc, endpoint) => {
            if (!acc[endpoint.applicationStage]) {
                return {
                    ...acc,
                    [endpoint.applicationStage]: [endpoint.pathname],
                };
            }

            return acc;
        }, {});

        return {
            stages: {
                ...stages,
                [ApplicationStage.Lead]: leadEndpoint,
                Contact: contactEndpoint,
            },
            references: {
                getEventDetail: getEventEndpoint,
            },
        } as ReferencePathPrefixes;
    }, [contactEndpoint, data?.endpoints, getEventEndpoint, leadEndpoint]);

    if (loading) {
        return <PortalLoadingElement />;
    }

    return (
        <ReferenceApplicationProvider referencePaths={referencePaths}>
            <LeadForm lead={lead} />
        </ReferenceApplicationProvider>
    );
};

const OldLeadDetails = () => {
    const { PageWithHeader } = useThemeComponents();
    const { id } = useParams<{ id: string }>();

    const { data, loading, refetch } = useGetLeadQuery({ variables: { id }, fetchPolicy: 'cache-and-network' });
    const lead = data?.lead;

    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useTranslation('applicationDetails');

    const extra = useMemo(() => ({ lead, refetch, forCI: true }), [lead, refetch]);

    if (loading) {
        return <PortalLoadingElement />;
    }

    // third checker is to user attempt visiting application from different endpoint type
    if (isEmpty(lead)) {
        return <NotFoundPage />;
    }

    const backNavigation = location?.state?.previousEndpoint ? location.state.previousEndpoint : '..';

    return (
        <LeadDetailsExtraContext.Provider value={extra}>
            <PageWithHeader
                onBack={() => navigate(backNavigation)}
                title={t(`applicationDetails:titles.${lead.isLead ? 'lead' : 'contact'}`, { id: lead?.identifier })}
            >
                <PortalLeadDetailsInner lead={lead} />
            </PageWithHeader>
        </LeadDetailsExtraContext.Provider>
    );
};

export default OldLeadDetails;
