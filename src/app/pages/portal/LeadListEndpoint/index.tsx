import { Route, Routes } from 'react-router-dom';
import { LeadListEndpointContextDataFragment } from '../../../api/fragments/LeadListEndpointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import withPrivateAccess from '../../../utilities/withPrivateAccess';
import MetaData from '../../shared/MetaData';
import NotFoundPage from '../NotFoundPage';
import LeadDetailsEntrypointPage from './LeadDetailsPage';
import LeadListPage from './LeadListPage';

export type LeadListEndpointProps = {
    endpoint: LeadListEndpointContextDataFragment;
};

const LeadListEndpoint = ({ endpoint }: LeadListEndpointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.title}`} />
            <Routes>
                <Route key={`lead-${endpoint.id}`} element={<LeadListPage endpoint={endpoint} />} path="" />
                <Route
                    key={`leadDetails-${endpoint.id}`}
                    element={<LeadDetailsEntrypointPage endpoint={endpoint} />}
                    path=":id"
                />
                <Route key="404" element={<NotFoundPage />} path="*" />
            </Routes>
        </>
    );
};

export default withPrivateAccess(LeadListEndpoint);
