import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { MobilityApplicationState } from '../Journey/shared';
import { useMobilityBookingLocation } from '../helper';

export type LocationInfoProps = {
    application: MobilityApplicationState;
};

const Container = styled.div`
    margin-top: 24px;
`;

const Title = styled.div`
    font-size: 20px;
    font-weight: bold;
`;

const LocationContent = styled.div`
    font-size: 16px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
`;

const LocationInfo = ({ application }: LocationInfoProps) => {
    const { t } = useTranslation(['mobilityOrderSummary', 'mobilityVehicleDetails']);

    const locationInfo = useMobilityBookingLocation(application);

    if (application.mobilityBookingDetails.location.__typename === 'MobilityBookingLocationHome') {
        return null;
    }

    return (
        <Container>
            <Title>{locationInfo.name}</Title>
            <LocationContent>{locationInfo.address}</LocationContent>
            <LocationContent>
                <div>
                    {t('mobilityOrderSummary:locationInfo.phone', {
                        value: locationInfo.phone,
                    })}
                </div>
                <div>
                    {t('mobilityOrderSummary:locationInfo.email', {
                        email: locationInfo.email,
                    })}
                </div>
            </LocationContent>
        </Container>
    );
};

export default LocationInfo;
