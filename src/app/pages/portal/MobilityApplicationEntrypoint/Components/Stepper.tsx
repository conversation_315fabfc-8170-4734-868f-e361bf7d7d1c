import type { TFunction } from 'i18next';
import { type Dispatch, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled, { css } from 'styled-components';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import type { StepsItemProps } from '../../../../themes/types';
import breakpoints from '../../../../utilities/breakpoints';
import getExpirationDuration from '../../../../utilities/journeys/getExpirationDuration';
import type { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import { usePersistentData, usePersistentDataListener } from '../../../../utilities/usePersistData';
import { type Action, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { GiftVoucherAction } from '../GiftCodeJourney/reducer';
import {
    type MobilityJourneyTemporaryValueType,
    mobilityJourneyTemporaryKey,
    retrieveMobilityJourneyValue,
} from '../helper';

export enum AvailableSteps {
    VehicleSelection = 'vehicleSelection',
    VoucherDetails = 'voucherDetails',
    BookingDetails = 'bookingDetails',
    CustomerDetails = 'customerDetails',
    Signature = 'signature',
    Payment = 'payment',
    Confirmation = 'confirmation',
}

export enum MobilityPageKind {
    Mobility = 'mobility',
    Gift = 'gift',
}

export type StepperProps = {
    currentPage: MobilityPageKind;
    currentStep: AvailableSteps;
    dispatchMobility?: Dispatch<Action<AllowedApplicationForPayment>>;
    dispatchGift?: Dispatch<GiftVoucherAction>;
};

type StepperItemProps = StepsItemProps & {
    value: AvailableSteps;
};

const Container = styled.div<{ currentStep: AvailableSteps }>`
    margin: 0px 0px 24px 0px;
    ${({ currentStep }) =>
        currentStep === AvailableSteps.VehicleSelection &&
        css`
            margin: 15px 36px 15px 36px;
        `}

    ${({ currentStep }) =>
        (currentStep === AvailableSteps.Payment || currentStep === AvailableSteps.Confirmation) &&
        css`
            margin: 0px 0px 36px 0px;
        `}

    ${({ currentStep }) =>
        currentStep === AvailableSteps.Confirmation &&
        css`
            @media screen and (min-width: ${breakpoints.md}) {
                display: flex;
                justify-content: center;
            }
        `}
`;

const useGetStepsLabel = (currentPage: MobilityPageKind, t: TFunction) =>
    useMemo(() => {
        const mobilityStepsLabel = [
            {
                label: t('mobilityOrderSummary:steps.vehicleSelection'),
                value: AvailableSteps.VehicleSelection,
                visible: true,
            },
            {
                label: t('mobilityOrderSummary:steps.bookingDetails'),
                value: AvailableSteps.BookingDetails,
                visible: true,
            },
            {
                label: t('mobilityOrderSummary:steps.customerDetails'),
                value: AvailableSteps.CustomerDetails,
                visible: true,
            },
            {
                label: t('mobilityOrderSummary:steps.signature'),
                value: AvailableSteps.Signature,
                // eslint-disable-next-line max-len
                visible: false, // We can set this as false for now because currently in mobility it doesn't have the signing
            },
            {
                label: t('mobilityOrderSummary:steps.payment'),
                value: AvailableSteps.Payment,
                visible: true,
            },
            {
                label: t('mobilityOrderSummary:steps.confirmation'),
                value: AvailableSteps.Confirmation,
                visible: true,
            },
        ];

        const giftStepsLabel = [
            {
                label: t('giftVoucherJourney:steps.vehicleSelection'),
                value: AvailableSteps.VehicleSelection,
                visible: true,
            },
            {
                label: t('giftVoucherJourney:steps.voucherDetails'),
                value: AvailableSteps.VoucherDetails,
                visible: true,
            },
            {
                label: t('giftVoucherJourney:steps.payment'),
                value: AvailableSteps.Payment,
                visible: true,
            },
            {
                label: t('giftVoucherJourney:steps.confirmation'),
                value: AvailableSteps.Confirmation,
                visible: true,
            },
        ];

        return currentPage === MobilityPageKind.Mobility ? mobilityStepsLabel : giftStepsLabel;
    }, [currentPage, t]);

const Stepper = ({ currentPage, currentStep, dispatchMobility, dispatchGift }: StepperProps) => {
    const { t } = useTranslation(['mobilityOrderSummary', 'giftVoucherJourney']);

    const navigate = useNavigate();
    const { setItem } = usePersistentData();
    const { sessionTimeout } = useCompany();

    const temporaryValue =
        currentPage === MobilityPageKind.Mobility
            ? usePersistentDataListener<MobilityJourneyTemporaryValueType>(mobilityJourneyTemporaryKey)
            : null;

    const { Steps } = useThemeComponents();

    const stepsLabel = useGetStepsLabel(currentPage, t);

    const [currentStepIndex, stepList] = useMemo(() => {
        const availableSteps = stepsLabel.filter(step => step.visible);
        const currentStepIndex = availableSteps.findIndex(step => step.value === currentStep);
        const stepState: StepperItemProps[] = [];
        availableSteps.forEach((step, index) => {
            if (index < currentStepIndex) {
                stepState.push({ index, title: step.label, value: step.value });
            } else if (index === currentStepIndex) {
                stepState.push({ index, title: step.label, value: step.value });
            } else {
                stepState.push({ index, title: step.label, value: step.value });
            }
        });

        return [currentStepIndex, stepState];
    }, [currentStep, stepsLabel]);

    const redirectToStep = useCallback(
        (stepIndex: number) => {
            if (temporaryValue) {
                const { sessionId, value } = retrieveMobilityJourneyValue(temporaryValue);

                setItem(
                    mobilityJourneyTemporaryKey,
                    value,
                    temporaryValue.expireAt ? getExpirationDuration(temporaryValue.expireAt) : sessionTimeout * 60,
                    sessionId
                );
            }
            const stepValue = stepList.find(step => step.index === stepIndex);
            switch (stepValue.value) {
                case AvailableSteps.VehicleSelection:
                    navigate('..');
                    break;

                case AvailableSteps.BookingDetails:
                    navigate(`../details/${temporaryValue?.bookingDetails?.inventoryStockId}`);
                    break;

                case AvailableSteps.VoucherDetails:
                    dispatchGift({ type: 'goTo', stage: JourneyStage.ApplicantKYC });
                    break;

                case AvailableSteps.CustomerDetails:
                    dispatchMobility({ type: 'goTo', stage: JourneyStage.ApplicantKYC });
                    break;

                case AvailableSteps.Signature:
                case AvailableSteps.Payment:
                case AvailableSteps.Confirmation:
                    // We can ignore this steps for now user not able to go to this steps directly from stepper
                    break;
            }
        },
        [dispatchGift, dispatchMobility, navigate, sessionTimeout, setItem, stepList, temporaryValue]
    );

    return (
        <Container currentStep={currentStep}>
            <Steps
                current={currentStepIndex}
                items={stepList}
                stepRedirectOnClick={redirectToStep}
                disableAtFinalStep
            />
        </Container>
    );
};

export default Stepper;
