/* eslint-disable max-len */
import { isNil } from 'lodash/fp';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { ApplicationStage } from '../../../../api/types';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getApplicationIdentifier } from '../../../../utilities/application';
import { usePersistentData } from '../../../../utilities/usePersistData';
import MobilityContent from '../../../shared/ThankYou/MobilityContent';
import LiveChat from '../../MobilityWebpageEndpoint/Webpage/LiveChat';
import type { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { mobilityKYCPersistKey } from '../ApplicantKycPage';
import Stepper, { AvailableSteps as MobilitySteps, MobilityPageKind } from '../Components/Stepper';
import { mobilityGuarantorKYCKey } from '../GuarantorKycPage';
import type { MobilityApplicationState } from '../Journey/shared';
import { mobilityJourneyTemporaryKey } from '../helper';

export type ThankYouPageProps = {
    state: State<MobilityApplicationState>;
    endpoint: MobilityApplicationEntrypointContextDataFragment;
    isAmend?: boolean;
    isCancel?: boolean;
};

const ThankYouPage = ({ state, endpoint, isAmend, isCancel }: ThankYouPageProps) => {
    const { t } = useTranslation(['mobilityThankYou']);
    const { StandardLayout } = useThemeComponents();

    const { deleteItem } = usePersistentData();

    useEffect(() => {
        deleteItem(mobilityKYCPersistKey);
        deleteItem(mobilityGuarantorKYCKey);
        deleteItem(mobilityJourneyTemporaryKey);
    }, [deleteItem]);

    const identifier = useMemo(
        () => getApplicationIdentifier(state.application, [ApplicationStage.Mobility]),
        [state.application]
    );

    const thankYouContent = useMemo(() => {
        const title = isCancel ? t('mobilityThankYou:titles.cancel') : t('mobilityThankYou:titles.default');
        const subTitle = isCancel
            ? t('mobilityThankYou:subTitles.cancelSuccessful')
            : t(`mobilityThankYou:subTitles.${isAmend ? 'amendSuccessful' : 'reserveSuccessful'}`);
        const description = isCancel
            ? t('mobilityThankYou:descriptions.cancelSuccessful')
            : t('mobilityThankYou:descriptions.reserveSuccessful');
        const externalModelInfoBaseUrl = endpoint.mobilityApplicationModule.externalModelInfo
            ? endpoint.mobilityApplicationModule.baseUrl
            : null;
        const reference = isCancel ? undefined : t('mobilityThankYou:reference', { reference: identifier });

        return { title, subTitle, description, application: state.application, externalModelInfoBaseUrl, reference };
    }, [isCancel, isAmend, state.application, endpoint.mobilityApplicationModule, t, identifier]);

    return (
        <StandardLayout title={thankYouContent.title}>
            <BasicProLayoutContainer>
                <Stepper currentPage={MobilityPageKind.Mobility} currentStep={MobilitySteps.Confirmation} />
                <MobilityContent {...thankYouContent} />

                {!isNil(endpoint.mobilityApplicationModule.liveChatSetting) && (
                    <LiveChat
                        chatSetting={
                            endpoint.mobilityApplicationModule.liveChatSetting.__typename ===
                                'UserlikeChatbotSetting' && endpoint.mobilityApplicationModule.liveChatSetting
                        }
                    />
                )}
            </BasicProLayoutContainer>
        </StandardLayout>
    );
};

export default ThankYouPage;
