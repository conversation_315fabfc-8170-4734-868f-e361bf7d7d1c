/* eslint-disable max-len */
import Icon from '@ant-design/icons';
import { Col, Row, Typography } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { get, isEmpty, isNil, isObject, keys } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import { CompanyTheme, CustomerKind } from '../../../../api/types';
import FormAutoTouch, { toPath } from '../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../components/ScrollToTop';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import Form from '../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
import getExpirationDuration from '../../../../utilities/journeys/getExpirationDuration';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import renderMarkdown from '../../../../utilities/renderMarkdown';
import useHandleError from '../../../../utilities/useHandleError';
import { usePersistentData, usePersistentDataListener } from '../../../../utilities/usePersistData';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { getApplicantAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import ResetKYCButtonPorsche from '../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useRefineKycPresets from '../../../shared/useRefineKycPresets';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import { StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import Stepper, { MobilityPageKind, AvailableSteps as MobilitySteps } from '../Components/Stepper';
import VehicleInfo from '../Components/VehicleInfo';
import {
    MobilityJourneyTemporaryValueType,
    mobilityJourneyTemporaryKey,
    retrieveMobilityJourneyValue,
} from '../helper';
import ConsentsAndDeclarations from './ConsentsAndDeclarations';
import CustomerDetails from './CustomerDetails';
import { ApplicantKYCInnerProps, ApplicantKYCProps, FeatureKYCPageLabel, MobilityKYCJourneyValues } from './shared';
import BackIcon from '../../../../icons/mobility/back.svg';

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

const StyledMarkdown = styled.div`
    margin-top: 20px;
`;

const ApplicantKycContainer = styled.div`
    @media screen and (max-width: ${breakpoints.md}) {
        & .ant-pro-footer-bar {
            .ant-pro-footer-bar-right {
                width: 100%;

                .ant-btn {
                    width: auto;
                }
            }
        }
    }
`;

export const mobilityKYCPersistKey = 'MobilityApplicant';

const ApplicantKYC = ({ dispatch, state, endpoint, handleEndSession }: ApplicantKYCProps) => {
    const { token, application } = state;
    const { applicantKYC, corporateKYC, applicantAgreements, corporateAgreements, applicant, module, documents } =
        application;
    if (module.__typename !== 'MobilityModule') {
        throw new Error('ModuleType not supported');
    }
    const { t } = useTranslation('customerDetails');

    const { setItem, getItem } = usePersistentData();
    const { sessionTimeout } = useCompany();

    const mobilityJourneyTemporaryValue =
        usePersistentDataListener<MobilityJourneyTemporaryValueType>(mobilityJourneyTemporaryKey);
    const { notification } = useThemeComponents();

    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();

    const [isCorporate, setIsCorporate] = useState<boolean>(false);
    const [prefill, setPrefill] = useState<boolean>(false);

    const isEditableField = useMemo(() => (applicant ? applicant.fields : []), [applicant]);
    const agreementsKYC = (isCorporate ? corporateAgreements : applicantAgreements).map(agreement => ({
        ...agreement,
        isAgreed: false,
    }));

    const applicantKycAgreements = useMemo(() => getApplicantAgreements(agreementsKYC), [agreementsKYC]);
    const agreementsValidator = useAgreementsValidator(applicantKycAgreements, 'agreements');
    const agreements = useAgreementsValues(applicantKycAgreements);

    const applicantKycFields = useMemo(
        () => getApplicantKyc(isCorporate ? corporateKYC : applicantKYC),
        [applicantKYC, corporateKYC, isCorporate]
    );

    const kycPresets = useRefineKycPresets(applicantKycFields);
    const kycExtraSettings = useMemo(
        () =>
            endpoint?.mobilityApplicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.mobilityApplicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const applicants = useMemo(() => {
        if (endpoint.mobilityApplicationModule.persistKYCData) {
            const sessionStorageValues =
                getItem<MobilityKYCJourneyValues>(mobilityKYCPersistKey)?.customer?.fields || {};
            if (get('UAEDrivingLicense.value[0].uploadDLCopy', sessionStorageValues)) {
                sessionStorageValues.UAEDrivingLicense.value[0].uploadDLCopy = null;
            }
            if (get('UAEDrivingLicense.value[0].uploadEIDPassportCopy', sessionStorageValues)) {
                sessionStorageValues.UAEDrivingLicense.value[0].uploadEIDPassportCopy = null;
            }

            return { fields: { ...getInitialValues(isEditableField, kycPresets, documents), ...sessionStorageValues } };
        }

        return { fields: { ...getInitialValues(isEditableField, kycPresets, documents) } };
    }, [documents, endpoint.mobilityApplicationModule.persistKYCData, getItem, isEditableField, kycPresets]);

    // Mobility booking does not have `proceed with customer`
    const applicantsValidator = useKYCFormValidator({
        field: kycPresets,
        extraSettings: kycExtraSettings,
        moduleCountryCode: module.company.countryCode,
        prefix: 'customer.fields',
        saveDraft: false,
    });

    const validations = useMemo(
        () => validators.compose(applicantsValidator, agreementsValidator),
        [agreementsValidator, applicantsValidator]
    );

    const validate = useValidator(validations, { prefill });

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const initialValues: MobilityKYCJourneyValues = useMemo(() => {
        if (isNil(getItem<MobilityKYCJourneyValues>(mobilityKYCPersistKey))) {
            return {
                agreements,
                customer: applicants,
                isCorporateCustomer: false,
                hasGuarantor: false,
                remarks: '',
                prefill: false,
            };
        }

        return { ...getItem<MobilityKYCJourneyValues>(mobilityKYCPersistKey), customer: applicants };
    }, [agreements, applicants, getItem]);

    const onSubmit = useHandleError(
        async (values: MobilityKYCJourneyValues & { remarks: string }) => {
            notification.loading({
                content: t('customerDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const submitAgreementKYC = await submitAgreements(
                state.token,
                values.agreements,
                isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                values.hasGuarantor
            );

            const [submitApplicantKYC] = await Promise.all([
                // Mobility booking does not have `proceed with customer`
                submitCustomerDetails({
                    token: submitAgreementKYC.token,
                    fields: values.customer.fields,
                    customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                    sameCorrespondenceAddress: values.prefill,
                }),
                updateApplicationFields(submitAgreementKYC.token, values.remarks),
            ]);

            notification.destroy('primary');
            // go to the journey
            if (
                submitApplicantKYC.__typename === 'GiftVoucherJourneyContext' ||
                submitApplicantKYC.application.__typename !== 'MobilityApplication'
            ) {
                throw new Error('unexpected type');
            }

            const expireAt = submitApplicantKYC.application.expireAt ? submitApplicantKYC.application.expireAt : null;

            const updatedTemporaryValue: MobilityJourneyTemporaryValueType = {
                ...mobilityJourneyTemporaryValue,
                kyc: values,
                token: submitApplicantKYC.token,
                expireAt,
            };

            const { sessionId, value } = retrieveMobilityJourneyValue(updatedTemporaryValue);
            setItem(
                mobilityJourneyTemporaryKey,
                value,
                expireAt ? getExpirationDuration(expireAt) : sessionTimeout * 60,
                sessionId
            );

            dispatch({
                type: 'next',
                token: submitApplicantKYC.token,
                application: submitApplicantKYC.application,
            });
        },
        [
            notification,
            t,
            submitAgreements,
            state.token,
            isCorporate,
            submitCustomerDetails,
            updateApplicationFields,
            dispatch,
            mobilityJourneyTemporaryValue,
            setItem,
            sessionTimeout,
        ],
        {},
        () => {
            notification.destroy('primary');
        }
    );

    const showResetKYCButton =
        application.module.__typename === 'MobilityModule' && application.module.showResetKYCButton;

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} validateOnMount>
            <Inner
                dispatch={dispatch}
                endpoint={endpoint}
                handleEndSession={handleEndSession}
                isCorporate={isCorporate}
                kycExtraSettings={kycExtraSettings}
                mobilityKycPreset={kycPresets}
                removeDocument={removeDocument}
                setIsCorporate={setIsCorporate}
                setPrefill={setPrefill}
                showResetButton={showResetKYCButton}
                showTabs={corporateKYC.length > 0}
                state={state}
                uploadDocument={uploadDocument}
            />
        </Formik>
    );
};

const Inner = ({
    state,
    kycExtraSettings,
    mobilityKycPreset: kycPresets,
    setIsCorporate,
    isCorporate,
    showTabs,
    setPrefill,
    endpoint,
    showResetButton = false,
    uploadDocument,
    removeDocument,
}: ApplicantKYCInnerProps) => {
    const { application } = state;
    const { applicantAgreements, module } = application;
    if (module.__typename !== 'MobilityModule') {
        throw new Error('ModuleType not supported');
    }
    const { t } = useTranslation('customerDetails');
    const {
        isSubmitting,
        handleSubmit,
        validateForm,
        submitForm,
        values,
        errors,
        setFieldTouched,
        resetForm,
        initialValues,
    } = useFormikContext<MobilityKYCJourneyValues>();
    const navigate = useNavigate();
    const { theme, Button, MobilityWebpageLayout } = useThemeComponents();
    const { layout } = useRouter();

    const { setItem, getItem } = usePersistentData();
    const mobilityJourneyTemporaryValue =
        usePersistentDataListener<MobilityJourneyTemporaryValueType>(mobilityJourneyTemporaryKey);
    const { sessionTimeout } = useCompany();

    const latestValues = useRef(values);
    const firstValidation = useRef(!isNil(getItem(mobilityKYCPersistKey)));
    latestValues.current = values;

    useEffect(() => {
        const saveData = () => {
            if (endpoint.mobilityApplicationModule.persistKYCData) {
                setItem(
                    mobilityKYCPersistKey,
                    latestValues.current,
                    sessionTimeout * 60,
                    mobilityJourneyTemporaryValue?.sessionId
                );
            }
        };

        window.addEventListener('beforeunload', saveData);

        return () => {
            saveData();
            window.removeEventListener('beforeunload', saveData);
        };
    }, [
        endpoint.mobilityApplicationModule.persistKYCData,
        sessionTimeout,
        setItem,
        latestValues,
        mobilityJourneyTemporaryValue?.sessionId,
    ]);

    useEffect(() => {
        const touchOnErrors = (fields, prefix: (string | number)[] = []) => {
            for (const key of keys(fields)) {
                const item = fields[key];

                if (isObject(item) || Array.isArray(item)) {
                    touchOnErrors(item, [...prefix, key]);
                }

                if (typeof item === 'string' || typeof item === 'number') {
                    const path = toPath([...prefix, key]);
                    setFieldTouched(path, true, false);
                }
            }
        };
        if (firstValidation.current) {
            touchOnErrors(errors);
            firstValidation.current = true;
        }
    }, [errors, setFieldTouched]);

    const translated = useTranslatedString();
    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const agreementsKYC = applicantAgreements.map(agreement => ({ ...agreement, isAgreed: false }));
    const applicantKycAgreements = useMemo(() => getApplicantAgreements(agreementsKYC), [agreementsKYC]);

    const title = (
        <Row style={{ width: '100%' }}>
            <Typography>{t('customerDetails:title')}</Typography>
        </Row>
    );

    const onBack = useCallback(() => {
        const { sessionId, value } = retrieveMobilityJourneyValue({
            ...mobilityJourneyTemporaryValue,
            kyc: latestValues.current,
        });

        setItem(
            mobilityJourneyTemporaryKey,
            value,
            mobilityJourneyTemporaryValue.expireAt
                ? getExpirationDuration(mobilityJourneyTemporaryValue.expireAt)
                : sessionTimeout * 60,
            sessionId
        );

        navigate(-1);
    }, [mobilityJourneyTemporaryValue, navigate, sessionTimeout, setItem]);

    const requirement = useMemo(() => {
        const overrideValue = module.rentalRequirement.overrides.find(
            requirement => requirement.dealerId === application.dealerId
        );

        if (!Array.isArray(overrideValue?.value)) {
            if (isEmpty(overrideValue?.value.defaultValue)) {
                if (isEmpty(module.rentalRequirement.defaultValue.defaultValue)) {
                    return '';
                }

                return translated(module.rentalRequirement.defaultValue);
            }

            return translated(overrideValue.value);
        }

        return module.rentalRequirement.defaultValue[0];
    }, [application.dealerId, module.rentalRequirement.defaultValue, module.rentalRequirement.overrides, translated]);

    const resetFormHandler = useCallback(async () => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    return (
        <ApplicantKycContainer>
            <ScrollToTop />
            <FormAutoTouch />
            <MobilityWebpageLayout
                backIcon={null} // To hide the back button in header
                preferredMobileFooterHeight={75}
                title={title}
                hasFooterBar
            >
                <BasicProLayoutContainer>
                    <Stepper currentPage={MobilityPageKind.Mobility} currentStep={MobilitySteps.CustomerDetails} />
                    <Form id="kycJourneyForm" name="kycJourneyForm" onSubmitCapture={handleSubmit}>
                        <Row gutter={[24, 50]}>
                            <Col {...leftColSpan}>
                                <Title size="large">{t('customerDetails:sectionTitles.mobilitySelectedCar')}</Title>
                                <VehicleInfo application={application} />
                                <StyledMarkdown>{renderMarkdown(requirement)}</StyledMarkdown>
                            </Col>
                            <Col {...rightColSpan}>
                                <Row gutter={[16, 16]}>
                                    <Col span={24}>
                                        <CustomerDetails
                                            customerKind={isCorporate ? CustomerKind.Corporate : CustomerKind.Local}
                                            hasGuarantorPreset={false}
                                            kycExtraSettings={kycExtraSettings}
                                            kycPresets={kycPresets}
                                            removeDocument={removeDocument}
                                            resetFormHandler={resetFormHandler}
                                            setIsCorporate={setIsCorporate}
                                            setPrefill={setPrefill}
                                            showTabs={showTabs}
                                            typeKYCPageTitle={FeatureKYCPageLabel.Default}
                                            uploadDocument={uploadDocument}
                                        />
                                    </Col>
                                    <Col span={24}>
                                        <ConsentsAndDeclarations applicationAgreements={applicantKycAgreements} />
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    </Form>
                </BasicProLayoutContainer>
                <StyledJourneyToolbar>
                    {showResetButton && (theme === CompanyTheme.Porsche || theme === CompanyTheme.PorscheV3) && (
                        <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                    )}
                    <Button
                        key="back"
                        form="bookingForm"
                        htmlType="button"
                        icon={<Icon className="porsche-arrow" component={BackIcon} />}
                        onClick={onBack}
                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="tertiary"
                    >
                        {t('customerDetails:previousButton')}
                    </Button>
                    <NextButton key="nextButton" disabled={isSubmitting} onSubmit={onSubmit} />
                </StyledJourneyToolbar>
            </MobilityWebpageLayout>
        </ApplicantKycContainer>
    );
};

export default ApplicantKYC;
