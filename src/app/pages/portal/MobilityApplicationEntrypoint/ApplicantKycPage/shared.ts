import { Dispatch, SetStateAction } from 'react';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../api/fragments/MobilityApplicationEntrypointContextData';
import type { LocalCustomerManagementModule } from '../../../../api/types';
import { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { MobilityApplicationState } from '../Journey/shared';

export type CustomerDetailsProps = {
    mobility: MobilityApplicationState;
    kycPresets: KycFieldSpecsFragment[];
    endpoint: MobilityApplicationEntrypointContextDataFragment;
    token: string;
    applicationAggrements: ApplicationAgreementDataFragment[];
};

export enum FeatureKYCPageLabel {
    GiftVoucher = 'giftVoucher',
    Default = 'default',
}

export type ApplicantKYCProps = {
    state?: State<MobilityApplicationState>;
    dispatch?: Dispatch<Action<MobilityApplicationState>>;
    endpoint: MobilityApplicationEntrypointContextDataFragment;
    handleEndSession?: () => void;
    mobilityKycPreset?: KycFieldSpecsFragment[];
    setIsCorporate?: Dispatch<SetStateAction<boolean>>;
    isCorporate?: boolean;
    showTabs: boolean;
    setPrefill?: Dispatch<SetStateAction<boolean>>;
};

export type ApplicantKYCInnerProps = ApplicantKYCProps & {
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    showResetButton?: boolean;
} & UploadDocumentProp;

export type MobilityApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type MobilityKYCJourneyValues = {
    agreements: AgreementValues;
    customer: MobilityApplicantFormValues;
    isCorporateCustomer: boolean;
    hasGuarantor: boolean;
    prefix?: string;
    prefill: boolean;
};
