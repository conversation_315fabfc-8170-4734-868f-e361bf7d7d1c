import { ApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { isEmpty, isNil, omit, range } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MobilityApplicationExpiredFragment } from '../../../api/fragments/MobilityApplicationExpired';
import { PeriodDataFragment } from '../../../api/fragments/PeriodData';
import { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';
import {
    ExtendMobilityStockExpiryDocument,
    ExtendMobilityStockExpiryMutation,
    ExtendMobilityStockExpiryMutationVariables,
} from '../../../api/mutations/extendMobilityStockExpiry';
import { DateTimeUnit, DateUnit, DateUnitInput, DayOfWeek, PeriodPayload } from '../../../api/types';
import getExpirationDuration from '../../../utilities/journeys/getExpirationDuration';
import { MobilityKYCJourneyValues } from './ApplicantKycPage/shared';
import {
    blockerFromStartDateForEndDateOptions,
    calculateBookingEntireDay,
    computeClosestMaxDate,
    defaultDisabledHoursAndMinutesOfTheDay,
    getDisabledHoursAndMinutesOfDate,
} from './CarDetailsPage/BookingForm/RentalFields/shared';
import DatePickerType from './shared';

export type MobilityApplicationStateWithExpired = Extract<
    MobilityApplicationExpiredFragment,
    {
        __typename: 'MobilityApplication';
    }
>;

const getRentalPrice = <T extends MobilityApplicationStateWithExpired>(detail: T['mobilityBookingDetails']) => {
    const start = dayjs(detail.period.start).startOf('minute');
    const end = dayjs(detail.period.end).startOf('minute');

    if (!start.isValid() || !end.isValid()) {
        return null;
    }

    // by default anyhow should be 1 day rental price
    const days = !start.isSame(end, 'day') ? Math.ceil(end.diff(start, 'day', true)) : 1;

    return detail.inventoryStockPrice * days;
};

const getMobilitiesPrice = <T extends MobilityApplicationStateWithExpired>(snapshots: T['mobilitySnapshots']) =>
    snapshots.map(snapshot => {
        switch (snapshot.__typename) {
            case 'MobilityAddonSnapshot':
                return snapshot.option.price;

            default:
                return null;
        }
    });

export const calculateTotalPrice = <T extends MobilityApplicationStateWithExpired>(application: T) => {
    const amounts = [
        getRentalPrice(application.mobilityBookingDetails),
        ...getMobilitiesPrice(application.mobilitySnapshots),
    ].filter(Boolean);

    return amounts.reduce<number>((total, value) => total + (value || 0), 0);
};

export const getUnavailableDayofWeek = (unavailableDayofWeek: DayOfWeek[]): number[] =>
    unavailableDayofWeek.map(day => {
        switch (day) {
            case DayOfWeek.Sunday:
                return 0;

            case DayOfWeek.Monday:
                return 1;

            case DayOfWeek.Tuesday:
                return 2;

            case DayOfWeek.Wednesday:
                return 3;

            case DayOfWeek.Thursday:
                return 4;

            case DayOfWeek.Friday:
                return 5;

            case DayOfWeek.Saturday:
                return 6;

            default:
                throw new Error('DayOfWeek not support');
        }
    });

export const getAvailableDateRange = (
    availableBookingRange: DateUnitInput,
    start?: string | Date | dayjs.Dayjs,
    end?: string | Date | dayjs.Dayjs
) => {
    let lastAvailableBookingDate = null;
    // in `disabledDate` API, it does not able to use `type` suchlike `disabledTime` feature
    let datepickerType: DatePickerType = null;
    // calculate the further available date based on the available booking range ( TO furthur dates )
    // we need to minus 1 day which is consider selected (start date or end date as first day of rental)
    if (!isNil(start) && isNil(end)) {
        if (availableBookingRange.unit === DateTimeUnit.Days) {
            if (availableBookingRange.value > 0) {
                datepickerType = DatePickerType.Start;
                lastAvailableBookingDate = dayjs(start).add(availableBookingRange.value - 1, 'day');
            }
        }

        if (availableBookingRange.unit === DateTimeUnit.Hours) {
            if (availableBookingRange.value > 0) {
                datepickerType = DatePickerType.Start;
                const days = Math.floor(availableBookingRange.value / 24);
                lastAvailableBookingDate = dayjs(start).add(days, 'day');
            }
        }
    }
    // calculate the earlier available date based on the available booking range ( TO earlier dates )
    // we need to minus 1 day which is consider selected (start date or end date as first day of rental)
    else if (isNil(start) && !isNil(end)) {
        if (availableBookingRange.unit === DateTimeUnit.Days) {
            if (availableBookingRange.value > 0) {
                datepickerType = DatePickerType.End;
                lastAvailableBookingDate = dayjs(end).subtract(availableBookingRange.value + 1, 'day');
            }
        }

        if (availableBookingRange.unit === DateTimeUnit.Hours) {
            if (availableBookingRange.value > 0) {
                datepickerType = DatePickerType.End;
                const days = Math.floor(availableBookingRange.value / 24);
                lastAvailableBookingDate = dayjs(end).subtract(days, 'day');
            }
        }
    }
    // if start or end date selected reset the state as null
    else {
        datepickerType = null;
        lastAvailableBookingDate = null;
    }

    return [datepickerType, lastAvailableBookingDate];
};

export const emptyHoursAndMinutes = () => ({
    disabledHours: () => range(0, 24),
    disabledMinutes: () => [0, 15, 30, 45],
});

export const getRoundedCurrentTimeToQuarterInterval = () => {
    let current = dayjs();
    const value = Math.round(current.minute() / 15) * 15;
    current = current.set('minute', value);

    return dayjs(current, 'HH:mm');
};

export const computeDisabledDateDatePicker = (
    value: dayjs.Dayjs,
    period: PeriodPayload,
    availableBookingRange: DateUnit,
    unavailableDayOfWeek: DayOfWeek[],
    unavailableTimeRange: PeriodDataFragment[],
    durationNextBooking: number,
    stock: Extract<StockInventorySpecsFragment, { __typename: 'MobilityStockInventory' }>,
    reservedTimeSlotRange: Record<string, Record<number, number[]>>
) => {
    // value each date of the calendar
    const date = dayjs(value);
    const dateString = date.format('YYYY-MM-DD');
    const { start, end } = period || {};
    // current date
    const todayDate = dayjs();

    // in `disabledDate` API, it does not able to use `type` suchlike `disabledTime` feature
    // availableBookingRange = max range of booking
    const [datepickerType, lastAvailableBookingDate] = getAvailableDateRange(availableBookingRange, start, end);

    const unavailableDayofWeek = getUnavailableDayofWeek(unavailableDayOfWeek);
    let checker = false;

    if (!isNil(lastAvailableBookingDate)) {
        checker =
            datepickerType === DatePickerType.Start
                ? !date.isBetween(dayjs(period?.start), lastAvailableBookingDate, 'day', '[]')
                : !date.isBetween(lastAvailableBookingDate, dayjs(period?.end), 'day', '[]');
    }

    const { isBlock } = calculateBookingEntireDay(
        value,
        unavailableTimeRange,
        datepickerType,
        durationNextBooking,
        stock.reservations,
        period
    );

    const maxDate = computeClosestMaxDate(start, stock.reservations, stock.blockPeriod);

    const blockerFromStartDate = start
        ? blockerFromStartDateForEndDateOptions(period, durationNextBooking, stock.reservations, unavailableTimeRange)
        : false;

    if (value) {
        // check for available period, prefer settings on stocks over inventories
        const inventory = stock.inventory.__typename === 'MobilityInventory' ? stock.inventory : null;
        const period = stock.period || inventory?.period;
        const isWithinAvailablePeriod =
            isEmpty(period) || (date.isSameOrAfter(period?.start, 'day') && date.isSameOrBefore(period?.end, 'day'));

        return (
            // firstly we block dates before today
            date.isBefore(todayDate, 'day') ||
            // not within valid period
            !isWithinAvailablePeriod ||
            // block the dates which already fully block ( blocked entire day)
            (!isNil(reservedTimeSlotRange[dateString]) &&
                Object.entries(reservedTimeSlotRange[dateString]).length > 23) ||
            // only allow available date range based on user selected date ( start date or end date first) and
            // `mobilityModule.availableDateRange`
            // availableDateRange = 0 :: means datepicker do not block any dates
            // availableDateRange > 0 :: means show limited available dates based on user choose date
            checker ||
            /**
             * we check whether the reservation listing has any calendar date chosen overlapping with
             * existing booking, we will take the closest start datetime of the reservation based on the
             * user selected date , vice versa to end date
             *  */
            (isBlock && !date.isSame(dayjs(period?.start), 'day')) ||
            (maxDate && date.isAfter(maxDate, 'day')) ||
            (blockerFromStartDate && date.isAfter(dayjs(period?.start), 'day')) ||
            /**
             * block if the user choose the end date and the date is all later than
             * all existing reservation end date, system will block the minDate as
             * the closest reservation end date
             * :: to prevent overlapping booking on existing booking
             */
            unavailableDayofWeek.includes(date.day())
        );
    }

    return false;
};

export const computeDefaultDisableTimeTimePicker = (
    period: PeriodPayload,
    start: string | Date,
    end: string | Date,
    typeTimePicker: DatePickerType,
    disabledTimeSlot: Record<number, number[]>,
    reservedTimeSlotRange: Record<string, Record<number, number[]>>,
    stock: Extract<StockInventorySpecsFragment, { __typename: 'MobilityStockInventory' }>,
    durationNextBooking: number
) => {
    // on load when neither of dates chosen
    if (isNil(period)) {
        return defaultDisabledHoursAndMinutesOfTheDay(typeTimePicker, period, disabledTimeSlot);
    }

    let date = null;
    date =
        typeTimePicker === DatePickerType.Start
            ? dayjs(period.start).format('YYYY-MM-DD')
            : dayjs(period.end).format('YYYY-MM-DD');

    const disabledArrayByDate = reservedTimeSlotRange[date];

    if (!isNil(disabledArrayByDate)) {
        return getDisabledHoursAndMinutesOfDate(
            typeTimePicker,
            disabledTimeSlot,
            disabledArrayByDate,
            {},
            {}, // block period should not include maximum allowable booking period
            typeTimePicker === DatePickerType.End ? dayjs(start).hour() : null,
            typeTimePicker === DatePickerType.End ? dayjs(start).minute() : null,
            period,
            stock.reservations,
            durationNextBooking
        );
    }

    return typeTimePicker === DatePickerType.Start
        ? defaultDisabledHoursAndMinutesOfTheDay(typeTimePicker, period, disabledTimeSlot)
        : defaultDisabledHoursAndMinutesOfTheDay(
              typeTimePicker,
              period,
              disabledTimeSlot,
              {}, // for admin no need blocking period , no need to add maximum allowable days or hours into checker
              !isNil(start) ? dayjs(start).hour() : null,
              !isNil(start) ? dayjs(start).minute() : null
          );
};
export const mobilityJourneyTemporaryKey = 'MobilityTemporaryApplication';

export type TemporaryMobilityDateType = Date | string | null;

export type TemporaryMobilityBookingDetails = {
    period: {
        start: TemporaryMobilityDateType;
        end: TemporaryMobilityDateType;
        startTime: TemporaryMobilityDateType;
        endTime: TemporaryMobilityDateType;
    } | null;
    inventoryStockId: string;
    location: string | null;
    stockIdentifier?: string;
    variantName?: string;
};

export type MobilityJourneyTemporaryValueType = {
    token: string;
    sessionId: string;
    expireAt?: TemporaryMobilityDateType;
    bookingDetails: TemporaryMobilityBookingDetails;
    kyc: MobilityKYCJourneyValues | null;
    draftCreated: boolean;
};

export const retrieveMobilityJourneyValue = (data: MobilityJourneyTemporaryValueType) => ({
    value: omit('sessionId', data),
    sessionId: data.sessionId,
});

export const useHandleTemporarySession = () => ({
    extendTemporarySession: async (
        apolloClient: ApolloClient<object>,
        setItem: (key: string, data: any, intervalInSeconds: number, sessionId: string) => void,
        temporaryValue: MobilityJourneyTemporaryValueType
    ) => {
        if (temporaryValue?.token) {
            const { data } = await apolloClient.mutate<
                ExtendMobilityStockExpiryMutation,
                ExtendMobilityStockExpiryMutationVariables
            >({
                mutation: ExtendMobilityStockExpiryDocument,
                variables: { token: temporaryValue.token },
            });

            if (data.result.application.__typename !== 'MobilityApplication') {
                throw new Error('unexpected type');
            }

            setItem(
                mobilityJourneyTemporaryKey,
                {
                    ...omit('sessionId', temporaryValue),
                    token: data.result.token,
                    expireAt: data.result.application.expireAt,
                },
                getExpirationDuration(data.result.application.expireAt),
                temporaryValue.sessionId
            );
        }
    },
});

export type MobilityApplicationInputForLocation = {
    mobilityBookingDetails: {
        location: MobilityApplicationStateWithExpired['mobilityBookingDetails']['location'];
    };
    applicant: MobilityApplicationExpiredFragment['applicant'];
};

export const useMobilityBookingLocation = (application: MobilityApplicationInputForLocation) => {
    const { t } = useTranslation(['mobilityOrderSummary']);

    const {
        // applicant,
        mobilityBookingDetails,
    } = application;
    const { location } = mobilityBookingDetails;

    return useMemo(() => {
        switch (location.__typename) {
            case 'MobilityBookingLocationPickup': {
                return {
                    type: location.type,
                    name: location.name,
                    address: location.address,
                    email: location.email,
                    phone: location.phone,
                    url: location.url,
                };
            }

            case 'MobilityBookingLocationHome': {
                return {
                    type: location.type,
                    name: t('mobilityOrderSummary:homeDelivery.label'),
                    // TODO: the rest, should be retrieved from KYC values.
                    // keep it empty for now, it will be covered by next ticket (KYCs)
                    address: null,
                    email: null,
                    phone: null,
                    url: null,
                };
            }

            default: {
                throw new Error('Unexpected mobility booking location type');
            }
        }
    }, [location, t]);
};
