import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useCancelApplicationMutation } from '../../../../api/mutations/cancelApplication';
import { ApplicationStage } from '../../../../api/types';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import type { FormValues } from './BookingForm/shared';

const useCancelMobilityApplication = (token: string) => {
    const [cancel] = useCancelApplicationMutation();
    const { t } = useTranslation('applicationDetails');
    const navigate = useNavigate();

    const { notification } = useThemeComponents();

    return useHandleError<FormValues>(
        async () => {
            notification.loading({
                content: t('applicationDetails:messages.cancellingBooking'),
                duration: 0,
                key: 'primary',
            });
            await cancel({
                variables: { token, stage: ApplicationStage.Mobility },
            }).finally(() => {
                notification.destroy('primary');
            });

            navigate('../apply', { state: { token, isCancel: true } });
        },
        [notification, t, cancel, token, navigate],
        {}
    );
};

export default useCancelMobilityApplication;
