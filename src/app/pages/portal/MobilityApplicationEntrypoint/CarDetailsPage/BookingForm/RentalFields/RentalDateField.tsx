/* eslint-disable max-len */
import { InfoCircleFilled } from '@ant-design/icons';
import { Popover } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useFormikContext } from 'formik';
import { isEmpty, isNil, merge } from 'lodash/fp';
import { useCallback, useMemo, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import styled from 'styled-components';
import { StockPublicDataFragment } from '../../../../../../api/fragments/StockPublicData';
import { PeriodPayload } from '../../../../../../api/types';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { getAvailableDateRange, getUnavailableDayofWeek } from '../../../helper';
import DatePickerType from '../../../shared';
import type { FormValues } from '../shared';
import {
    blockerFromStartDateForEndDateOptions,
    calculateAvailaleHoursForNextBooking,
    calculateBookingEntireDay,
    calculateDisabledInventoryStockBlockPeriod,
    computeClosestMaxDate,
    RentalFieldProps,
} from './shared';

const StyledDiv = styled.div`
    div.ant-form-item {
        margin-bottom: var(--mobility-rental-field-margin-bottom-inner, 0px);
    }
`;

const getFocusToStartDateInput = (inputs: NodeListOf<HTMLElement>) => {
    if (!isNil(inputs) && inputs.length > 0) {
        inputs[1].classList.remove('.ant-picker-input-active');
        inputs[0].classList.add('ant-picker-input-active');
        inputs[0].focus();
    }
};

const fullyBookedDuringAvailableStockPeriod = (
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>,

    reservedTimeSlotRange: {
        [x: string]: Record<number, number[]>;
    }
) => {
    if (isNil(stock?.period)) {
        return false;
    }

    const stockPeriodStart = dayjs(stock?.period.start);
    const stockPeriodEnd = dayjs(stock?.period.end);

    const rangeOfAvailablePeriod = [stockPeriodStart.format('YYYY-MM-DD')];
    let startDate = stockPeriodStart;

    while (startDate.isBefore(stockPeriodEnd, 'day')) {
        startDate = startDate.add(1, 'day');
        rangeOfAvailablePeriod.push(startDate.format('YYYY-MM-DD'));
    }

    const arrayAvailablePeriodinBoolean = [];
    rangeOfAvailablePeriod.forEach(availablePeriod => {
        arrayAvailablePeriodinBoolean.push(
            Object.keys(isNil(reservedTimeSlotRange[availablePeriod]) ? false : reservedTimeSlotRange[availablePeriod])
                .length > 23
        );
    });

    return arrayAvailablePeriodinBoolean.every(period => period);
};
const RentalDateField = ({ module, stock, application, disabledTimeSlot }: RentalFieldProps) => {
    const { t } = useTranslation(['mobilityVehicleDetails', 'common']);
    const {
        FormFields: { RangePickerField, FixedRangePickerField },
    } = useThemeComponents();

    const {
        minimumAdvancedBooking: minimumBooking,
        durationBeforeNextBooking: durationNextBooking,
        availableNumberOfBookingRange: availableBookingRange,
        unavailableDayOfWeek,
        unavailableTimeRange,
    } = module;

    const reservedRange = calculateAvailaleHoursForNextBooking(
        stock.reservations,
        minimumBooking,
        durationNextBooking,
        disabledTimeSlot
    );

    const blockedPeriodRange = calculateDisabledInventoryStockBlockPeriod(
        stock.blockPeriod,
        minimumBooking,
        durationNextBooking,
        disabledTimeSlot
    );

    const reservedTimeSlotRange = useMemo(
        () => merge(reservedRange, blockedPeriodRange),
        [blockedPeriodRange, reservedRange]
    );

    const availableStockPeriodSlot = fullyBookedDuringAvailableStockPeriod(stock, reservedTimeSlotRange);

    const { values, setFieldValue, getFieldProps } = useFormikContext<FormValues>();

    const { state } = useLocation();

    const [periodState, setPeriodState] = useState<PeriodPayload>(state?.rentalPeriod || null);
    const onCalendarChange = useCallback(
        (value, dateStrings, info) => {
            const [start, end] = value ?? [];

            const rentalPeriod = {
                start: start ? dayjs(start).toDate() : undefined,
                end: end ? dayjs(end).toDate() : undefined,
            };

            setPeriodState(rentalPeriod);
            setFieldValue('rentalPeriod', rentalPeriod);
        },
        [setFieldValue]
    );

    useEffect(() => {
        if (isNil(values.rentalPeriod)) {
            setFieldValue('startTime', undefined);
            setFieldValue('endTime', undefined);
        }
    }, [setFieldValue, values.rentalPeriod]);

    useEffect(() => {
        if (
            (!isNil(values.rentalPeriod) &&
                !isNil(values.rentalPeriod?.start) &&
                !isNil(values.rentalPeriod?.end) &&
                !dayjs(values.rentalPeriod?.start).isSame(dayjs(periodState?.start), 'day')) ||
            !dayjs(values.rentalPeriod?.end).isSame(dayjs(periodState?.end), 'day')
        ) {
            setPeriodState(values.rentalPeriod);
        }
    }, [periodState?.end, periodState?.start, values.rentalPeriod]);

    useEffect(() => {
        const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');

        if (!isNil(inputs) && inputs.length > 0 && isNil(values.rentalPeriod?.start)) {
            // add the blockers node
            const node = document.createElement('div');
            node.className = 'endDateBlocker';
            node.style.position = 'absolute';
            node.style.width = '110%';
            node.style.height = '50px';

            inputs[1].appendChild(node);
        }

        if (!isNil(inputs) && inputs.length > 0 && !isNil(values.rentalPeriod?.start)) {
            const node = document.querySelector('.endDateBlocker');
            if (node) {
                node.remove();
            }
        }
    }, [values.rentalPeriod?.start]);

    const onChange = useCallback(
        value => {
            const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');

            if (!isNil(inputs) && inputs.length > 0) {
                getFocusToStartDateInput(inputs);
                const [start, end] = value ?? [];

                const rentalPeriod = {
                    start: start ? dayjs(start).toDate() : undefined,
                    end: end ? dayjs(end).toDate() : undefined,
                };
                setFieldValue('rentalPeriod', rentalPeriod);

                setPeriodState(rentalPeriod);
            }
        },
        [setFieldValue]
    );

    const onOpenChange = useCallback(
        open => {
            const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
            // 0 refers start and 1 refers endDate
            if (open && !isNil(inputs) && isNil(values.rentalPeriod?.start) && inputs.length > 0) {
                getFocusToStartDateInput(inputs);
            }

            const { value } = getFieldProps('rentalPeriod');
            setPeriodState(value);

            if (!isNil(inputs) && inputs.length > 0 && !open) {
                inputs[1].classList.remove('.ant-picker-input-active');
            }

            if (!isNil(inputs) && inputs.length > 0 && open) {
                inputs[1].click();
                getFocusToStartDateInput(inputs);
                if (!isNil(values.rentalPeriod?.end)) {
                    setFieldValue('rentalPeriod', {
                        start: undefined,
                        end: undefined,
                    });
                    setPeriodState({
                        start: undefined,
                        end: undefined,
                    });
                }
            }
        },
        [getFieldProps, setFieldValue, values.rentalPeriod?.end, values.rentalPeriod?.start]
    );

    const onClick = useCallback(() => {
        const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
        if (!isNil(inputs) && inputs.length > 0) {
            // 0 refers start and 1 refers endDate
            getFocusToStartDateInput(inputs);
            if (!isNil(values.rentalPeriod?.end)) {
                setFieldValue('rentalPeriod', {
                    start: undefined,
                    end: undefined,
                });
                setPeriodState({
                    start: undefined,
                    end: undefined,
                });
            }
        }
    }, [setFieldValue, values.rentalPeriod?.end]);

    const currentPeriodInSeconds = useMemo(() => {
        if (application) {
            const {
                mobilityBookingDetails: { period },
            } = application;

            return dayjs(period.end).diff(period.start, 'second');
        }

        return 0;
    }, [application]);

    const disabledDate: RangePickerProps<Dayjs>['disabledDate'] = useCallback(
        value => {
            // value each date of the calendar
            const date = dayjs(value);
            const dateString = date.format('YYYY-MM-DD');
            const { start, end } = periodState || {};

            // current date
            const todayDate = dayjs();

            // in `disabledDate` API, it does not able to use `type` suchlike `disabledTime` feature
            // availableBookingRange = max range of booking
            const [datepickerType, lastAvailableBookingDate] = getAvailableDateRange(availableBookingRange, start, end);

            const unavailableDayofWeek = getUnavailableDayofWeek(unavailableDayOfWeek);
            let checker = false;

            if (!isNil(lastAvailableBookingDate)) {
                checker =
                    datepickerType === DatePickerType.Start
                        ? !date.isBetween(dayjs(start), lastAvailableBookingDate, 'day', '[]')
                        : !date.isBetween(lastAvailableBookingDate, dayjs(end), 'day', '[]');
            }

            // update / amend mobility booking
            if (application) {
                if (value) {
                    // check for available period, prefer settings on stocks over inventories
                    const inventory = stock.inventory.__typename === 'MobilityInventory' ? stock.inventory : null;
                    const period = stock?.period || inventory?.period;
                    const isWithinAvailablePeriod =
                        isEmpty(period) ||
                        (dayjs(value).isSameOrAfter(period.start, 'day') &&
                            dayjs(value).isSameOrBefore(period.end, 'day'));

                    // in `disabledDate` API, it does not able to use `type` suchlike `disabledTime` feature
                    const now = dayjs();
                    const dateString = dayjs(value).format('YYYY-MM-DD');

                    const endDateOfAvailableBooking = value
                        .add(currentPeriodInSeconds, 'second')
                        .add(durationNextBooking, 'hour');

                    const timeDifferentFromStartDateToEndDateBooking = Math.abs(
                        dayjs(application.mobilityBookingDetails.period.start).diff(
                            dayjs(application.mobilityBookingDetails.period.end),
                            'minute'
                        )
                    );

                    const unavailableToAmend = unavailableDayofWeek.includes(endDateOfAvailableBooking.day());

                    const sortedReservations = stock.reservations
                        .map(reserve => dayjs(reserve.bookingPeriod.start))
                        .sort((a, b) => (dayjs(a).isAfter(dayjs(b)) ? 1 : -1));
                    const closestReservation = sortedReservations.find(reservation => {
                        const startDate = dayjs(reservation);

                        return dayjs(date).isSameOrBefore(startDate, 'day');
                    });
                    const dateAfterAddedBookingRange = !isNil(closestReservation)
                        ? dayjs(closestReservation).subtract(timeDifferentFromStartDateToEndDateBooking, 'minute')
                        : null;

                    const fullyBlockDatesBasedOnNumberOfDaysBooking =
                        !isNil(closestReservation) && !isNil(dateAfterAddedBookingRange)
                            ? date.isBetween(dayjs(closestReservation), dayjs(dateAfterAddedBookingRange), 'day', '[]')
                            : false;

                    return (
                        date.add(1, 'second').isBefore(now.add(minimumBooking, 'day')) ||
                        (!isNil(reservedTimeSlotRange[dateString]) &&
                            Object.entries(reservedTimeSlotRange[dateString]).length > 23) ||
                        fullyBlockDatesBasedOnNumberOfDaysBooking ||
                        !isWithinAvailablePeriod ||
                        // only allow available date range based on user selected date ( start date or end date first) and
                        // `mobilityModule.availableDateRange`
                        // availableDateRange = 0 :: means datepicker do not block any dates
                        // availableDateRange > 0 :: means show limited available dates based on user choose date
                        unavailableDayofWeek.includes(date.day()) ||
                        unavailableToAmend
                    );
                }

                return false;
            }

            const { isBlock } = calculateBookingEntireDay(
                value,
                unavailableTimeRange,
                datepickerType,
                durationNextBooking,
                stock.reservations,
                periodState
            );

            const maxDate = computeClosestMaxDate(start, stock.reservations, stock.blockPeriod);

            const blockerFromStartDate = start
                ? blockerFromStartDateForEndDateOptions(
                      values.rentalPeriod,
                      durationNextBooking,
                      stock.reservations,
                      unavailableTimeRange
                  )
                : false;

            if (value) {
                // check for available period, prefer settings on stocks over inventories
                const inventory = stock.inventory.__typename === 'MobilityInventory' ? stock.inventory : null;
                const period = stock.period || inventory?.period;
                const isWithinAvailablePeriod =
                    isEmpty(period) ||
                    (date.isSameOrAfter(period.start, 'day') && date.isSameOrBefore(period.end, 'day'));

                return (
                    // firstly we block dates before today
                    date.isBefore(todayDate, 'day') ||
                    // not within valid period
                    !isWithinAvailablePeriod ||
                    // block the dates which already fully block ( blocked entire day)
                    (!isNil(reservedTimeSlotRange[dateString]) &&
                        Object.entries(reservedTimeSlotRange[dateString]).length > 23) ||
                    // only allow available date range based on user selected date ( start date or end date first) and
                    // `mobilityModule.availableDateRange`
                    // availableDateRange = 0 :: means datepicker do not block any dates
                    // availableDateRange > 0 :: means show limited available dates based on user choose date
                    checker ||
                    /**
                     * we check whether the reservation listing has any calendar date chosen overlapping with
                     * existing booking, we will take the closest start datetime of the reservation based on the
                     * user selected date , vice versa to end date
                     *  */
                    (isBlock && !date.isSame(dayjs(values.rentalPeriod?.start), 'day')) ||
                    (maxDate && date.isAfter(maxDate, 'day')) ||
                    (blockerFromStartDate && date.isAfter(dayjs(values.rentalPeriod?.start), 'day')) ||
                    /**
                     * block if the user choose the end date and the date is all later than
                     * all existing reservation end date, system will block the minDate as
                     * the closest reservation end date
                     * :: to prevent overlapping booking on existing booking
                     */
                    unavailableDayofWeek.includes(date.day())
                );
            }

            return false;
        },
        [
            application,
            availableBookingRange,
            currentPeriodInSeconds,
            durationNextBooking,
            minimumBooking,
            periodState,
            reservedTimeSlotRange,
            stock.blockPeriod,
            stock.inventory,
            stock.period,
            stock.reservations,
            unavailableDayOfWeek,
            unavailableTimeRange,
            values.rentalPeriod,
        ]
    );

    if (application) {
        const label = (
            <>
                {t('mobilityVehicleDetails:fields.rentalPeriod.label')}
                &nbsp;
                <Popover content={t('mobilityVehicleDetails:messages.amendDuration')} trigger="hover">
                    <InfoCircleFilled />
                </Popover>
            </>
        );

        return (
            <FixedRangePickerField
                disabled={availableStockPeriodSlot}
                disabledDate={disabledDate}
                label={label}
                name="rentalPeriod"
                periodInSeconds={currentPeriodInSeconds}
                showTime={false}
                // Resetting style
                style={null}
                readOnly
            />
        );
    }

    return (
        <StyledDiv>
            <RangePickerField
                defaultValue={null}
                disabled={[availableStockPeriodSlot, availableStockPeriodSlot]}
                disabledDate={disabledDate}
                errorMessage={availableStockPeriodSlot ? t('mobilityVehicleDetails:messages.fullyBooked') : undefined}
                label={t('mobilityVehicleDetails:fields.rentalPeriod.label')}
                name="rentalPeriod"
                onCalendarChange={onCalendarChange}
                onChange={onChange}
                onClick={onClick}
                onOpenChange={onOpenChange}
                showTime={false}
                autoFocus
                readOnly
            />
        </StyledDiv>
    );
};

export default RentalDateField;
