import { Col, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { isNil, uniq, isEmpty, set, getOr, isNumber, head, range, merge } from 'lodash/fp';
import styled from 'styled-components';
import { DebugJourneyDataFragment } from '../../../../../../api/fragments/DebugJourneyData';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { PeriodDataFragment } from '../../../../../../api/fragments/PeriodData';
import { StockBlockingPeriodDataFragment } from '../../../../../../api/fragments/StockBlockingPeriod';
import { StockPublicDataFragment } from '../../../../../../api/fragments/StockPublicData';
import { MobilityStockReservation, PeriodPayload } from '../../../../../../api/types';
import DatePickerType from '../../../shared';

export const StyledStartTimePicker = styled(Col)`
    width: 100%;
`;

export const StyledEndTimePicker = styled(Col)`
    width: 100%;
`;

export const TimePickerContainer = styled(Row)`
    justify-content: space-between;
`;

export const colSpan = {
    xl: 11,
    lg: 24,
};

export const rightColSpan = {
    xl: 12,
    lg: 24,
};

export type RentalFieldProps = {
    application?: Extract<DebugJourneyDataFragment['application'], { __typename: 'MobilityApplication' }> | null;
    module: MobilityApplicationEntrypointContextDataFragment['mobilityApplicationModule'];
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
    disabledTimeSlot?: HourMinutesMapping;
    typeTimePicker?: DatePickerType;
    name?: string;
    disabled?: boolean;
};

export type HourMinutesMapping = { [time: string]: number[] };

// creates a hour-minutes object mapping based on time range
export const calculateDisabledTimeRange = (unavailableTimeRange: PeriodDataFragment[]): HourMinutesMapping =>
    // map unvailable time range
    unavailableTimeRange.reduce((acc, time) => {
        let startTime = dayjs(time.start);
        const endTime = dayjs(time.end);

        // only block hours and minutes before the end the time
        // loop until start time is greater than end time
        while (startTime.isSameOrBefore(endTime, 'second')) {
            const hour = startTime.hour();
            const minute = startTime.minute();

            acc[hour] = acc[hour] ? [...acc[hour], minute] : [minute];

            startTime = startTime.add(15, 'minute');
        }

        // return mapped disabled time object
        return acc;
    }, {});

export const buildHourMinutesMapping = (start: Dayjs, end: Dayjs, hourMinutesMapping: HourMinutesMapping) => {
    const startTime = dayjs(start);
    const endTime = dayjs(end);

    if (startTime.isSameOrBefore(endTime, 'second')) {
        const hour = startTime.hour();
        const minute = startTime.minute();

        const updatedHourMinutesMapping = set(
            hour,
            hourMinutesMapping[hour] ? [...hourMinutesMapping[hour], minute] : [minute],
            hourMinutesMapping
        );

        return buildHourMinutesMapping(startTime.add(15, 'minute'), endTime, updatedHourMinutesMapping);
    }

    return hourMinutesMapping;
};

// this is originally for reverse computation from start to end and end to start
export const calculateDisabledHoursAndMinutesOfDate = (start: Dayjs, end: Dayjs) => {
    const reservedHourAndMinutesTimeSlot: Record<string, number[]> = {};
    let startTime = start;

    while (
        start.format('YYYY-MM-DD') === startTime.format('YYYY-MM-DD') &&
        dayjs(startTime).isSameOrBefore(end, 'second')
    ) {
        const hour = startTime.hour();
        const minute = startTime.minute();

        if (!reservedHourAndMinutesTimeSlot[hour]) {
            reservedHourAndMinutesTimeSlot[hour] = [minute];
        } else {
            reservedHourAndMinutesTimeSlot[hour] = uniq([...reservedHourAndMinutesTimeSlot[hour], minute]);
        }

        startTime = startTime.add(15, 'minute');
    }

    return reservedHourAndMinutesTimeSlot;
};

export const calculateDisabledReservationTimeSlot = (
    // application reservations for the mobility stock
    reservations: MobilityStockReservation[],
    // booking offset
    durationBeforeNextBooking: number,
    // booking hour offset from current day
    minimumBookingHour: number,
    disableTimeRange: HourMinutesMapping
) => {
    const reservedTimeSlot: Record<string, Record<number, number[]>> = {};

    // when onload the datepicker, we should block the hours and time based on date start off until system current time
    const today = dayjs().startOf('day');
    const todayAddedDurationNextBooking = dayjs().add(minimumBookingHour, 'hour');

    // update into the disabled today time
    reservedTimeSlot[today.format('YYYY-MM-DD')] = calculateDisabledHoursAndMinutesOfDate(
        today,
        todayAddedDurationNextBooking
    );

    reservations.forEach(reserve => {
        const { bookingPeriod } = reserve;

        const { end, start } = bookingPeriod;
        let startTime = dayjs(start).subtract(durationBeforeNextBooking, 'hour');
        const endTime = dayjs(end).add(durationBeforeNextBooking, 'hour');
        while (startTime.isSameOrBefore(endTime, 'second')) {
            const date = startTime.format('YYYY-MM-DD');

            const calculated = calculateDisabledHoursAndMinutesOfDate(startTime, endTime);
            const reservedDateTimeSlot = reservedTimeSlot[date];

            const hourKeys = Object.keys({ ...disableTimeRange, ...calculated, ...reservedDateTimeSlot });
            reservedTimeSlot[date] = hourKeys.reduce((acc, hourKey) => {
                acc[hourKey] = uniq([
                    ...(disableTimeRange[hourKey] ?? []),
                    ...(calculated[hourKey] ?? []),
                    ...getOr([], hourKey, reservedDateTimeSlot),
                ]);

                return acc;
            }, {});

            const newDate = startTime.add(1, 'day').format('YYYY-MM-DD');
            startTime = dayjs(newDate);
        }
    });

    return reservedTimeSlot;
};

export const calculateDisabledInventoryStockBlockPeriod = (
    // application reservations for the mobility stock
    blockPeriods: StockBlockingPeriodDataFragment[],
    // booking offset
    durationBeforeNextBooking: number,
    // booking hour offset from current day
    minimumBookingHour: number,
    disableTimeRange: HourMinutesMapping
) => {
    const reservedTimeSlot: Record<string, Record<number, number[]>> = {};

    // when onload the datepicker, we should block the hours and time based on date start off until system current time
    const today = dayjs().startOf('day');
    const todayAddedDurationNextBooking = dayjs().add(minimumBookingHour, 'hour');

    // update into the disabled today time
    reservedTimeSlot[today.format('YYYY-MM-DD')] = calculateDisabledHoursAndMinutesOfDate(
        today,
        todayAddedDurationNextBooking
    );

    blockPeriods.forEach(block => {
        const { end, start } = block;

        let startTime = dayjs(start).subtract(durationBeforeNextBooking, 'hour');
        const endTime = dayjs(end).add(durationBeforeNextBooking, 'hour');
        while (startTime.isSameOrBefore(endTime, 'second')) {
            const date = startTime.format('YYYY-MM-DD');

            const calculated = calculateDisabledHoursAndMinutesOfDate(startTime, endTime);
            const reservedDateTimeSlot = reservedTimeSlot[date];

            const hourKeys = Object.keys({ ...disableTimeRange, ...calculated, ...reservedDateTimeSlot });

            reservedTimeSlot[date] = hourKeys.reduce((acc, hourKey) => {
                acc[hourKey] = uniq([
                    ...(disableTimeRange[hourKey] ?? []),
                    ...(calculated[hourKey] ?? []),
                    ...getOr([], hourKey, reservedDateTimeSlot),
                ]);

                return acc;
            }, {});

            const newDate = startTime.add(1, 'day').format('YYYY-MM-DD');
            startTime = dayjs(newDate);
        }
    });

    return reservedTimeSlot;
};

export const defaultDisabledHoursAndMinutesOfTheDay = (
    typeTimepicker: DatePickerType,
    rentalPeriod: PeriodPayload,
    disabledTimeSlot?: Record<number, number[]>,
    availableNumberOfBookingHoursMinuteMapping?: Record<number, number[]>,
    blockHourFromStartTime?: number,
    blockMinuteFromStartTime?: number
) => ({
    disabledHours: () => {
        const mergedHours = merge(disabledTimeSlot, availableNumberOfBookingHoursMinuteMapping);
        const hours: number[] = Object.entries(mergedHours)
            .map(([key, value]) => {
                if (value.length === 4) {
                    return Number(key);
                }
                if (Number(key) < blockHourFromStartTime) {
                    return Number(key);
                }

                return null;
            })
            .filter(key => isNumber(key));

        const isSameDate =
            !isNil(rentalPeriod?.start) && !isNil(rentalPeriod?.end)
                ? dayjs(rentalPeriod?.start).isSame(dayjs(rentalPeriod?.end), 'day')
                : false;

        const blockingHoursBasedOnStartTime: number[] = range(0, 24)
            .filter(value => {
                if (typeTimepicker === DatePickerType.End) {
                    if (value > hours.find(value => value > blockHourFromStartTime) || value < blockHourFromStartTime) {
                        return value;
                    }
                }

                return null;
            })
            .filter(key => isNumber(key));

        const blockHoursArray = uniq([
            ...hours,
            ...(isSameDate ? blockingHoursBasedOnStartTime : []),
            !isNil(blockMinuteFromStartTime) && blockMinuteFromStartTime === 45 && blockHourFromStartTime,
        ]).filter(key => isNumber(key));

        return blockHoursArray;
    },
    disabledMinutes: (selectedHours: number) => {
        const mergedHours = merge(disabledTimeSlot, availableNumberOfBookingHoursMinuteMapping);

        return calculateDisabledMinutes(
            selectedHours,
            mergedHours,
            typeTimepicker,
            blockMinuteFromStartTime,
            blockHourFromStartTime,
            rentalPeriod
        );
    },
});

export const getDisabledHoursAndMinutesOfDate = (
    // what hours needed to disabled for minutes
    typeTimepicker: DatePickerType,
    disabledTimeSlot?: HourMinutesMapping,
    disabledArrayByDate?: HourMinutesMapping,
    disabledStartDateTimeHours?: HourMinutesMapping,
    availableNumberOfBookingRange?: HourMinutesMapping,
    blockHourFromStartTime?: number,
    blockMinuteFromStartTime?: number,
    rentalPeriod?: PeriodPayload,
    reservations?: MobilityStockReservation[],
    durationNextBooking?: number,
    // when amend flow, we need to ensure the booking should not less than 24 x N( number of days)
    minimumBookingDays?: number,
    startTime?: string | Date
) => {
    // first, we get all the keys of the hours
    const disabledHourKeyArray = !isEmpty(disabledTimeSlot)
        ? Object.keys(disabledTimeSlot).map(value => Number(value))
        : [];
    const disabledReservedHourKeyArray = !isEmpty(disabledArrayByDate)
        ? Object.keys(disabledArrayByDate).map(value => Number(value))
        : [];
    const disabledDateTimeHoursKeyArray = !isEmpty(disabledStartDateTimeHours)
        ? Object.keys(disabledStartDateTimeHours).map(value => Number(value))
        : [];

    const availableNumberOfBookingRangeKeyArray = !isEmpty(availableNumberOfBookingRange)
        ? Object.keys(availableNumberOfBookingRange).map(value => Number(value))
        : [];

    // concatenate all the uniq keys
    const concatDisabledHourKeyArray = uniq([
        ...disabledHourKeyArray,
        ...disabledReservedHourKeyArray,
        ...disabledDateTimeHoursKeyArray,
        ...availableNumberOfBookingRangeKeyArray,
    ]);

    // we iterate the datetime objects based on the unique keys and generate
    // new concatanate hours and minutes array
    const concatDisabledHourValue: HourMinutesMapping = concatDisabledHourKeyArray.reduce(
        (newArray, currentValue, curentIndex) => ({
            ...newArray,
            [currentValue]: uniq([
                ...(!isNil(disabledTimeSlot) && !isNil(disabledTimeSlot[currentValue])
                    ? disabledTimeSlot[currentValue]
                    : []),
                ...(!isNil(disabledArrayByDate) && !isNil(disabledArrayByDate[currentValue])
                    ? disabledArrayByDate[currentValue]
                    : []),
                ...(!isNil(disabledStartDateTimeHours) && !isNil(disabledStartDateTimeHours[currentValue])
                    ? disabledStartDateTimeHours[currentValue]
                    : []),
                ...(!isNil(availableNumberOfBookingRange) && !isNil(availableNumberOfBookingRange[currentValue])
                    ? availableNumberOfBookingRange[currentValue]
                    : []),
            ]),
        }),
        {}
    );

    // getting the hours should be disabled
    const rangeHours = range(0, 24);
    const isSameDate =
        !isNil(rentalPeriod?.start) && !isNil(rentalPeriod?.end)
            ? dayjs(rentalPeriod?.start).isSame(dayjs(rentalPeriod?.end), 'day')
            : false;
    const disabledHourArray = Object.entries(concatDisabledHourValue).map(([key, value]) => {
        if (value.length > 3) {
            return Number(key);
        }

        return null;
    });

    let disabledHoursRange = [];

    if (isSameDate) {
        disabledHoursRange =
            typeTimepicker === DatePickerType.End
                ? rangeHours.filter(value => {
                      if (
                          value > disabledHourArray.find(value => value > blockHourFromStartTime) ||
                          value < blockHourFromStartTime
                      ) {
                          return value;
                      }

                      return null;
                  })
                : [];
    } else if (
        !isNil(reservations) &&
        !isNil(durationNextBooking) &&
        reservations.length > 0 &&
        durationNextBooking > 0
    ) {
        // when we select a range of booking dates but there is booking in the midway
        // we use `blockHourFromStartTime` to handle checking whether is it startTime or endTime
        let reservedTime = null;

        if (typeTimepicker === DatePickerType.Start) {
            reservedTime = reservations.find(reserve =>
                dayjs(reserve.bookingPeriod.end).isSame(dayjs(rentalPeriod?.start), 'day')
            )?.bookingPeriod.end;
        } else {
            reservedTime = reservations.find(reserve =>
                dayjs(reserve.bookingPeriod.start).isSame(dayjs(rentalPeriod?.end), 'day')
            )?.bookingPeriod.start;
        }
        /**
         *
         * after we have reservedTime, we do know what datepicker the user now startTime :: should block
         * before hours based on reservation endTime
         *
         *
         * in short
         * :: startTime add hours to the reservation endtime
         * :: endTime subtract hours to the reservation startTime
         */
        let reservedTimeHour = dayjs(reservedTime);

        if (typeTimepicker === DatePickerType.Start && !dayjs(reservedTime).isSame(dayjs(), 'D')) {
            reservedTimeHour = dayjs(reservedTime).add(durationNextBooking, 'hour');
        }

        if (typeTimepicker === DatePickerType.End && !dayjs(reservedTime).isSame(dayjs(), 'D')) {
            reservedTimeHour = dayjs(reservedTime).subtract(durationNextBooking, 'hour');
        }

        /**
         * block hours
         */
        if (!dayjs(reservedTime).isSame(dayjs(), 'D')) {
            disabledHoursRange =
                typeTimepicker === DatePickerType.Start
                    ? rangeHours.filter(hour => hour < reservedTimeHour.hour())
                    : rangeHours.filter(hour => hour > reservedTimeHour.hour());
        }
    }

    const hoursLessThanMinimumBookingDays = rangeHours.map(
        hour =>
            typeTimepicker === DatePickerType.End &&
            calculateValidityMinimumHoursBooking(rentalPeriod, minimumBookingDays, hour, startTime) &&
            hour
    );

    const disabledTime: number[] = uniq([
        ...disabledHourArray,
        ...disabledHoursRange,
        ...hoursLessThanMinimumBookingDays,
        !isNil(blockMinuteFromStartTime) && blockMinuteFromStartTime === 45 && blockHourFromStartTime,
    ]).filter(key => isNumber(key));

    return {
        disabledHours: () => disabledTime,
        disabledMinutes: (selectedHours: number) =>
            typeTimepicker === DatePickerType.Start
                ? calculateDisabledMinutes(selectedHours, concatDisabledHourValue, typeTimepicker)
                : calculateDisabledMinutes(
                      selectedHours,
                      concatDisabledHourValue,
                      typeTimepicker,
                      blockMinuteFromStartTime,
                      blockHourFromStartTime,
                      rentalPeriod,
                      minimumBookingDays
                  ),
    };
};

export const calculateAvailaleHoursForNextBooking = (
    stock: MobilityStockReservation[],
    minimumBooking: number,
    durationNextBooking: number,
    unavailableTimeRange: HourMinutesMapping
) => {
    let addedAdditionalBlockingHours = 0;
    const convertedMinimumBookingInHour = minimumBooking * 24;

    addedAdditionalBlockingHours =
        durationNextBooking > convertedMinimumBookingInHour ? durationNextBooking : convertedMinimumBookingInHour;

    return calculateDisabledReservationTimeSlot(
        stock,
        durationNextBooking,
        addedAdditionalBlockingHours,
        unavailableTimeRange
    );
};

const calculateValidityMinimumHoursBooking = (
    rentalPeriod: PeriodPayload,
    minimumBookingDay: number,
    currentHour: number,
    startTime: string | Date
) => {
    if (!startTime) {
        return false;
    }
    const startDate = dayjs(rentalPeriod.start)
        .set('hour', dayjs(startTime).hour())
        .set('minute', dayjs(startTime).minute());
    const endDate = dayjs(rentalPeriod.end).set('h', currentHour);

    const minutes = [0, 15, 30, 45];
    /**
     * we do a comparison each 15 minutes of hour whether does end date is less than
     * the minimum booking period.
     * E.G:
     * user initally booked and paid 3 days rental
     * for next amendment, it should allow user to choose the period of
     * greater than 48 hours and less than or equal to 72 hours
     *
     * In short
     * :: 24 * n > x (amended dates) <= 24 * (n+1)
     */
    const truthyArray = minutes.map(minute => {
        const newEndDate = endDate.set('m', minute);

        return newEndDate.diff(startDate, 'day') < minimumBookingDay;
    });

    return truthyArray.every(value => value);
};

export type BlockEntireDayType = {
    blockEntireDayType: 'start' | 'end';
    isBlock: boolean;
    timeDifferent: { start: number; end: number };
};

export const calculateBookingEntireDay = (
    date: Dayjs,
    unavailableTimeRange: PeriodDataFragment[],
    datePickerType: DatePickerType,
    durationNextBooking: number,
    reservations: MobilityStockReservation[],
    periodState: PeriodPayload
): BlockEntireDayType => {
    if (isNil(datePickerType)) {
        return { blockEntireDayType: null, isBlock: false, timeDifferent: null };
    }

    const hasBookingDate = head(
        reservations
            .filter(reservation => dayjs(reservation.bookingPeriod.start).isSame(dayjs(date), 'day'))
            .map(reservation => dayjs(reservation.bookingPeriod.start))
            .sort((a, b) => (dayjs(a).isAfter(dayjs(b)) ? -1 : 1))
    );

    if (hasBookingDate) {
        const sortedUnavailableTimeRangeEarliest = head(
            unavailableTimeRange.map(time => ({ start: dayjs(time.start), end: dayjs(time.end) })).sort(() => 1)
        );
        const sortedUnavailableTimeRangeLatest = head(
            unavailableTimeRange.map(time => ({ start: dayjs(time.start), end: dayjs(time.end) })).sort(() => -1)
        );

        const availableBookingBasedOnSystemBlockingTime = {
            start: dayjs(sortedUnavailableTimeRangeEarliest.end).add(durationNextBooking, 'hour'),
            end: dayjs(sortedUnavailableTimeRangeLatest.start).subtract(durationNextBooking, 'hour'),
        };
        const hoursAndMinutes = {
            start: hasBookingDate
                .set('hour', availableBookingBasedOnSystemBlockingTime.start.hour())
                .set('minute', availableBookingBasedOnSystemBlockingTime.start.minute()),

            end: hasBookingDate
                .set('hour', availableBookingBasedOnSystemBlockingTime.end.hour())
                .set('minute', availableBookingBasedOnSystemBlockingTime.end.minute()),
        };

        const timeDifferent = {
            start: Math.abs(hasBookingDate.diff(hoursAndMinutes.start, 'minute')),
            end: Math.abs(hoursAndMinutes.end.diff(hasBookingDate, 'minute')),
        };

        return {
            blockEntireDayType: datePickerType === DatePickerType.Start ? 'start' : 'end',
            isBlock: timeDifferent.start <= 120,
            timeDifferent,
        };
    }

    return { blockEntireDayType: null, isBlock: false, timeDifferent: null };
};

export const blockerFromStartDateForEndDateOptions = (
    periodState: PeriodPayload,
    durationNextBooking: number,
    reservations: MobilityStockReservation[],
    unavailableTimeRange: PeriodDataFragment[]
): boolean => {
    const hasBookingDate = head(
        reservations
            .filter(reservation => dayjs(reservation.bookingPeriod.start).isSame(dayjs(periodState?.start), 'day'))
            .map(reservation => dayjs(reservation.bookingPeriod.start))
            .sort((a, b) => (dayjs(a).isAfter(dayjs(b)) ? -1 : 1))
    );

    if (hasBookingDate) {
        const sortedUnavailableTimeRangeLatest = head(
            unavailableTimeRange.map(time => ({ start: dayjs(time.start), end: dayjs(time.end) })).sort(() => -1)
        );

        const availableBookingBasedOnSystemBlockingTime = dayjs(sortedUnavailableTimeRangeLatest.start).subtract(
            durationNextBooking,
            'hour'
        );
        const hoursAndMinutes = hasBookingDate
            .set('hour', availableBookingBasedOnSystemBlockingTime.hour())
            .set('minute', availableBookingBasedOnSystemBlockingTime.minute());

        const timeDifferent = Math.abs(hasBookingDate.diff(hoursAndMinutes, 'minute'));

        return timeDifferent <= 120;
    }

    return false;
};

export const calculateDisabledMinutes = (
    selectedHours: number,
    disabledTimeSlot: HourMinutesMapping,
    typeTimepicker: DatePickerType,
    startTimeMinute?: number,
    startTimeHour?: number,
    rentalPeriod?: PeriodPayload,
    minimumBookingDays?: number
) => {
    if (selectedHours < 0) {
        return [0, 15, 30, 45];
    }

    if (typeTimepicker === DatePickerType.End && !isNil(rentalPeriod) && minimumBookingDays > 0) {
        /**
         * the reason why we only check `minimumBookingDays` more than 0 days, whenever the user booking is same date
         * start date and end date
         *
         * E.G: 21/07/2023(start date) - 21/07/2023(end date)
         * we should not block any time as the customer already paid for the entire day ( 1 day rental), so should allow
         * user amend any time of the date selected
         */
        const startDate = dayjs(rentalPeriod.start).set('h', startTimeHour).set('m', startTimeMinute);
        const endDate = dayjs(rentalPeriod.end).set('h', selectedHours);

        const minutes = [0, 15, 30, 45];

        const minutesToBlock = minutes.map(minute => {
            const newEndDate = endDate.set('m', minute);

            return newEndDate.diff(startDate, 'minute') <= minimumBookingDays * 24 * 60 && minute;
        });

        return minutesToBlock;
    }

    if (
        typeTimepicker === DatePickerType.End &&
        !isNil(rentalPeriod) &&
        dayjs(rentalPeriod?.start).isSame(dayjs(rentalPeriod?.end), 'day') &&
        startTimeMinute &&
        selectedHours === startTimeHour
    ) {
        return [0, 15, 30, 45].filter(value => value <= startTimeMinute);
    }

    return disabledTimeSlot[selectedHours] ?? [];
};

export const computeClosestMaxDate = (
    start: string | Date,
    reservations: MobilityStockReservation[],
    blockPeriods: StockBlockingPeriodDataFragment[]
) => {
    let maxDate = null;

    const maxReservedDate = start
        ? head(
              reservations
                  .filter(reservation => dayjs(start).isSameOrBefore(dayjs(reservation.bookingPeriod.start), 'day'))
                  .map(reservation => dayjs(reservation.bookingPeriod.start))
                  .sort((a, b) => (a.isAfter(b) ? 1 : -1))
          )
        : null;

    const maxBlockPeriodDate = start
        ? head(
              blockPeriods
                  .filter(block => dayjs(start).isSameOrBefore(dayjs(block.start), 'day'))
                  .map(block => dayjs(block.start))
                  .sort((a, b) => (a.isAfter(b) ? 1 : -1))
          )
        : null;

    if (!isNil(maxReservedDate) && isNil(maxBlockPeriodDate)) {
        maxDate = maxReservedDate;
    } else if (isNil(maxReservedDate) && !isNil(maxBlockPeriodDate)) {
        maxDate = maxBlockPeriodDate;
    } else if (!isNil(maxReservedDate) && !isNil(maxBlockPeriodDate)) {
        maxDate = dayjs(maxReservedDate).isSameOrBefore(maxBlockPeriodDate, 'day')
            ? maxReservedDate
            : maxBlockPeriodDate;
    }

    return maxDate;
};
