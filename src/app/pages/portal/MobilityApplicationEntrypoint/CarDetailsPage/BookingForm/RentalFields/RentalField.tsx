import { Col, Row } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useState, useEffect } from 'react';
import { useLocation } from 'react-router';
import { usePersistentDataListener } from '../../../../../../utilities/usePersistData';
import { MobilityJourneyTemporaryValueType, mobilityJourneyTemporaryKey } from '../../../helper';
import DatePickerType from '../../../shared';
import type { FormValues } from '../shared';
import RentalDateField from './RentalDateField';
import RentalTimeField from './RentalTimeField';
import {
    calculateDisabledTimeRange,
    colSpan,
    RentalFieldProps,
    StyledEndTimePicker,
    StyledStartTimePicker,
    TimePickerContainer,
    rightColSpan,
} from './shared';

// Combination of Rental Day and Rental Time Field
const RentalField = ({ module, stock, application }: RentalFieldProps) => {
    const { unavailableTimeRange } = module;
    const disabledTimeSlot = calculateDisabledTimeRange(unavailableTimeRange);

    const [internalRentalDates, setInternalRentalDates] = useState<{ start: string | Date; end: string | Date }>(
        application?.mobilityBookingDetails.period
    );
    const { state } = useLocation();
    const { values, setFieldValue } = useFormikContext<FormValues>();

    const mobilityJourneyTemporaryValue =
        usePersistentDataListener<MobilityJourneyTemporaryValueType>(mobilityJourneyTemporaryKey);

    useEffect(() => {
        if (
            isNil(state?.rentalPeriod) &&
            (isNil(internalRentalDates) ||
                !dayjs(internalRentalDates.start).isSame(dayjs(values.rentalPeriod?.start), 'second') ||
                !dayjs(internalRentalDates.end).isSame(dayjs(values.rentalPeriod?.end), 'second'))
        ) {
            setInternalRentalDates(values.rentalPeriod);
            setFieldValue('startTime', undefined);
            setFieldValue('endTime', undefined);
        }

        if (isNil(values.rentalPeriod?.start) && isNil(values.rentalPeriod?.end)) {
            setFieldValue('startTime', undefined);
            setFieldValue('endTime', undefined);
        }

        if (
            !dayjs(internalRentalDates?.start).isSame(dayjs(values.rentalPeriod?.start), 'day') ||
            !dayjs(internalRentalDates?.end).isSame(dayjs(values.rentalPeriod?.end), 'day')
        ) {
            setFieldValue('startTime', undefined);
            setFieldValue('endTime', undefined);
        }
    }, [internalRentalDates, setFieldValue, state?.rentalPeriod, values.rentalPeriod]);

    useEffect(() => {
        if (mobilityJourneyTemporaryValue && !isNil(values?.startTime) && !isNil(values?.endTime)) {
            setFieldValue('startTime', values?.startTime);
            setFieldValue('endTime', values?.endTime);
        }
    }, [
        mobilityJourneyTemporaryValue,
        setFieldValue,
        state?.endTime,
        state?.startTime,
        values?.endTime,
        values?.startTime,
    ]);

    return (
        <Row>
            <Col span={24}>
                <RentalDateField
                    application={application}
                    disabledTimeSlot={disabledTimeSlot}
                    module={module}
                    stock={stock}
                />
            </Col>

            <Col span={24}>
                <TimePickerContainer>
                    <StyledStartTimePicker {...colSpan}>
                        <RentalTimeField
                            application={application}
                            disabledTimeSlot={disabledTimeSlot}
                            module={module}
                            name="startTime"
                            stock={stock}
                            typeTimePicker={DatePickerType.Start}
                        />
                    </StyledStartTimePicker>

                    <StyledEndTimePicker {...rightColSpan}>
                        <RentalTimeField
                            application={application}
                            disabled={isNil(values.startTime)}
                            disabledTimeSlot={disabledTimeSlot}
                            module={module}
                            name="endTime"
                            stock={stock}
                            typeTimePicker={DatePickerType.End}
                        />
                    </StyledEndTimePicker>
                </TimePickerContainer>
            </Col>
        </Row>
    );
};

export default RentalField;
