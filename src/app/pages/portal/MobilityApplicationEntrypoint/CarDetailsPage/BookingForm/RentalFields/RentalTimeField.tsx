import type { PickerTimeProps } from 'antd/lib/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useFormikContext } from 'formik';
import { head, isNil, merge, xor } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DateTimeUnit } from '../../../../../../api';
import { useThemeComponents } from '../../../../../../themes/hooks';
import DatePickerType from '../../../shared';
import type { FormValues } from '../shared';
import {
    calculateAvailaleHoursForNextBooking,
    calculateDisabledHoursAndMinutesOfDate,
    calculateDisabledInventoryStockBlockPeriod,
    defaultDisabledHoursAndMinutesOfTheDay,
    getDisabledHoursAndMinutesOfDate,
    RentalFieldProps,
} from './shared';

const RentalTimeField = ({
    module,
    stock,
    application,
    disabledTimeSlot,
    disabled,
    typeTimePicker,
    name,
}: RentalFieldProps) => {
    const { t } = useTranslation(['mobilityVehicleDetails', 'common']);

    const {
        FormFields: { TimePickerField },
    } = useThemeComponents();

    const { values, setFieldValue } = useFormikContext<FormValues>();

    const {
        minimumAdvancedBooking: minimumBooking,
        durationBeforeNextBooking: durationNextBooking,
        availableNumberOfBookingRange,
    } = module;

    const reservedRange = calculateAvailaleHoursForNextBooking(
        stock.reservations,
        minimumBooking,
        durationNextBooking,
        disabledTimeSlot
    );

    const blockPeriodsRange = calculateDisabledInventoryStockBlockPeriod(
        stock.blockPeriod,
        minimumBooking,
        durationNextBooking,
        disabledTimeSlot
    );

    const availableNumberOfBookingHoursMinuteMapping = useMemo(() => {
        if (typeTimePicker === DatePickerType.End && availableNumberOfBookingRange.unit === DateTimeUnit.Hours) {
            const start = dayjs(values?.rentalPeriod?.start);

            const startTimeAddedHoursAvailable = start
                .set('hour', dayjs(values?.startTime).hour())
                .set('minute', dayjs(values?.startTime).minute())
                .set('second', dayjs(values?.startTime).second());

            const end = startTimeAddedHoursAvailable.add(availableNumberOfBookingRange.value, 'hour');
            if (end.isAfter(startTimeAddedHoursAvailable, 'day')) {
                return null;
            }

            return calculateDisabledHoursAndMinutesOfDate(end, end.endOf('day'));
        }

        return null;
    }, [
        availableNumberOfBookingRange.unit,
        availableNumberOfBookingRange.value,
        typeTimePicker,
        values?.rentalPeriod?.start,
        values?.startTime,
    ]);

    const reservedTimeSlotRange = useMemo(
        () => merge(reservedRange, blockPeriodsRange),
        [blockPeriodsRange, reservedRange]
    );
    // by default it should be disabled
    const [disabledTimePicker, setDisabledTimePicker] = useState<boolean>(
        (!isNil(values.rentalPeriod?.end) && !isNil(values.rentalPeriod?.start)) ?? true
    );

    useEffect(() => {
        if (application && typeTimePicker === DatePickerType.End) {
            setDisabledTimePicker(true);
        }
    }, [application, typeTimePicker]);

    const disabledTimeValue = useMemo(() => {
        if (application) {
            const date =
                typeTimePicker === DatePickerType.Start
                    ? dayjs(values.rentalPeriod.start).format('YYYY-MM-DD')
                    : dayjs(values.rentalPeriod.end).format('YYYY-MM-DD');

            const minimumBookingHours = dayjs(application.mobilityBookingDetails.period.end).diff(
                dayjs(application.mobilityBookingDetails.period.start),
                'day'
            );

            const disabledArrayByDate = reservedTimeSlotRange[date];

            return getDisabledHoursAndMinutesOfDate(
                typeTimePicker,
                disabledTimeSlot,
                disabledArrayByDate,
                {},
                availableNumberOfBookingHoursMinuteMapping,
                !isNil(values?.startTime) ? dayjs(values.startTime).hour() : null,
                !isNil(values?.startTime) ? dayjs(values.startTime).minute() : null,
                values.rentalPeriod,
                stock.reservations,
                durationNextBooking,
                minimumBookingHours,
                values.startTime
            );
        }
        // on load when neither of dates chosen
        if (isNil(values?.rentalPeriod)) {
            return defaultDisabledHoursAndMinutesOfTheDay(typeTimePicker, values.rentalPeriod, disabledTimeSlot);
        }

        let date = null;
        date =
            typeTimePicker === DatePickerType.Start
                ? dayjs(values.rentalPeriod.start).format('YYYY-MM-DD')
                : dayjs(values.rentalPeriod.end).format('YYYY-MM-DD');

        const disabledArrayByDate = reservedTimeSlotRange[date];

        if (!isNil(disabledArrayByDate)) {
            return getDisabledHoursAndMinutesOfDate(
                typeTimePicker,
                disabledTimeSlot,
                disabledArrayByDate,
                {},
                availableNumberOfBookingHoursMinuteMapping,
                !isNil(values?.startTime) ? dayjs(values.startTime).hour() : null,
                !isNil(values?.startTime) ? dayjs(values.startTime).minute() : null,
                values.rentalPeriod,
                stock.reservations,
                durationNextBooking
            );
        }

        return typeTimePicker === DatePickerType.Start
            ? defaultDisabledHoursAndMinutesOfTheDay(
                  typeTimePicker,
                  values.rentalPeriod,
                  disabledTimeSlot,
                  availableNumberOfBookingHoursMinuteMapping
              )
            : defaultDisabledHoursAndMinutesOfTheDay(
                  typeTimePicker,
                  values.rentalPeriod,
                  disabledTimeSlot,
                  availableNumberOfBookingHoursMinuteMapping,
                  !isNil(values?.startTime) ? dayjs(values.startTime).hour() : null,
                  !isNil(values?.startTime) ? dayjs(values.startTime).minute() : null
              );
    }, [
        application,
        availableNumberOfBookingHoursMinuteMapping,
        disabledTimeSlot,
        durationNextBooking,
        reservedTimeSlotRange,
        stock.reservations,
        typeTimePicker,
        values.rentalPeriod,
        values.startTime,
    ]);

    const onSelect = useCallback(
        (time: Dayjs) => {
            if (typeTimePicker === DatePickerType.Start) {
                if (isNil(values.startTime)) {
                    const disabledMinutes = disabledTimeValue.disabledMinutes(time.hour());
                    const remainderMinutes = head(xor(disabledMinutes, [0, 15, 30, 45]));
                    const updatedTime = time.set('minute', remainderMinutes);
                    setFieldValue('startTime', updatedTime);
                } else {
                    setFieldValue('endTime', undefined);
                }
            } else if (typeTimePicker === DatePickerType.End) {
                if (isNil(values.endTime)) {
                    const disabledMinutes = disabledTimeValue.disabledMinutes(time.hour());
                    const remainderMinutes = head(xor(disabledMinutes, [0, 15, 30, 45]));
                    const updatedTime = time.set('minute', remainderMinutes);

                    setFieldValue('endTime', updatedTime);
                }
            }
        },
        [disabledTimeValue, setFieldValue, typeTimePicker, values.endTime, values.startTime]
    );

    useEffect(() => {
        setDisabledTimePicker(isNil(values.rentalPeriod?.end) && isNil(values.rentalPeriod?.start));
    }, [values.rentalPeriod?.end, values.rentalPeriod?.start]);
    const disabledTime: PickerTimeProps<Dayjs>['disabledTime'] = useCallback(
        (time: Dayjs) => disabledTimeValue,

        [disabledTimeValue]
    );

    return (
        <TimePickerField
            {...t('mobilityVehicleDetails:fields.timeRental', {
                returnObjects: true,
                type: typeTimePicker === DatePickerType.Start ? 'Start' : 'End',
            })}
            disabled={disabledTimePicker || disabled}
            disabledTime={disabledTime}
            minuteStep={15}
            name={name}
            onSelect={onSelect}
            showNow={false}
            hideDisabledOptions
        />
    );
};

export default RentalTimeField;
