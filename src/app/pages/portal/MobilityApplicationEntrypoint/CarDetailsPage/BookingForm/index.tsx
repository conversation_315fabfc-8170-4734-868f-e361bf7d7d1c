/* eslint-disable max-len */
import { useFormikContext } from 'formik';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { StockPublicDataFragment } from '../../../../../api/fragments/StockPublicData';
import Form from '../../../../../components/fields/Form';

import BookingFields from './BookingFields';

export type BookingFormProps = {
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
    endpoint: MobilityApplicationEntrypointContextDataFragment;
};

const BookingForm = ({ stock, endpoint }: BookingFormProps) => {
    const { handleSubmit } = useFormikContext();

    return (
        <Form id="bookingForm" name="bookingForm" onSubmitCapture={handleSubmit}>
            <BookingFields endpoint={endpoint} stock={stock} />
        </Form>
    );
};

export default BookingForm;
