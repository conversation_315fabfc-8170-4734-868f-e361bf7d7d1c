import { Col, ColProps } from 'antd';
import { isEmpty, isFinite } from 'lodash/fp';
import { useMemo } from 'react';
import { StockPublicDataFragment } from '../../../../../api/fragments/StockPublicData';
import { MobilityAddonSnapshot } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import type { MobilityDetail } from './shared';

export type MobilityDetailFieldProps = {
    isAmend: boolean;
    value: MobilityDetail;
    prefixName: string;
    colSpan: ColProps;
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
};

const MobilityDetailField = ({ isAmend, value, stock, prefixName, colSpan }: MobilityDetailFieldProps) => {
    const {
        FormFields: { SelectField },
    } = useThemeComponents();

    const translatedString = useTranslatedString();
    const { formatAmountWithCurrency } = useCompanyFormats();

    const [label, options] = useMemo(() => {
        if (isAmend) {
            if (isEmpty(value.addon)) {
                return [null, []];
            }

            const addon = JSON.parse(value.addon) as MobilityAddonSnapshot;

            return [
                translatedString(addon.title),
                [
                    {
                        label: `${translatedString(addon.option.name)}${
                            isFinite(addon.option.price) ? ` (+${formatAmountWithCurrency(addon.option.price)})` : ''
                        }`,
                        value: value.addon,
                    },
                ],
            ];
        }

        const mobility = stock.mobilities.find(
            mobility => mobility.id === value.id && mobility.__typename === 'MobilityAddon'
        );

        if (mobility?.__typename !== 'MobilityAddon') {
            return [null, []];
        }

        const options = mobility.options.map(option => ({
            label: `${translatedString(option.name)}${
                mobility.isChargeable ? ` (+${formatAmountWithCurrency(option.price)})` : ''
            }`,
            value: JSON.stringify(option),
        }));

        return [translatedString(mobility.title), options];
    }, [formatAmountWithCurrency, isAmend, stock.mobilities, translatedString, value]);

    return (
        <Col {...colSpan}>
            <SelectField disabled={isAmend} label={label} name={`${prefixName}.addon`} options={options} />
        </Col>
    );
};

export default MobilityDetailField;
