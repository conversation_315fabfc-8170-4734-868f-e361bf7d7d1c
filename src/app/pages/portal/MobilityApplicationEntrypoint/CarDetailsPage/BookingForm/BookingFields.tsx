/* eslint-disable max-len */
import { InfoCircleFilled } from '@ant-design/icons';
import { Col, Divider, Row, Popover } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { FieldArray, useFormikContext } from 'formik';
import { uniq } from 'lodash';
import { getOr, isNil, sum, isEmpty } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import styled from 'styled-components';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { PeriodDataFragment } from '../../../../../api/fragments/PeriodData';
import { StockPublicDataFragment } from '../../../../../api/fragments/StockPublicData';
import { MobilityDetailsAddonPayload, MobilityStockReservation, PeriodPayload } from '../../../../../api/types';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../../../themes/hooks';
import renderMarkdown from '../../../../../utilities/renderMarkdown';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import { useExtraDataContext } from '../ExtraDataContext';
import MobilityDetailField from './MobilityDetailField';
import RentalField from './RentalFields/RentalField';
import type { FormValues, MobilityDetail } from './shared';

export type BookingFieldsProps = {
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
    endpoint: MobilityApplicationEntrypointContextDataFragment;
};

const VehicleInfo = styled.div`
    margin-bottom: 24px;
`;

const VehicleName = styled.div`
    font-size: 28px;
    font-weight: bold;
`;

const StyledDivider = styled(Divider)`
    margin: 12px 0;
    border-color: #cecece;
`;

const TotalAmountContent = styled.div`
    font-size: 16px;
    font-weight: bold;

    &:last-child {
        margin-left: 10px;
    }
`;

const RentalDisclaimer = styled.div`
    color: #666666;
    font-size: 14px;
`;

const Container = styled.div`
    margin-bottom: 50px;
`;

const getRentalDays = (start: string | Date, end: string | Date) => {
    if (isNil(start) || isNil(end)) {
        return 0;
    }

    const beginning = dayjs(start).startOf('minute');
    const termination = dayjs(end).startOf('minute');

    if (!beginning.isValid() || !termination.isValid()) {
        return null;
    }

    return Math.ceil(Math.ceil(termination.diff(beginning, 'hour', true)) / 24);
};

const getConcatenateDateWithTime = (rentalPeriod: PeriodPayload, startTime: string | Date, endTime: string | Date) => {
    const start = dayjs(rentalPeriod.start);
    const convertedStartTime = dayjs(startTime);
    const concatStart = start.set('hour', convertedStartTime.hour()).set('minute', convertedStartTime.minute());
    const end = dayjs(rentalPeriod.end);
    const convertedEndTime = dayjs(endTime);
    const concatEnd = end.set('hour', convertedEndTime.hour()).set('minute', convertedEndTime.minute());

    return { start: concatStart, end: concatEnd };
};

export const disabledTimeRange = (unavailableTimeRange: PeriodDataFragment[]) => {
    const unavailableSlot: Record<number, number[]> = {};

    unavailableTimeRange.forEach(time => {
        let startTime = dayjs(time.start);
        const endTime = dayjs(time.end);

        // only block hours and minutes before the end the time
        while (startTime.isSameOrBefore(endTime, 'second')) {
            const hour = startTime.hour();
            const minute = startTime.minute();

            if (!unavailableSlot[hour]) {
                unavailableSlot[hour] = [minute];
            } else {
                unavailableSlot[hour] = [...unavailableSlot[hour], minute];
            }

            startTime = startTime.add(15, 'minute');
        }
    });

    return unavailableSlot;
};

export const getDisabledHoursAndMinutes = (start: Dayjs, end: Dayjs) => {
    const reservedHourAndMinutesTimeSlot: Record<string, number[]> = {};
    let startTime = start;

    while (
        start.format('YYYY-MM-DD') === startTime.format('YYYY-MM-DD') &&
        dayjs(startTime).isSameOrBefore(end, 'second')
    ) {
        const hour = startTime.hour();
        const minute = startTime.minute();

        if (!reservedHourAndMinutesTimeSlot[hour]) {
            reservedHourAndMinutesTimeSlot[hour] = [minute];
        } else {
            reservedHourAndMinutesTimeSlot[hour] = uniq([...reservedHourAndMinutesTimeSlot[hour], minute]);
        }

        startTime = startTime.add(15, 'minute');
    }

    return reservedHourAndMinutesTimeSlot;
};

export const disabledReservationTimeSlot = (
    reservations: Omit<MobilityStockReservation, 'applicationId'>[],
    durationBeforeNextBooking: number,
    minimumBookingHour: number,
    unavailableTimeRange: PeriodDataFragment[]
) => {
    const reservedTimeSlot: Record<string, Record<number, number[]>> = {};
    // when onload the datepicker, we should block the hours and time based on date start off until system current time
    const today = dayjs();
    const todayAddedDurationNextBooking = today.add(minimumBookingHour, 'hour');
    const todayStartOff = today.startOf('day');
    // update into the disabled today time
    reservedTimeSlot[today.format('YYYY-MM-DD')] = getDisabledHoursAndMinutes(
        todayStartOff,
        todayAddedDurationNextBooking
    );
    const disableTimeRange = disabledTimeRange(unavailableTimeRange);

    reservations.forEach(reserve => {
        const { bookingPeriod } = reserve;
        const { end, start } = bookingPeriod;
        let startTime = dayjs(start).subtract(durationBeforeNextBooking, 'hour');
        const endTime = dayjs(end).add(durationBeforeNextBooking, 'hour');
        while (startTime.isSameOrBefore(endTime, 'second')) {
            const date = startTime.format('YYYY-MM-DD');

            if (isNil(reservedTimeSlot[date])) {
                reservedTimeSlot[date] = {
                    ...disableTimeRange,
                    ...getDisabledHoursAndMinutes(startTime, endTime),
                };
            } else {
                // update the existing disabling time
                reservedTimeSlot[date] = {
                    ...disableTimeRange,
                    ...getDisabledHoursAndMinutes(startTime, endTime),
                    ...reservedTimeSlot[date],
                };
            }

            const newDate = startTime.add(1, 'day').format('YYYY-MM-DD');
            startTime = dayjs(newDate);
        }
    });

    return reservedTimeSlot;
};

const BookingFields = ({ stock, endpoint }: BookingFieldsProps) => {
    const { t } = useTranslation(['mobilityVehicleDetails', 'carList']);
    const {
        FormFields: { SelectField },
    } = useThemeComponents();
    const translatedString = useTranslatedString();
    const { formatAmountWithCurrency } = useCompanyFormats();

    const company = useCompany(true);

    const { application, token, isCancel } = useExtraDataContext();
    const { state } = useLocation();
    const { FormFields } = useThemeComponents();

    const isAmend = !!application && !!token;
    const { values } = useFormikContext<FormValues>();

    const fixedRentalDays = useMemo(() => {
        if (application) {
            const {
                mobilityBookingDetails: { period },
            } = application;

            return getRentalDays(period.start, period.end);
        }

        if (!isNil(values.rentalPeriod) && !isNil(values.startTime) && !isNil(values.endTime)) {
            const concatDate = getConcatenateDateWithTime(values.rentalPeriod, values.startTime, values.endTime);

            return getRentalDays(concatDate.start.toDate(), concatDate.end.toDate());
        }

        return !isNil(state?.rentalPeriod?.start) && !isNil(state?.rentalPeriod?.end)
            ? getRentalDays(state.rentalPeriod?.start, state.rentalPeriod?.end)
            : null;
    }, [
        application,
        state?.rentalPeriod?.start,
        state?.rentalPeriod?.end,
        values.endTime,
        values.rentalPeriod,
        values.startTime,
    ]);

    const locationOptions = useMemo(
        () =>
            [
                ...endpoint.mobilityApplicationModule.locations.map(location => ({
                    label: location.name,
                    value: JSON.stringify(location),
                })),
                endpoint.mobilityApplicationModule.homeDelivery.isEnable && {
                    label: t('common:homeDelivery'),
                    value: JSON.stringify({
                        ...endpoint.mobilityApplicationModule.homeDelivery,
                        isHomeDelivery: true,
                    }),
                },
            ].filter(Boolean),
        [endpoint, t]
    );

    const disabledTimeSlot = disabledTimeRange(endpoint.mobilityApplicationModule.unavailableTimeRange);
    const stockCarPrice = useMemo(() => {
        if (stock.inventory.__typename === 'MobilityInventory') {
            return stock.price || stock.inventory.variant.vehiclePrice;
        }

        throw new Error('Inventory type is not supported');
    }, [stock]);

    const total = useMemo(() => {
        if (application) {
            const {
                mobilityBookingDetails: { inventoryStockPrice },
                mobilitySnapshots,
            } = application;

            return sum([
                inventoryStockPrice * fixedRentalDays,
                ...mobilitySnapshots.map(snapshot =>
                    snapshot.__typename === 'MobilityAddonSnapshot' ? snapshot.option.price : 0
                ),
            ]);
        }

        let value = 0;

        if (
            values.rentalPeriod?.start &&
            values.rentalPeriod?.end &&
            !isNil(values.startTime) &&
            !isNil(values.endTime) &&
            stock.inventory.__typename === 'MobilityInventory'
        ) {
            const concatDate = getConcatenateDateWithTime(values.rentalPeriod, values.startTime, values.endTime);

            const days = getRentalDays(concatDate.start.toDate(), concatDate.end.toDate());

            value += stockCarPrice * days;
        }

        values.mobilityDetails.forEach(detail => {
            if (detail.addon) {
                const addon: MobilityDetailsAddonPayload = JSON.parse(detail.addon);

                value += addon.price || 0;
            }
        });

        return value;
    }, [
        application,
        values.rentalPeriod,
        values.startTime,
        values.endTime,
        values.mobilityDetails,
        stock.inventory.__typename,
        fixedRentalDays,
        stockCarPrice,
    ]);

    if (stock.inventory.__typename !== 'MobilityInventory') {
        return null;
    }

    const disclaimer = useMemo(() => {
        const data = endpoint.mobilityApplicationModule.rentalDisclaimer.overrides.find(
            override =>
                stock.inventory.__typename === 'MobilityInventory' && override.dealerId === stock.inventory.dealer.id
        );

        if (isEmpty(data?.value.defaultValue)) {
            if (isEmpty(endpoint.mobilityApplicationModule.rentalDisclaimer.defaultValue.defaultValue)) {
                return '';
            }

            return translatedString(endpoint.mobilityApplicationModule.rentalDisclaimer.defaultValue);
        }

        return translatedString(data.value);
    }, [
        endpoint.mobilityApplicationModule.rentalDisclaimer.defaultValue,
        endpoint.mobilityApplicationModule.rentalDisclaimer.overrides,
        stock.inventory.__typename,
        stock.inventory.dealer.id,
        translatedString,
    ]);

    const isBookingCodeVisible = useMemo(() => {
        if (isAmend || isCancel) {
            return false;
        }

        const data = endpoint.mobilityApplicationModule.bookingCode.overrides.find(
            override =>
                stock.inventory.__typename === 'MobilityInventory' && override.dealerId === stock.inventory.dealer.id
        );

        if (!isNil(data)) {
            return data?.viewable ?? false;
        }

        return endpoint.mobilityApplicationModule.bookingCode?.viewable ?? false;
    }, [
        endpoint.mobilityApplicationModule.bookingCode.overrides,
        endpoint.mobilityApplicationModule.bookingCode?.viewable,
        isAmend,
        isCancel,
        stock.inventory.__typename,
        stock.inventory.dealer.id,
    ]);

    const [bookingCodeLabel, bookingCodeInputProps] = useMemo(() => {
        const { label, tooltips, placeholder } = t('mobilityVehicleDetails:fields.bookingCode', {
            returnObjects: true,
        }) as {
            label: string;
            tooltips?: {
                [countryCode: string]: string;
            };
            placeholder: string;
        };

        const tooltip = tooltips?.[company.countryCode] ?? tooltips?.default;

        return [
            <>
                {label}
                &nbsp;
                <Popover content={tooltip} trigger="hover">
                    <InfoCircleFilled />
                </Popover>
            </>,
            { placeholder },
        ];
    }, [company?.countryCode, t]);

    return (
        <Container>
            <Row gutter={14}>
                <Col span={24}>
                    <VehicleInfo>
                        <VehicleName>{translatedString(stock.inventory.variant.name)}</VehicleName>
                        {t('carList:carPricePerDay', {
                            amount: formatAmountWithCurrency(stockCarPrice),
                        })}
                    </VehicleInfo>
                </Col>
                <Col span={24}>
                    <RentalField
                        application={application}
                        disabledTimeSlot={disabledTimeSlot}
                        module={endpoint.mobilityApplicationModule}
                        stock={stock}
                    />
                </Col>
                <Col span={24}>
                    <SelectField
                        {...t('mobilityVehicleDetails:fields.location', { returnObjects: true })}
                        name="location"
                        options={locationOptions}
                    />
                </Col>
                {isBookingCodeVisible && (
                    <Col span={24}>
                        <FormFields.InputField {...bookingCodeInputProps} label={bookingCodeLabel} name="bookingCode" />
                    </Col>
                )}
                <FieldArray
                    name="mobilityDetails"
                    render={() =>
                        getOr([], 'mobilityDetails', values).map((value: MobilityDetail, index: number) => (
                            <MobilityDetailField
                                key={`mobilityDetails.${index.toString()}`}
                                colSpan={{ span: 24 }}
                                isAmend={isAmend}
                                prefixName={`mobilityDetails.[${index}]`}
                                stock={stock}
                                value={value}
                            />
                        ))
                    }
                />
                <Col span={24}>
                    <>
                        <TotalAmountContent>{t('mobilityVehicleDetails:totalAmount.label')}</TotalAmountContent>
                        <TotalAmountContent>
                            {t('mobilityVehicleDetails:totalAmount.value', { value: formatAmountWithCurrency(total) })}
                        </TotalAmountContent>
                        <StyledDivider />
                    </>
                </Col>
                <Col span={24}>
                    <RentalDisclaimer>{renderMarkdown(disclaimer)}</RentalDisclaimer>
                </Col>
            </Row>
        </Container>
    );
};

export default BookingFields;
