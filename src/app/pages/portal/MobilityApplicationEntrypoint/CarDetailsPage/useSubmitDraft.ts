/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { omit } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { MobilityLocationDataFragment } from '../../../../api/fragments/MobilityLocationData';
import {
    DraftMobilityApplicationDocument,
    DraftMobilityApplicationMutation,
    DraftMobilityApplicationMutationVariables,
} from '../../../../api/mutations/draftMobilityApplication';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import getExpirationDuration from '../../../../utilities/journeys/getExpirationDuration';
import useHandleError from '../../../../utilities/useHandleError';
import { usePersistentData } from '../../../../utilities/usePersistData';
import {
    MobilityJourneyTemporaryValueType,
    mobilityJourneyTemporaryKey,
    retrieveMobilityJourneyValue,
} from '../helper';
import type { FormValues } from './BookingForm/shared';

const useSubmitDraft = (
    stockId: string,
    endpoint: MobilityApplicationEntrypointContextDataFragment,
    stockIdentifier: string,
    variantName: string,
    temporaryValue?: MobilityJourneyTemporaryValueType
) => {
    const { t } = useTranslation(['carDetails']);
    const navigate = useNavigate();
    const apolloClient = useApolloClient();
    const { currentLanguageId } = useLanguage();
    const { notification } = useThemeComponents();
    const { sessionTimeout } = useCompany();

    const { setItem } = usePersistentData();

    return useHandleError<FormValues>(
        async values => {
            notification.loading({
                content: t('carDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            // concatenate the date and time into the API argument;
            const startDate = dayjs(values.rentalPeriod.start)
                .set('hour', dayjs(values.startTime).hour())
                .set('minute', dayjs(values.startTime).minute());
            const endDate = dayjs(values.rentalPeriod.end)
                .set('hour', dayjs(values.endTime).hour())
                .set('minute', dayjs(values.endTime).minute());
            const period = {
                start: startDate.toDate(),
                end: endDate.toDate(),
            };

            // which contain assignee id, later we not need that
            // and detect home delivery too
            const rawLocation = JSON.parse(values.location) as MobilityLocationDataFragment & {
                isHomeDelivery?: boolean;
            };

            const pickup = {
                ...omit(
                    ['assignee', 'assigneeId', 'isHomeDelivery', 'isEnable', 'moduleName', 'moduleId'],
                    rawLocation
                ),
                address: rawLocation.address,
                email: rawLocation.email,
                phone: rawLocation.phone,
                url: rawLocation.url,
                name: rawLocation.name,
            };

            const { data } = await apolloClient
                .mutate<DraftMobilityApplicationMutation, DraftMobilityApplicationMutationVariables>({
                    mutation: DraftMobilityApplicationDocument,
                    variables: {
                        moduleId: endpoint.mobilityApplicationModule.id,
                        customer: { newLocalCustomer: { fields: [] } },
                        endpointId: endpoint.id,
                        mobilityDetails: values.mobilityDetails.map(detail => ({
                            ...detail,
                            addon: JSON.parse(detail.addon),
                        })),
                        mobilityBookingDetails: {
                            inventoryStockId: stockId,
                            location: {
                                pickup: !rawLocation.isHomeDelivery ? pickup : null,
                                homeDelivery: rawLocation.isHomeDelivery ? pickup : null,
                                isHomeDelivery: rawLocation?.isHomeDelivery ?? false,
                            },
                            period,
                            bookingCode: values.bookingCode,
                        },
                        languageId: currentLanguageId,
                    },
                })
                .finally(() => {
                    notification.destroy('primary');
                });

            // Before go to next journey, set the state first
            const expireAt =
                data.result.application.__typename === 'MobilityApplication' ? data.result.application.expireAt : null;
            const updatedTemporaryValue: MobilityJourneyTemporaryValueType = {
                ...temporaryValue,
                token: data.result.token,
                expireAt,
                bookingDetails: {
                    period: {
                        start: values.rentalPeriod.start,
                        end: values.rentalPeriod.end,
                        startTime: values.startTime,
                        endTime: values.endTime,
                    },
                    inventoryStockId: stockId,
                    stockIdentifier,
                    variantName,
                    location: values.location,
                },
                draftCreated: true,
            };

            const { sessionId, value } = retrieveMobilityJourneyValue(updatedTemporaryValue);

            setItem(
                mobilityJourneyTemporaryKey,
                value,
                expireAt ? getExpirationDuration(expireAt) : sessionTimeout * 60,
                sessionId
            );

            // go to the journey
            navigate('../apply', { state: { token: data.result.token } });
        },
        [
            apolloClient,
            currentLanguageId,
            endpoint.id,
            endpoint.mobilityApplicationModule.id,
            navigate,
            notification,
            sessionTimeout,
            setItem,
            stockId,
            stockIdentifier,
            t,
            temporaryValue,
            variantName,
        ],
        {}
    );
};

export default useSubmitDraft;
