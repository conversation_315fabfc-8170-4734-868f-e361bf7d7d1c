import { Col, Grid, Row } from 'antd';
import { Formik } from 'formik';
import { isEmpty, isEqualWith, isNil, pick } from 'lodash/fp';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { StockPublicDataFragment } from '../../../../api/fragments/StockPublicData';
import { MobilityKind } from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
import { computeMobilityGTMData, MobilityGTMEvents } from '../../../../utilities/googleTagManager';
import { usePersistentData, usePersistentDataListener } from '../../../../utilities/usePersistData';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import JourneyToolbar from '../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import Stepper, { MobilityPageKind, AvailableSteps as MobilitySteps } from '../Components/Stepper';
import { MobilityJourneyTemporaryValueType, mobilityJourneyTemporaryKey } from '../helper';
import BookingForm from './BookingForm';
import type { FormValues, MobilityDetail } from './BookingForm/shared';
import useValidator from './BookingForm/useValidator';
import BookingInformations from './BookingInformations';
import { useExtraDataContext } from './ExtraDataContext';
import VehicleImages from './VehicleImages';
import useAmendMobilityApplication from './useAmendMobilityApplication';
import useCancelMobilityApplication from './useCancelMobilityApplication';
import useSubmitDraft from './useSubmitDraft';
import useSubmitGiftVoucher from './useSubmitGiftVoucher';
import useUpdateMobilityApplicationDraft from './useUpdateMobilityApplicationDraft';

const FooterContainer = styled.div`
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;

    .p-button {
        line-height: 1;
    }
`;

const LeftButtonWrapper = styled.div`
    flex-grow: 0;
    overflow: hidden;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    & > .ant-btn {
        overflow: hidden;
        span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
`;

const RightButtonWrapper = styled.div`
    display: flex;
    flex-direction: row;
    flex-grow: 1;

    column-gap: 8px;
`;

const Container = styled.div`
    & .ant-pro-page-container-children-content {
        margin: 0;

        @media screen and (min-width: ${breakpoints.md}) {
            margin: 24px 60px 0 60px;
        }
    }
`;

const BookingContainer = styled.div`
    padding: 0 24px;

    @media screen and (min-width: ${breakpoints.md}) {
        padding: 0;
    }
`;

const isEqual = isEqualWith((one, other, key) => {
    if (key !== '__typename') {
        return undefined;
    }

    return true;
});

type InnerProps = {
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
    endpoint: MobilityApplicationEntrypointContextDataFragment;
    isCancel?: boolean;
};

const Inner = ({ stock, endpoint, isCancel = false }: InnerProps) => {
    const navigate = useNavigate();
    const translated = useTranslatedString();
    const screens = Grid.useBreakpoint();
    const { t } = useTranslation(['carDetails', 'mobilityVehicleDetails']);
    const { Button, MobilityWebpageLayout, BackButton } = useThemeComponents();
    const { layout } = useRouter();

    const { application, token, isAmend } = useExtraDataContext();
    const { currentLanguageId } = useLanguage();

    const draftGiftVoucher = useSubmitGiftVoucher(stock, endpoint, currentLanguageId);

    const { getSessionId } = usePersistentData();
    const mobilityJourneyTemporaryValue =
        usePersistentDataListener<MobilityJourneyTemporaryValueType>(mobilityJourneyTemporaryKey);

    const persistedSessionId = useMemo(() => getSessionId(mobilityJourneyTemporaryKey), [getSessionId]);
    const { state } = useLocation();
    const isOnlyOneVariant = useMemo(
        () => !!state?.isOnlyOneVariant && state?.isOnlyOneVariant,
        [state?.isOnlyOneVariant]
    );

    const isBookingCodeVisible = useMemo(() => {
        // Not applicable for amend or cancellation
        if (isAmend || state?.isCancel) {
            return false;
        }

        const data = endpoint.mobilityApplicationModule.bookingCode.overrides.find(
            override =>
                stock.inventory.__typename === 'MobilityInventory' && override.dealerId === stock.inventory.dealer.id
        );

        if (!isNil(data)) {
            return data?.viewable ?? false;
        }

        return endpoint.mobilityApplicationModule.bookingCode?.viewable ?? false;
    }, [
        endpoint.mobilityApplicationModule.bookingCode.overrides,
        endpoint.mobilityApplicationModule.bookingCode?.viewable,
        isAmend,
        state?.isCancel,
        stock.inventory,
    ]);

    const validate = useValidator(isBookingCodeVisible);
    const submitDraft = useSubmitDraft(
        stock.id,
        endpoint,
        stock.identifier,
        stock.inventory.__typename === 'MobilityInventory' && translated(stock.inventory.variant.name),
        mobilityJourneyTemporaryValue
    );
    const amendMobilityApplication = useAmendMobilityApplication(token);
    const cancelMobilityApplication = state?.isCancel && useCancelMobilityApplication(token);
    const updateMobilityApplicationDraft = useUpdateMobilityApplicationDraft(mobilityJourneyTemporaryValue);
    // only trigger when new session
    useEffect(() => {
        if (
            mobilityJourneyTemporaryValue?.sessionId !== persistedSessionId &&
            stock?.__typename === 'MobilityStockInventory' &&
            stock.inventory.__typename === 'MobilityInventory'
        ) {
            const data = computeMobilityGTMData(
                MobilityGTMEvents.BookingDetailsView,
                translated(stock.inventory.variant.name),
                stock.identifier
            );

            window.dataLayer?.push(data);
        }
    }, [
        mobilityJourneyTemporaryValue?.sessionId,
        persistedSessionId,
        stock?.__typename,
        stock?.inventory.__typename,
        stock.identifier,
        stock.inventory,
        translated,
    ]);

    const vehicleImages = useMemo(() => {
        if (stock?.__typename !== 'MobilityStockInventory' || stock.inventory.__typename !== 'MobilityInventory') {
            return null;
        }

        if (stock.images && !isEmpty(stock.images)) {
            return stock.images.map(image => image.url);
        }

        if (stock.inventory.variant.images && !isEmpty(stock.inventory.variant.images)) {
            return stock.inventory.variant.images.map(image => image.url);
        }

        return [];
    }, [stock]);

    const onSubmit = useMemo(() => {
        if (state?.isCancel) {
            return cancelMobilityApplication;
        }

        if (isAmend) {
            return amendMobilityApplication;
        }

        if (mobilityJourneyTemporaryValue?.draftCreated) {
            return updateMobilityApplicationDraft;
        }

        return submitDraft;
    }, [
        state?.isCancel,
        isAmend,
        mobilityJourneyTemporaryValue?.draftCreated,
        submitDraft,
        cancelMobilityApplication,
        amendMobilityApplication,
        updateMobilityApplicationDraft,
    ]);

    const initialValues: FormValues = useMemo(() => {
        if (application) {
            const location =
                application.mobilityBookingDetails.location.__typename === 'MobilityBookingLocationHome'
                    ? { ...endpoint.mobilityApplicationModule.homeDelivery, isHomeDelivery: true }
                    : endpoint.mobilityApplicationModule.locations.find(item =>
                          isEqual(item.id, application.mobilityBookingDetails.location.id)
                      );

            return {
                location: location ? JSON.stringify(location) : null,
                rentalPeriod: application.mobilityBookingDetails.period,
                mobilityDetails: application.mobilitySnapshots
                    .map(snapshot =>
                        snapshot.__typename === 'MobilityAddonSnapshot'
                            ? { id: snapshot.mobilityId, kind: MobilityKind.Addon, addon: JSON.stringify(snapshot) }
                            : null
                    )
                    .filter(Boolean),
                startTime: application.mobilityBookingDetails.period.start,
                endTime: application.mobilityBookingDetails.period.end,
            };
        }

        const mobilityDetails: MobilityDetail[] = stock.mobilities
            .filter(mobility => mobility.__typename === 'MobilityAddon')
            .sort((a, b) => a.order - b.order)
            .map(mobility => ({
                ...pick(['id', 'kind'], mobility),
                addon:
                    mobility.__typename === 'MobilityAddon' && mobility.options.length > 0
                        ? JSON.stringify(mobility.options[0])
                        : null,
            }));

        const locationState =
            endpoint.mobilityApplicationModule.locations.length === 1
                ? JSON.stringify(endpoint.mobilityApplicationModule.locations[0])
                : null;

        const rentalPeriodState =
            !isNil(state?.rentalPeriod?.start) && !isNil(state?.rentalPeriod?.end)
                ? { start: state.rentalPeriod.start, end: state.rentalPeriod.end }
                : null;

        if (mobilityJourneyTemporaryValue) {
            const tempRentalPeriod =
                !isNil(mobilityJourneyTemporaryValue.bookingDetails.period?.start) &&
                !isNil(mobilityJourneyTemporaryValue.bookingDetails.period?.end)
                    ? {
                          start: mobilityJourneyTemporaryValue.bookingDetails.period.start,
                          end: mobilityJourneyTemporaryValue.bookingDetails.period.end,
                      }
                    : null;

            return {
                location: mobilityJourneyTemporaryValue.bookingDetails.location || locationState,
                rentalPeriod: tempRentalPeriod || rentalPeriodState,
                mobilityDetails,
                startTime: !isNil(mobilityJourneyTemporaryValue.bookingDetails.period?.start)
                    ? mobilityJourneyTemporaryValue.bookingDetails.period?.startTime
                    : undefined,
                endTime: !isNil(mobilityJourneyTemporaryValue.bookingDetails.period?.end)
                    ? mobilityJourneyTemporaryValue.bookingDetails.period?.endTime
                    : undefined,
            };
        }

        return {
            location: locationState,
            rentalPeriod: rentalPeriodState,
            mobilityDetails,
            startTime: !isNil(state?.rentalPeriod?.start) ? state?.rentalPeriod?.startTime : undefined,
            endTime: !isNil(state?.rentalPeriod?.end) ? state?.rentalPeriod?.endTime : undefined,
            bookingCode: '',
        };
    }, [
        application,
        stock.mobilities,
        endpoint.mobilityApplicationModule.locations,
        endpoint.mobilityApplicationModule.homeDelivery,
        state?.rentalPeriod?.start,
        state?.rentalPeriod?.end,
        state?.rentalPeriod?.startTime,
        state?.rentalPeriod?.endTime,
        mobilityJourneyTemporaryValue,
    ]);

    const draftGiftVoucherCallback = useCallback(async () => {
        await draftGiftVoucher();
    }, [draftGiftVoucher]);

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ isSubmitting }) => (
                <Container>
                    <FormAutoTouch />
                    <MobilityWebpageLayout
                        backIcon={null} // To hide the back button in header
                        preferredMobileFooterHeight={75}
                        title={t('mobilityVehicleDetails:title')}
                        hasFooterBar
                    >
                        <Stepper currentPage={MobilityPageKind.Mobility} currentStep={MobilitySteps.BookingDetails} />
                        <Row gutter={[{ xs: 0, sm: 0, md: 24 }, 24]}>
                            <Col md={14} xs={24}>
                                <VehicleImages sources={vehicleImages} />
                                {screens.md && <BookingInformations stock={stock} />}
                            </Col>
                            <Col md={10} xs={24}>
                                <BookingContainer>
                                    <BookingForm endpoint={endpoint} stock={stock} />
                                    {!screens.md && <BookingInformations stock={stock} />}
                                </BookingContainer>
                            </Col>
                        </Row>

                        <JourneyToolbar>
                            <FooterContainer>
                                {endpoint.mobilityApplicationModule.giftVoucherModuleId && (
                                    <LeftButtonWrapper>
                                        <Button
                                            onClick={draftGiftVoucherCallback}
                                            porscheFallbackIcon="arrow-right"
                                            porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                                            type="link"
                                            isCompact
                                            showIcon
                                        >
                                            {t('carDetails:actions.giftVoucher')}
                                        </Button>
                                    </LeftButtonWrapper>
                                )}

                                <RightButtonWrapper>
                                    {!isCancel && !isAmend && !isOnlyOneVariant && (
                                        <BackButton
                                            key="back"
                                            form="bookingForm"
                                            htmlType="button"
                                            onClick={() => navigate('..')}
                                            porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                                            type="tertiary"
                                        >
                                            {t('carDetails:actions.back')}
                                        </BackButton>
                                    )}
                                    <Button
                                        key="submit"
                                        disabled={isSubmitting}
                                        form="bookingForm"
                                        htmlType="submit"
                                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                                        type="primary"
                                    >
                                        {isCancel ? t('carDetails:actions.cancel') : t('carDetails:actions.next')}
                                    </Button>
                                </RightButtonWrapper>
                            </FooterContainer>
                        </JourneyToolbar>
                    </MobilityWebpageLayout>
                </Container>
            )}
        </Formik>
    );
};

export default Inner;
