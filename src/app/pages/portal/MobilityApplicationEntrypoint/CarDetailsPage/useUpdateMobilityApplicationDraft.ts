import { useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { omit } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MobilityLocationDataFragment } from '../../../../api/fragments/MobilityLocationData';
import {
    UpdateMobilityApplicationDraftDocument,
    UpdateMobilityApplicationDraftMutation,
    UpdateMobilityApplicationDraftMutationVariables,
} from '../../../../api/mutations/updateMobilityApplicationDraft';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import getExpirationDuration from '../../../../utilities/journeys/getExpirationDuration';
import useHandleError from '../../../../utilities/useHandleError';
import { usePersistentData } from '../../../../utilities/usePersistData';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import {
    MobilityJourneyTemporaryValueType,
    mobilityJourneyTemporaryKey,
    retrieveMobilityJourneyValue,
} from '../helper';
import type { FormValues } from './BookingForm/shared';

const useUpdateMobilityApplicationDraft = (temporaryValue: MobilityJourneyTemporaryValueType) => {
    const apolloClient = useApolloClient();
    const { t } = useTranslation('carDetails');
    const navigate = useNavigate();
    const { notification } = useThemeComponents();
    const { setItem } = usePersistentData();
    const { sessionTimeout } = useCompany();

    return useHandleError<FormValues>(
        async ({ rentalPeriod, location, endTime, startTime, mobilityDetails }) => {
            // concatenate the date and time into the API argument;
            const startDate = dayjs(rentalPeriod.start)
                .set('hour', dayjs(startTime).hour())
                .set('minute', dayjs(startTime).minute());
            const endDate = dayjs(rentalPeriod.end)
                .set('hour', dayjs(endTime).hour())
                .set('minute', dayjs(endTime).minute());
            const period = {
                start: startDate.toDate(),
                end: endDate.toDate(),
            };

            notification.loading({
                content: t('carDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            // which contain assignee id, later we not need that
            // and detect home delivery too
            const rawLocation = JSON.parse(location) as MobilityLocationDataFragment & {
                isHomeDelivery?: boolean;
            };
            const pickup = {
                ...omit(
                    ['assignee', 'assigneeId', 'isHomeDelivery', 'isEnable', 'moduleName', 'moduleId'],
                    rawLocation
                ),
                address: rawLocation.address,
                email: rawLocation.email,
                phone: rawLocation.phone,
                url: rawLocation.url,
                name: rawLocation.name,
            };

            const { data } = await apolloClient
                .mutate<UpdateMobilityApplicationDraftMutation, UpdateMobilityApplicationDraftMutationVariables>({
                    mutation: UpdateMobilityApplicationDraftDocument,
                    variables: {
                        token: temporaryValue?.token,
                        mobilityDetails: mobilityDetails.map(detail => ({
                            ...detail,
                            addon: JSON.parse(detail.addon),
                        })),
                        mobilityBookingDetails: {
                            inventoryStockId: temporaryValue.bookingDetails.inventoryStockId,
                            location: {
                                pickup: !rawLocation.isHomeDelivery ? pickup : null,
                                homeDelivery: rawLocation.isHomeDelivery ? pickup : null,
                                isHomeDelivery: rawLocation?.isHomeDelivery ?? false,
                            },
                            period,
                        },
                    },
                })
                .finally(() => {
                    notification.destroy('primary');
                });

            // Before go to next journey, set the state first
            const expireAt =
                data.result.application.__typename === 'MobilityApplication' ? data.result.application.expireAt : null;
            if (temporaryValue) {
                const updatedTemporaryValue: MobilityJourneyTemporaryValueType = {
                    ...temporaryValue,
                    token: data.result.token,
                    expireAt,
                    bookingDetails: {
                        period: {
                            start: rentalPeriod.start,
                            end: rentalPeriod.end,
                            startTime,
                            endTime,
                        },
                        inventoryStockId: temporaryValue.bookingDetails.inventoryStockId,
                        location,
                    },
                };

                const { sessionId, value } = retrieveMobilityJourneyValue(updatedTemporaryValue);

                setItem(
                    mobilityJourneyTemporaryKey,
                    value,
                    expireAt ? getExpirationDuration(expireAt) : sessionTimeout * 60,
                    sessionId
                );
            }

            navigate('../apply', {
                state: { token: data.result.token, isAmend: true, stage: JourneyStage.ApplicantKYC },
            });
        },
        [notification, t, apolloClient, temporaryValue, navigate, setItem, sessionTimeout],
        {}
    );
};

export default useUpdateMobilityApplicationDraft;
