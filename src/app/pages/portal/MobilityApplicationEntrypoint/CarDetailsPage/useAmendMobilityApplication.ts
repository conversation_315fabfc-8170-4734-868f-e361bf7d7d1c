import dayjs from 'dayjs';
import { omit } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MobilityLocationDataFragment } from '../../../../api/fragments/MobilityLocationData';
import { useAmendMobilityApplicationMutation } from '../../../../api/mutations/amendMobilityApplication';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import type { FormValues } from './BookingForm/shared';

const useAmendMobilityApplication = (token: string) => {
    const [amend] = useAmendMobilityApplicationMutation();
    const { t } = useTranslation('carDetails');
    const navigate = useNavigate();
    const { notification } = useThemeComponents();

    return useHandleError<FormValues>(
        async ({ rentalPeriod, location, endTime, startTime }) => {
            const end = dayjs(endTime);
            const start = dayjs(startTime);
            const period = {
                start: dayjs(rentalPeriod.start).set('hour', start.hour()).set('minute', start.minute()).toDate(),
                end: dayjs(rentalPeriod.end).set('hour', end.hour()).set('minute', end.minute()).toDate(),
            };

            notification.loading({
                content: t('carDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            // which contain assignee id, later we not need that
            // and detect home delivery too
            const rawLocation = JSON.parse(location) as MobilityLocationDataFragment & {
                isHomeDelivery?: boolean;
            };
            const pickup = {
                ...omit(
                    ['assignee', 'assigneeId', 'isHomeDelivery', 'isEnable', 'moduleName', 'moduleId'],
                    rawLocation
                ),
                address: rawLocation.address,
                email: rawLocation.email,
                phone: rawLocation.phone,
                url: rawLocation.url,
                name: rawLocation.name,
            };

            const { data } = await amend({
                variables: {
                    token,
                    period,
                    location: {
                        pickup: !rawLocation.isHomeDelivery ? pickup : null,
                        homeDelivery: rawLocation.isHomeDelivery ? pickup : null,
                        isHomeDelivery: rawLocation?.isHomeDelivery ?? false,
                    },
                },
            }).finally(() => {
                notification.destroy('primary');
            });

            navigate('../apply', { state: { token: data.result.token, isAmend: true } });
        },
        [notification, t, amend, token, navigate],
        {}
    );
};

export default useAmendMobilityApplication;
