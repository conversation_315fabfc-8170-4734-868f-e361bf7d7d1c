import type { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getStageFromApplication } from './InitializeStage';
import type { MobilityApplicationState } from './shared';

// eslint-disable-next-line import/prefer-default-export
export const reducer = (
    state: State<MobilityApplicationState>,
    action: Action<MobilityApplicationState>
): State<MobilityApplicationState> => {
    switch (action.type) {
        case 'refresh':
            return { ...state, token: action.token, application: action.application };

        case 'next':
            return {
                ...state,
                stage: getStageFromApplication(action.application),
                token: action.token,
                application: action.application,
            };

        case 'goTo':
            return { ...state, stage: action.stage };

        default:
            return state;
    }
};
