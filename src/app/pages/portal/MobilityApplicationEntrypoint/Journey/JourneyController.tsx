import { isEqual, isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import { useLocation, useNavigate, type Location } from 'react-router-dom';
import urlJoin from 'url-join';
import { DebugJourneyDataFragment } from '../../../../api/fragments/DebugJourneyData';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../../api/fragments/MobilityApplicationEntrypointContextData';
import { ApplicationKind, ApplicationSigningPurpose } from '../../../../api/types';
import ApplicationSessionModal from '../../../../components/ApplicationSessionExpireModal';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useHeaderContext } from '../../../../layouts/HeaderContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    calculateMobilityPeriod,
    computeMobilityGTMData,
    MobilityGTMEvents,
} from '../../../../utilities/googleTagManager';
import getExpirationDuration from '../../../../utilities/journeys/getExpirationDuration';
import useApplicationConcurrencyRedirect from '../../../../utilities/useApplicationConcurrencyRedirect';
import { usePersistentData, usePersistentDataListener } from '../../../../utilities/usePersistData';
import useEndSession from '../../../shared/useEndSession';
import useExtendSession from '../../../shared/useExtendSession';
import LiveChat from '../../MobilityWebpageEndpoint/Webpage/LiveChat';
import AdyenPaymentPage from '../../StandardApplicationEntrypoint/AdyenPaymentPage';
import FiservPaymentPage from '../../StandardApplicationEntrypoint/FiservPaymentPage';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import {
    NamiralRedirectPage,
    NamirialPage,
    NamirialRejectPage,
    NamirialTimeoutPage,
} from '../../StandardApplicationEntrypoint/NamirialPage';
import OTPPage from '../../StandardApplicationEntrypoint/OTPPage';
import PayGatePaymentPage from '../../StandardApplicationEntrypoint/PayGatePaymentPage';
import PorschePaymentPage from '../../StandardApplicationEntrypoint/PorschePaymentPage';
import TtbPaymentPage from '../../StandardApplicationEntrypoint/TtbPaymentPage';
import ApplicantKYC from '../ApplicantKycPage';
import GuarantorKYC from '../GuarantorKycPage';
import ThankYouPage from '../ThankYouPage';
import {
    mobilityJourneyTemporaryKey,
    MobilityJourneyTemporaryValueType,
    retrieveMobilityJourneyValue,
} from '../helper';
import InitializeStage from './InitializeStage';
import { reducer } from './reducer';
import type { MobilityApplicationState } from './shared';

export type JourneyControllerProps = {
    initialToken: string;
    initialApplication: MobilityApplicationState;
    initialStage: JourneyStage;
    endpoint: MobilityApplicationEntrypointContextDataFragment;
    journeyStages: JourneyStage[];
};

const convertStageIntoMobilityGTMEvents = (stage: JourneyStage, isReceived: boolean) => {
    if (isReceived) {
        return MobilityGTMEvents.PlacedOrderSuccessfully;
    }

    switch (stage) {
        case JourneyStage.ApplicantKYC:
            return MobilityGTMEvents.CustomerDetails;

        case JourneyStage.Deposit:
            return MobilityGTMEvents.ProceedToPayment;

        default:
            return null;
    }
};

const JourneyStageToUrlMap = {
    [JourneyStage.ApplicantKYC]: 'customerdetails',
    [JourneyStage.Deposit]: 'payment',
};

const JourneyController = ({
    initialToken,
    initialApplication,
    initialStage,
    endpoint,
    journeyStages,
}: JourneyControllerProps) => {
    const initialState = useRef({
        token: initialToken,
        stage: initialStage,
        application: initialApplication,
        stages: journeyStages,
    });
    const navigate = useNavigate();
    const [state, dispatch] = useReducer(reducer, initialState.current);
    const {
        pathname,
        state: locationState,
    }: Location<{
        token?: string;
        myInfoAuthorizationCode?: string;
        isAmend?: boolean;
        isCancel?: boolean;
        linkId?: string;
    }> = useLocation();
    const { myInfoAuthorizationCode, isAmend, isCancel, linkId } = locationState;

    const { sessionTimeout } = useCompany();
    const { deleteItem, setItem, getSessionId } = usePersistentData();
    const persistedSessionId = useMemo(() => getSessionId(mobilityJourneyTemporaryKey), [getSessionId]);
    const mobilityJourneyTemporaryValue =
        usePersistentDataListener<MobilityJourneyTemporaryValueType>(mobilityJourneyTemporaryKey);

    const { MobilityWebpageLayout } = useThemeComponents();

    const { setOnLogoClick } = useHeaderContext();

    const extendSession = useExtendSession(dispatch);

    const handleExtendSession = useCallback(async () => {
        const result = await extendSession(state.token, ApplicationKind.Mobility);

        if (result.application.__typename !== 'MobilityApplication') {
            throw new Error('unexpected type');
        }

        const expireAt = result.application.expireAt ? result.application.expireAt : null;

        const updatedTemporaryValue: MobilityJourneyTemporaryValueType = {
            ...mobilityJourneyTemporaryValue,
            token: result.token,
            expireAt,
        };

        const { sessionId, value } = retrieveMobilityJourneyValue(updatedTemporaryValue);

        setItem(
            mobilityJourneyTemporaryKey,
            value,
            expireAt ? getExpirationDuration(expireAt) : sessionTimeout * 60,
            sessionId
        );
    }, [extendSession, mobilityJourneyTemporaryValue, sessionTimeout, setItem, state]);

    const endSession = useEndSession();
    const handleEndSession = useCallback(async () => {
        if (mobilityJourneyTemporaryValue?.token) {
            await endSession(mobilityJourneyTemporaryValue.token, ApplicationKind.Mobility);
            deleteItem(mobilityJourneyTemporaryKey);

            const data = computeMobilityGTMData(
                MobilityGTMEvents.SessionTimedOut,
                mobilityJourneyTemporaryValue.bookingDetails.variantName,
                mobilityJourneyTemporaryValue.bookingDetails.stockIdentifier,
                calculateMobilityPeriod(
                    mobilityJourneyTemporaryValue.bookingDetails.period.start,
                    mobilityJourneyTemporaryValue.bookingDetails.period.startTime,
                    mobilityJourneyTemporaryValue.bookingDetails.period.end,
                    mobilityJourneyTemporaryValue.bookingDetails.period.endTime
                ),
                mobilityJourneyTemporaryValue.bookingDetails.location
            );

            window.dataLayer?.push(data);
        }
    }, [deleteItem, endSession, mobilityJourneyTemporaryValue?.bookingDetails, mobilityJourneyTemporaryValue?.token]);

    const onSessionDuplicated = useCallback(() => {
        deleteItem(mobilityJourneyTemporaryKey);
    }, [deleteItem]);

    useApplicationConcurrencyRedirect(initialToken, onSessionDuplicated);

    const baseURL = useMemo(() => urlJoin(endpoint.pathname, 'apply'), [endpoint.pathname]);

    useEffect(() => {
        const currentState = locationState || {};
        const newState = { myInfoAuthorizationCode, token: state.token, isAmend, isCancel, linkId };

        if (isEqual(currentState, newState)) {
            return;
        }

        if (JourneyStageToUrlMap[state.stage]) {
            const newPath = `${baseURL}/${JourneyStageToUrlMap[state.stage]}`;
            if (pathname !== newPath) {
                navigate(newPath, { state: newState, replace: true });
            }
        }

        if (state.application.draftFlow.isReceived) {
            const newPath = `${baseURL}/thankyou`;
            if (pathname !== newPath) {
                navigate(newPath, { state: newState, replace: true });
            }
        }
    }, [
        state.token,
        navigate,
        myInfoAuthorizationCode,
        isAmend,
        isCancel,
        linkId,
        state.stage,
        state.application.draftFlow.isReceived,
    ]);

    useEffect(() => {
        const eventGTM = convertStageIntoMobilityGTMEvents(state.stage, state.application.draftFlow.isReceived);

        // push the data only certain JourneyStage
        // KYC, deposit and thank you page
        if (persistedSessionId !== mobilityJourneyTemporaryValue?.sessionId && eventGTM) {
            const mobilityGTMData = computeMobilityGTMData(
                eventGTM,
                mobilityJourneyTemporaryValue?.bookingDetails?.variantName,
                mobilityJourneyTemporaryValue?.bookingDetails?.stockIdentifier,
                calculateMobilityPeriod(
                    mobilityJourneyTemporaryValue?.bookingDetails?.period.start,
                    mobilityJourneyTemporaryValue?.bookingDetails?.period.startTime,
                    mobilityJourneyTemporaryValue?.bookingDetails?.period.end,
                    mobilityJourneyTemporaryValue?.bookingDetails?.period.endTime
                ),
                mobilityJourneyTemporaryValue?.bookingDetails
                    ? JSON.parse(mobilityJourneyTemporaryValue?.bookingDetails?.location)?.name
                    : null
            );
            window.dataLayer?.push(mobilityGTMData);
        }
    }, [
        mobilityJourneyTemporaryValue?.bookingDetails,
        mobilityJourneyTemporaryValue?.sessionId,
        persistedSessionId,
        state.application.draftFlow.isReceived,
        state.stage,
    ]);

    useEffect(() => {
        setOnLogoClick(() => handleEndSession);
    }, [handleEndSession, setOnLogoClick]);

    const journeyContent = useMemo(() => {
        switch (state.stage) {
            case JourneyStage.Initialize:
                return <InitializeStage dispatch={dispatch} state={state} />;

            case JourneyStage.ApplicantKYC:
                return (
                    <ApplicantKYC
                        dispatch={dispatch}
                        endpoint={endpoint}
                        handleEndSession={handleEndSession}
                        showTabs={initialApplication.corporateKYC.length > 0}
                        state={state}
                    />
                );

            case JourneyStage.GuarantorKYC:
                return (
                    <GuarantorKYC
                        dispatch={dispatch}
                        endpoint={endpoint}
                        handleEndSession={handleEndSession}
                        showTabs={false}
                        state={state}
                    />
                );

            case JourneyStage.Otp:
                return <OTPPage dispatch={dispatch} purpose={ApplicationSigningPurpose.Mobility} state={state} />;

            case JourneyStage.Namirial:
            case JourneyStage.GuarantorNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.Mobility} state={state} />;

            case JourneyStage.NamirialRedirect:
            case JourneyStage.GuarantorNamirialRedirect:
                return (
                    <NamiralRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Mobility}
                        state={state}
                    />
                );

            case JourneyStage.NamirialReject:
            case JourneyStage.GuarantorNamirialReject:
                return <NamirialRejectPage />;

            case JourneyStage.NamirialTimeout:
            case JourneyStage.GuarantorNamirialTimeout:
                return <NamirialTimeoutPage />;

            case JourneyStage.Deposit:
                switch (state.application.deposit?.__typename) {
                    case 'ApplicationAdyenDeposit':
                        return (
                            <AdyenPaymentPage
                                CustomLayout={MobilityWebpageLayout}
                                applicationModuleId={endpoint.mobilityApplicationModule.id}
                                dispatch={dispatch}
                                giftVoucherModuleId={endpoint.mobilityApplicationModule.giftVoucherModuleId}
                                promoCodeModuleId={endpoint.mobilityApplicationModule.promoCodeModuleId}
                                state={state}
                            />
                        );

                    case 'ApplicationPorscheDeposit':
                        return (
                            <PorschePaymentPage
                                CustomLayout={MobilityWebpageLayout}
                                applicationModuleId={endpoint.mobilityApplicationModule.id}
                                dispatch={dispatch}
                                giftVoucherModuleId={endpoint.mobilityApplicationModule.giftVoucherModuleId}
                                promoCodeModuleId={endpoint.mobilityApplicationModule.promoCodeModuleId}
                                state={state}
                            />
                        );

                    case 'ApplicationFiservDeposit':
                        return (
                            <FiservPaymentPage
                                CustomLayout={MobilityWebpageLayout}
                                applicationModuleId={endpoint.mobilityApplicationModule.id}
                                dispatch={dispatch}
                                giftVoucherModuleId={endpoint.mobilityApplicationModule.giftVoucherModuleId}
                                promoCodeModuleId={endpoint.mobilityApplicationModule.promoCodeModuleId}
                                state={state}
                            />
                        );

                    case 'ApplicationPayGateDeposit':
                        return (
                            <PayGatePaymentPage
                                CustomLayout={MobilityWebpageLayout}
                                applicationModuleId={endpoint.mobilityApplicationModule.id}
                                dispatch={dispatch}
                                giftVoucherModuleId={endpoint.mobilityApplicationModule.giftVoucherModuleId}
                                promoCodeModuleId={endpoint.mobilityApplicationModule.promoCodeModuleId}
                                state={state}
                            />
                        );

                    case 'ApplicationTtbDeposit':
                        return (
                            <TtbPaymentPage
                                CustomLayout={MobilityWebpageLayout}
                                applicationModuleId={endpoint.mobilityApplicationModule.id}
                                dispatch={dispatch}
                                giftVoucherModuleId={endpoint.mobilityApplicationModule.giftVoucherModuleId}
                                promoCodeModuleId={endpoint.mobilityApplicationModule.promoCodeModuleId}
                                state={state}
                            />
                        );

                    default:
                        return <NotFoundResult />;
                }

            case JourneyStage.Unknown:
            default:
                return <NotFoundResult />;
        }
    }, [MobilityWebpageLayout, endpoint, handleEndSession, initialApplication.corporateKYC.length, state]);

    if (state.application.draftFlow.isReceived) {
        return <ThankYouPage endpoint={endpoint} isAmend={isAmend} isCancel={isCancel} state={state} />;
    }

    return (
        <>
            <ApplicationSessionModal
                endSession={handleEndSession}
                expireAt={state.application.expireAt}
                extendSession={handleExtendSession}
            />
            {journeyContent}
            {!isNil(endpoint.mobilityApplicationModule.liveChatSetting) && (
                <LiveChat
                    chatSetting={
                        endpoint.mobilityApplicationModule.liveChatSetting.__typename === 'UserlikeChatbotSetting' &&
                        endpoint.mobilityApplicationModule.liveChatSetting
                    }
                />
            )}
        </>
    );
};

export default JourneyController;
