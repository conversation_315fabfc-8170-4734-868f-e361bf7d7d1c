import { Dispatch, useEffect } from 'react';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { State, Action } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { MobilityApplicationState } from './shared';

export const getStageFromApplication = (application: State<MobilityApplicationState>['application']) => {
    const { draftFlow, deposit, signing } = application;

    if (!draftFlow.isApplicantKYCCompleted) {
        return JourneyStage.ApplicantKYC;
    }

    if (!draftFlow.areGlobalAgreementsCompleted) {
        return JourneyStage.Unknown;
    }

    if (draftFlow.hasGuarantor && !draftFlow.isGuarantorCompleted) {
        return JourneyStage.GuarantorKYC;
    }

    if (deposit && !draftFlow.isDepositCompleted) {
        return JourneyStage.Deposit;
    }

    if (signing && !draftFlow.isSigningCompleted) {
        // this will only have value if there's signing
        switch (signing.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.Namirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.Otp;

            default:
                return JourneyStage.Unknown;
        }
    }

    return JourneyStage.Unknown;
};

export type InitializeStageProps = {
    state: State<MobilityApplicationState>;
    dispatch: Dispatch<Action<MobilityApplicationState>>;
};

const InitializeStage = ({ state, dispatch }: InitializeStageProps) => {
    const { application } = state;

    useEffect(() => {
        dispatch({ type: 'goTo', stage: getStageFromApplication(application) });
    }, [application, dispatch]);

    return <PortalLoadingElement />;
};

export default InitializeStage;
