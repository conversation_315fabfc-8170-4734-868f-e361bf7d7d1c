import { Formik } from 'formik';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PaymentStatus } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import useHandleError from '../../../../../utilities/useHandleError';
import useValidator from '../../../../../utilities/useValidator';
import useAgreementsValidator from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues, {
    AgreementValues,
} from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import useGiftVoucherPaymentAgreements from '../AdyenPaymentPage/useGiftVoucherPaymentAgreements';
import Inner from './Inner';
import type { GiftVoucherTtbPaymentPageProps } from './shared';
import useGiftVoucherTtbDepositSubmission, { submitPayment } from './useGiftVoucherTtbDepositSubmission';

type TtbPaymentFormValues = AgreementValues & { promoCodeId?: string };

const TtbPaymentPage = ({ state, dispatch, CustomLayout, ...props }: GiftVoucherTtbPaymentPageProps) => {
    const { token, giftVoucher } = state;
    const { t } = useTranslation(['paymentDetails']);
    const { notification } = useThemeComponents();

    const paymentAgreements = useGiftVoucherPaymentAgreements(state);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitTtbDeposit = useGiftVoucherTtbDepositSubmission();

    useEffect(() => {
        if ([PaymentStatus.Failed, PaymentStatus.Error].includes(giftVoucher.deposit.status)) {
            notification.error(t('paymentDetails:resultCode.error'));
        }
    }, [giftVoucher.deposit.status, notification, t]);

    const onSubmit = useHandleError(
        async (values: TtbPaymentFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentRedirecting'),
                duration: 0,
                key: 'primary',
            });
            const { promoCodeId, ...agreementValues } = values;

            if (giftVoucher.deposit.amount !== 0) {
                try {
                    // call payment submission api
                    const paymentResult = await submitPayment(agreementValues, token, promoCodeId);
                    const { redirectUrl } = paymentResult;

                    if (redirectUrl) {
                        // redirect to 3DS
                        window.location.replace(redirectUrl);

                        return;
                    }

                    // there's an error
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                } catch {
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                }
            }

            const result = await submitTtbDeposit(token, agreementValues, false).finally(() => {
                notification.destroy('primary');
            });

            const { giftVoucher: latestGiftVoucher } = result;

            if (latestGiftVoucher.draftFlow.isDepositCompleted) {
                dispatch({
                    type: 'next',
                    token: result.token,
                    giftVoucher: latestGiftVoucher,
                });
            } else {
                // there's an error
                notification.warn(t('paymentDetails:resultCode.error'));
            }
        },
        [notification, t, giftVoucher.deposit.amount, submitTtbDeposit, token, dispatch],
        {}
    );

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.giftVoucher.deposit.amount > 0 ? validation : null}
        >
            <Inner CustomLayout={CustomLayout} dispatch={dispatch} state={state} {...props} />
        </Formik>
    );
};

export default TtbPaymentPage;
