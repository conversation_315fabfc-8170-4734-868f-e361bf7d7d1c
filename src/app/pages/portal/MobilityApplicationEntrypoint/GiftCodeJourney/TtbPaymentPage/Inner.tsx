import Icon from '@ant-design/icons';
import { Col, Form, Grid, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import FormAutoTouch from '../../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../../components/ScrollToTop';
import { useRouter } from '../../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../themes/hooks';
import AgreementField from '../../../../shared/CIPage/ConsentAndDeclarations/AgreementField';
import { getPaymentAgreements } from '../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import { AgreementValues } from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import DealerInfo from '../../../ConfiguratorApplicationEntrypoint/DealerInfo';
import { Title } from '../../../ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/ui';
import BookingDeposit from '../../../ConfiguratorApplicationEntrypoint/ThankYou/BookingDeposit';
import { JourneyStage } from '../../../FinderApplicationPublicAccessEntrypoint/types';
import JourneyToolbar from '../../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import NextButton from '../../../StandardApplicationEntrypoint/shared/NextButton';
import Stepper, { AvailableSteps as GiftCodeSteps, MobilityPageKind } from '../../Components/Stepper';
import GiftVoucherVehicleDetails from '../AdyenPaymentPage/GiftVoucherVehicleDetails';
import type { GiftVoucherTtbPaymentPageProps } from './shared';
import BackIcon from '../../../../../icons/mobility/back.svg';

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };

const Inner = ({
    state,
    dispatch,
    CustomLayout,
    promoCodeModuleId,
    applicationModuleId,
}: GiftVoucherTtbPaymentPageProps & {
    canSkipDeposit?: boolean;
}) => {
    const { t } = useTranslation(['paymentDetails']);
    const { giftVoucher } = state;
    const { deposit } = giftVoucher;

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.giftVoucher.purchaserAgreements),
        [state.giftVoucher.purchaserAgreements]
    );

    const { isSubmitting, handleSubmit } = useFormikContext<AgreementValues>();

    const screens = Grid.useBreakpoint();
    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const { Button, BackButton } = useThemeComponents();

    if (deposit.__typename !== 'ApplicationTtbDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    return (
        <CustomLayout
            backIcon={<BackButton type="link">{t('paymentDetails:actions.back')}</BackButton>}
            onBack={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
            preferredMobileFooterHeight={75}
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={<Typography style={{ alignItems: 'center', width: '100%' }}>{t('paymentDetails:title')}</Typography>}
            hasFooterBar
        >
            <FormAutoTouch />
            <Form
                id="completeTtbPaymentForm"
                layout="vertical"
                name="completeTtbPaymentForm"
                onSubmitCapture={handleSubmit}
            >
                <BasicProLayoutContainer>
                    <ScrollToTop />
                    <Stepper
                        currentPage={MobilityPageKind.Gift}
                        currentStep={GiftCodeSteps.Payment}
                        dispatchGift={dispatch}
                    />
                    <Row gutter={[24, 24]}>
                        <Col
                            className="paymentDiv"
                            offset={screens.lg && paymentAgreements.length === 0 ? 4 : null}
                            {...colSpan}
                        >
                            <Row gutter={[16, 16]}>
                                <>
                                    <GiftVoucherVehicleDetails state={state} />
                                    {!!giftVoucher.dealer && (
                                        <Col xs={24}>
                                            <DealerInfo dealer={giftVoucher.dealer} />
                                        </Col>
                                    )}
                                </>
                            </Row>
                        </Col>
                        {deposit.amount > 0 && paymentAgreements.length > 0 && (
                            <Col {...colSpan}>
                                <Title>{t('paymentDetails:messages.checkAgreements')}</Title>
                                <Row gutter={[16, 16]}>
                                    {paymentAgreements.map(agreement => (
                                        <Col xs={24}>
                                            <AgreementField key={agreement.id} agreement={agreement} />
                                        </Col>
                                    ))}
                                </Row>
                            </Col>
                        )}
                        {deposit.amount > 0 && (
                            <Col {...colSpan}>
                                <Title>{t('paymentDetails:titles.paymentTitle')}</Title>
                                <div style={{ marginBottom: '15px' }}>
                                    <BookingDeposit
                                        depositAmount={deposit.amount}
                                        label={t('paymentDetails:giftVoucher.deposit.title')}
                                    />
                                </div>
                            </Col>
                        )}
                    </Row>
                </BasicProLayoutContainer>
            </Form>

            <JourneyToolbar preferredMobileFooterHeight={80}>
                <Button
                    key="back"
                    form="bookingForm"
                    htmlType="button"
                    icon={<Icon className="porsche-arrow" component={BackIcon} />}
                    onClick={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
                    porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                    type="tertiary"
                >
                    {t('paymentDetails:actions.back')}
                </Button>
                <NextButton
                    key="nextButton"
                    disabled={isSubmitting}
                    form="completeTtbPaymentForm"
                    htmlType="submit"
                    type="primary"
                >
                    {deposit.amount === 0 ? t('paymentDetails:actions.submit') : t('paymentDetails:actions.pay')}
                </NextButton>
            </JourneyToolbar>
        </CustomLayout>
    );
};

export default Inner;
