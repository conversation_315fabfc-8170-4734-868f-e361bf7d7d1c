import Icon from '@ant-design/icons';
import { Col, Form, Grid, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationAgreementDataFragment } from '../../../../../api/fragments/ApplicationAgreementData';
import FormAutoTouch from '../../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../../components/ScrollToTop';
import { useRouter } from '../../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../themes/hooks';
import AgreementField from '../../../../shared/CIPage/ConsentAndDeclarations/AgreementField';
import { getPaymentAgreements } from '../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import { AgreementValues } from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import DealerInfo from '../../../ConfiguratorApplicationEntrypoint/DealerInfo';
import BookingDeposit from '../../../ConfiguratorApplicationEntrypoint/ThankYou/BookingDeposit';
import { Title } from '../../../EventApplicationEntrypoint/ApplicantForm/shared';
import { JourneyStage } from '../../../FinderApplicationPublicAccessEntrypoint/types';
import AdyenPayment from '../../../StandardApplicationEntrypoint/AdyenPaymentPage/AdyenPayment';
import { AgreementTypes } from '../../../StandardApplicationEntrypoint/AdyenPaymentPage/shared';
import { AdyenResult } from '../../../StandardApplicationEntrypoint/AdyenPaymentPage/useAdyenPayment';
import JourneyToolbar from '../../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import NextButton from '../../../StandardApplicationEntrypoint/shared/NextButton';
import Stepper, { AvailableSteps as GiftCodeSteps, MobilityPageKind } from '../../Components/Stepper';
import GiftVoucherVehicleDetails from './GiftVoucherVehicleDetails';
import type { GiftVoucherAdyenPaymentPageProps } from './shared';
import BackIcon from '../../../../../icons/mobility/back.svg';

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };

export const getMandatory = (agreement: ApplicationAgreementDataFragment) => {
    switch (agreement.__typename) {
        case AgreementTypes.CheckboxApplicationAgreement:
        case AgreementTypes.MarketingApplicationAgreement:
            return agreement.isMandatory;

        default:
            return false;
    }
};

export type AdyenFormValues = AgreementValues & { promoCodeId?: string };

const Inner = ({
    state,
    dispatch,
    CustomLayout,
    promoCodeModuleId,
    applicationModuleId,
    adyenSessionResult,
    onChangeSessionResult,
}: GiftVoucherAdyenPaymentPageProps & {
    adyenSessionResult?: string | null;
    onChangeSessionResult: (newSessionData: string) => void;
}) => {
    const { t } = useTranslation(['paymentDetails']);
    const { giftVoucher } = state;
    const { deposit, draftFlow } = giftVoucher;
    const { Button, BackButton } = useThemeComponents();

    const [allowToSubmit, setAllowToSubmit] = useState(false);

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.giftVoucher.purchaserAgreements),
        [state.giftVoucher.purchaserAgreements]
    );

    const { values, handleSubmit, submitForm } = useFormikContext<AdyenFormValues>();

    const screens = Grid.useBreakpoint();
    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const onPaymentCompleted = useCallback(
        (result: AdyenResult) => {
            // Result code already authorised from the front-end
            const canSubmit = draftFlow.isDepositCompleted || result.resultCode === 'Authorised';

            if (canSubmit) {
                onChangeSessionResult(result.sessionResult);
                setAllowToSubmit(true);
            }
        },
        [draftFlow.isDepositCompleted, onChangeSessionResult]
    );

    useEffect(() => {
        // Why using useEffect?
        // There is race condition when setting up new session data
        // and submitting the form. We need to wait for both
        if (adyenSessionResult && allowToSubmit) {
            submitForm();
        }
    }, [adyenSessionResult, allowToSubmit, submitForm]);

    if (deposit.__typename !== 'ApplicationAdyenDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    return (
        <CustomLayout
            backIcon={<BackButton type="link">{t('paymentDetails:actions.back')}</BackButton>}
            onBack={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
            preferredMobileFooterHeight={75}
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={<Typography style={{ alignItems: 'center', width: '100%' }}>{t('paymentDetails:title')}</Typography>}
            hasFooterBar
        >
            <FormAutoTouch />
            <Form id="completeAdyenForm" layout="vertical" name="completeAdyenForm" onSubmitCapture={handleSubmit}>
                <BasicProLayoutContainer>
                    <ScrollToTop />
                    <Stepper
                        currentPage={MobilityPageKind.Gift}
                        currentStep={GiftCodeSteps.Payment}
                        dispatchGift={dispatch}
                    />
                    <Row gutter={[24, 24]}>
                        <Col
                            className="paymentDiv"
                            offset={screens.lg && paymentAgreements.length === 0 ? 4 : null}
                            {...colSpan}
                        >
                            <Row gutter={[16, 16]}>
                                <>
                                    <GiftVoucherVehicleDetails state={state} />
                                    {!!giftVoucher.dealer && (
                                        <Col xs={24}>
                                            <DealerInfo dealer={giftVoucher.dealer} />
                                        </Col>
                                    )}
                                </>
                            </Row>
                        </Col>
                        {deposit.amount > 0 && paymentAgreements.length > 0 && (
                            <Col {...colSpan}>
                                <Title>{t(`paymentDetails:messages.checkAgreements`)}</Title>
                                <Row gutter={[16, 16]}>
                                    {paymentAgreements.map(agreement => (
                                        <Col xs={24}>
                                            <AgreementField key={agreement.id} agreement={agreement} />
                                        </Col>
                                    ))}
                                </Row>
                            </Col>
                        )}
                        {deposit.amount > 0 && (
                            <Col {...colSpan}>
                                <Title>{t('paymentDetails:titles.paymentTitle')}</Title>
                                <div style={{ marginBottom: '15px' }}>
                                    <BookingDeposit
                                        depositAmount={deposit.amount}
                                        label={t('paymentDetails:giftVoucher.deposit.title')}
                                    />
                                </div>
                                <div
                                    style={
                                        deposit?.sessionId &&
                                        paymentAgreements.some(
                                            agreement => !values[agreement.id].isAgreed && getMandatory(agreement)
                                        )
                                            ? {
                                                  opacity: '30%',
                                                  pointerEvents: 'none',
                                              }
                                            : {}
                                    }
                                >
                                    <AdyenPayment deposit={deposit} onPaymentCompleted={onPaymentCompleted} />
                                </div>
                            </Col>
                        )}
                    </Row>
                </BasicProLayoutContainer>
            </Form>

            <JourneyToolbar preferredMobileFooterHeight={0}>
                <Button
                    key="back"
                    form="bookingForm"
                    htmlType="button"
                    icon={<Icon className="porsche-arrow" component={BackIcon} />}
                    onClick={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
                    porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                    type="tertiary"
                >
                    {t('paymentDetails:actions.back')}
                </Button>
                {deposit?.amount === 0 && (
                    <NextButton key="nextButton" form="completeAdyenForm" htmlType="submit" type="primary">
                        {t('paymentDetails:actions.submit')}
                    </NextButton>
                )}
            </JourneyToolbar>
        </CustomLayout>
    );
};

export default Inner;
