import Icon from '@ant-design/icons';
import { Grid, Typography, Form, Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { ComponentType, Dispatch, PropsWithChildren, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetPorschePaymentMethodsQuery } from '../../../../../api';
import FormAutoTouch from '../../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../../components/ScrollToTop';
import { useRouter } from '../../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { DefaultLayoutProps } from '../../../../../themes/default/Standard/Layout';
import { useThemeComponents } from '../../../../../themes/hooks';
import AgreementField from '../../../../shared/CIPage/ConsentAndDeclarations/AgreementField';
import { getPaymentAgreements } from '../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import { AgreementValues } from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import DealerInfo from '../../../ConfiguratorApplicationEntrypoint/DealerInfo';
import BookingDeposit from '../../../ConfiguratorApplicationEntrypoint/ThankYou/BookingDeposit';
import { Title } from '../../../EventApplicationEntrypoint/ApplicantForm/shared';
import { JourneyStage } from '../../../FinderApplicationPublicAccessEntrypoint/types';
import PaymentSuccess from '../../../StandardApplicationEntrypoint/PorschePaymentPage/PaymentSuccess';
import PorschePaymentWidget from '../../../StandardApplicationEntrypoint/PorschePaymentPage/PorschePaymentWidget';
import JourneyToolbar from '../../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import NextButton from '../../../StandardApplicationEntrypoint/shared/NextButton';
import Stepper, { AvailableSteps as GiftCodeSteps, MobilityPageKind } from '../../Components/Stepper';
import GiftVoucherVehicleDetails from '../AdyenPaymentPage/GiftVoucherVehicleDetails';
import { getMandatory } from '../AdyenPaymentPage/Inner';
import { GiftVoucherAction, GiftVoucherState } from '../reducer';
import BackIcon from '../../../../../icons/mobility/back.svg';

export type GiftVoucherPorschePaymentInnerProps = {
    state: GiftVoucherState;
    dispatch: Dispatch<GiftVoucherAction>;
    CustomLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    canSkipDeposit?: boolean;
    isSuccessful: boolean;
    promoCodeModuleId?: string;
    applicationModuleId?: string;
};

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };

export type PorschePaymentFormValues = AgreementValues & { promoCodeId?: string };

const Inner = ({ state, dispatch, CustomLayout, isSuccessful }: GiftVoucherPorschePaymentInnerProps) => {
    const { t } = useTranslation(['paymentDetails']);
    const { giftVoucher } = state;
    const { deposit } = giftVoucher;

    const { BackButton, Button } = useThemeComponents();

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.giftVoucher.purchaserAgreements),
        [state.giftVoucher.purchaserAgreements]
    );

    const { values, handleSubmit } = useFormikContext<PorschePaymentFormValues>();

    const screens = Grid.useBreakpoint();
    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const { data, loading } = useGetPorschePaymentMethodsQuery({ variables: { token: state.token } });

    const hasSinglePaymentMethod = data?.result.length === 1;

    if (deposit.__typename !== 'ApplicationPorscheDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    return (
        <CustomLayout
            backIcon={<BackButton type="link">{t('paymentDetails:actions.back')}</BackButton>}
            onBack={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
            preferredMobileFooterHeight={75}
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={<Typography style={{ alignItems: 'center', width: '100%' }}>{t('paymentDetails:title')}</Typography>}
            hasFooterBar
        >
            <ScrollToTop />
            <Stepper currentPage={MobilityPageKind.Gift} currentStep={GiftCodeSteps.Payment} dispatchGift={dispatch} />
            <FormAutoTouch />
            <Form
                id="completePorschePaymentForm"
                layout="vertical"
                name="completePorschePaymentForm"
                onSubmitCapture={handleSubmit}
            >
                <BasicProLayoutContainer>
                    <Row gutter={[24, 24]}>
                        <Col
                            className="paymentDiv"
                            offset={screens.lg && paymentAgreements.length === 0 ? 4 : null}
                            {...colSpan}
                        >
                            <Row gutter={[16, 16]}>
                                <>
                                    <GiftVoucherVehicleDetails state={state} />
                                    {!!giftVoucher.dealer && (
                                        <Col xs={24}>
                                            <DealerInfo dealer={giftVoucher.dealer} />
                                        </Col>
                                    )}
                                </>
                            </Row>
                        </Col>
                        {deposit.amount > 0 && paymentAgreements.length > 0 && (
                            <Col {...colSpan}>
                                <Title>{t('paymentDetails:messages.checkAgreements')}</Title>
                                <Row gutter={[16, 16]}>
                                    {paymentAgreements.map(agreement => (
                                        <Col xs={24}>
                                            <AgreementField key={agreement.id} agreement={agreement} />
                                        </Col>
                                    ))}
                                </Row>
                            </Col>
                        )}
                        {deposit.amount > 0 && (
                            <Col {...colSpan}>
                                <Title>{t('paymentDetails:titles.paymentTitle')}</Title>
                                <div style={{ marginBottom: '15px' }}>
                                    <BookingDeposit
                                        depositAmount={deposit.amount}
                                        label={t('paymentDetails:giftVoucher.deposit.title')}
                                    />
                                </div>
                                <div
                                    style={
                                        paymentAgreements.some(
                                            agreement => !values[agreement.id].isAgreed && getMandatory(agreement)
                                        )
                                            ? {
                                                  opacity: '30%',
                                                  pointerEvents: 'none',
                                              }
                                            : {}
                                    }
                                >
                                    {isSuccessful ? (
                                        <PaymentSuccess />
                                    ) : (
                                        !loading && (
                                            <PorschePaymentWidget
                                                deposit={deposit}
                                                hasSinglePaymentMethod={hasSinglePaymentMethod}
                                            />
                                        )
                                    )}
                                </div>
                            </Col>
                        )}
                    </Row>
                </BasicProLayoutContainer>
            </Form>
            <JourneyToolbar>
                <Button
                    key="back"
                    form="bookingForm"
                    htmlType="button"
                    icon={<Icon className="porsche-arrow" component={BackIcon} />}
                    onClick={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
                    porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                    type="tertiary"
                >
                    {t('paymentDetails:actions.back')}
                </Button>
                <NextButton key="nextButton" form="completePorschePaymentForm" htmlType="submit" type="primary">
                    {t('paymentDetails:actions.submit')}
                </NextButton>
            </JourneyToolbar>
        </CustomLayout>
    );
};

export default Inner;
