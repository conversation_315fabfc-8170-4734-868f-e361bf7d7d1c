import { GiftVoucherDataFragment } from '../../../../api/fragments/GiftVoucherData';
import { JourneyStage } from '../../FinderApplicationPublicAccessEntrypoint/types';
import type { State } from '../../StandardApplicationEntrypoint/Journey/shared';

export type GiftVoucherState = Omit<State<GiftVoucherDataFragment>, 'application' | 'stages'> & {
    giftVoucher: GiftVoucherDataFragment;
};

export type GiftVoucherAction =
    | ({ type: 'refresh' | 'next' } & Omit<GiftVoucherState, 'stage' | 'stages'>)
    | { type: 'goTo'; stage: JourneyStage };

export const reducer = (state: GiftVoucherState, action: GiftVoucherAction): GiftVoucherState => {
    switch (action.type) {
        case 'refresh':
            return { ...state, token: action.token, giftVoucher: action.giftVoucher };

        case 'next':
            return {
                ...state,
                stage: getStageFromGiftVoucher(action.giftVoucher),
                token: action.token,
                giftVoucher: action.giftVoucher,
            };

        case 'goTo':
            return { ...state, stage: action.stage };

        default:
            return state;
    }
};

export const getStageFromGiftVoucher = (giftVoucher: GiftVoucherDataFragment) => {
    const { areKYCsCompleted, isDepositCompleted } = giftVoucher.draftFlow;

    if (!areKYCsCompleted) {
        return JourneyStage.ApplicantKYC;
    }

    if (!isDepositCompleted) {
        return JourneyStage.Deposit;
    }

    return JourneyStage.Unknown;
};
