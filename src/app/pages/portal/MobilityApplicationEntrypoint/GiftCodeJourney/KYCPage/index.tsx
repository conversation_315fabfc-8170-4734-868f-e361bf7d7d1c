/* eslint-disable max-len */
import Icon from '@ant-design/icons';
import { Col, Row, Typography } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import {
    ApplicationAgreementDataFragment,
    GiftVoucherDataFragment,
    KycFieldSpecsFragment,
    PrefetchKycConsentsDataFragment,
} from '../../../../../api/fragments';
import { usePrefetchGiftVoucherKycConsentsQuery } from '../../../../../api/queries/prefetchGiftVoucherKYCConsents';
import { CustomerKind } from '../../../../../api/types';
import FormAutoTouch from '../../../../../components/FormAutoTouch';
import PortalLoadingElement from '../../../../../components/PortalLoadingElement';
import ScrollToTop from '../../../../../components/ScrollToTop';
import { useLanguage } from '../../../../../components/contexts/LanguageContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import Form from '../../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getInitialValues } from '../../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../../utilities/kycPresets/useKYCValidators';
import useHandleError from '../../../../../utilities/useHandleError';
import useValidator from '../../../../../utilities/useValidator';
import validators from '../../../../../utilities/validators';
import { getApplicantAgreements } from '../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import useAgreementSubmission, {
    FeatureTypeSubmission,
} from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValidator from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import ResetKYCButtonPorsche from '../../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import useRefineKycPresets from '../../../../shared/useRefineKycPresets';
import useCustomerDetailsSubmission from '../../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import { getApplicantKyc } from '../../../StandardApplicationEntrypoint/KYCPage/getKyc';
import { NextButton } from '../../../StandardApplicationEntrypoint/shared/JourneyButton';
import JourneyToolbar from '../../../StandardApplicationEntrypoint/shared/JourneyToolbar';
import ConsentsAndDeclarations from '../../ApplicantKycPage/ConsentsAndDeclarations';
import CustomerDetails from '../../ApplicantKycPage/CustomerDetails';
import { FeatureKYCPageLabel } from '../../ApplicantKycPage/shared';
import Stepper, { AvailableSteps as GiftCodeSteps, MobilityPageKind } from '../../Components/Stepper';
import { useChangeVehicleDrawer } from '../ChangeVehicle';
import GiftVoucherAmount from '../GiftVoucherAmount';
import GiftVoucherVehicleInfo, { GiftVoucherVehicleInfoProps } from '../GiftVoucherVehicleInfo';
import PurchaseVehicle from '../PurchaseVehicle';
import RentalDuration from '../RentalDuration';
import useUpdateGiftVoucher from '../useUpdateGiftVoucher';
import {
    ApplicantKycContainer,
    GiftVoucherKYCInnerProps,
    GiftVoucherKYCJourneyValues,
    GiftVoucherKYCPageProps,
    leftColSpan,
    rightColSpan,
} from './shared';
import useDeleteGiftVoucherDocument from './useDeleteApplicationDocument';
import useDraftGiftVoucher from './useDraftGiftVoucher';
import useUploadGiftVoucherDocument from './useUploadGiftVoucherDocument';
import BackIcon from '../../../../../icons/mobility/back.svg';

const retrieveKYCConsents = (
    isDrafted: boolean,
    prefetchData: PrefetchKycConsentsDataFragment,
    giftVoucher?: GiftVoucherDataFragment
): { agreements: ApplicationAgreementDataFragment[]; kycFields: KycFieldSpecsFragment[] } => {
    if (isDrafted) {
        return {
            agreements: giftVoucher?.purchaserAgreements,
            kycFields: giftVoucher?.purchaserKYC,
        };
    }

    return {
        agreements: prefetchData?.agreements || [],
        kycFields: prefetchData?.kycFields || [],
    };
};

const DrawerButton = styled.div`
    margin-bottom: 28px;
`;

const KYCPage = ({ dispatch, state, endpoint }: GiftVoucherKYCPageProps) => {
    const { token, giftVoucher } = state;
    const isDrafted = useMemo(() => !!token, [token]);
    const [prefetchData, setPrefetchData] = useState<{
        applicant: PrefetchKycConsentsDataFragment;
    } | null>(null);

    // Only capture the first loading for both kyc and agreement
    // This to prevent flicker on the front side
    const [preFetchDataLoaded, setFetchDataLoaded] = useState(false);

    usePrefetchGiftVoucherKycConsentsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            mobilityModuleId: endpoint.mobilityApplicationModule.id,
        },
        onCompleted: data => {
            setPrefetchData({
                applicant: data.applicant,
            });
            setFetchDataLoaded(true);
        },
        skip: isDrafted,
    });

    if (isDrafted && state?.giftVoucher?.module.__typename !== 'GiftVoucherModule') {
        throw new Error('ModuleType not supported');
    }
    const { t } = useTranslation('customerDetails');

    const { notification } = useThemeComponents();
    const { currentLanguageId } = useLanguage();

    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateGiftVoucherJourney = useUpdateGiftVoucher();
    const draftGiftVoucher = useDraftGiftVoucher();
    const [isCorporate] = useState<boolean>(false);
    const [prefill, setPrefill] = useState<boolean>(false);

    const isEditableField = useMemo(() => {
        if (isDrafted) {
            return state?.giftVoucher?.purchaser ? state?.giftVoucher?.purchaser.fields : [];
        }

        return [];
    }, [isDrafted, state?.giftVoucher?.purchaser]);

    const { agreements: purchaseAgreements, kycFields: purchaserKyc } = retrieveKYCConsents(
        isDrafted,
        prefetchData?.applicant,
        state?.giftVoucher
    );
    const agreementsKYC = purchaseAgreements.map(agreement => ({
        ...agreement,
        isAgreed: false,
    }));

    const applicantKycAgreements = useMemo(() => getApplicantAgreements(agreementsKYC), [agreementsKYC]);
    const agreementsValidator = useAgreementsValidator(applicantKycAgreements, 'agreements');
    const agreements = useAgreementsValues(applicantKycAgreements);

    const applicantKycFields = useMemo(() => getApplicantKyc(purchaserKyc), [purchaserKyc]);

    const kycPresets = useRefineKycPresets(applicantKycFields);
    const kycExtraSettings = useMemo(
        () =>
            endpoint?.mobilityApplicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.mobilityApplicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const applicants = useMemo(
        () => ({
            fields: {
                ...getInitialValues(isEditableField, kycPresets, isDrafted ? state?.giftVoucher?.documents : []),
            },
        }),
        [state?.giftVoucher?.documents, isDrafted, isEditableField, kycPresets]
    );

    // Mobility booking does not have `proceed with customer`
    const applicantsValidator = useKYCFormValidator({
        field: kycPresets,
        extraSettings: kycExtraSettings,
        moduleCountryCode: endpoint.mobilityApplicationModule.company.countryCode,
        prefix: 'customer.fields',
        saveDraft: false,
    });

    const validatorDrafting = validators.only(
        () => !isDrafted,
        validators.compose(validators.requiredString('stockId'), validators.requiredNumber('value'))
    );
    const validations = useMemo(
        () => validators.compose(applicantsValidator, agreementsValidator, validatorDrafting),
        [agreementsValidator, applicantsValidator, validatorDrafting]
    );

    const validate = useValidator(validations, { prefill });

    const uploadDocument = useUploadGiftVoucherDocument(token);
    const removeDocument = useDeleteGiftVoucherDocument(token);

    const initialValues: GiftVoucherKYCJourneyValues = useMemo(
        () => ({
            agreements,
            customer: applicants,
            isCorporateCustomer: false,
            hasGuarantor: false,
            remarks: '',
            prefill: false,
            numberOfBookingReferenceDays: giftVoucher?.numberOfBookingReferenceDays ?? 1,
            stockId: giftVoucher?.stock?.__typename === 'MobilityStockInventory' ? giftVoucher?.stock?.id : null,
            value: giftVoucher?.stock?.__typename === 'MobilityStockInventory' ? giftVoucher?.stock?.price : null,
        }),
        [agreements, applicants, giftVoucher?.stock, giftVoucher?.numberOfBookingReferenceDays]
    );

    const onSubmit = useHandleError(
        async (values: GiftVoucherKYCJourneyValues & { remarks: string }) => {
            notification.loading({
                content: t('customerDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            if (isDrafted) {
                const updateGiftVoucher = await updateGiftVoucherJourney(values, state.token);

                const submitAgreementKYC = await submitAgreements(
                    updateGiftVoucher.token,
                    values.agreements,
                    isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                    values.hasGuarantor,
                    FeatureTypeSubmission.GiftVoucher
                );

                const submitApplicantKYC = await submitCustomerDetails({
                    token: submitAgreementKYC.token,
                    fields: values.customer.fields,
                    customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                    sameCorrespondenceAddress: values.prefill,
                    featureTypeSubmission: FeatureTypeSubmission.GiftVoucher,
                });

                notification.destroy('primary');
                // go to the journey
                if (!submitApplicantKYC || submitApplicantKYC.__typename === 'ApplicationJourney') {
                    throw new Error('unexpected missing gift voucher');
                }

                dispatch({
                    type: 'next',
                    token: submitApplicantKYC.token,
                    giftVoucher: submitApplicantKYC.giftVoucher,
                });
            } else {
                const drafted = await draftGiftVoucher(values, endpoint, currentLanguageId);

                const submitAgreementKYC = await submitAgreements(
                    drafted.token,
                    values.agreements,
                    isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                    values.hasGuarantor,
                    FeatureTypeSubmission.GiftVoucher
                );

                const submitApplicantKYC = await submitCustomerDetails({
                    token: submitAgreementKYC.token,
                    fields: values.customer.fields,
                    customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                    sameCorrespondenceAddress: values.prefill,
                    featureTypeSubmission: FeatureTypeSubmission.GiftVoucher,
                });

                // go to the journey
                if (!submitApplicantKYC || submitApplicantKYC.__typename === 'ApplicationJourney') {
                    throw new Error('unexpected missing gift voucher');
                }

                notification.destroy('primary');

                dispatch({
                    type: 'next',
                    token: submitApplicantKYC.token,
                    giftVoucher: submitApplicantKYC.giftVoucher,
                });
            }
        },
        [
            notification,
            t,
            isDrafted,
            updateGiftVoucherJourney,
            state.token,
            submitAgreements,
            isCorporate,
            submitCustomerDetails,
            dispatch,
            draftGiftVoucher,
            endpoint,
            currentLanguageId,
        ],
        {},
        () => {
            notification.destroy('primary');
        }
    );

    if (!preFetchDataLoaded && !isDrafted) {
        return <PortalLoadingElement />;
    }

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} validateOnMount>
            <Inner
                applicantPrefetch={prefetchData}
                dispatch={dispatch}
                endpoint={endpoint}
                giftVoucherKycPreset={kycPresets}
                isDrafted={isDrafted}
                kycExtraSettings={kycExtraSettings}
                preFetchDataLoading={preFetchDataLoaded}
                removeDocument={removeDocument}
                setPrefill={setPrefill}
                state={state}
                uploadDocument={uploadDocument}
            />
        </Formik>
    );
};

const Inner = ({
    state,
    giftVoucherKycPreset: kycPresets,
    kycExtraSettings,
    uploadDocument,
    removeDocument,
    setPrefill,
    endpoint,
    applicantPrefetch,
    preFetchDataLoading,
    isDrafted,
}: GiftVoucherKYCInnerProps) => {
    if (isDrafted && state?.giftVoucher?.module.__typename !== 'GiftVoucherModule') {
        throw new Error('ModuleType not supported');
    }

    const { t } = useTranslation('customerDetails');
    const { isSubmitting, handleSubmit, validateForm, submitForm, values, resetForm, initialValues } =
        useFormikContext<GiftVoucherKYCJourneyValues>();
    const navigate = useNavigate();
    const { Button, MobilityWebpageLayout } = useThemeComponents();
    const { layout } = useRouter();

    const latestValues = useRef(values);
    latestValues.current = values;

    const { agreements: purchaseAgreements } = retrieveKYCConsents(
        isDrafted,
        applicantPrefetch?.applicant,
        state?.giftVoucher
    );

    // Outer state just for vehicle info, Since it can be changed from drawer
    // Intentionally not refetch the stock details again,
    // as necessary information was retrieved from the drawer
    const [selectedStock, setSelectedStock] = useState<GiftVoucherVehicleInfoProps['stock']>(() => {
        if (state?.giftVoucher?.stock?.__typename === 'MobilityStockInventory') {
            return state?.giftVoucher?.stock as unknown as GiftVoucherVehicleInfoProps['stock'];
        }

        return null;
    });

    const { render, open } = useChangeVehicleDrawer(endpoint.mobilityApplicationModule.id, isDrafted, setSelectedStock);

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const agreementsKYC = purchaseAgreements.map(agreement => ({ ...agreement, isAgreed: false }));

    const applicantKycAgreements = useMemo(() => getApplicantAgreements(agreementsKYC), [agreementsKYC]);

    const title = (
        <Row style={{ width: '100%' }}>
            <Typography>{t('customerDetails:giftVoucher.title')}</Typography>
        </Row>
    );

    const onBack = useCallback(() => {
        navigate(-1);
    }, [navigate]);

    const resetFormHandler = useCallback(async () => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    return (
        <ApplicantKycContainer>
            <ScrollToTop />
            <FormAutoTouch />
            <MobilityWebpageLayout
                backIcon={null} // To hide the back button in header
                footerMaxWidth="var(--mobility-page-max-width)"
                preferredMobileFooterHeight={75}
                title={title}
                hasFooterBar
            >
                <BasicProLayoutContainer>
                    <Stepper currentPage={MobilityPageKind.Gift} currentStep={GiftCodeSteps.VoucherDetails} />
                    <Form
                        id="giftVoucherKycJourneyForm"
                        name="giftVoucherKycJourneyForm"
                        onSubmitCapture={handleSubmit}
                    >
                        <Row gutter={[24, 50]}>
                            <Col {...leftColSpan}>
                                {selectedStock ? <GiftVoucherVehicleInfo stock={selectedStock} /> : <PurchaseVehicle />}
                                <DrawerButton>
                                    <Button
                                        onClick={open}
                                        porscheFallbackIcon="arrow-right"
                                        type="link"
                                        isCompact
                                        showIcon
                                    >
                                        {selectedStock
                                            ? t('giftVoucherJourney:changeVehicle')
                                            : t('giftVoucherJourney:selectVehicle')}
                                    </Button>
                                </DrawerButton>
                                {values.stockId && (
                                    <RentalDuration
                                        availableNumberOfBookingRange={
                                            endpoint.mobilityApplicationModule.availableNumberOfBookingRange
                                        }
                                    />
                                )}
                                <GiftVoucherAmount />
                            </Col>
                            <Col {...rightColSpan}>
                                <Row gutter={[16, 16]}>
                                    <Col span={24}>
                                        <CustomerDetails
                                            customerKind={CustomerKind.Local}
                                            hasGuarantorPreset={false}
                                            kycExtraSettings={kycExtraSettings}
                                            kycPresets={kycPresets}
                                            removeDocument={removeDocument}
                                            resetFormHandler={resetFormHandler}
                                            setIsCorporate={undefined}
                                            setPrefill={setPrefill}
                                            showResetButton={false}
                                            showTabs={false}
                                            typeKYCPageTitle={FeatureKYCPageLabel.GiftVoucher}
                                            uploadDocument={uploadDocument}
                                        />
                                    </Col>
                                    <Col span={24}>
                                        <ConsentsAndDeclarations applicationAgreements={applicantKycAgreements} />
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    </Form>
                </BasicProLayoutContainer>
                <JourneyToolbar>
                    <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                    <Button
                        key="back"
                        form="bookingForm"
                        htmlType="button"
                        icon={<Icon className="porsche-arrow" component={BackIcon} />}
                        onClick={onBack}
                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="tertiary"
                    >
                        {t('customerDetails:previousButton')}
                    </Button>
                    <NextButton key="nextButton" disabled={isSubmitting} onSubmit={onSubmit} />
                </JourneyToolbar>
            </MobilityWebpageLayout>
            {render()}
        </ApplicantKycContainer>
    );
};

export default KYCPage;
