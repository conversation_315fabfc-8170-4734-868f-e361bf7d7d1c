import { Col, Row, Typography } from 'antd';
import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useThemeComponents } from '../../../../../themes/hooks';
import breakpoints from '../../../../../utilities/breakpoints';
import { disabledTimeRange, getDisabledHoursAndMinutes } from '../../CarDetailsPage/BookingForm/BookingFields';
import { colSpan } from '../../CarDetailsPage/BookingForm/RentalFields/shared';
import { getAvailableDateRange, getUnavailableDayofWeek } from '../../helper';
import DatePickerType from '../../shared';
import { useCarListingState } from '../CarListingStateProvider';
import TimePickerPeriodFilter from './TimePickerPeriodFilter';

export const TimePickerContainer = styled(Row)`
    justify-content: space-between;

    @media screen and (min-width: ${breakpoints.xl}) {
        margin-top: 15px;
    }
`;

const getFocusToStartDateInput = (inputs: NodeListOf<HTMLElement>) => {
    inputs[1].classList.remove('.ant-picker-input-active');
    inputs[0].classList.add('ant-picker-input-active');
    inputs[0].focus();
    const calendarContainer = document.querySelector('.ant-picker-dropdown');

    if (calendarContainer) {
        calendarContainer.classList.remove('ant-picker-dropdown-hidden');
    }
};

const StyledCol = styled(Col)`
    width: 100%;

    @media screen and (max-width: ${breakpoints.xl}) {
        margin-top: 15px;
    }
`;

const PeriodFilter = () => {
    const { t } = useTranslation(['carList', 'common']);
    const { RangePicker } = useThemeComponents();

    const {
        state: { rentalPeriod },
        actions,
        meta: { endpoint },
    } = useCarListingState();

    const durationNextBooking = endpoint.mobilityApplicationModule.durationBeforeNextBooking;
    const minimumBooking = endpoint.mobilityApplicationModule.minimumAdvancedBooking;
    let addedAdditionalBlockingHours = 0;
    const convertedMinimumBookingInHour = minimumBooking * 24;

    addedAdditionalBlockingHours =
        durationNextBooking > convertedMinimumBookingInHour ? durationNextBooking : convertedMinimumBookingInHour;

    const disabledTimeSlot = disabledTimeRange(endpoint.mobilityApplicationModule.unavailableTimeRange);

    const today = dayjs();
    const todayAddedDurationNextBooking = today.add(addedAdditionalBlockingHours, 'hour');
    const todayStartOff = today.startOf('day');

    const todayBlockingHoursAndMinutes: Record<number, number[]> = useMemo(
        () => ({
            ...disabledTimeSlot,
            ...getDisabledHoursAndMinutes(todayStartOff, todayAddedDurationNextBooking),
        }),
        [disabledTimeSlot, todayAddedDurationNextBooking, todayStartOff]
    );

    const unavailableDayofWeek = getUnavailableDayofWeek(endpoint.mobilityApplicationModule.unavailableDayOfWeek);
    const availableBookingRange = endpoint.mobilityApplicationModule.availableNumberOfBookingRange;

    useEffect(() => {
        const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
        if (isNil(rentalPeriod?.start)) {
            // add the blockers node
            const node = document.createElement('div');
            node.className = 'endDateBlocker';
            node.style.position = 'absolute';
            node.style.width = '110%';
            node.style.height = '50px';

            inputs[1].appendChild(node);
        } else {
            const node = document.querySelector('.endDateBlocker');
            if (node) {
                node.remove();
            }
        }
    }, [rentalPeriod?.start]);

    const onChange = useCallback(
        value => {
            const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
            const [start, end] = value ?? [];
            getFocusToStartDateInput(inputs);

            if (
                (!isNil(rentalPeriod?.start) && !dayjs(start).isSame(rentalPeriod?.start)) ||
                (!isNil(rentalPeriod?.end) && !dayjs(end).isSame(rentalPeriod?.end))
            ) {
                const range = dayjs(rentalPeriod?.start).isSame(start) ? 'end' : 'start';
                const stateRentalPeriod =
                    range === 'start'
                        ? {
                              start: dayjs(start).toDate(),
                              end: undefined,
                          }
                        : {
                              start: undefined,
                              end: dayjs(end).toDate(),
                          };

                actions.setRentalPeriod(stateRentalPeriod);
                actions.setStartTime(undefined);
                actions.setEndTime(undefined);
            }
        },
        [actions, rentalPeriod?.end, rentalPeriod?.start]
    );

    const onBlur = useCallback(() => {
        const calendarContainer = document.querySelector('.ant-picker-dropdown');

        calendarContainer.classList.add('ant-picker-dropdown-hidden');
    }, []);

    const onCalendarChange = useCallback(
        value => {
            const [start, end] = value || [];
            actions.setRentalPeriod({
                start: !isNil(start) ? start.toDate() : null,
                end: !isNil(end) ? end.toDate() : null,
            });
        },
        [actions]
    );
    const disabledDate = useCallback(
        value => {
            if (value) {
                const todayDate = dayjs();
                const date = dayjs(value);
                const start = rentalPeriod?.start;
                const end = rentalPeriod?.end;

                const [datepickerType, lastAvailableBookingDate] = getAvailableDateRange(
                    availableBookingRange,
                    start,
                    end
                );

                if (date.isSame(today, 'day')) {
                    return (
                        date.isBefore(todayDate.add(addedAdditionalBlockingHours, 'hour'), 'day') ||
                        Object.entries(todayBlockingHoursAndMinutes).length > 23 ||
                        unavailableDayofWeek.includes(date.day())
                    );
                }
                let checker = false;

                if (!isNil(lastAvailableBookingDate)) {
                    checker =
                        datepickerType === DatePickerType.Start
                            ? !date.isBetween(dayjs(start), lastAvailableBookingDate, 'day', '[]')
                            : !date.isBetween(lastAvailableBookingDate, dayjs(end), 'day', '[]');
                }

                return (
                    date.isBefore(todayDate.add(addedAdditionalBlockingHours, 'hour'), 'day') ||
                    // only allow available date range based on user selected date ( start date or end date first) and
                    // `mobilityModule.availableDateRange`
                    // availableDateRange = 0 :: means datepicker do not block any dates
                    // availableDateRange > 0 :: means show limited available dates based on user choose date
                    checker ||
                    Object.entries(disabledTimeSlot).length > 23 ||
                    unavailableDayofWeek.includes(date.day())
                );
            }

            return false;
        },
        [
            addedAdditionalBlockingHours,
            availableBookingRange,
            disabledTimeSlot,
            rentalPeriod?.end,
            rentalPeriod?.start,
            today,
            todayBlockingHoursAndMinutes,
            unavailableDayofWeek,
        ]
    );

    const onOpenChange = useCallback(
        open => {
            const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
            // 0 refers start and 1 refers endDate
            if (open && isNil(rentalPeriod?.start)) {
                getFocusToStartDateInput(inputs);
            }

            if (!open) {
                inputs[1].classList.remove('.ant-picker-input-active');
            }

            if (open) {
                inputs[1].click();
                getFocusToStartDateInput(inputs);

                if (isNil(rentalPeriod?.start)) {
                    getFocusToStartDateInput(inputs);
                }

                if (!isNil(rentalPeriod?.end)) {
                    actions.setRentalPeriod({
                        start: undefined,
                        end: undefined,
                    });
                }
            }
        },
        [actions, rentalPeriod?.end, rentalPeriod?.start]
    );

    const onClick = useCallback(() => {
        const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
        // 0 refers start and 1 refers endDate
        getFocusToStartDateInput(inputs);
        if (!isNil(rentalPeriod?.end)) {
            actions.setRentalPeriod({
                start: undefined,
                end: undefined,
            });
        }
    }, [actions, rentalPeriod?.end]);

    return (
        <div>
            <Typography.Title level={5} style={{ width: '100%' }}>
                {t('carList:filter.rentalPeriod')}
            </Typography.Title>

            <Col span={24}>
                <RangePicker
                    {...t('mobilityVehicleDetails:fields.rentalPeriod', { returnObjects: true })}
                    disabledDate={disabledDate}
                    onBlur={onBlur}
                    onCalendarChange={onCalendarChange}
                    onChange={onChange}
                    onClick={onClick}
                    onOpenChange={onOpenChange}
                    showTime={false}
                    value={[
                        !isNil(rentalPeriod?.start) ? dayjs(rentalPeriod?.start) : undefined,
                        !isNil(rentalPeriod?.end) ? dayjs(rentalPeriod?.end) : undefined,
                    ]}
                />
            </Col>
            <TimePickerContainer>
                <StyledCol {...colSpan}>
                    <Typography.Title level={5} style={{ width: '100%' }}>
                        {t('carList:filter.startTime')}
                    </Typography.Title>
                    <TimePickerPeriodFilter
                        disabledTimeSlot={disabledTimeSlot}
                        name="startTime"
                        todayBlockingHoursAndMinutes={todayBlockingHoursAndMinutes}
                        type={DatePickerType.Start}
                    />
                </StyledCol>

                <StyledCol {...colSpan}>
                    <Typography.Title level={5} style={{ width: '100%' }}>
                        {t('carList:filter.endTime')}
                    </Typography.Title>
                    <TimePickerPeriodFilter
                        disabledTimeSlot={disabledTimeSlot}
                        name="endTime"
                        todayBlockingHoursAndMinutes={todayBlockingHoursAndMinutes}
                        type={DatePickerType.End}
                    />
                </StyledCol>
            </TimePickerContainer>
        </div>
    );
};

export default PeriodFilter;
