import { PickerTimeProps } from 'antd/lib/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { isNil } from 'lodash/fp';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { TimePickerFieldProps } from '../../../../../components/TimePickerField';
import { useThemeComponents } from '../../../../../themes/hooks';
import { TranslationFieldType } from '../../../../../utilities/common';
import { calculateDisabledMinutes, HourMinutesMapping } from '../../CarDetailsPage/BookingForm/RentalFields/shared';
import DatePickerType from '../../shared';
import { useCarListingState } from '../CarListingStateProvider';

type TimePickerPeriodFilterProps = {
    type: DatePickerType;
    disabledTimeSlot: HourMinutesMapping;
    todayBlockingHoursAndMinutes: HourMinutesMapping;
    name: string;
};

const TimePickerPeriodFilter = ({
    type,
    disabledTimeSlot,
    todayBlockingHoursAndMinutes,
    name,
}: TimePickerPeriodFilterProps) => {
    const { t } = useTranslation(['carList', 'common']);

    const {
        state: { endTime, startTime, rentalPeriod },
        actions,
    } = useCarListingState();

    const { TimePicker } = useThemeComponents();

    const onChange: TimePickerFieldProps['onChange'] = useCallback(
        (value, dateString) => {
            const { end, start } = rentalPeriod;
            if (type === DatePickerType.End) {
                actions.setEndTime(value.toDate());

                const concatEnd = dayjs(end).set('hour', value.hour()).set('minute', value.minute());
                actions.setRentalPeriod({
                    start: dayjs(start).toDate(),
                    end: concatEnd.toDate(),
                });
            } else if (type === DatePickerType.Start) {
                actions.setStartTime(value.toDate());

                const concatStart = dayjs(start).set('hour', value.hour()).set('minute', value.minute());
                actions.setRentalPeriod({
                    start: concatStart.toDate(),
                    end: dayjs(end).toDate(),
                });
            }
        },
        [actions, rentalPeriod, type]
    );

    useEffect(() => {
        // if the selected start date and end date are the same day
        if (dayjs(rentalPeriod?.start).isSame(dayjs(rentalPeriod?.end), 'day')) {
            // swap whichever lesser time to be start time
            // greater time to be end time
            if (!isNil(endTime) && !isNil(startTime) && dayjs(endTime).isBefore(dayjs(startTime), 'minute')) {
                // define the same values from the time pickers in order to prevent wrong value assigned.
                actions.setStartTime(endTime);
                actions.setEndTime(startTime);
            }
        }
    }, [actions, endTime, rentalPeriod?.end, rentalPeriod?.start, startTime]);

    const disabledTime: PickerTimeProps<Dayjs>['disabledTime'] = useCallback(
        (time: Dayjs) => {
            const today = dayjs();
            if (
                type === DatePickerType.Start &&
                !isNil(rentalPeriod?.start) &&
                dayjs(rentalPeriod?.start).isSame(today, 'day')
            ) {
                return {
                    disabledHours: () =>
                        Object.entries(todayBlockingHoursAndMinutes).map(
                            ([key, value]) => value.length === 4 && Number(key)
                        ),
                    disabledMinutes: (selectedHours: number) =>
                        calculateDisabledMinutes(selectedHours, todayBlockingHoursAndMinutes, type),
                };
            }
            if (
                type === DatePickerType.End &&
                !isNil(rentalPeriod?.end) &&
                dayjs(rentalPeriod?.end).isSame(today, 'day')
            ) {
                return {
                    disabledHours: () =>
                        Object.entries(todayBlockingHoursAndMinutes).map(
                            ([key, value]) => value.length === 4 && Number(key)
                        ),
                    disabledMinutes: (selectedHours: number) =>
                        calculateDisabledMinutes(selectedHours, todayBlockingHoursAndMinutes, type),
                };
            }

            return {
                disabledHours: () =>
                    Object.entries(disabledTimeSlot).map(([key, value]) => value.length === 4 && Number(key)),
                disabledMinutes: (selectedHours: number) =>
                    calculateDisabledMinutes(
                        selectedHours,
                        disabledTimeSlot,
                        type,
                        !isNil(startTime) ? dayjs(startTime).minute() : null,
                        !isNil(startTime) ? dayjs(startTime).hour() : null
                    ),
            };
        },
        [disabledTimeSlot, rentalPeriod?.end, rentalPeriod?.start, startTime, todayBlockingHoursAndMinutes, type]
    );

    return (
        <TimePicker
            {...t<
                string,
                {
                    returnObjects: true;
                    type: 'Start';
                },
                TranslationFieldType
            >('mobilityVehicleDetails:fields.timeRental', {
                returnObjects: true,
                type: 'Start',
            })}
            disabled={isNil(rentalPeriod?.end) || isNil(rentalPeriod?.start)}
            disabledTime={disabledTime}
            minuteStep={15}
            name={name}
            onChange={onChange}
            showNow={false}
            hideDisabledOptions
            inputReadOnly
        />
    );
};

export default TimePickerPeriodFilter;
