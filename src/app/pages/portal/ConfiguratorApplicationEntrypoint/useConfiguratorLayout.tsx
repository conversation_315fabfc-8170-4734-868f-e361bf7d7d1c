import { isNil } from 'lodash/fp';
import { Dispatch, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { getPorscheIDConfigByApplicationJourney } from '../../../components/PorscheID/getPorscheIDConfig';
import { JourneyStage } from '../FinderApplicationPublicAccessEntrypoint/types';
import { Action, State } from '../StandardApplicationEntrypoint/Journey/shared';
import type { ConfiguratorApplicationState } from './Journey/shared';

const useConfiguratorLayout = (
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>,
    state: State<ConfiguratorApplicationState>
): {
    title: string;
    onBack: () => void;
    shouldShowBackIcon?: boolean;
    /**
     * This is used to determine whether to wrap the content page with JourneyLayout. default is true.
     */
    useJourneyLayout?: boolean;
} => {
    const { t } = useTranslation([
        'customerDetails',
        'journey',
        'paymentDetails',
        'confirmRemoteFlow',
        'otpPage',
        'appointmentPage',
    ]);
    const { application, stage: journeyStage } = state;

    const navigate = useNavigate();

    const shouldShowBackIcon = !application.withCustomerDevice;
    const isApplyingFromDetails = useMemo(
        () =>
            (application.configuration.withFinancing && application.configuration.requestForFinancing) ||
            !isNil(application.draftFlow.isApplyingForFinanceCompleted) ||
            !isNil(application.draftFlow.isApplyingForInsuranceCompleted),
        [
            application.configuration.requestForFinancing,
            application.configuration.withFinancing,
            application.draftFlow.isApplyingForFinanceCompleted,
            application.draftFlow.isApplyingForInsuranceCompleted,
        ]
    );

    const useJourneyLayout = true;

    const { isPorscheIdLoginMandatory } = getPorscheIDConfigByApplicationJourney(application);

    /* TO DO VF-354-Refactor Will need update/adjust the functions in here after refactor */
    const applicantKYCBackAction = useMemo(
        () => (isPorscheIdLoginMandatory ? () => navigate('..') : () => navigate(-1)),
        [isPorscheIdLoginMandatory, navigate]
    );

    switch (journeyStage) {
        case JourneyStage.PorscheIdLoginRegister:
            return {
                title: t('customerDetails:title'),
                onBack: shouldShowBackIcon ? () => navigate(-1) : () => {},
                shouldShowBackIcon: shouldShowBackIcon && !isApplyingFromDetails,
                useJourneyLayout,
            };

        case JourneyStage.ApplicantKYC:
            return {
                title: t('customerDetails:title'),
                onBack: shouldShowBackIcon ? () => applicantKYCBackAction() : () => {},
                shouldShowBackIcon: shouldShowBackIcon && !isApplyingFromDetails,
                useJourneyLayout,
            };

        case JourneyStage.GuarantorKYC:
            return {
                title: t('customerDetails:title'),
                onBack: () => navigate('..'),
                shouldShowBackIcon,
                useJourneyLayout,
            };

        case JourneyStage.Deposit:
            return {
                title: t('paymentDetails:title'),
                onBack: () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC }),
                shouldShowBackIcon,
                useJourneyLayout,
            };

        case JourneyStage.ConfirmEmailSend:
            return {
                title: t('confirmRemoteFlow:title'),
                onBack: () => {},
                shouldShowBackIcon,
                useJourneyLayout: false,
            };

        case JourneyStage.Otp:
            return {
                title: t('otpPage:title'),
                onBack: () => navigate('..'),
                shouldShowBackIcon,
                useJourneyLayout,
            };

        default:
            return {
                title: '',
                onBack: () => {},
                shouldShowBackIcon,
                useJourneyLayout,
            };
    }
};

export default useConfiguratorLayout;
