/* eslint-disable max-len */
import { Form } from 'antd';
import isEqual from 'fast-deep-equal';
import { Formik, useFormikContext } from 'formik';
import { isEmpty } from 'lodash/fp';
import { ReactNode, useCallback, useEffect, useRef } from 'react';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { CalculatorContext } from '../../../../calculator/computing';
import { BankFieldContext } from '../../../../calculator/computing/defaultFields/bankField';
import { GenericCalculatorValues } from '../../../../calculator/types';
import useInitialValue from '../../../../utilities/useInitialValue';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import useApplyNewCondition from '../../FinderApplicationPublicAccessEntrypoint/OfferPage/applyNew/useApplyNewCondition';
import { usePromoCodeErrorModal } from '../../StandardApplicationEntrypoint/CarDetailsPage/CalculatorStage/PromoCodeErrorModal';
import { usePersistConfiguratorJourneyValues } from '../usePersistConfiguratorJourneyValues';
import { ActionsProps, ConfiguratorFormValues, State } from './shared';
import useSubmitDraft from './useSubmitDraft';

export type ConfiguratorFormProps = {
    calculatorContext?: CalculatorContext<GenericCalculatorValues>;
    children: JSX.Element | ReactNode;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    initialFormValues: Partial<ConfiguratorFormValues>;
    isDraft: boolean;
    token?: string;
    state: State;
    actions: ActionsProps;
};

const validations = validators.compose(validators.requiredString('dealerId'));

const ConfiguratorForm = ({
    calculatorContext,
    children,
    endpoint,
    initialFormValues,
    isDraft,
    token,
    state,
    actions,
}: ConfiguratorFormProps) => {
    const { configuration, configuratorId, configuratorBlocks, options, dealerId, email } = initialFormValues;
    const { availableFinanceProducts } = calculatorContext.getFieldContext<BankFieldContext>('bank');
    const { persistedValue: persistedJourneyValues, transformTemporaryCalculatorValues } =
        usePersistConfiguratorJourneyValues();

    const basicInitConfiguration = !isEmpty(persistedJourneyValues?.configuration)
        ? persistedJourneyValues.configuration
        : {
              ...configuration,
              withFinancing: configuration.withFinancing && availableFinanceProducts.length > 0,
              requestForFinancing: configuration.requestForFinancing && availableFinanceProducts.length > 0,
          };

    const { canApplyFinancing, canApplyRequestFinancing, canApplyInsurance, canApplyAppointment } =
        useApplyNewCondition(state.applyNewApplication, endpoint.configuratorApplicationModule);
    const applyNewInitConfiguration = state.applyNewApplication
        ? {
              ...basicInitConfiguration,
              requestForFinancing: canApplyRequestFinancing,
              withFinancing: canApplyFinancing,
              tradeIn: state.applyNewApplication.configuration.tradeIn,
              testDrive: canApplyAppointment,
              withInsurance: canApplyInsurance,
          }
        : basicInitConfiguration;

    const initialValues = useInitialValue<ConfiguratorFormValues>({
        calculator: persistedJourneyValues?.calculator
            ? transformTemporaryCalculatorValues(persistedJourneyValues)
            : (calculatorContext?.values as GenericCalculatorValues),
        configuration: {
            ...applyNewInitConfiguration,
        },
        configuratorId,
        configuratorBlocks,
        options,
        dealerId: dealerId || state.dealerId,
        email,
    });

    const promoCodeErrorModal = usePromoCodeErrorModal();
    const showPromoCodeErrorModal = useCallback(
        (title: string) => {
            promoCodeErrorModal.open();
            promoCodeErrorModal.changeTitle(title);
        },
        [promoCodeErrorModal]
    );

    const validate = useValidator(validations);

    const onSubmit = useSubmitDraft(
        calculatorContext,
        endpoint,
        state,
        showPromoCodeErrorModal,
        actions,
        token,
        state.applyNewApplication?.id
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form
                    id="configuratorForm"
                    name="configuratorForm"
                    onSubmitCapture={() => {
                        actions.setSaveAsDraft(false);
                        handleSubmit();
                    }}
                >
                    <CalculatorFormSyncLink calculatorContext={calculatorContext} />
                    {children}
                    {promoCodeErrorModal.render(() => {
                        handleSubmit();
                        promoCodeErrorModal.close();
                    })}
                </Form>
            )}
        </Formik>
    );
};

export default ConfiguratorForm;

type CalculatorFormSyncLinkProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
};

const CalculatorFormSyncLink = ({ calculatorContext }: CalculatorFormSyncLinkProps) => {
    const { setFieldValue, values } = useFormikContext<ConfiguratorFormValues>();

    const previousValuesRef = useRef(values.calculator);
    const currentValues = (calculatorContext || {}).values as ConfiguratorFormValues['calculator'];

    useEffect(() => {
        if (!isEqual(previousValuesRef.current, currentValues)) {
            setFieldValue('calculator', currentValues, false);
            previousValuesRef.current = currentValues;
        }
    }, [previousValuesRef, currentValues, setFieldValue]);

    return null;
};
