import { useFormikContext } from 'formik';
import { head } from 'lodash/fp';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { CalculatorContext } from '../../../../calculator/computing';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useDealerSelectFieldOptions } from '../../../../components/fields/DealerSelectField';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import VehicleCard from './VehicleCard';
import DealerSelection from './components/DealerSelection';
import { ActionsProps, State, ConfiguratorFormValues } from './shared';
import { DefaultContainer } from './ui';

const VehiclesContainer = styled.div`
    margin-top: 2.875rem;
    color: #000000;

    & .ant-form-item {
        height: var(--configurator-vehicle-select-height);
        min-width: 13.75rem;
        width: fit-content;
        margin-bottom: 0;

        .ant-row {
            flex-direction: column;

            .ant-form-item-label {
                padding: 0 0 var(--configurator-vehicle-select-label-padding-bottom);
                line-height: 1.5715;
                white-space: initial;
                text-align: left;
            }

            .ant-form-item-control {
                width: 100%;

                .ant-form-item-control-input {
                    min-height: auto;
                }
            }
        }
    }

    @media screen and (max-width: ${breakpoints.md}) {
        & .ant-form-item {
            width: 100%;
        }
    }

    @media screen and (min-width: 123.75em) {
        & .ant-form-item {
            width: 11.12vw;
        }
    }
`;

const VehicleInfo = styled.div<{ isDefault: boolean }>`
    padding-bottom: ${props => (props.isDefault ? '68px' : '120px')};

    & .variant-selection-title {
        padding: 4rem 0 2.5rem;
    }

    @media screen and (max-width: ${breakpoints.md}) {
        padding-bottom: 48px;

        & .variant-selection-title {
            padding: 3rem 0 2rem;
        }
    }
`;

const VehicleCardsContainer = styled.div`
    display: flex;
    gap: 18px;
    overflow: auto;
`;

export type VehiclesProps = {
    modelConfigurator: ModelConfiguratorDetailsFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    actions: ActionsProps;
    state: State;
};

const Vehicles = ({ modelConfigurator, calculatorContext, actions, state }: VehiclesProps) => {
    const { t } = useTranslation(['configuratorDetails', 'financeProductDetails', 'moduleDetails']);
    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();
    const company = useCompany();
    const { options: dealerOptions } = useDealerSelectFieldOptions({
        companyId: company?.id,
        productionOnly: true,
        checkConfiguratorStock: true,
        isTranslatedOption: true,
    });
    const { Title } = useThemeComponents();
    const translated = useTranslatedString();

    useEffect(() => {
        if (!values.dealerId && dealerOptions?.length === 1) {
            setFieldValue('dealerId', head(dealerOptions).value);
        }
    }, [dealerOptions, setFieldValue, values.dealerId]);

    useEffect(() => {
        if (values.dealerId) {
            actions.setDealerId(values.dealerId);
            actions.setSelectedSection(null);
        }
    }, [actions, values.dealerId]);

    const isSingleVariant = modelConfigurator.variantConfigurators.length <= 1;

    return (
        <DefaultContainer>
            <VehiclesContainer>
                <DealerSelection label={t('configuratorDetails:labels.displayVehiclesFrom')} />
                <VehicleInfo isDefault={isSingleVariant || !state.dealerId}>
                    {state.dealerId && !isSingleVariant && (
                        <>
                            <div className="variant-selection-title">
                                <Title>
                                    {t('configuratorDetails:titles.model', {
                                        model: translated(modelConfigurator.model.name),
                                    })}
                                </Title>
                            </div>
                            <VehicleCardsContainer>
                                {modelConfigurator.variantConfigurators.map(variantConfigurator => (
                                    <VehicleCard
                                        key={variantConfigurator.id}
                                        actions={actions}
                                        calculatorContext={calculatorContext}
                                        modelConfigurator={modelConfigurator}
                                        state={state}
                                        variantConfiguratorId={variantConfigurator.id}
                                        vehicle={variantConfigurator.variant}
                                    />
                                ))}
                            </VehicleCardsContainer>
                        </>
                    )}
                </VehicleInfo>
            </VehiclesContainer>
        </DefaultContainer>
    );
};

export default Vehicles;
