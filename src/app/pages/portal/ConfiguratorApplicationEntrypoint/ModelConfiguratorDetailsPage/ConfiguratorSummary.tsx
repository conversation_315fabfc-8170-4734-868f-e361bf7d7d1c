/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { Grid, Space } from 'antd';
import { useFormikContext } from 'formik';
import { head, isEmpty } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import {
    UpdateConfiguratorApplicationDocument,
    UpdateConfiguratorApplicationMutation,
    UpdateConfiguratorApplicationMutationVariables,
} from '../../../../api/mutations/updateConfiguratorApplication';
import {
    BlockType,
    FinancingPreferenceValue,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
} from '../../../../api/types';
import { CalculatorContext } from '../../../../calculator/computing';
import getFinancingFromCalculatorValues from '../../../../calculator/getFinancingFromCalculatorValues';
import getVehicleFromCalculatorContext from '../../../../calculator/getVehicleFromCalculatorContext';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
import { GTMEvents, useConfiguratorTagManagerArguments } from '../../../../utilities/googleTagManager';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import {
    hasFinancingScenario,
    hasNoFinancingScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import NextButton from '../../StandardApplicationEntrypoint/shared/NextButton';
import SaveOrderModal from './SaveOrderModal';
import SummaryInfo from './SummaryInfo';
import UpdateExtraAmounts from './UpdateExtraAmounts';
import { Container, ImageContainer } from './ViewContainer';
import AffixWrapper from './components/AffixWrapper';
import Media from './components/Media';
import { ActionsProps, State, ConfiguratorFormValues } from './shared';
import { getConfiguratorBlocks } from './useSubmitDraft';

const SummaryContainer = styled.div`
    color: #000000;

    padding: 0 24px;

    /* Desktop and Tablet Screen */
    @media (min-width: ${breakpoints.lg}) {
        width: 40vw;
        padding: 0 50px 24px 50px;
    }
`;

const Title = styled.div`
    font-size: 20px;
    font-weight: 700;
    margin-block: 30px;
    color: var(--ant-primary-color);
`;

const StyledSpace = styled(Space)`
    margin-top: 1rem;
    width: 100%;
`;

export type ConfiguratorSummaryProps = {
    modelConfigurator: ModelConfiguratorDetailsFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    token?: string;
    isDraft: boolean;
    state: State;
    actions: ActionsProps;
};

type ImageSectionProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
};

const ImageSection = ({ variantConfigurator }: ImageSectionProps) => {
    const { values } = useFormikContext<ConfiguratorFormValues>();

    const file = useMemo(() => {
        const colorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);
        const trimBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);

        const matrix = variantConfigurator.matrix.find(
            matrix => matrix.colorId === head(colorBlock?.ids) && matrix.trimId === head(trimBlock?.ids)
        );

        return { filename: matrix?.variantColorImage?.filename, source: matrix?.variantColorImage?.url };
    }, [values.configuratorBlocks, variantConfigurator.matrix]);

    return (
        <ImageContainer isHidden={false}>
            <Media fileName={file?.filename} source={file?.source} />
        </ImageContainer>
    );
};

const ConfiguratorSummary = ({
    modelConfigurator,
    calculatorContext,
    endpoint,
    token,
    isDraft,
    state,
    actions,
}: ConfiguratorSummaryProps) => {
    const { t } = useTranslation('configuratorDetails');
    const { values } = useFormikContext<ConfiguratorFormValues>();
    const screens = Grid.useBreakpoint();
    const isMobile = useMemo(() => !screens.lg, [screens]);
    const translatedString = useTranslatedString();
    const { Button, notification, ConfiguratorSubHeader, BackButton } = useThemeComponents();
    const [openSaveOrderModal, setOpenSaveOrderModal] = useState<boolean>(false);
    const apolloClient = useApolloClient();
    const { setShowSummary } = actions;

    const variantConfigurator = useMemo(
        () =>
            modelConfigurator.variantConfigurators.find(
                variantConfigurator =>
                    variantConfigurator.id === values.configuratorId &&
                    variantConfigurator.__typename === 'VariantConfigurator'
            ),
        [modelConfigurator.variantConfigurators, values.configuratorId]
    );

    const variantName = useMemo(() => {
        switch (variantConfigurator.variant.__typename) {
            case 'LocalVariant':
                return variantConfigurator.variant.name.defaultValue;

            default:
                return '';
        }
    }, [variantConfigurator]);

    const onBack = useCallback(() => {
        setShowSummary(false);
        window.scroll(0, 0);
    }, [setShowSummary]);

    const tagManagerArguments = useConfiguratorTagManagerArguments(
        values,
        variantConfigurator,
        modelConfigurator.model.name.defaultValue,
        GTMEvents.SaveYourOrderButtonClick
    );

    const sendEmail = useCallback(async () => {
        if (!isDraft) {
            window.dataLayer?.push(tagManagerArguments);

            const [vehicle] = getVehicleFromCalculatorContext([calculatorContext]);

            await apolloClient.mutate<
                UpdateConfiguratorApplicationMutation,
                UpdateConfiguratorApplicationMutationVariables
            >({
                mutation: UpdateConfiguratorApplicationDocument,
                variables: {
                    vehicle,
                    customer: {
                        newLocalCustomer: {
                            fields: [
                                {
                                    key: LocalCustomerFieldKey.Email,
                                    stringValue: values.email,
                                    source: LocalCustomerFieldSource.UserInput,
                                },
                            ],
                        },
                    },
                    financing:
                        endpoint.configuratorApplicationModule.bankModuleId && values.calculator.financeProduct
                            ? getFinancingFromCalculatorValues(values.calculator)
                            : null,
                    configuration: values.configuration,
                    dealerId: values.dealerId,
                    configuratorBlocks: getConfiguratorBlocks(values.configuratorBlocks, values.options),
                    isDraft: true,
                    token,
                },
            });

            notification.success({
                type: 'success',
                content: t('configuratorDetails:messages.emailSent'),
                duration: 3,
                key: 'primary',
            });
        } else {
            setOpenSaveOrderModal(true);
        }
    }, [
        isDraft,
        tagManagerArguments,
        calculatorContext,
        apolloClient,
        values.email,
        values.calculator,
        values.configuration,
        values.dealerId,
        values.configuratorBlocks,
        values.options,
        endpoint.configuratorApplicationModule.bankModuleId,
        token,
        notification,
        t,
    ]);

    const onSubmit = useCallback(() => {
        window.dataLayer?.push({ ...tagManagerArguments, event: GTMEvents.PlaceYourOrderButtonClick });
    }, [tagManagerArguments]);

    const canSubmit = useMemo(() => {
        if (
            !values.calculator.financeProduct &&
            ((hasFinancingScenario(endpoint.configuratorApplicationModule.scenarios) &&
                endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Mandatory) ||
                (hasNoFinancingScenario(endpoint.configuratorApplicationModule.scenarios) &&
                    endpoint.configuratorApplicationModule.showFinanceCalculator))
        ) {
            return false;
        }

        return true;
    }, [
        endpoint.configuratorApplicationModule.financingPreference,
        endpoint.configuratorApplicationModule.scenarios,
        endpoint.configuratorApplicationModule.showFinanceCalculator,
        values.calculator.financeProduct,
    ]);

    const canNotNextForApplyNew = useMemo(
        () =>
            state.applyNewApplication &&
            !(
                values.configuration.withFinancing ||
                values.configuration.withInsurance ||
                values.configuration.testDrive ||
                values.configuration.requestForFinancing
            ),
        [
            state.applyNewApplication,
            values.configuration.requestForFinancing,
            values.configuration.testDrive,
            values.configuration.withFinancing,
            values.configuration.withInsurance,
        ]
    );

    const StyledBackButton = styled(BackButton)`
        &.ant-btn {
            color: var(--configurator-back-color, '#fff');
            height: initial;
            padding: 0px;
        }
    `;

    return (
        <>
            <UpdateExtraAmounts calculatorContext={calculatorContext} variantConfigurator={variantConfigurator} />
            <SaveOrderModal
                actions={actions}
                modelConfigurator={modelConfigurator}
                openModal={openSaveOrderModal}
                setOpenModal={setOpenSaveOrderModal}
                state={state}
                variantConfigurator={variantConfigurator}
            />
            {!state.applyNewApplication && (
                <AffixWrapper>
                    <ConfiguratorSubHeader>
                        <StyledBackButton onClick={onBack} type="link">
                            {t('configuratorDetails:actions.back')}
                        </StyledBackButton>
                    </ConfiguratorSubHeader>
                </AffixWrapper>
            )}

            <Container bannerHeight={44} fixedPosition>
                {!isMobile && (
                    <div className="imageWrapper">
                        <ImageSection variantConfigurator={variantConfigurator} />
                    </div>
                )}
                {isMobile && <ImageSection variantConfigurator={variantConfigurator} />}
                <SummaryContainer>
                    <Title>
                        {t('configuratorDetails:titles.summary', {
                            modelName: translatedString(variantConfigurator.variant.name),
                        })}
                    </Title>
                    <SummaryInfo
                        actions={actions}
                        calculatorContext={calculatorContext}
                        endpoint={endpoint}
                        modelConfigurator={modelConfigurator}
                        state={state}
                        variantConfigurator={variantConfigurator}
                    />
                    {canSubmit && (
                        <StyledSpace direction="vertical">
                            <NextButton
                                className="PlaceOrder"
                                data-model={modelConfigurator.model.name.defaultValue}
                                data-variant={variantName}
                                disabled={!isEmpty(calculatorContext?.errors) || canNotNextForApplyNew}
                                htmlType="submit"
                                onClick={onSubmit}
                                type="primary"
                                block
                            >
                                {t('configuratorDetails:actions.placeOrder')}
                            </NextButton>
                            {!state.applyNewApplication && (
                                <Button
                                    className="SaveOrder"
                                    data-model={modelConfigurator.model.name.defaultValue}
                                    data-variant={variantName}
                                    disabled={!isEmpty(calculatorContext?.errors)}
                                    onClick={sendEmail}
                                    type="tertiary"
                                    block
                                >
                                    {t('configuratorDetails:actions.saveOrder')}
                                </Button>
                            )}
                        </StyledSpace>
                    )}
                </SummaryContainer>
            </Container>
        </>
    );
};

export default ConfiguratorSummary;
