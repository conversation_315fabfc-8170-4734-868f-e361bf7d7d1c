import { CloseOutlined } from '@ant-design/icons';
import { Button as AntdButton } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import DealerSelectField from '../../../../components/fields/DealerSelectField';
import InputField from '../../../../components/fields/InputField';
import { useThemeComponents } from '../../../../themes/hooks';
import { GTMEvents, useConfiguratorTagManagerArguments } from '../../../../utilities/googleTagManager';
import { emailRegex } from '../../../../utilities/validators';
import { ActionsProps, State, ConfiguratorFormValues } from './shared';
import { DropdownContainer, ModalBody } from './ui';

const StyledDealerSelectField = styled(DealerSelectField)`
    &.ant-select {
        &:not(.ant-select-customize-input) {
            & .ant-select-selection-item {
                padding-top: 3px;
                font-size: 16px;
                text-align: right;
            }

            &:hover > .ant-select-selector {
                border-color: #d9d9d9;
            }

            &.ant-select-focused > .ant-select-selector {
                border-color: #d9d9d9;
            }

            & > .ant-select-selector {
                height: 37px;
                padding-right: 0;
            }
        }

        & > .ant-select-arrow {
            right: 0;
        }
    }
`;

const StyledCloseButton = styled(AntdButton)`
    &.ant-btn {
        padding: 0;
        border: none;
        height: 1.5rem;
        width: 1.5rem;
        margin-left: auto;

        &:hover {
            color: #000;
        }

        &:active {
            color: #000;
        }

        &:focus {
            color: #000;
        }
    }
`;

const StyledCloseOutlined = styled(CloseOutlined)`
    font-size: 1.5rem;
`;

const Title = styled.div`
    font-weight: 700;
    font-size: 18px;
`;

const Body = styled.div`
    margin-top: 0.5rem;
    margin-bottom: 1rem;
`;

export const ActionContainer = styled.div`
    text-align: center;
    margin-top: 1rem;

    & > button.ant-btn {
        width: 80%;
    }
`;

const renderDropdowns = menu => <DropdownContainer>{menu}</DropdownContainer>;

export type SaveOrderModalProps = {
    openModal: boolean;
    setOpenModal: (value: boolean) => void;
    state: State;
    actions: ActionsProps;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    variantConfigurator: VariantConfiguratorDetailsFragment;
};

const SaveOrderModal = ({
    openModal,
    state,
    setOpenModal,
    actions,
    modelConfigurator,
    variantConfigurator,
}: SaveOrderModalProps) => {
    const { t } = useTranslation('configuratorDetails');
    const {
        Button,
        FormFields: { InputField },
        Modal,
    } = useThemeComponents();
    const { values, handleSubmit } = useFormikContext<ConfiguratorFormValues>();
    const { setEmailSent, setSaveAsDraft } = actions;
    const { emailSent } = state;
    const company = useCompany();

    const variantName = useMemo(() => {
        switch (variantConfigurator.variant.__typename) {
            case 'LocalVariant':
                return variantConfigurator.variant.name.defaultValue;

            default:
                return '';
        }
    }, [variantConfigurator]);

    const tagManagerArguments = useConfiguratorTagManagerArguments(
        values,
        variantConfigurator,
        modelConfigurator.model.name.defaultValue,
        GTMEvents.SaveYourOrderButtonClick
    );

    const onClose = useCallback(() => {
        setOpenModal(false);
        setEmailSent(false);
    }, [setOpenModal, setEmailSent]);

    const onSent = useCallback(async () => {
        if (values.email && values.dealerId && emailRegex.test(values.email)) {
            setSaveAsDraft(true);
            window.dataLayer?.push(tagManagerArguments);
            handleSubmit();
        }
    }, [handleSubmit, setSaveAsDraft, values.dealerId, values.email]);

    return (
        <Modal footer={null} onCancel={onClose} open={openModal} centered>
            <ModalBody>
                <StyledCloseButton onClick={onClose}>
                    <StyledCloseOutlined />
                </StyledCloseButton>
                <Title>{t('configuratorDetails:saveOrderModal.title')}</Title>
                <Body>{t('configuratorDetails:saveOrderModal.body')}</Body>

                <StyledDealerSelectField
                    {...t('configuratorDetails:fields.preferredDealership', { returnObjects: true })}
                    companyId={company.id}
                    dropdownRender={renderDropdowns}
                    name="dealerId"
                    checkConfiguratorStock
                    isTranslatedOption
                    productionOnly
                    required
                />

                <InputField name="email" placeholder={t('configuratorDetails:labels.email')} />
                <ActionContainer>
                    {!emailSent && (
                        <Button
                            data-model={modelConfigurator.model.name.defaultValue}
                            data-variant={variantName}
                            onClick={onSent}
                            type="primary"
                        >
                            {t('configuratorDetails:actions.enter')}
                        </Button>
                    )}
                    {emailSent && t('configuratorDetails:messages.emailSent')}
                </ActionContainer>
            </ModalBody>
        </Modal>
    );
};

export default SaveOrderModal;
