/* eslint-disable max-len */
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import { useGetLowestMonthlyInstalmentQuery } from '../../../../api/queries/getLowestMonthlyInstalment';
import { ApplicationKind, LowestMonthlyInstalmentPayload } from '../../../../api/types';
import { CalculatorContext } from '../../../../calculator/computing';
import { VehicleFieldContext } from '../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../calculator/types';
import Chatbot from '../../../../components/Chatbot';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useDealerSelectFieldOptions } from '../../../../components/fields/DealerSelectField';
import breakpoints from '../../../../utilities/breakpoints';
import getDefaultTotalPrice from '../../../../utilities/getDefaultTotalPrice';
import { roundUpWithPrecision } from '../../../../utilities/rounding';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import useDefaultMarketTypeValue from '../../../../utilities/useDefaultMarketTypeValue';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { getLocalVariant } from '../../StandardApplicationEntrypoint/CarDetailsPage/CalculatorStage/CalculatorPage/VehicleBrief';
import FooterDrawer from './FooterDrawer';
import DealerSelection from './components/DealerSelection';
import { ActionsProps, State } from './shared';
import { WidgetBar } from './ui';
import Calculator from '../../../../assets/ci/configurator/calculator.svg';

const FooterContainer = styled.div<{ visible: boolean }>`
    position: sticky;
    bottom: 0;
    z-index: 1000;
    opacity: ${({ visible }) => (visible ? 1 : 0)};
    transition: opacity 0.3s ease-in-out;
    pointer-events: ${({ visible }) => (visible ? 'auto' : 'none')};
`;

const DealerSelectionContainer = styled.div`
    background-color: #fff;
    padding: 10px 0;

    & .dealerSelection {
        width: fit-content;
        margin-left: auto;
        margin-right: auto;

        & > .ant-form-item {
            margin-bottom: 0;
        }
    }

    .ant-form-horizontal & {
        .ant-form-item-label {
            display: flex;
            align-items: center;
            padding-bottom: 0;
        }

        .ant-form-item-control {
            margin-left: 8px;

            .ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow
                .ant-select-selection-item,
            .ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow
                .ant-select-selection-placeholder {
                margin-right: 8px;
            }
        }
    }
`;

const FooterWidgetContainer = styled.div`
    background-color: var(--configurator-widget-background, var(--ant-primary-color));

    margin-left: auto;
    padding: 24px;

    display: flex;
    position: relative;
    align-items: center;
    cursor: pointer;

    @media screen and (min-width: ${breakpoints.lg}) {
        max-width: 40vw;
        padding: 24px 50px;
    }
`;

const TextContainer = styled.div``;

const TotalPrice = styled.div`
    font-size: 18px;
    font-weight: 700;
    color: var(--configurator-widget-text-color, #fff);
`;

const CalculatorPrice = styled.div`
    font-size: 16px;
    color: var(--configurator-widget-text-color, #fff);

    display: flex;
    align-items: center;

    & > svg {
        margin-right: 0.5rem;
    }
`;

export type FooterWidgetProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    variantConfigurator: VariantConfiguratorDetailsFragment;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    openDrawer: boolean;
    setOpenDrawer: (value: boolean) => void;
    state: State;
    actions: ActionsProps;
    visible?: boolean;
    bannerHeight?: number;
};

const FooterWidget = ({
    calculatorContext,
    modelConfigurator,
    variantConfigurator,
    endpoint,
    openDrawer,
    setOpenDrawer,
    state,
    actions,
    visible = true,
    bannerHeight,
}: FooterWidgetProps) => {
    const { t } = useTranslation(['configuratorDetails']);
    const companyFormats = useCompanyFormats();
    const { values } = calculatorContext;
    const company = useCompany();

    const { options: dealerOptions } = useDealerSelectFieldOptions({
        companyId: company?.id,
        productionOnly: true,
        checkConfiguratorStock: true,
        isTranslatedOption: true,
    });

    const onCloseDrawer = useCallback(() => setOpenDrawer(false), [setOpenDrawer]);

    // prepare live chat link
    const translateString = useTranslatedString();
    const modelName = translateString(modelConfigurator.model.name);
    const variantName = useMemo(() => {
        switch (variantConfigurator.variant.__typename) {
            case 'LocalVariant':
                return translateString(variantConfigurator.variant.name);

            default:
                return '';
        }
    }, [translateString, variantConfigurator]);

    const vehicleFieldContext = calculatorContext.getFieldContext<VehicleFieldContext>('vehicle');
    const { selectedVehicle } = vehicleFieldContext;
    const showFromValue = useMemo(
        () => !!endpoint.configuratorApplicationModule.showFromValueOnVehicleDetails,
        [endpoint.configuratorApplicationModule]
    );

    const marketTypeValue = useDefaultMarketTypeValue(state.dealerId);

    const [totalPrice, carPrice] = useMemo(() => {
        if (!selectedVehicle) {
            return [];
        }

        const variant = getLocalVariant(selectedVehicle);

        return [
            getDefaultTotalPrice(
                variant.vehiclePrice,
                endpoint.configuratorApplicationModule.marketType,
                marketTypeValue
            ),
            variant.vehiclePrice,
        ];
    }, [selectedVehicle, endpoint.configuratorApplicationModule, marketTypeValue]);

    const data: LowestMonthlyInstalmentPayload = useMemo(
        () => ({
            variantSuiteId: selectedVehicle?.versioning.suiteId,
            totalPrice,
            carPrice,
            bankModuleId: endpoint.configuratorApplicationModule.bankModuleId,
            applicationModuleId: endpoint.configuratorApplicationModule.id,
            dealerId: state.dealerId,
            assignedOnly: true,
        }),
        [selectedVehicle, totalPrice, carPrice, endpoint.configuratorApplicationModule, state.dealerId]
    );

    const { data: lowestMonthlyInstalmentData } = useGetLowestMonthlyInstalmentQuery({
        variables: {
            data,
        },
        skip: !showFromValue,
    });

    return (
        <>
            <FooterDrawer
                actions={actions}
                bannerHeight={bannerHeight}
                calculatorContext={calculatorContext}
                endpoint={endpoint}
                modelConfigurator={modelConfigurator}
                onClose={onCloseDrawer}
                state={state}
                variantConfigurator={variantConfigurator}
                visible={openDrawer}
            />
            <FooterContainer visible={visible}>
                {dealerOptions.length > 1 && (
                    <DealerSelectionContainer>
                        <DealerSelection
                            label={t('configuratorDetails:labels.displayOptionsFrom')}
                            placement="topRight"
                        />
                    </DealerSelectionContainer>
                )}
                <FooterWidgetContainer onClick={() => setOpenDrawer(true)}>
                    <WidgetBar />
                    <TextContainer>
                        <TotalPrice>
                            {t('configuratorDetails:footerWidget.totalPrice', {
                                value: companyFormats.formatAmountWithCurrency(values.totalPrice),
                            })}
                        </TotalPrice>
                        {showFromValue &&
                            endpoint.configuratorApplicationModule.bankModuleId &&
                            values.financeProduct && (
                                <CalculatorPrice>
                                    <Calculator />
                                    <span>
                                        {t('configuratorDetails:footerWidget.installment', {
                                            value: companyFormats.formatAmountWithCurrency(
                                                roundUpWithPrecision(
                                                    lowestMonthlyInstalmentData?.lowestMonthlyInstalment
                                                )
                                            ),
                                        })}
                                    </span>
                                </CalculatorPrice>
                            )}
                    </TextContainer>
                    <Chatbot
                        applicationKind={ApplicationKind.Configurator}
                        chatSetting={{
                            ...endpoint.configuratorApplicationModule?.liveChatSetting,
                            modelName,
                            variantName,
                        }}
                    />
                </FooterWidgetContainer>
            </FooterContainer>
        </>
    );
};

export default FooterWidget;
