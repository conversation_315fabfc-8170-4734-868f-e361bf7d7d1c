/* eslint-disable max-len */
import { Divider } from 'antd';
import { MutableRefObject, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { CalculatorContext } from '../../../../calculator/computing';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { useBanner } from './Banner';
import PageDescription from './PageDescription';
import Vehicles from './Vehicles';
import ViewContainer from './ViewContainer';
import { ActionsProps, State } from './shared';

const StyledDivider = styled(Divider)`
    margin: 0;
    border-top-color: #ccc;
`;

export type ConfiguratorDetailsProps = {
    modelConfiguratorSettings: ModelConfiguratorDetailsFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    state: State;
    actions: ActionsProps;
    dealerRef: MutableRefObject<string>;
    updateDealerRef: (value: string) => void;
};

const ConfiguratorDetails = ({
    modelConfiguratorSettings,
    calculatorContext,
    endpoint,
    state,
    actions,
    dealerRef,
    updateDealerRef,
}: ConfiguratorDetailsProps) => {
    const vehiclesRef = useRef<HTMLDivElement>();
    const introSectionRef = useRef<HTMLDivElement>();

    const banner = useBanner(modelConfiguratorSettings);

    useEffect(() => {
        if (dealerRef.current !== state.dealerId) {
            if (dealerRef.current) {
                vehiclesRef.current.scrollIntoView({ block: 'end' });
            }

            updateDealerRef(state.dealerId);
        }
    }, [dealerRef, state.dealerId, updateDealerRef]);

    return (
        <>
            <div ref={introSectionRef}>
                {banner.render()}
                <PageDescription modelConfigurator={modelConfiguratorSettings} />
                <div ref={vehiclesRef}>
                    <Vehicles
                        actions={actions}
                        calculatorContext={calculatorContext}
                        modelConfigurator={modelConfiguratorSettings}
                        state={state}
                    />
                </div>
            </div>
            <StyledDivider />
            {state.dealerId && (
                <ViewContainer
                    actions={actions}
                    bannerHeight={banner.showExternalLink ? banner.externalLinkHeight : 0}
                    calculatorContext={calculatorContext}
                    dealerRef={dealerRef}
                    endpoint={endpoint}
                    introSectionRef={introSectionRef}
                    modelConfigurator={modelConfiguratorSettings}
                    state={state}
                />
            )}
        </>
    );
};

export default ConfiguratorDetails;
