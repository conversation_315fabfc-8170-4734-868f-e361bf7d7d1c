/* eslint-disable max-len */
import { DownOutlined, MinusCircleFilled, PlusCircleFilled, UpOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useFormikContext } from 'formik';
import { head, isEmpty } from 'lodash/fp';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { BlockDetailsFragment } from '../../../../api/fragments/BlockDetails';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { InventoryDetailsPublicDataFragment } from '../../../../api/fragments/InventoryDetailsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import { useGetLowestMonthlyInstalmentQuery } from '../../../../api/queries/getLowestMonthlyInstalment';
import {
    BlockType,
    CompanyTheme,
    FinancingPreferenceValue,
    LowestMonthlyInstalmentPayload,
    OptionKind,
    OptionType,
} from '../../../../api/types';
import { GenericGridCalculator, InsuranceGridCalculator } from '../../../../calculator/Implementations';
import { CalculatorContext } from '../../../../calculator/computing';
import { BankFieldContext } from '../../../../calculator/computing/defaultFields/bankField';
import { InsurerFieldContext } from '../../../../calculator/computing/defaultFields/insurerField';
import { VehicleFieldContext } from '../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../calculator/types';
import ParagraphEllipsis from '../../../../components/ParagraphEllipsis';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import DealerSelectField, { useDealerSelectFieldOptions } from '../../../../components/fields/DealerSelectField';
import CheckboxField from '../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../themes/hooks';
import getDefaultTotalPrice from '../../../../utilities/getDefaultTotalPrice';
import renderMarkdown from '../../../../utilities/renderMarkdown';
import { roundUpWithPrecision } from '../../../../utilities/rounding';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import useDefaultMarketTypeValue from '../../../../utilities/useDefaultMarketTypeValue';
import usePlaceholderValues from '../../../../utilities/usePlaceholderValues';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
    hasNoFinancingScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import FinancingDisclaimer from '../../../shared/CIPage/FinancingDisclaimer';
import TradeIn from '../../../shared/CIPage/TradeIn';
import { CheckboxContainer, StyledInfoCircleFilled } from '../../../shared/CIPage/ui';
import useDealerSpecificPaymentSetting from '../../../shared/useDealerSpecificPaymentSetting';
import useValidPromoCode from '../../../shared/useValidPromoCode';
import useApplyNewCondition from '../../FinderApplicationPublicAccessEntrypoint/OfferPage/applyNew/useApplyNewCondition';
import { OfferPageActionStateProps } from '../../FinderApplicationPublicAccessEntrypoint/types';
import { getLocalVariant } from '../../StandardApplicationEntrypoint/CarDetailsPage/CalculatorStage/CalculatorPage/VehicleBrief';
import CarPriceField from './CarPrice';
import PromoCodeField from './PromoCodeField';
import ProceedToSummary from './components/ProceedToSummary';
import TermsAndConditionModal from './components/TermsAndConditionModal';
import { ApplicationScenarioConfig } from './helpers';
import { ActionsProps, State, ConfiguratorFormValues } from './shared';
import {
    AdditionalInfo,
    AdditionalInfoContainer,
    Container,
    Content,
    ExpendButton,
    Label,
    PackageDetailsContainer,
    SectionContainer,
    StyledDivider,
    Terms,
    TermsContainer,
    Value,
} from './ui';

export type SummaryInfoProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    variantConfigurator: VariantConfiguratorDetailsFragment;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    isSummaryContent?: boolean;
    state: State;
    actions: ActionsProps;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    onProceedToSummary?: () => void;
};

const THEME_TEXT_COLOR = 'var(--configurator-widget-popup-text-color, #fff)';

const DealerFieldContainer = styled.div<{ isSummaryContent: boolean }>`
    & .ant-form-item {
        margin-bottom: 1rem;
        background-color: transparent !important;

        & > .ant-form-item-row {
            display: var(--configurator-widget-dealer-select-container-form-item-display);

            & > .ant-form-item-label {
                flex: initial;
            }

            & > .ant-form-item-control {
                flex: 1 1 0;
            }
        }
    }

    & .ant-form-item-label {
        height: var(--configurator-widget-dealer-select-container-height);
        font-size: 16px;
        font-weight: 700;

        & > label {
            color: ${({ isSummaryContent }) => (isSummaryContent ? '#000000' : THEME_TEXT_COLOR)};
            height: var(--configurator-widget-dealer-select-container-height);
            padding-bottom: 4px;

            &:after {
                display: none;
            }

            &.ant-form-item-required:not(.ant-form-item-required-mark-optional):before {
                display: none;
            }
        }
    }

    & .ant-form-item-control {
        height: var(--configurator-widget-dealer-select-container-height);

        & .ant-form-item-control-input {
            height: var(--configurator-widget-dealer-select-container-height);
            min-height: var(--configurator-widget-dealer-select-container-height);
        }
    }

    & .ant-select-arrow > svg {
        fill: ${({ isSummaryContent }) => (isSummaryContent ? '#000000' : THEME_TEXT_COLOR)};
    }
`;

const StyledDealerSelectField = styled(DealerSelectField)<{ isSummaryContent: boolean; companyTheme?: CompanyTheme }>`
    &.ant-select:not(.ant-select-customize-input) {
        & > .ant-select-selector {
            height: auto;
            min-height: auto;
            ${({ companyTheme }) =>
                [CompanyTheme.Default, CompanyTheme.Volkswagen, CompanyTheme.Skoda].includes(companyTheme)
                    ? 'border-bottom: none; padding-left: 0;'
                    : ''}
        }

        & .ant-select-selection-item {
            text-align: right;
            color: ${({ isSummaryContent, companyTheme }) =>
                isSummaryContent || companyTheme === CompanyTheme.Porsche ? '#000000 !important' : THEME_TEXT_COLOR};
        }

        ${({ companyTheme }) =>
            companyTheme === CompanyTheme.Porsche &&
            `
            & .ant-select-arrow > svg {
                fill: #000000 !important;
            }
        `}
        & .ant-select-selection-placeholder {
            text-align: right;
        }
    }
`;

const ViewMoreLessLink = styled(Button)`
    &.ant-btn {
        text-align: left;
        display: flex;
        padding: 0;
        color: #000;
        align-items: baseline;

        & > .anticon {
            font-size: 10px;
            margin-left: auto;
        }
    }
`;

const StyledInstallment = styled.span`
    margin-right: 5px;
`;

const PriceDisclaimerContainer = styled.div`
    opacity: 0.8;
`;

const renderDropdowns = menu => <div>{menu}</div>;

export type OptionInfoDetailsProps = {
    optionBlock: BlockDetailsFragment;
    isSummaryContent: boolean;
};

const OptionInfoDetails = ({ optionBlock, isSummaryContent }: OptionInfoDetailsProps) => {
    const { values } = useFormikContext<ConfiguratorFormValues>();
    const companyFormats = useCompanyFormats();
    const translatedString = useTranslatedString();

    const optionSetting = useMemo(() => {
        if (optionBlock?.__typename !== 'OptionsBlock') {
            return null;
        }

        return optionBlock.optionSettings;
    }, [optionBlock]);

    const valueOption = useMemo(
        () => values.options.find(valueOption => valueOption.id === optionBlock.id),
        [optionBlock.id, values.options]
    );

    if (optionBlock?.__typename !== 'OptionsBlock' || !values?.options || !valueOption) {
        return null;
    }

    switch (optionSetting?.__typename) {
        case 'SingleSelectOptionSettings': {
            if (valueOption.kind !== OptionKind.SingleSelect || !valueOption.values) {
                return null;
            }

            const { values: optionValues } = valueOption;

            const selectedOption = optionSetting.options.find(option => option.id === optionValues);

            return (
                <>
                    <Content>
                        <Label>{translatedString(optionBlock.sectionTitle)}</Label>
                        <Value>
                            {selectedOption.type === OptionType.Price
                                ? companyFormats.formatAmountWithCurrency(selectedOption.valuePrice)
                                : translatedString(selectedOption.valueText)}
                        </Value>
                    </Content>
                    <AdditionalInfoContainer>{translatedString(selectedOption.label)}</AdditionalInfoContainer>
                    <StyledDivider />
                </>
            );
        }

        case 'MultiSelectOptionSettings': {
            if (valueOption.kind !== OptionKind.MultiSelect || !valueOption.values) {
                return null;
            }

            const { values: optionValues } = valueOption;

            const selectedOptions = optionSetting.options.filter(option => optionValues.includes(option.id));

            return (
                <>
                    <Content>
                        <Label>{translatedString(optionBlock.sectionTitle)}</Label>
                    </Content>
                    <AdditionalInfoContainer>
                        {selectedOptions.map(selectedOption => (
                            <AdditionalInfo key={`additional-info-multi-${selectedOption.id}`}>
                                <span>{translatedString(selectedOption.label)}</span>
                                <span>
                                    {selectedOption.type === OptionType.Price
                                        ? companyFormats.formatAmountWithCurrency(selectedOption.valuePrice)
                                        : translatedString(selectedOption.valueText)}
                                </span>
                            </AdditionalInfo>
                        ))}
                    </AdditionalInfoContainer>
                    <StyledDivider />
                </>
            );
        }

        case 'ComboOptionSettings': {
            const comboOptionBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Options);

            if (valueOption.kind !== OptionKind.Combo || !comboOptionBlock) {
                return null;
            }

            const { values: optionValues } = valueOption;

            const selectedOptions = optionSetting.options.filter(option =>
                optionValues.some(optionValue => optionValue.id === option.id)
            );

            return (
                <>
                    <Content>
                        <Label>{translatedString(optionBlock.sectionTitle)}</Label>
                        {optionSetting.price && (
                            <Value>{companyFormats.formatAmountWithCurrency(optionSetting.price)}</Value>
                        )}
                    </Content>
                    <AdditionalInfoContainer>
                        {selectedOptions.map(selectedOption => (
                            <AdditionalInfo key={`additional-info-combo-${selectedOption.id}`}>
                                <span>{translatedString(selectedOption.label)}</span>
                                <span>
                                    {optionValues.find(optionValue => optionValue.id === selectedOption.id)?.value}
                                </span>
                            </AdditionalInfo>
                        ))}
                    </AdditionalInfoContainer>
                    <StyledDivider />
                </>
            );
        }

        default:
            return null;
    }
};

export type OptionInfoProps = {
    optionBlocks: BlockDetailsFragment[];
    isSummaryContent: boolean;
};

const OptionInfo = ({ optionBlocks, isSummaryContent }: OptionInfoProps) => (
    <>
        {optionBlocks.map(optionBlock => (
            <OptionInfoDetails key={optionBlock.id} isSummaryContent={isSummaryContent} optionBlock={optionBlock} />
        ))}
    </>
);

const SummaryInfo = ({
    calculatorContext,
    variantConfigurator,
    endpoint,
    isSummaryContent = true,
    state,
    actions,
    modelConfigurator,
    onProceedToSummary,
}: SummaryInfoProps) => {
    const { values } = useFormikContext<ConfiguratorFormValues>();
    const [packageShowDetails, setPackageShowDetails] = useState<boolean>(false);
    const { t } = useTranslation(['calculators', 'configuratorDetails', 'configuratorJourney']);
    const { values: calculatorValues, latestErrorMessage, setLatestErrorMessage, change } = calculatorContext;
    const [openTermsModal, setOpenTermsModal] = useState<boolean>(false);
    const [viewMore, setViewMore] = useState<boolean>(true);
    const companyFormats = useCompanyFormats();
    const translatedString = useTranslatedString();
    const company = useCompany();

    const {
        Checkbox,
        ErrorMessageBox,
        ConfiguratorDepositDisplay,
        theme,
        Tooltip,
        FormFields: { SelectField },
        WarningMessageBox,
    } = useThemeComponents();

    const { options: dealerOptions } = useDealerSelectFieldOptions({
        companyId: company?.id,
        productionOnly: true,
        checkConfiguratorStock: true,
        isTranslatedOption: true,
    });

    const { packageSetting, exteriorSetting, interiorSetting, optionBlocks } = useMemo(() => {
        // package
        const packageBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Package);

        const selectedPackageBlock = variantConfigurator.blocks.find(
            block => block.__typename === 'PackageBlock' && block.id === packageBlock?.blockId
        );

        const packageSetting =
            selectedPackageBlock?.__typename === 'PackageBlock'
                ? selectedPackageBlock.packageSettings.find(
                      packageSetting => packageSetting.id === head(packageBlock?.ids)
                  )
                : null;

        // exterior color
        const exteriorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);

        const selectedExteriorBlock = variantConfigurator.blocks.find(
            block => block.__typename === 'ColorBlock' && block.id === exteriorBlock?.blockId
        );

        const exteriorSetting =
            selectedExteriorBlock?.__typename === 'ColorBlock'
                ? selectedExteriorBlock.colorSettings.find(colorSetting => colorSetting.id === head(exteriorBlock?.ids))
                : null;

        // interior color
        const interiorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);

        const selectedInteriorBlock = variantConfigurator.blocks.find(
            block => block.__typename === 'TrimBlock' && block.id === interiorBlock?.blockId
        );

        const interiorSetting =
            selectedInteriorBlock?.__typename === 'TrimBlock'
                ? selectedInteriorBlock.trimSettings.find(trimSetting => trimSetting.id === head(interiorBlock?.ids))
                : null;

        // option setting
        const optionBlocks = variantConfigurator.blocks.filter(block => block.__typename === 'OptionsBlock');

        return {
            packageSetting,
            exteriorSetting,
            interiorSetting,
            optionBlocks,
        };
    }, [values, variantConfigurator.blocks]);

    const { availableFinanceProducts, selectedBank } = calculatorContext.getFieldContext<BankFieldContext>('bank');

    const { availableInsuranceProducts } = calculatorContext.getFieldContext<InsurerFieldContext>('insurerId');
    // Intentionally froze selected bank, so it won't rerender on bank change
    const initialSelectedBank = useRef(selectedBank);

    const marketTypeValue = useDefaultMarketTypeValue(values.dealerId, initialSelectedBank.current);
    const placeholders = usePlaceholderValues({
        module: endpoint.configuratorApplicationModule,
        marketTypeValue,
    });

    const dynamicMarketTypeValue = useDefaultMarketTypeValue(values.dealerId, selectedBank);
    const dynamicPlaceholderValues = usePlaceholderValues({
        module: endpoint.configuratorApplicationModule,
        marketTypeValue: dynamicMarketTypeValue,
    });

    const priceDisclaimer = useMemo(() => {
        const priceDisclaimerData = endpoint.configuratorApplicationModule?.priceDisclaimer.overrides?.find(
            ({ dealerId: inputId }) => inputId === values.dealerId
        );

        return priceDisclaimerData?.value
            ? priceDisclaimerData.value.map(disclaimer => translatedString(disclaimer, placeholders))
            : endpoint.configuratorApplicationModule.priceDisclaimer?.defaultValue.map(disclaimer =>
                  translatedString(disclaimer, placeholders)
              );
    }, [
        endpoint.configuratorApplicationModule.priceDisclaimer?.defaultValue,
        endpoint.configuratorApplicationModule.priceDisclaimer.overrides,
        translatedString,
        values.dealerId,
        placeholders,
    ]);

    const vehicleFieldContext = calculatorContext.getFieldContext<VehicleFieldContext>('vehicle');
    const { selectedVehicle } = vehicleFieldContext;
    const showFromValue = useMemo(
        () => !!endpoint.configuratorApplicationModule.showFromValueOnVehicleDetails,
        [endpoint.configuratorApplicationModule]
    );

    const [totalPrice, carPrice] = useMemo(() => {
        if (!selectedVehicle) {
            return [];
        }

        const variant = getLocalVariant(selectedVehicle);

        return [
            getDefaultTotalPrice(
                variant.vehiclePrice,
                endpoint.configuratorApplicationModule.marketType,
                marketTypeValue
            ),
            variant.vehiclePrice,
        ];
    }, [selectedVehicle, endpoint.configuratorApplicationModule.marketType, marketTypeValue]);

    const hasValidPromoCode = useValidPromoCode({
        dealerId: values.dealerId,
        variantSuiteId: selectedVehicle?.versioning.suiteId,
        promoCodeModuleId: endpoint.configuratorApplicationModule.promoCodeModule?.id,
        applicationModuleId: endpoint.configuratorApplicationModule.id,
    });

    const data: LowestMonthlyInstalmentPayload = useMemo(
        () => ({
            variantSuiteId: selectedVehicle?.versioning.suiteId,
            totalPrice,
            carPrice,
            bankModuleId: endpoint.configuratorApplicationModule.bankModuleId,
            applicationModuleId: endpoint.configuratorApplicationModule.id,
            dealerId: values.dealerId,
            assignedOnly: true,
        }),
        [selectedVehicle, totalPrice, carPrice, endpoint.configuratorApplicationModule, values.dealerId]
    );

    const { data: lowestMonthlyInstalmentData } = useGetLowestMonthlyInstalmentQuery({
        variables: {
            data,
        },
        skip: !showFromValue,
    });

    const applicationScenario = useMemo((): ApplicationScenarioConfig => {
        const withFinancing = endpoint.configuratorApplicationModule.showFinanceCalculator;
        const isFinancingOptional = withFinancing
            ? endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Optional
            : false;
        const withInsurance = hasInsuranceScenario(endpoint.configuratorApplicationModule.scenarios);
        const isInsuranceOptional = withInsurance ? endpoint.configuratorApplicationModule.isInsuranceOptional : false;

        return {
            withFinancing,
            isFinancingOptional,
            withInsurance,
            isInsuranceOptional,
        };
    }, [
        endpoint.configuratorApplicationModule.financingPreference,
        endpoint.configuratorApplicationModule.isInsuranceOptional,
        endpoint.configuratorApplicationModule.scenarios,
        endpoint.configuratorApplicationModule.showFinanceCalculator,
    ]);

    // Need to detect the values, as this component remounted from
    // configurator vehicle page to summary page
    const initialApplicationConfiguration = {
        withFinancing: values.configuration.withFinancing ?? applicationScenario.withFinancing ?? false, // If optional, by default we open the finance calculator
        withInsurance: values.configuration.withInsurance ?? applicationScenario.withInsurance,
        tradeIn: endpoint.configuratorApplicationModule.tradeIn,
        testDrive: endpoint.configuratorApplicationModule.testDrive,
    };

    const [openCalculator, setOpenCalculator] = useState(() => {
        if (endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Request) {
            return values?.configuration?.requestForFinancing ?? false;
        }

        if (hasFinancingScenario(endpoint.configuratorApplicationModule.scenarios)) {
            return initialApplicationConfiguration.withFinancing;
        }

        return endpoint.configuratorApplicationModule.showFinanceCalculator;
    });

    const toggle = useCallback(
        value => {
            change('isFinancingEnabled', value.target.checked);
            setOpenCalculator(value.target.checked);
        },
        [change]
    );

    const toggleRequest = useCallback(value => {
        setOpenCalculator(value.target.checked);
    }, []);

    const closeErrorMessageBox = useCallback(() => {
        setLatestErrorMessage(latestErrorMessage?.fieldName, null);
    }, [latestErrorMessage?.fieldName, setLatestErrorMessage]);

    const financingTitle = useMemo(() => {
        if (hasFinancingScenario(endpoint.configuratorApplicationModule.scenarios)) {
            if (endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Request) {
                return t('calculators:fields.requestForFinancing.label');
            }

            return t('calculators:fields.withFinancing.label');
        }
        if (endpoint.configuratorApplicationModule.showFinanceCalculator) {
            return t('calculators:fields.financeCalculator.label');
        }

        return null;
    }, [
        endpoint.configuratorApplicationModule.financingPreference,
        endpoint.configuratorApplicationModule.scenarios,
        endpoint.configuratorApplicationModule.showFinanceCalculator,
        t,
    ]);

    const stockVariants: InventoryDetailsPublicDataFragment[] = useMemo(
        () => variantConfigurator?.inventories || [],
        [variantConfigurator?.inventories]
    );

    const headerFinancing = useMemo(() => {
        if (endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Request) {
            return null;
        }

        return (
            <CheckboxContainer hasTooltip={false} hideCheckbox={!applicationScenario.isFinancingOptional} hasPrice>
                {applicationScenario.isFinancingOptional ? (
                    <CheckboxField customComponent={Checkbox} name="configuration.withFinancing" onClick={toggle}>
                        {financingTitle}
                    </CheckboxField>
                ) : (
                    <span>{financingTitle}</span>
                )}
            </CheckboxContainer>
        );
    }, [
        Checkbox,
        applicationScenario.isFinancingOptional,
        endpoint.configuratorApplicationModule.financingPreference,
        financingTitle,
        toggle,
    ]);

    const headerRequestFinancing = useMemo(() => {
        if (endpoint.configuratorApplicationModule.financingPreference !== FinancingPreferenceValue.Request) {
            return null;
        }

        const tooltipText = t('configuratorDetails:requestForFinancing.tooltip');

        return (
            <CheckboxContainer hasPrice={false} hasTooltip>
                <CheckboxField
                    customComponent={Checkbox}
                    name="configuration.requestForFinancing"
                    onClick={toggleRequest}
                >
                    {t('configuratorDetails:requestForFinancing.label')}
                </CheckboxField>
                {!isEmpty(tooltipText) && (
                    <Tooltip title={tooltipText}>
                        <StyledInfoCircleFilled />
                    </Tooltip>
                )}
            </CheckboxContainer>
        );
    }, [Checkbox, Tooltip, endpoint.configuratorApplicationModule.financingPreference, t, toggleRequest]);

    const showFinancingSection =
        endpoint.configuratorApplicationModule.bankModuleId &&
        calculatorValues.financeProduct &&
        (applicationScenario.withFinancing || endpoint.configuratorApplicationModule.showFinanceCalculator);

    const isFinancingSectionBigBottom = useMemo(
        () =>
            !endpoint.configuratorApplicationModule.testDrive &&
            !endpoint.configuratorApplicationModule.tradeIn &&
            !(applicationScenario.withInsurance || endpoint.configuratorApplicationModule.showInsuranceCalculator)
                ? true
                : values.configuration.withFinancing,
        [
            applicationScenario.withInsurance,
            endpoint.configuratorApplicationModule.showInsuranceCalculator,
            endpoint.configuratorApplicationModule.testDrive,
            endpoint.configuratorApplicationModule.tradeIn,
            values.configuration.withFinancing,
        ]
    );

    const isInsuranceSectionBigBottom = useMemo(
        () =>
            !endpoint.configuratorApplicationModule.testDrive && !endpoint.configuratorApplicationModule.tradeIn
                ? true
                : values.configuration.withInsurance,
        [
            endpoint.configuratorApplicationModule.testDrive,
            endpoint.configuratorApplicationModule.tradeIn,
            values.configuration.withInsurance,
        ]
    );

    const showNoFPWarning =
        availableFinanceProducts.length <= 0 &&
        ((hasFinancingScenario(endpoint.configuratorApplicationModule.scenarios) &&
            endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Mandatory) ||
            (hasNoFinancingScenario(endpoint.configuratorApplicationModule.scenarios) &&
                endpoint.configuratorApplicationModule.showFinanceCalculator));

    const { canApplyFinancing, canApplyRequestFinancing, canApplyInsurance, canApplyAppointment } =
        useApplyNewCondition(state.applyNewApplication, endpoint.configuratorApplicationModule);

    const isFinancingSectionVisible = useMemo(
        () =>
            (state.applyNewApplication && (canApplyFinancing || canApplyRequestFinancing)) ||
            !state.applyNewApplication,
        [state.applyNewApplication, canApplyFinancing, canApplyRequestFinancing]
    );

    const isInsuranceSectionVisible = useMemo(
        () =>
            availableInsuranceProducts.length > 0 &&
            ((state.applyNewApplication && canApplyInsurance) || !state.applyNewApplication),
        [availableInsuranceProducts.length, state.applyNewApplication, canApplyInsurance]
    );

    const isAppointmentSectionVisible = useMemo(
        () => (state.applyNewApplication && canApplyAppointment) || !state.applyNewApplication,
        [state.applyNewApplication, canApplyAppointment]
    );

    const [termsTitle, termsText] = useMemo(() => {
        if (values.dealerId) {
            const foundTitle = endpoint.configuratorApplicationModule.termsTitle.overrides.find(
                i => i.dealerId === values.dealerId
            )?.value;
            const foundText = endpoint.configuratorApplicationModule.termsText.overrides.find(
                i => i.dealerId === values.dealerId
            )?.value;

            return [
                foundTitle?.defaultValue ? foundTitle : endpoint.configuratorApplicationModule.termsTitle.defaultValue,
                foundText?.defaultValue ? foundText : endpoint.configuratorApplicationModule.termsText.defaultValue,
            ];
        }

        return [
            endpoint.configuratorApplicationModule.termsTitle.defaultValue,
            endpoint.configuratorApplicationModule.termsText.defaultValue,
        ];
    }, [
        endpoint.configuratorApplicationModule.termsText,
        endpoint.configuratorApplicationModule.termsTitle,
        values.dealerId,
    ]);

    const { paymentSetting, depositAmount } = useDealerSpecificPaymentSetting(
        endpoint.configuratorApplicationModule,
        values.dealerId
    );

    return (
        <>
            {(termsTitle || termsText) && (
                <TermsAndConditionModal
                    body={translatedString(termsText)}
                    modalVisible={openTermsModal}
                    setModalVisible={setOpenTermsModal}
                    title={translatedString(termsTitle)}
                />
            )}
            <Container>
                <SectionContainer isSummaryContent={isSummaryContent} hideFormItemBorder isCheckboxChecked>
                    {!state.applyNewApplication && dealerOptions.length > 1 && (
                        <>
                            <DealerFieldContainer isSummaryContent={isSummaryContent}>
                                <StyledDealerSelectField
                                    {...t('configuratorDetails:fields.preferredDealership', { returnObjects: true })}
                                    companyId={company.id}
                                    companyTheme={theme}
                                    customComponent={SelectField}
                                    dropdownRender={renderDropdowns}
                                    isSummaryContent={isSummaryContent}
                                    name="dealerId"
                                    placement="bottomRight"
                                    size="small"
                                    checkConfiguratorStock
                                    isTranslatedOption
                                    productionOnly
                                />
                            </DealerFieldContainer>
                            <StyledDivider />
                        </>
                    )}
                    <Content>
                        <Label>{t('configuratorDetails:labels.basicPrice')}</Label>
                        <Value>{companyFormats.formatAmountWithCurrency(calculatorValues.carPrice)}</Value>
                    </Content>
                    <StyledDivider />
                    {packageSetting && (
                        <>
                            <Content>
                                <Label>{translatedString(packageSetting.packageName)}</Label>
                                <Value>
                                    {packageSetting.packageType.__typename === 'PackageTypeWithPrice'
                                        ? companyFormats.formatAmountWithCurrency(packageSetting.packageType.price)
                                        : packageSetting.packageType.description}
                                </Value>
                            </Content>
                            {packageSetting.additionalDetails && packageSetting.additionalDetails.length > 0 && (
                                <>
                                    <ExpendButton
                                        icon={packageShowDetails ? <MinusCircleFilled /> : <PlusCircleFilled />}
                                        onClick={() => setPackageShowDetails(prevState => !prevState)}
                                    >
                                        {t(
                                            `configuratorDetails:labels.${
                                                packageShowDetails ? 'hideDetails' : 'showDetails'
                                            }`
                                        )}
                                    </ExpendButton>
                                    {packageShowDetails &&
                                        (packageSetting.additionalDetails || []).map((detail, index) => (
                                            <PackageDetailsContainer
                                                key={`package-details-container-${index.toString()}`}
                                            >
                                                <Label>
                                                    <u>{translatedString(detail?.title)}</u>
                                                </Label>
                                                <Value>
                                                    {renderMarkdown(translatedString(detail.description) || '')}
                                                </Value>
                                            </PackageDetailsContainer>
                                        ))}
                                </>
                            )}
                            <StyledDivider />
                        </>
                    )}

                    {exteriorSetting && (
                        <>
                            <Content>
                                <Label>{t('configuratorDetails:labels.exteriorColour')}</Label>
                                <Value>
                                    {exteriorSetting.price
                                        ? companyFormats.formatAmountWithCurrency(exteriorSetting.price)
                                        : t('configuratorDetails:labels.included')}
                                </Value>
                            </Content>
                            <AdditionalInfoContainer>{translatedString(exteriorSetting.name)}</AdditionalInfoContainer>
                            <StyledDivider />
                        </>
                    )}

                    {interiorSetting && (
                        <>
                            <Content>
                                <Label>{t('configuratorDetails:labels.interiorColour')}</Label>
                                <Value>
                                    {interiorSetting.price
                                        ? companyFormats.formatAmountWithCurrency(interiorSetting.price)
                                        : t('configuratorDetails:labels.included')}
                                </Value>
                            </Content>
                            <AdditionalInfoContainer>{translatedString(interiorSetting.name)}</AdditionalInfoContainer>
                            <StyledDivider />
                        </>
                    )}

                    <OptionInfo isSummaryContent={isSummaryContent} optionBlocks={optionBlocks} />

                    {!state.applyNewApplication && hasValidPromoCode && (
                        <>
                            <Content>
                                <Label>{t('configuratorDetails:labels.promoCode')}</Label>
                            </Content>
                            <AdditionalInfoContainer>
                                <PromoCodeField
                                    actions={actions}
                                    calculatorContext={calculatorContext}
                                    endpoint={endpoint}
                                    isSummaryContent={isSummaryContent}
                                    state={state}
                                />
                            </AdditionalInfoContainer>
                            <StyledDivider />
                        </>
                    )}

                    <Content>
                        <Label>{t('configuratorDetails:labels.totalPrice')}</Label>
                        <Value>{companyFormats.formatAmountWithCurrency(calculatorValues.totalPrice)}</Value>
                    </Content>
                    {showFromValue &&
                        endpoint.configuratorApplicationModule.showFinanceCalculator &&
                        endpoint.configuratorApplicationModule.bankModuleId &&
                        calculatorValues.financeProduct && (
                            <AdditionalInfoContainer>
                                <AdditionalInfo>
                                    <span>{t('configuratorDetails:labels.monthlyPayment')}</span>
                                    <ViewMoreLessLink onClick={() => setViewMore(prevState => !prevState)} type="link">
                                        <StyledInstallment>
                                            {t('configuratorDetails:footerWidget.installment', {
                                                value: companyFormats.formatAmountWithCurrency(
                                                    roundUpWithPrecision(
                                                        lowestMonthlyInstalmentData?.lowestMonthlyInstalment
                                                    )
                                                ),
                                            })}
                                        </StyledInstallment>
                                        {viewMore ? <UpOutlined /> : <DownOutlined />}
                                    </ViewMoreLessLink>
                                </AdditionalInfo>
                            </AdditionalInfoContainer>
                        )}
                    {priceDisclaimer.map((disclaimer, index) => (
                        <PriceDisclaimerContainer>
                            <ParagraphEllipsis
                                collapseText={t('calculators:readLess')}
                                expandText={t('calculators:readMore')}
                                row={1}
                            >
                                {renderMarkdown(disclaimer)}
                            </ParagraphEllipsis>
                        </PriceDisclaimerContainer>
                    ))}
                </SectionContainer>
                {viewMore && (
                    <>
                        {isFinancingSectionVisible && (
                            <>
                                {showNoFPWarning && (
                                    <SectionContainer isSummaryContent={isSummaryContent} isCheckboxChecked>
                                        <WarningMessageBox
                                            description={t(`calculators:notification.noFinanceProducts.description`)}
                                            message={t(`calculators:notification.noFinanceProducts.title`)}
                                        />
                                    </SectionContainer>
                                )}
                                {availableFinanceProducts.length > 0 && (
                                    <SectionContainer
                                        hidden={!showFinancingSection}
                                        isCheckboxChecked={isFinancingSectionBigBottom}
                                        isSummaryContent={isSummaryContent}
                                    >
                                        {headerFinancing}
                                        {headerRequestFinancing}
                                        <div hidden={!openCalculator} style={{ marginTop: '16px' }}>
                                            <GenericGridCalculator calculatorContext={calculatorContext}>
                                                <CarPriceField key="carPrice" fieldKey="carPrice" size={0} />
                                            </GenericGridCalculator>
                                            <FinancingDisclaimer
                                                applicationModule={endpoint.configuratorApplicationModule}
                                                dealerId={values.dealerId}
                                                disclaimerParameters={dynamicPlaceholderValues}
                                                financeProductId={calculatorValues.financeProduct}
                                                totalPrice={totalPrice}
                                                vehicleId={data.variantSuiteId}
                                            />
                                            {latestErrorMessage?.message && (
                                                <ErrorMessageBox
                                                    description={latestErrorMessage.message}
                                                    onClose={closeErrorMessageBox}
                                                    closable
                                                />
                                            )}
                                        </div>
                                    </SectionContainer>
                                )}
                            </>
                        )}

                        {isInsuranceSectionVisible && (
                            <SectionContainer
                                hidden={
                                    !(
                                        applicationScenario.withInsurance ||
                                        endpoint.configuratorApplicationModule.showInsuranceCalculator
                                    )
                                }
                                isCheckboxChecked={isInsuranceSectionBigBottom}
                                isSummaryContent={isSummaryContent}
                            >
                                <InsuranceGridCalculator
                                    applicationModule={endpoint.configuratorApplicationModule}
                                    calculatorContext={calculatorContext}
                                    dealerId={values.dealerId}
                                    hideCheckbox={!applicationScenario.isInsuranceOptional}
                                    initialInsuranceConfiguration={
                                        initialApplicationConfiguration.withInsurance ||
                                        (!endpoint.configuratorApplicationModule.isInsuranceOptional &&
                                            endpoint.configuratorApplicationModule.showInsuranceCalculator)
                                    }
                                    isPublicAccess
                                />
                            </SectionContainer>
                        )}

                        {isAppointmentSectionVisible && (
                            <SectionContainer
                                hidden={!endpoint.configuratorApplicationModule.testDrive}
                                isCheckboxChecked={!endpoint.configuratorApplicationModule.tradeIn}
                                isSummaryContent={isSummaryContent}
                            >
                                <CheckboxContainer
                                    hasPrice={false}
                                    hideCheckbox={false}
                                    style={{ marginBottom: '24px' }}
                                    hasTooltip
                                >
                                    <CheckboxField customComponent={Checkbox} name="configuration.testDrive">
                                        {t('configuratorJourney:applicantkyc.fields.requestTestDrive.label')}
                                        <Tooltip
                                            placement="top"
                                            title={t(
                                                'configuratorJourney:applicantkyc.fields.requestTestDrive.tooltip'
                                            )}
                                        >
                                            <StyledInfoCircleFilled />
                                        </Tooltip>
                                    </CheckboxField>
                                </CheckboxContainer>
                            </SectionContainer>
                        )}

                        {isFinancingSectionVisible && (
                            <SectionContainer
                                hidden={!endpoint.configuratorApplicationModule.tradeIn}
                                isSummaryContent={isSummaryContent}
                                isCheckboxChecked
                            >
                                <TradeIn
                                    calculatorContext={calculatorContext}
                                    disabledCheckbox={
                                        state.applyNewApplication && state.applyNewApplication.configuration.tradeIn
                                    }
                                    isTradeInAmountVisible={
                                        endpoint.configuratorApplicationModule.isTradeInAmountVisible
                                    }
                                    moduleType={endpoint.configuratorApplicationModule.__typename}
                                    state={state as unknown as OfferPageActionStateProps['state']}
                                />
                            </SectionContainer>
                        )}
                    </>
                )}

                {!!paymentSetting && depositAmount && (
                    <ConfiguratorDepositDisplay
                        isSummaryContent={isSummaryContent}
                        label={t('configuratorDetails:labels.reservationDeposit')}
                        value={companyFormats.formatAmountWithCurrency(depositAmount)}
                    />
                )}

                {(endpoint.configuratorApplicationModule.termsTitle?.defaultValue ||
                    endpoint.configuratorApplicationModule.termsText?.defaultValue) && (
                    <TermsContainer>
                        <Terms isSummaryContent={isSummaryContent} onClick={() => setOpenTermsModal(true)} underline>
                            {t('configuratorDetails:labels.termsAndConditions')}
                        </Terms>
                    </TermsContainer>
                )}

                {!isSummaryContent && (
                    <ProceedToSummary
                        buttonType={theme === CompanyTheme.PorscheV3 ? 'primary' : 'tertiary'}
                        modelConfigurator={modelConfigurator}
                        onProceedToSummary={onProceedToSummary}
                        setHasError={actions.setHasError}
                        setShowSummary={actions.setShowSummary}
                        stockInventory={stockVariants}
                        variantConfigurator={variantConfigurator}
                        isPreSummary
                    />
                )}
            </Container>
        </>
    );
};

export default SummaryInfo;
