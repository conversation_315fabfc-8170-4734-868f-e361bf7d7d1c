/* eslint-disable max-len */
import dayjs from 'dayjs';
import { head } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { LabelsPublicDataFragment } from '../../../../api/fragments/LabelsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { PromoCodeDataFragment } from '../../../../api/fragments/PromoCodeData';
import { useGetLabelsByFeatureModuleIdQuery } from '../../../../api/queries/getLabelsByFeatureModuleId';
import { useGetModelConfiguratorForApplicationQuery } from '../../../../api/queries/getModelConfiguratorForApplication';
import {
    FinanceProductSortingField,
    FinancingPreferenceValue,
    InsuranceProductSortingField,
    ModuleType,
    SortingOrder,
} from '../../../../api/types';
import { GenericCalculatorProvider } from '../../../../calculator/CalculatorProvider';
import { GenericCalculatorValues } from '../../../../calculator/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import breakpoints from '../../../../utilities/breakpoints';
import { getInitialCalculatorValues } from '../../../../utilities/journeys/calculator';
import useDefaultMarketTypeValue from '../../../../utilities/useDefaultMarketTypeValue';
import useInitialValue from '../../../../utilities/useInitialValue';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useLocalFinanceProducts from '../../../shared/CIPage/useLocalFinanceProducts';
import useLocalInsuranceProducts from '../../../shared/CIPage/useLocalInsuranceProduct';
import getIsDealerOptionsVisible, {
    getIncludeDealerOptionsForFinancing,
} from '../../../shared/getIsDealerOptionsVisible';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import ConfiguratorDetails from './ConfiguratorDetails';
import ConfiguratorForm from './ConfiguratorForm';
import ConfiguratorSummary from './ConfiguratorSummary';
import { ActionsProps, SelectedSectionOption, State, ConfiguratorFormValues } from './shared';

export const ConfiguratorContainer = styled.div`
    & .ant-pro-page-container-warp {
        width: 100%;
        z-index: 100;

        & .ant-page-header {
            display: none;
        }
    }

    & .ant-pro-page-container-children-content {
        margin: 0;
    }

    @media screen and (min-width: ${breakpoints.md}) {
        & .ant-pro-page-container-warp {
            position: fixed;
        }
    }
`;

type SetShowSummaryAction = { type: 'setShowSummary'; showSummary: boolean };
type SetPromoCodeAction = { type: 'setPromoCode'; promoCode: PromoCodeDataFragment | null };
type SetPromoCodeInputAction = { type: 'setPromoCodeInput'; promoCodeInput: string };
type SetPromoCodeErrorAction = { type: 'setPromoCodeError'; promoCodeError: string };
type SetSaveAsDraftAction = { type: 'setSaveAsDraft'; saveAsDraft: boolean };
type SetEmailSentAction = { type: 'setEmailSent'; emailSent: boolean };
type SetDealerIdAction = { type: 'setDealerId'; dealerId: string };
type SetSelectedVariantConfigurator = { type: 'setVariantConfigurator'; variantConfiguratorId: string };
type SetSelectedSection = { type: 'setSelectedSection'; selectedSection: SelectedSectionOption };
type SetHasError = { type: 'setHasError'; hasError: boolean };
type SetLabel = { type: 'setLabel'; label: LabelsPublicDataFragment[] | null };
type SetLabelDisplay = { type: 'setLabelDisplay'; labelDisplay: boolean };
type Action =
    | SetShowSummaryAction
    | SetPromoCodeAction
    | SetSaveAsDraftAction
    | SetEmailSentAction
    | SetPromoCodeInputAction
    | SetPromoCodeErrorAction
    | SetDealerIdAction
    | SetSelectedVariantConfigurator
    | SetSelectedSection
    | SetHasError
    | SetLabel
    | SetLabelDisplay;

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setShowSummary':
            return { ...state, showSummary: action.showSummary };

        case 'setPromoCode':
            return { ...state, promoCode: action.promoCode };

        case 'setSaveAsDraft':
            return { ...state, saveAsDraft: action.saveAsDraft };

        case 'setEmailSent':
            return { ...state, emailSent: action.emailSent };

        case 'setPromoCodeInput':
            return { ...state, promoCodeInput: action.promoCodeInput };

        case 'setPromoCodeError':
            return { ...state, promoCodeError: action.promoCodeError };

        case 'setDealerId':
            return { ...state, dealerId: action.dealerId };

        case 'setVariantConfigurator':
            return { ...state, variantConfiguratorId: action.variantConfiguratorId };

        case 'setSelectedSection':
            return { ...state, selectedSection: action.selectedSection };

        case 'setHasError':
            return { ...state, hasError: action.hasError };

        case 'setLabel':
            return { ...state, label: action.label };

        case 'setLabelDisplay':
            return { ...state, labelDisplay: action.labelDisplay };

        default:
            return state;
    }
};

export type ModelConfiguratorDetailsInnerPageProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    initialCalculatorValues: Partial<GenericCalculatorValues>;
    initialFormValues: Partial<ConfiguratorFormValues>;
    initialShowSummary: boolean;
    token?: string;
    overrideCalculator: boolean;
    applyNewApplication?: ConfiguratorApplicationState | null;
};

const ModelConfiguratorDetailsInnerPage = ({
    endpoint,
    initialCalculatorValues: initialCalculatorValuesProp,
    initialFormValues: initialFormValuesProps,
    initialShowSummary,
    token,
    overrideCalculator,
    applyNewApplication,
}: ModelConfiguratorDetailsInnerPageProps) => {
    const { urlIdentifier } = useParams<{ urlIdentifier: string }>();
    const { configuratorApplicationModule } = endpoint;

    const [state, dispatch] = useReducer(reducer, {
        showSummary: initialShowSummary ?? false,
        promoCode: null,
        saveAsDraft: false,
        emailSent: false,
        promoCodeInput: null,
        promoCodeError: null,
        dealerId: null,
        variantConfiguratorId: null,
        selectedSection: null,
        hasError: false,
        label: null,
        labelDisplay: false,
        applyNewApplication,
    });

    const dealerId = useMemo(() => {
        const { dealerId } = initialFormValuesProps;

        return dealerId || state.dealerId;
    }, [initialFormValuesProps, state]);

    const actions: ActionsProps = useMemo(
        () => ({
            setShowSummary: (showSummary: boolean) => dispatch({ type: 'setShowSummary', showSummary }),
            setPromoCode: (promoCode: PromoCodeDataFragment | null) => dispatch({ type: 'setPromoCode', promoCode }),
            setSaveAsDraft: (saveAsDraft: boolean) => dispatch({ type: 'setSaveAsDraft', saveAsDraft }),
            setEmailSent: (emailSent: boolean) => dispatch({ type: 'setEmailSent', emailSent }),
            setPromoCodeInput: (promoCodeInput: string) => dispatch({ type: 'setPromoCodeInput', promoCodeInput }),
            setPromoCodeError: (promoCodeError: string) => dispatch({ type: 'setPromoCodeError', promoCodeError }),
            setDealerId: (dealerId: string) => dispatch({ type: 'setDealerId', dealerId }),
            setVariantConfigurator: (variantConfiguratorId: string) =>
                dispatch({ type: 'setVariantConfigurator', variantConfiguratorId }),
            setSelectedSection: (selectedSection: SelectedSectionOption) =>
                dispatch({ type: 'setSelectedSection', selectedSection }),
            setHasError: (hasError: boolean) => dispatch({ type: 'setHasError', hasError }),
            setLabel: (label: LabelsPublicDataFragment[] | null) => dispatch({ type: 'setLabel', label }),
            setLabelDisplay: (labelDisplay: boolean) => dispatch({ type: 'setLabelDisplay', labelDisplay }),
        }),
        [dispatch]
    );

    const { data: labelData } = useGetLabelsByFeatureModuleIdQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: configuratorApplicationModule.id,
        },
    });

    useEffect(() => {
        if (labelData?.label.length > 0) {
            actions.setLabel(labelData?.label);
        }
    }, [actions, labelData]);

    const dealerRef = useRef<string>();
    const updateDealerRef = useCallback((value: string) => {
        dealerRef.current = value;
    }, []);

    const { loading: loadingConfigurator, data } = useGetModelConfiguratorForApplicationQuery({
        // workaround for AN-2696
        // 2 blocks under different variant configurator end up with same id
        fetchPolicy: 'no-cache',
        variables: {
            urlIdentifier,
            bankModuleId: configuratorApplicationModule.bankModuleId,
            productionOnly: true,
            dealerId,
            applicationModuleIds: [configuratorApplicationModule.id],
        },
    });

    const { loading: loadingFinanceProducts, financeProducts: financeProductData } = useLocalFinanceProducts({
        module: endpoint.configuratorApplicationModule,
        dealerIds: dealerId ? [dealerId] : [],
        sort: { field: FinanceProductSortingField.Order, order: SortingOrder.Asc },
    });

    const { insuranceProducts } = useLocalInsuranceProducts({
        module: endpoint.configuratorApplicationModule,
        sort: { field: InsuranceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds: dealerId ? [dealerId] : [],
    });

    const availableVariantConfigurators = useMemo(() => {
        if (!data?.modelConfigurator) {
            return null;
        }

        return data.modelConfigurator.variantConfigurators
            .filter(variantConfigurator =>
                data.modelConfigurator.module.__typename === ModuleType.ConfiguratorModule &&
                data.modelConfigurator.module.isInventoryEnabled
                    ? variantConfigurator.inventories.some(inventory => inventory.availableStock > 0)
                    : true
            )
            .map(variantConfigurator => ({
                ...variantConfigurator,
                variant: {
                    ...variantConfigurator.variant,
                    vehiclePrice: variantConfigurator.vehiclePrice,
                },
            }))
            .sort((a, b) => {
                if (a.variant.__typename === 'LocalVariant' && b.variant.__typename === 'LocalVariant') {
                    return a.variant.order - b.variant.order;
                }

                return 0;
            });
    }, [data?.modelConfigurator]);

    const bankDisplayPreference = useMemo(
        () => configuratorApplicationModule.bankDisplayPreference,
        [configuratorApplicationModule]
    );

    const modelConfiguratorSettings: ModelConfiguratorDetailsFragment = useMemo(() => {
        if (!data?.modelConfigurator) {
            return null;
        }

        return {
            ...data.modelConfigurator,
            variantConfigurators: availableVariantConfigurators.map(variantConfigurator => ({
                ...variantConfigurator,
                blocks: variantConfigurator.blocks.filter(block => {
                    if (!block.isActive) {
                        return false;
                    }

                    const inventories = variantConfigurator?.inventories || [];

                    switch (block.__typename) {
                        case 'ColorBlock':
                            return block.colorSettings.some(colorSetting =>
                                inventories.some(
                                    inventory =>
                                        inventory.__typename === 'ConfiguratorInventory' &&
                                        inventory.availableStock > 0 &&
                                        inventory.colorSetting.id === colorSetting.id
                                )
                            );

                        case 'TrimBlock':
                            return block.trimSettings.some(trimSetting =>
                                inventories.some(
                                    inventory =>
                                        inventory.__typename === 'ConfiguratorInventory' &&
                                        inventory.availableStock > 0 &&
                                        inventory.trimSetting.id === trimSetting.id
                                )
                            );

                        case 'PackageBlock':
                            return block.packageSettings.some(packageSetting =>
                                inventories.some(
                                    inventory =>
                                        inventory.__typename === 'ConfiguratorInventory' &&
                                        inventory.availableStock > 0 &&
                                        inventory.packageSetting?.id === packageSetting.id
                                )
                            );

                        default:
                            return true;
                    }
                }),
            })),
        };
    }, [data, availableVariantConfigurators]);

    const initialFormValues = useMemo(
        () => ({
            ...initialFormValuesProps,
            configuratorId: initialFormValuesProps.configuratorId || head(availableVariantConfigurators)?.id,
        }),
        [availableVariantConfigurators, initialFormValuesProps]
    );

    const variants = useMemo(() => {
        if (!data?.modelConfigurator) {
            return null;
        }

        const variantConfigurator = head(availableVariantConfigurators);

        if (!variantConfigurator) {
            return null;
        }

        const variants = availableVariantConfigurators.map(configurator => configurator.variant);

        return variants || [];
    }, [data?.modelConfigurator, availableVariantConfigurators]);

    const calculatorValues = useInitialValue<Partial<GenericCalculatorValues>>(initialCalculatorValuesProp);

    const insuranceCalculatorValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (!hasInsuranceScenario(configuratorApplicationModule.scenarios)) {
            return {};
        }

        return {
            dateOfBirth: undefined,
            isInsuranceEnabled: true,
            noClaimDiscount: 50,
            yearsOfDriving: 8,
            dateOfRegistration: dayjs(),
        };
    }, [configuratorApplicationModule.scenarios]);

    const marketTypeValue = useDefaultMarketTypeValue(dealerId);
    const initialCalculatorValues = useMemo(
        () => ({
            ...calculatorValues,
            ...(!!overrideCalculator && insuranceCalculatorValues),
            ...(!!overrideCalculator &&
                getInitialCalculatorValues(marketTypeValue, configuratorApplicationModule.marketType)),
            isFinancingEnabled: hasFinancingScenario(endpoint.configuratorApplicationModule.scenarios),
        }),
        [
            calculatorValues,
            overrideCalculator,
            insuranceCalculatorValues,
            configuratorApplicationModule.marketType,
            endpoint.configuratorApplicationModule.scenarios,
            marketTypeValue,
        ]
    );

    /**
     * isDraft = false :: current journey is drafted application, we should update application
     * isDraft = true :: current journey is not drafted, we should 'draft' application
     */
    const isDraft = useMemo(() => !initialShowSummary, [initialShowSummary]);
    if (loadingFinanceProducts || loadingConfigurator) {
        return <PortalLoadingElement />;
    }

    if (!data?.modelConfigurator && !loadingConfigurator) {
        return <NotFoundResult />;
    }

    return (
        <GenericCalculatorProvider
            bankDisplayPreference={bankDisplayPreference}
            companyId={configuratorApplicationModule.company.id}
            dealerId={dealerId}
            financeProducts={financeProductData || []}
            hasFinancingCalculator={endpoint?.configuratorApplicationModule?.showFinanceCalculator}
            includeDealerOptionsForFinancing={getIncludeDealerOptionsForFinancing(configuratorApplicationModule)}
            initialValues={{
                ...initialCalculatorValues,
                vehicle: initialCalculatorValues?.vehicle || head(availableVariantConfigurators)?.variant?.id,
                carPrice: head(availableVariantConfigurators)?.vehiclePrice,
            }}
            insuranceProducts={insuranceProducts || []}
            insurerDisplayPreference={configuratorApplicationModule.insurerDisplayPreference}
            isDealerOptionsVisible={getIsDealerOptionsVisible(configuratorApplicationModule)}
            isFinancingOptional={
                endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Request ||
                endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Optional
            }
            marketType={configuratorApplicationModule.marketType}
            promoCode={state.promoCode}
            promoCodeViewable={false}
            vehicles={variants}
            isPublicAccess
        >
            {calculatorContext => (
                <ConfiguratorForm
                    actions={actions}
                    calculatorContext={calculatorContext}
                    endpoint={endpoint}
                    initialFormValues={initialFormValues}
                    isDraft={isDraft}
                    state={state}
                    token={token}
                >
                    {!state.showSummary && (
                        <ConfiguratorDetails
                            actions={actions}
                            calculatorContext={calculatorContext}
                            dealerRef={dealerRef}
                            endpoint={endpoint}
                            modelConfiguratorSettings={modelConfiguratorSettings}
                            state={state}
                            updateDealerRef={updateDealerRef}
                        />
                    )}

                    {state.showSummary && (
                        <ConfiguratorSummary
                            actions={actions}
                            calculatorContext={calculatorContext}
                            endpoint={endpoint}
                            isDraft={isDraft}
                            modelConfigurator={modelConfiguratorSettings}
                            state={state}
                            token={token}
                        />
                    )}
                </ConfiguratorForm>
            )}
        </GenericCalculatorProvider>
    );
};

export default ModelConfiguratorDetailsInnerPage;
