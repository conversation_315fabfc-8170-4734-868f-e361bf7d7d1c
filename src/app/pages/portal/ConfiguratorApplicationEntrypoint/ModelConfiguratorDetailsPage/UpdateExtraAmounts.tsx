import { useFormikContext } from 'formik';
import { head } from 'lodash/fp';
import { useEffect } from 'react';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import { BlockType, OptionKind } from '../../../../api/types';
import { CalculatorContext } from '../../../../calculator/computing';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { ConfiguratorFormValues } from './shared';

export type UpdateExtraAmountsProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    variantConfigurator: VariantConfiguratorDetailsFragment;
};

const UpdateExtraAmounts = ({ calculatorContext, variantConfigurator }: UpdateExtraAmountsProps) => {
    const { change, values: calculatorValues } = calculatorContext;
    const { values } = useFormikContext<ConfiguratorFormValues>();

    useEffect(() => {
        const extraAmounts = [];
        const extraFinancedAmounts = [];
        if (calculatorValues.vehicle && values.configuratorBlocks && values.configuratorBlocks.length > 0) {
            // package
            const packageBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Package);
            const selectedPackageBlock = variantConfigurator.blocks.find(
                block => block.__typename === 'PackageBlock' && block.id === packageBlock?.blockId
            );

            const packageSetting =
                selectedPackageBlock?.__typename === 'PackageBlock'
                    ? selectedPackageBlock.packageSettings.find(
                          packageSetting => packageSetting.id === head(packageBlock?.ids)
                      )
                    : null;

            if (packageSetting?.packageType?.__typename === 'PackageTypeWithPrice') {
                if (packageSetting.packageType.financing) {
                    extraFinancedAmounts.push(packageSetting.packageType.price);
                } else {
                    extraAmounts.push(packageSetting.packageType.price);
                }
            }

            // exterior color
            const exteriorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);

            const selectedExteriorBlock = variantConfigurator.blocks.find(
                block => block.__typename === 'ColorBlock' && block.id === exteriorBlock?.blockId
            );

            const exteriorSetting =
                selectedExteriorBlock?.__typename === 'ColorBlock'
                    ? selectedExteriorBlock.colorSettings.find(
                          colorSetting => colorSetting.id === head(exteriorBlock?.ids)
                      )
                    : null;

            if (exteriorSetting?.price) {
                extraFinancedAmounts.push(exteriorSetting.price);
            }

            // interior color
            const interiorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);

            const selectedInteriorBlock = variantConfigurator.blocks.find(
                block => block.__typename === 'TrimBlock' && block.id === interiorBlock?.blockId
            );

            const interiorSetting =
                selectedInteriorBlock?.__typename === 'TrimBlock'
                    ? selectedInteriorBlock.trimSettings.find(
                          trimSetting => trimSetting.id === head(interiorBlock?.ids)
                      )
                    : null;

            if (interiorSetting?.price) {
                extraFinancedAmounts.push(interiorSetting.price);
            }

            // option setting
            const optionBlocks = variantConfigurator.blocks.filter(block => block.__typename === 'OptionsBlock');
            optionBlocks.forEach(optionBlock => {
                if (optionBlock?.__typename === 'OptionsBlock') {
                    const valueOption = values.options.find(valueOption => valueOption.id === optionBlock.id);

                    if (
                        optionBlock.optionSettings.__typename === 'SingleSelectOptionSettings' &&
                        valueOption?.kind === OptionKind.SingleSelect
                    ) {
                        const { values: optionValue } = valueOption;
                        const selectedOptionSetting = optionBlock.optionSettings.options.find(
                            optionSetting => optionSetting.id === optionValue
                        );

                        if (selectedOptionSetting?.valuePrice) {
                            if (optionBlock.financing) {
                                extraFinancedAmounts.push(selectedOptionSetting.valuePrice);
                            } else {
                                extraAmounts.push(selectedOptionSetting.valuePrice);
                            }
                        }
                    }

                    if (
                        optionBlock.optionSettings.__typename === 'MultiSelectOptionSettings' &&
                        valueOption?.kind === OptionKind.MultiSelect
                    ) {
                        const { values: optionValues } = valueOption;
                        if (optionValues) {
                            const selectedOptionSettings = optionBlock.optionSettings.options.filter(optionSetting =>
                                optionValues.includes(optionSetting.id)
                            );

                            const optionAmounts = selectedOptionSettings
                                .map(optionSetting => optionSetting?.valuePrice && optionSetting.valuePrice)
                                .filter(Boolean);

                            if (optionBlock.financing) {
                                extraFinancedAmounts.push(...optionAmounts);
                            } else {
                                extraAmounts.push(...optionAmounts);
                            }
                        }
                    }

                    if (
                        optionBlock.optionSettings.__typename === 'ComboOptionSettings' &&
                        valueOption?.kind === OptionKind.Combo
                    ) {
                        const comboOptionBlock = values.configuratorBlocks.find(
                            block => block.blockType === BlockType.Options
                        );

                        if (comboOptionBlock && optionBlock.optionSettings.price) {
                            if (optionBlock.financing) {
                                extraFinancedAmounts.push(optionBlock.optionSettings.price);
                            } else {
                                extraAmounts.push(optionBlock.optionSettings.price);
                            }
                        }
                    }
                }
            });
        }

        const extraAmount = extraAmounts.reduce<number>((total, value) => total + (value || 0), 0);
        const extraFinancedAmount = extraFinancedAmounts.reduce<number>((total, value) => total + (value || 0), 0);

        change('extraFinancedAmount', extraFinancedAmount);
        change('extraAmount', extraAmount);
    }, [change, values.configuratorBlocks, values.options, calculatorValues.vehicle, variantConfigurator]);

    return null;
};

export default UpdateExtraAmounts;
