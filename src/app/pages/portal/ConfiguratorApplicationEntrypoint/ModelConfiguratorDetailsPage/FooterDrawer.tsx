/* eslint-disable max-len */
import { CloseOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import { CalculatorContext } from '../../../../calculator/computing';
import { GenericCalculatorValues } from '../../../../calculator/types';
import ThemeDrawer from '../../../../components/ThemeDrawer';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import SummaryInfo from './SummaryInfo';
import { ActionsProps, State } from './shared';
import { WidgetBar } from './ui';

const THEME_BG_COLOR = 'var(--configurator-widget-popup-background, var(--ant-primary-color))';
const THEME_TEXT_COLOR = 'var(--configurator-widget-popup-text-color, #fff)';
const THEME_WIDGET_TITLE_COLOR = 'var(--configurator-widget-active-color, #fff)';
const THEME_WIDGET_TITLE_WEIGHT = 'var(--configurator-widget-title-font-weight, 400)';

const Container = styled.div`
    padding: 24px;
    padding-top: 0;
    position: relative;
    background-color: ${THEME_BG_COLOR};
    width: 100%;

    @media screen and (min-width: ${breakpoints.lg}) {
        width: 40vw;
    }
`;

const Title = styled.div`
    background-color: ${THEME_BG_COLOR};
    color: ${THEME_WIDGET_TITLE_COLOR};
    font-weight: ${THEME_WIDGET_TITLE_WEIGHT};
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 35px 0;
    position: sticky;
    top: 0;
    z-index: 100;
`;

const StyledDrawer = styled(ThemeDrawer)<{ bannerHeight: number }>`
    & .ant-drawer-mask {
        background-color: transparent;
    }

    &.ant-drawer-bottom.ant-drawer-open {
        & > .ant-drawer-content-wrapper {
            box-shadow: none;
        }
    }

    & .ant-drawer-content {
        background-color: transparent;

        & .ant-drawer-header {
            border: none;
            background-color: ${THEME_BG_COLOR};

            & .ant-drawer-title {
                color: ${THEME_TEXT_COLOR};
                font-size: 20px;
            }
        }
    }

    & .ant-drawer-body {
        padding: 0;
        overflow-y: scroll;
    }

    & .ant-drawer-body,
    .ant-drawer-body .ant-typography-expand,
    .ant-drawer-body .ant-typography-ellipsis {
        background-color: ${THEME_BG_COLOR};
        color: ${THEME_TEXT_COLOR};

        & .ant-form-item > .ant-form-item-row input.ant-typography,
        .ant-form-item > .ant-form-item-row span.ant-typography {
            background-color: transparent;
        }
    }

    & .ant-btn.ant-btn-link > span {
        color: ${THEME_TEXT_COLOR};
    }

    @media screen and (min-width: ${breakpoints.lg}) {
        margin-top: ${({ bannerHeight = 0 }) => `${80 + bannerHeight}px`};

        & .ant-drawer-content-wrapper {
            width: fit-content;
            margin-left: auto;
        }
    }
`;

const StyledButton = styled(Button)`
    width: 24px;
    height: 24px;
    padding: 0;

    border: none;
    background-color: transparent;
    color: ${THEME_TEXT_COLOR};

    & > span {
        font-size: 24px;
    }

    &:hover {
        background-color: transparent;
        color: ${THEME_TEXT_COLOR};
    }

    &:active {
        background-color: transparent;
        color: ${THEME_TEXT_COLOR};
    }

    &:focus {
        background-color: transparent;
        color: ${THEME_TEXT_COLOR};
    }
`;

export type FooterDrawerProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    variantConfigurator: VariantConfiguratorDetailsFragment;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    visible: boolean;
    onClose: () => void;
    state: State;
    actions: ActionsProps;
    bannerHeight?: number;
};

const FooterDrawer = ({
    calculatorContext,
    modelConfigurator,
    variantConfigurator,
    endpoint,
    visible,
    onClose,
    state,
    actions,
    bannerHeight,
}: FooterDrawerProps) => {
    const { t } = useTranslation('configuratorDetails');
    const translatedString = useTranslatedString();
    const { CloseButton } = useThemeComponents();

    return (
        <StyledDrawer
            bannerHeight={bannerHeight}
            closable={false}
            extra={<StyledButton icon={<CloseOutlined />} onClick={onClose} />}
            height="100%"
            onClose={onClose}
            open={visible}
            placement="bottom"
            destroyOnClose
        >
            <Container>
                <Title>
                    <WidgetBar $isOpen={visible} onClick={onClose} />
                    {t('configuratorDetails:titles.summary', {
                        modelName: translatedString(variantConfigurator.variant.name),
                    })}
                    <CloseButton onClick={onClose} type="link" />
                </Title>
                <SummaryInfo
                    actions={actions}
                    calculatorContext={calculatorContext}
                    endpoint={endpoint}
                    isSummaryContent={false}
                    modelConfigurator={modelConfigurator}
                    onProceedToSummary={onClose}
                    state={state}
                    variantConfigurator={variantConfigurator}
                />
            </Container>
        </StyledDrawer>
    );
};

export default FooterDrawer;
