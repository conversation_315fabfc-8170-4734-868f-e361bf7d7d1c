import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Card, Divider, Button, Space } from 'antd';
import { saveAs } from 'file-saver';
import { useFormikContext } from 'formik';
import { isArray, isEmpty, isNil, split, trim } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { EnergyUnit } from '../../../../api';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VehicleCalculatorSpecsFragment } from '../../../../api/fragments/VehicleCalculatorSpecs';
import { CalculatorContext } from '../../../../calculator/computing';
import { GenericCalculatorValues } from '../../../../calculator/types';
import Image from '../../../../components/Image';
import { useThemeComponents } from '../../../../themes/hooks';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import usePublic from '../../../../utilities/usePublic';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { techinicalFormatAmount, powerOutputFormatAmount } from '../../../admin/AddVehiclePage/AddVariantPage/helper';
import { StyledLabelContainer } from '../CarListingPage/ui';
import { ActionsProps, State, ConfiguratorFormValues } from './shared';
import { Title } from './ui';
import Flash from '../../../../assets/ci/configurator/flash.svg';
import FuelConsumptionRate from '../../../../assets/ci/configurator/fuelConsumptionRate.svg';
import Speed from '../../../../assets/ci/configurator/speedometer.svg';

const StyledCard = styled(Card)`
    min-width: 280px;
    width: 280px;
    max-height: 100%;
    color: var(--ant-primary-color);

    & .ant-card-cover {
        height: fit-content;
        margin: 0;

        & > .ant-image {
            width: 100%;
            aspect-ratio: 16 / 9;
        }
    }

    & .ant-card-body {
        padding: 16px;
    }
`;

const StyledImage = styled(Image)`
    object-fit: cover;
    height: 100%;
`;

const StyledTitle = styled(Title)`
    margin-top: 6px;
    color: #000000;
`;

const Description = styled.div`
    font-size: 14px;
    font-weight: 900;
    color: #000;
`;

const GeneralInfoContainer = styled.div`
    margin-top: 24px;
    display: flex;
`;

const GeneralInfo = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
    text-align: center;
    color: #000000;
`;

const GeneralInfoDivider = styled(Divider)`
    height: 3rem;
`;

const ViewmoreDivider = styled(Divider)`
    margin: 0;
    margin-top: 1rem;
`;

const ViewMoreLessLink = styled(Button)`
    &.ant-btn {
        font-size: 16px;
        text-align: left;
        display: flex;
        align-items: center;
        padding: 0;
        margin: 1rem 0 0;
        color: #000;
        font-weight: 900;

        & > .anticon {
            font-size: 10px;
            margin-left: auto;
        }
    }
`;

const Ul = styled.ul`
    padding-left: 24px;
    font-size: 15px;
    color: #000;

    & > li {
        list-style-type: disc;
        &::marker {
            color: var(--ant-primary-color);
            font-size: 1.3rem;
        }
    }
`;

const SelectButtonContainer = styled.div`
    margin-top: 24px;
`;

const IconContainer = styled.div`
    height: 25px;
    display: flex;
    align-items: center;
`;

export const StyledLabel = styled.span`
    float: left;
    margin-top: 10px;
    padding: 5px 12px;
    font-size: 14px;
    border-radius: 20px;
    font-weight: normal;
    margin-right: 5px;
`;

export type VehicleCardProps = {
    variantConfiguratorId: string;
    vehicle: VehicleCalculatorSpecsFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    actions: ActionsProps;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    state: State;
};

const VehicleCard = ({
    variantConfiguratorId,
    vehicle,
    calculatorContext,
    actions,
    modelConfigurator,
    state,
}: VehicleCardProps) => {
    const { change, values: calculatorValues } = calculatorContext;
    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();
    const { t } = useTranslation('configuratorDetails');
    const [viewMore, setViewMore] = useState<boolean>(false);
    const translatedString = useTranslatedString();
    const companyFormats = useCompanyFormats();
    const { Button } = useThemeComponents();
    const fallbackSrc = usePublic('empty.svg');
    const { label } = state;

    const labelDisplay = useCallback(
        (id: string) => {
            if (label) {
                const currentLabel = label.find(labelItem => labelItem.id === id);

                return (
                    (currentLabel?.moduleVariants || []).find(moduleVariant =>
                        (moduleVariant.addedVariants || []).some(
                            variant => variant.versioning.suiteId === vehicle.versioning.suiteId
                        )
                    ) && currentLabel?.isActive
                );
            }

            return false;
        },
        [label, vehicle.versioning.suiteId]
    );

    useEffect(() => {
        if (labelDisplay) {
            actions.setLabelDisplay(true);
        }
    }, [actions, labelDisplay]);

    const image = useMemo(() => {
        switch (vehicle.__typename) {
            case 'LocalVariant':
                return vehicle.images?.length ? vehicle.images[0].url : fallbackSrc;

            default:
                return fallbackSrc;
        }
    }, [fallbackSrc, vehicle]);

    const selected = useMemo(
        () => values.configuratorId === variantConfiguratorId,
        [values.configuratorId, variantConfiguratorId]
    );

    const features = useMemo(() => {
        if (vehicle.__typename !== 'LocalVariant') {
            return [];
        }

        return split(/\r?\n/, translatedString(vehicle.features))
            .map(feature => trim(feature))
            .filter(feature => !isEmpty(feature));
    }, [translatedString, vehicle]);

    const downloadBrochure = useCallback(() => {
        if (vehicle?.__typename !== 'LocalVariant' || !vehicle.brochure) {
            return;
        }

        saveAs(vehicle.brochure.url, vehicle.brochure.filename);
    }, [vehicle]);

    useEffect(() => {
        if (!calculatorValues.financeProduct && values.configuration.withFinancing) {
            setFieldValue('configuration.withFinancing', false);
        }
    }, [calculatorValues.financeProduct, setFieldValue, values.configuration.withFinancing]);

    if (vehicle.__typename !== 'LocalVariant') {
        return null;
    }

    const variantConfigurator = useMemo(
        () => modelConfigurator.variantConfigurators.find(variant => variant.id === variantConfiguratorId),
        [modelConfigurator.variantConfigurators, variantConfiguratorId]
    );

    const energyConsumptionField = useMemo(() => {
        switch (vehicle.energyConsumption?.unit) {
            case EnergyUnit.Liters:
                return t('configuratorDetails:vehicleCard.energyConsumptionLiters', {
                    value: techinicalFormatAmount(vehicle.energyConsumption?.value),
                });
            case EnergyUnit.Kwh:
                return t('configuratorDetails:vehicleCard.energyConsumptionKwh', {
                    value: techinicalFormatAmount(vehicle.energyConsumption?.value),
                });
            default:
                return t('configuratorDetails:vehicleCard.energyConsumptionDefault', {
                    value: techinicalFormatAmount(vehicle.energyConsumption?.value),
                });
        }
    }, [t, vehicle.energyConsumption?.unit, vehicle.energyConsumption?.value]);

    const selectVehicle = useCallback(
        (variantConfiguratorId: string, variantId: string) => {
            setFieldValue('configuratorId', variantConfiguratorId);
            change('vehicle', variantId);
            actions.setSelectedSection(null);
        },
        [actions, change, setFieldValue]
    );

    return (
        <StyledCard cover={<StyledImage preview={false} src={image} />}>
            <StyledTitle>{translatedString(vehicle.name)}</StyledTitle>
            <Description>
                {t('configuratorDetails:vehicleCard.price', {
                    value: companyFormats.formatAmountWithCurrency(variantConfigurator.vehiclePrice),
                })}
            </Description>
            <StyledLabelContainer isLabel={state.labelDisplay}>
                {isArray(label) &&
                    label.map(
                        labelItem =>
                            labelDisplay(labelItem.id) && (
                                <StyledLabel
                                    style={{
                                        backgroundColor: `#${labelItem.backgroundColor}`,
                                        color: `#${labelItem.textColor}`,
                                    }}
                                >
                                    {translatedString(labelItem.labelName)}
                                </StyledLabel>
                            )
                    )}
            </StyledLabelContainer>

            {(!isNil(vehicle.energyConsumption) || !isNil(vehicle.powerOutput) || !isNil(vehicle.topSpeed)) && (
                <GeneralInfoContainer>
                    {!isNil(vehicle.energyConsumption) && (
                        <GeneralInfo>
                            <IconContainer>
                                <FuelConsumptionRate />
                            </IconContainer>
                            {energyConsumptionField}
                        </GeneralInfo>
                    )}
                    {!isNil(vehicle.powerOutput) && (
                        <>
                            {!isNil(vehicle.energyConsumption) && <GeneralInfoDivider type="vertical" />}
                            <GeneralInfo>
                                <IconContainer>
                                    <Flash />
                                </IconContainer>
                                {t('configuratorDetails:vehicleCard.powerOutput', {
                                    value: powerOutputFormatAmount(vehicle.powerOutput),
                                })}
                            </GeneralInfo>
                        </>
                    )}
                    {!isNil(vehicle.topSpeed) && (
                        <>
                            {(!isNil(vehicle.energyConsumption) || !isNil(vehicle.powerOutput)) && (
                                <GeneralInfoDivider type="vertical" />
                            )}
                            <GeneralInfo>
                                <IconContainer>
                                    <Speed />
                                </IconContainer>
                                {t('configuratorDetails:vehicleCard.topSpeed', {
                                    value: techinicalFormatAmount(vehicle.topSpeed),
                                })}
                            </GeneralInfo>
                        </>
                    )}
                </GeneralInfoContainer>
            )}
            <SelectButtonContainer>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <Button
                        className={selected ? 'selected' : ''}
                        onClick={() => selectVehicle(variantConfiguratorId, vehicle.id)}
                        type={selected ? 'primary' : 'tertiary'}
                        block
                    >
                        {t(`configuratorDetails:actions.${selected ? 'selected' : 'select'}`)}
                    </Button>
                    {vehicle.brochure && (
                        <Button
                            className="DownloadBrochure"
                            data-model={modelConfigurator.model.name.defaultValue}
                            data-variant={vehicle.name.defaultValue}
                            onClick={downloadBrochure}
                            type="tertiary"
                            block
                        >
                            {t('configuratorDetails:actions.downloadBrochure')}
                        </Button>
                    )}
                </Space>
            </SelectButtonContainer>
            {translatedString(vehicle.features) && features.length > 0 && (
                <>
                    <ViewmoreDivider />
                    <ViewMoreLessLink onClick={() => setViewMore(prevState => !prevState)} type="link" block>
                        {t(`configuratorDetails:actions.${viewMore ? 'viewLess' : 'viewMore'}`)}
                        {viewMore ? <UpOutlined /> : <DownOutlined />}
                    </ViewMoreLessLink>
                    {viewMore && (
                        <div>
                            <Ul>
                                {features.map((feature: string, index: number) => (
                                    <li key={`feature-${index.toString()}`}>{feature}</li>
                                ))}
                            </Ul>
                        </div>
                    )}
                </>
            )}
        </StyledCard>
    );
};

export default VehicleCard;
