/* eslint-disable max-len */
import { Grid } from 'antd';
import { useFormikContext } from 'formik';
import { compact, head, isEqual, isNil, last, pick } from 'lodash/fp';
import { MutableRefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useIntersection } from 'react-use';
import styled from 'styled-components';
import { BlockDetailsFragment } from '../../../../api/fragments/BlockDetails';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { InventoryDetailsPublicDataFragment } from '../../../../api/fragments/InventoryDetailsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../api/fragments/VariantConfiguratorDetails';
import { BlockType, OptionKind } from '../../../../api/types';
import { CalculatorContext } from '../../../../calculator/computing';
import { GenericCalculatorValues } from '../../../../calculator/types';
import breakpoints from '../../../../utilities/breakpoints';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import FooterWidget from './FooterWidget';
import SectionHeaderBar from './SectionHeaderBar';
import Exterior from './Sections/Exterior';
import Interior from './Sections/Interior';
import Options from './Sections/Options';
import Packages from './Sections/Packages';
import UpdateExtraAmounts from './UpdateExtraAmounts';
import Media from './components/Media';
import SlideShow from './components/SlideShow';
import getVariantStockBySection from './getVariantStockBySection';
import type { ActionsProps, State, ConfiguratorFormValues, ConfiguratorBlock } from './shared';

const VISIBLE_THRESHOLD = 0.6;

const blockOrder: BlockDetailsFragment['__typename'][] = ['ColorBlock', 'TrimBlock', 'PackageBlock', 'OptionsBlock'];

const blockSorter = (a: BlockDetailsFragment, b: BlockDetailsFragment) => {
    const aIndex = blockOrder.indexOf(a.__typename);
    const bIndex = blockOrder.indexOf(b.__typename);

    return aIndex - bIndex;
};

const optionBlockSorter = (a: BlockDetailsFragment, b: BlockDetailsFragment) =>
    a.__typename === 'OptionsBlock' && b.__typename === 'OptionsBlock' ? a.order - b.order : 0;

export const Container = styled.div<{ fixedPosition: boolean; bannerHeight: number }>`
    @media screen and (min-width: ${breakpoints.lg}) {
        display: flex;
        flex-direction: row;

        .imageWrapper {
            width: 60vw;
            max-height: ${({ bannerHeight = 0 }) => `calc(100vh - ${80 + bannerHeight}px)`};
            bottom: 0;
            position: ${props => (props.fixedPosition ? 'sticky' : 'relative')};
            top: ${({ bannerHeight = 0, fixedPosition = false }) => (fixedPosition ? `${80 + bannerHeight}px` : '0')};
        }
    }
`;

export const ImageContainer = styled.div<{ isHidden: boolean }>`
    overflow: hidden;

    img,
    video {
        aspect-ratio: 3 / 2;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    @media screen and (min-width: ${breakpoints.lg}) {
        position: absolute;
        ${({ isHidden }) => isHidden && 'display: none;'}
        width: 100%;
        height: 100%;

        & > .ant-image {
            width: 100%;
            height: 100%;
        }

        img,
        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
`;

type ImageSectionProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
    block: BlockDetailsFragment;
    imageToShow: string;
    packageIndex: number;
};

const ImageSection = ({ block, imageToShow, packageIndex, variantConfigurator }: ImageSectionProps) => {
    const { values } = useFormikContext<ConfiguratorFormValues>();

    const file = useMemo(() => {
        const colorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);
        const trimBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);

        const matrix = variantConfigurator.matrix.find(
            matrix => matrix.colorId === head(colorBlock?.ids) && matrix.trimId === head(trimBlock?.ids)
        );

        switch (block.__typename) {
            case 'ColorBlock':
                return { filename: matrix?.variantColorImage?.filename, source: matrix?.variantColorImage?.url };

            case 'TrimBlock':
                return { filename: matrix?.variantTrimImage?.filename, source: matrix?.variantTrimImage?.url };

            default:
                return null;
        }
    }, [block, values.configuratorBlocks, variantConfigurator.matrix]);

    if (block.__typename === 'OptionsBlock') {
        const optionFromValues = values.options.find(
            option => option.id === block.id && option.kind === OptionKind.SingleSelect
        );

        const optionImage =
            typeof optionFromValues?.values === 'string' &&
            block.optionSettings.__typename === 'SingleSelectOptionSettings'
                ? block.optionSettings.options.find(option => option.id === optionFromValues?.values)?.image
                : null;

        return (
            <ImageContainer isHidden={imageToShow !== block.id}>
                {optionImage && optionImage?.url ? (
                    <Media fileName={optionImage.filename} source={optionImage.url} />
                ) : (
                    <SlideShow
                        images={block.sectionImages.map(image => ({ fileName: image.filename, source: image.url }))}
                    />
                )}
            </ImageContainer>
        );
    }

    if (block.__typename === 'PackageBlock') {
        return (
            <>
                {block.packageSettings.map((item, index) => (
                    <ImageContainer key={item.id} isHidden={imageToShow !== block.id || index !== packageIndex}>
                        <Media fileName={item.sectionImage?.filename} source={item.sectionImage?.url} />
                    </ImageContainer>
                ))}
            </>
        );
    }

    return (
        <ImageContainer isHidden={imageToShow !== block.id}>
            <Media fileName={file?.filename} source={file?.source} />
        </ImageContainer>
    );
};

type SectionContentProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    block: BlockDetailsFragment;
    packageIndex: number;
    openWidgetDrawer: boolean;
    setPackageIndex: React.Dispatch<React.SetStateAction<number>>;
    actions: ActionsProps;
    stockInventory?: InventoryDetailsPublicDataFragment[];
    state: State;
    onSectionVisible?: (blockId: string) => void;
};

const SectionContent = ({
    openWidgetDrawer,
    packageIndex,
    setPackageIndex,
    actions,
    state,
    stockInventory,
    onSectionVisible,
    ...props
}: SectionContentProps) => {
    const ref = useRef<HTMLDivElement>(null);
    const { variantConfigurator, block } = props;
    const lastBlock = useMemo(() => last(variantConfigurator.blocks), [variantConfigurator.blocks]);

    const stockList = getVariantStockBySection(block.type, stockInventory);

    const commonProps = useMemo(
        () => ({
            ...props,
            ...pick(['setHasError', 'setSelectedSection', 'setShowSummary'], actions),
            hasError: state.hasError,
            isLastBlock: lastBlock.id === block.id,
        }),
        [actions, block.id, lastBlock.id, props, state.hasError]
    );

    const { isIntersecting, intersectionRatio } = useIntersection(ref, { threshold: [VISIBLE_THRESHOLD] }) || {};

    useEffect(() => {
        if (isIntersecting && intersectionRatio > VISIBLE_THRESHOLD && !!onSectionVisible) {
            onSectionVisible(block.id);
        }
    }, [block, isIntersecting, intersectionRatio, onSectionVisible]);

    const content = useMemo(() => {
        switch (block.type) {
            case BlockType.Color:
                return (
                    <Exterior
                        filteredStockInventoryBySelectedSection={stockList}
                        stockInventory={stockInventory}
                        {...commonProps}
                    />
                );

            case BlockType.Trim:
                return (
                    <Interior
                        filteredStockInventoryBySelectedSection={stockList}
                        stockInventory={stockInventory}
                        {...commonProps}
                    />
                );

            case BlockType.Package:
                return (
                    <Packages
                        filteredStockInventoryBySelectedSection={stockList}
                        packageIndex={packageIndex}
                        setPackageIndex={setPackageIndex}
                        stockInventory={stockInventory}
                        {...commonProps}
                    />
                );

            case BlockType.Options:
                return <Options openWidgetDrawer={openWidgetDrawer} stockInventory={stockInventory} {...commonProps} />;

            default:
                return null;
        }
    }, [block.type, commonProps, openWidgetDrawer, packageIndex, setPackageIndex, stockInventory, stockList]);

    return <div ref={ref}>{content}</div>;
};

export type ViewContainerProps = {
    modelConfigurator: ModelConfiguratorDetailsFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    state: State;
    actions: ActionsProps;
    introSectionRef: MutableRefObject<HTMLDivElement>;
    dealerRef: MutableRefObject<string>;
    bannerHeight: number;
};

const ViewContainer = ({
    modelConfigurator,
    calculatorContext,
    endpoint,
    state,
    actions,
    introSectionRef,
    dealerRef,
    bannerHeight,
}: ViewContainerProps) => {
    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();
    const [imageToShow, setImageToShow] = useState<string>(null);
    const showFooter = useRef(false);
    const [isSticky, setSticky] = useState<boolean>(false);

    const [packageIndex, setPackageIndex] = useState<number>(0);
    const [openWidgetDrawer, setOpenWidgetDrawer] = useState<boolean>(false);
    const screens = Grid.useBreakpoint();
    const isMobile = useMemo(() => !screens.lg, [screens]);

    const previousValueRef = useRef(dealerRef.current !== state.dealerId ? null : values.configuratorId);
    const variantConfigurator = useMemo(
        () =>
            modelConfigurator.variantConfigurators.find(
                variantConfigurator =>
                    variantConfigurator.id === values.configuratorId &&
                    variantConfigurator.__typename === 'VariantConfigurator'
            ),
        [modelConfigurator.variantConfigurators, values.configuratorId]
    );

    const sortedBlocks = useMemo(
        () =>
            variantConfigurator?.blocks
                .sort(blockSorter)
                .sort(optionBlockSorter)
                .filter(block => block.isActive) || [],
        [variantConfigurator?.blocks]
    );

    const stockVariants: InventoryDetailsPublicDataFragment[] = useMemo(
        () => variantConfigurator?.inventories || [],
        [variantConfigurator?.inventories]
    );

    const translate = useTranslatedString();

    useEffect(() => {
        if (
            !state.selectedSection &&
            stockVariants.length > 0 &&
            !isEqual(previousValueRef.current, values.configuratorId)
        ) {
            const variantColorBlock = variantConfigurator.blocks.find(block => block.type === BlockType.Color);
            const variantTrimBlock = variantConfigurator.blocks.find(block => block.type === BlockType.Trim);
            const variantPackageBlock = variantConfigurator.blocks.find(block => block.type === BlockType.Package);

            // Find the default selected stock with the variant color and trim block
            let stock: InventoryDetailsPublicDataFragment = null;

            if (variantColorBlock.__typename === 'ColorBlock' && variantTrimBlock.__typename === 'TrimBlock') {
                variantColorBlock.colorSettings.some(color =>
                    variantTrimBlock.trimSettings.some(trim => {
                        stock = stockVariants.find(
                            stock =>
                                stock.availableStock > 0 &&
                                stock.__typename === 'ConfiguratorInventory' &&
                                stock.packageSetting?.defaultSelected &&
                                !!(color.id === stock.colorSetting.id) &&
                                !!(trim.id === stock.trimSetting.id)
                        );

                        return !!stock;
                    })
                );
            }

            const initialConfiguratorBlocks: ConfiguratorBlock[] = [];

            if (stock?.__typename === 'ConfiguratorInventory') {
                initialConfiguratorBlocks.push({
                    blockId: variantColorBlock.id,
                    blockType: BlockType.Color,
                    ids: [stock.colorSetting.id],
                    combo: [],
                });

                initialConfiguratorBlocks.push({
                    blockId: variantTrimBlock.id,
                    blockType: BlockType.Trim,
                    ids: [stock.trimSetting.id],
                    combo: [],
                });

                if (stock.packageSetting && !isNil(variantPackageBlock)) {
                    initialConfiguratorBlocks.push({
                        blockId: variantPackageBlock.id,
                        blockType: BlockType.Package,
                        ids: [stock.packageSetting.id],
                        combo: [],
                    });
                }
            }

            setFieldValue('configuratorBlocks', initialConfiguratorBlocks);
            actions.setHasError(false);
        }
    }, [actions, setFieldValue, state.selectedSection, stockVariants, values, variantConfigurator]);

    useEffect(() => {
        if (previousValueRef.current === values.configuratorId) {
            // no changes
            return;
        }

        if (variantConfigurator && variantConfigurator.blocks.length > 0) {
            setImageToShow(variantConfigurator.blocks[0].id);

            const optionBlocks = variantConfigurator.blocks.filter(block => block.__typename === 'OptionsBlock');

            const options = compact(
                optionBlocks.map(optionBlock => {
                    if (optionBlock.__typename === 'OptionsBlock') {
                        switch (optionBlock.optionSettings.__typename) {
                            case 'ComboOptionSettings': {
                                const options = optionBlock.optionSettings.options.map(option => ({
                                    id: option.id,
                                    value: null,
                                }));

                                return { id: optionBlock.id, kind: OptionKind.Combo, values: options };
                            }

                            case 'MultiSelectOptionSettings':
                                return { id: optionBlock.id, kind: OptionKind.MultiSelect, values: null };

                            case 'SingleSelectOptionSettings':
                                return {
                                    id: optionBlock.id,
                                    kind: OptionKind.SingleSelect,
                                    values:
                                        optionBlock.optionSettings?.options?.length === 1
                                            ? head(optionBlock.optionSettings.options).id
                                            : null,
                                };

                            default:
                                return null;
                        }
                    }

                    return null;
                })
            );

            setFieldValue('options', options);

            // update previous value
            previousValueRef.current = values.configuratorId;
        }
    }, [actions, setFieldValue, state.selectedSection, stockVariants, values.configuratorId, variantConfigurator]);

    useEffect(() => {
        const handleScroll = () => {
            if (!isMobile) {
                const threshold = window.outerHeight * 0.6;
                setSticky(window.scrollY > threshold);
            }
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [isMobile]);

    const onActiveSectionChange = useCallback(
        (blockId: string) => {
            const index = sortedBlocks.findIndex(block => block.id === blockId);

            setImageToShow(blockId);
            showFooter.current = index > 0;
        },
        [isMobile, sortedBlocks]
    );

    if (!variantConfigurator) {
        return null;
    }

    return (
        <>
            <UpdateExtraAmounts calculatorContext={calculatorContext} variantConfigurator={variantConfigurator} />
            <Container bannerHeight={bannerHeight} fixedPosition={isSticky}>
                {!isMobile && (
                    <div className="imageWrapper">
                        {sortedBlocks.map(block => (
                            <ImageSection
                                key={`image-${block.id}`}
                                block={block}
                                imageToShow={imageToShow}
                                packageIndex={packageIndex}
                                variantConfigurator={variantConfigurator}
                            />
                        ))}
                    </div>
                )}
                <div className="selectorWrapper">
                    <SectionHeaderBar
                        bannerHeight={bannerHeight}
                        text={translate(modelConfigurator.configuratorTitle)}
                    />
                    {sortedBlocks.map(block => (
                        <SectionContent
                            key={`sectionContent-${block.id}`}
                            actions={actions}
                            block={block}
                            modelConfigurator={modelConfigurator}
                            onSectionVisible={onActiveSectionChange}
                            openWidgetDrawer={openWidgetDrawer}
                            packageIndex={packageIndex}
                            setPackageIndex={setPackageIndex}
                            state={state}
                            stockInventory={stockVariants}
                            variantConfigurator={variantConfigurator}
                        />
                    ))}
                    <FooterWidget
                        actions={actions}
                        bannerHeight={bannerHeight}
                        calculatorContext={calculatorContext}
                        endpoint={endpoint}
                        modelConfigurator={modelConfigurator}
                        openDrawer={openWidgetDrawer}
                        setOpenDrawer={setOpenWidgetDrawer}
                        state={state}
                        variantConfigurator={variantConfigurator}
                        visible={showFooter.current}
                    />
                </div>
            </Container>
        </>
    );
};

export default ViewContainer;
