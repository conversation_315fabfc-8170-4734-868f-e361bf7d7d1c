import { ApolloError, useApolloClient } from '@apollo/client';
import { compact } from 'lodash/fp';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
// eslint-disable-next-line max-len
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import {
    ApplyNewConfiguratorApplicationMutation,
    ApplyNewConfiguratorApplicationMutationVariables,
    ApplyNewConfiguratorApplicationDocument,
} from '../../../../api/mutations/applyNewConfiguratorApplication';
import {
    DraftConfiguratorApplicationDocument,
    DraftConfiguratorApplicationMutation,
    DraftConfiguratorApplicationMutationVariables,
} from '../../../../api/mutations/draftConfiguratorApplication';
import {
    UpdateConfiguratorApplicationDocument,
    UpdateConfiguratorApplicationMutation,
    UpdateConfiguratorApplicationMutationVariables,
} from '../../../../api/mutations/updateConfiguratorApplication';
import {
    BlockType,
    ConfiguratorBlockPayload,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    OptionKind,
} from '../../../../api/types';
import { CalculatorContext } from '../../../../calculator/computing';
import getFinancingFromCalculatorValues from '../../../../calculator/getFinancingFromCalculatorValues';
import getInsuranceValueFromCalculator from '../../../../calculator/getInsuranceValueFromCalculator';
import getVehicleFromCalculatorContext from '../../../../calculator/getVehicleFromCalculatorContext';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import useDebounce from '../../../../utilities/useDebounce';
import useHandleError from '../../../../utilities/useHandleError';
import { usePersistConfiguratorJourneyValues } from '../usePersistConfiguratorJourneyValues';
import type { ActionsProps, State, ConfiguratorFormValues, OptionFormValues, ConfiguratorBlock } from './shared';

export const getConfiguratorBlocks = (configuratorBlocks: ConfiguratorBlock[], options: OptionFormValues[]) => {
    const otherBlocks: ConfiguratorBlockPayload[] = configuratorBlocks
        .filter(block => block.blockType !== BlockType.Options)
        .map(optionBlock => ({
            blockId: optionBlock.blockId,
            ids: optionBlock.ids,
            combo: [],
        }));

    const optionBlocks: ConfiguratorBlockPayload[] = compact(
        options.map(valueOption => {
            switch (valueOption.kind) {
                case OptionKind.Combo: {
                    const { id, values: optionValues } = valueOption;

                    const comboOptionBlock = configuratorBlocks.find(
                        block => block.blockId === id && block.blockType === BlockType.Options
                    );

                    if (comboOptionBlock) {
                        return { blockId: id, ids: [], combo: optionValues };
                    }

                    return null;
                }

                case OptionKind.MultiSelect: {
                    const { id, values: optionValues } = valueOption;

                    if (!optionValues) {
                        return null;
                    }

                    return { blockId: id, ids: optionValues, combo: [] };
                }

                case OptionKind.SingleSelect: {
                    const { id, values: optionValues } = valueOption;

                    if (!optionValues) {
                        return null;
                    }

                    return { blockId: id, ids: [optionValues], combo: [] };
                }

                default:
                    return null;
            }
        })
    );

    return [...otherBlocks, ...optionBlocks];
};

const useSubmitDraft = (
    calculatorContext: CalculatorContext<GenericCalculatorValues>,
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment,
    state: State,
    showPromoCodeErrorModal: (title: string) => void,
    actions: ActionsProps,
    token?: string,
    applicationId?: string
) => {
    const { t } = useTranslation(['configuratorDetails']);
    const { notification } = useThemeComponents();
    const navigate = useNavigate();
    const apolloClient = useApolloClient();
    const { promoCode, saveAsDraft: isDraft } = state;
    const { setPromoCode, setEmailSent, setPromoCodeError } = actions;

    // create a reference which will always be up to date
    const contextReference = useRef(calculatorContext);
    contextReference.current = calculatorContext;
    const { currentLanguageId } = useLanguage();

    const { save: persistConfiguratorJourneyValue, persistedValue } = usePersistConfiguratorJourneyValues();

    return useDebounce(
        useHandleError<ConfiguratorFormValues>(
            async values => {
                notification.loading({
                    content: t('configuratorDetails:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                try {
                    // apply new flow
                    if (applicationId) {
                        const variables = {
                            applicationId,
                            moduleId: endpoint.configuratorApplicationModule.id,
                            financing: getFinancingFromCalculatorValues(values.calculator),
                            insurancing: values.configuration.withInsurance
                                ? getInsuranceValueFromCalculator(values.calculator)
                                : null,
                            configuration:
                                // eslint-disable-next-line max-len
                                values.configuration as ApplyNewConfiguratorApplicationMutationVariables['configuration'],
                        };
                        const { data } = await apolloClient.mutate<
                            ApplyNewConfiguratorApplicationMutation,
                            ApplyNewConfiguratorApplicationMutationVariables
                        >({
                            mutation: ApplyNewConfiguratorApplicationDocument,
                            variables,
                        });
                        if (data) {
                            navigate('../apply', { state: { token: data.result.token } });
                        }

                        return;
                    }

                    const [vehicle] = getVehicleFromCalculatorContext([contextReference.current]);

                    const { data } =
                        !isDraft && token
                            ? await apolloClient
                                  .mutate<
                                      UpdateConfiguratorApplicationMutation,
                                      UpdateConfiguratorApplicationMutationVariables
                                  >({
                                      mutation: UpdateConfiguratorApplicationDocument,
                                      variables: {
                                          configuration: values.configuration,
                                          customer: {
                                              existingLocalCustomer: null,
                                          },
                                          token,
                                          dealerId: values.dealerId,
                                          financing:
                                              endpoint.configuratorApplicationModule.bankModuleId &&
                                              values.calculator.financeProduct
                                                  ? getFinancingFromCalculatorValues(values.calculator, promoCode)
                                                  : null,
                                          configuratorBlocks: getConfiguratorBlocks(
                                              values.configuratorBlocks,
                                              values.options
                                          ),
                                          vehicle,
                                          isDraft,
                                      },
                                  })
                                  .finally(() => {
                                      notification.destroy('primary');
                                  })
                            : await apolloClient
                                  .mutate<
                                      DraftConfiguratorApplicationMutation,
                                      DraftConfiguratorApplicationMutationVariables
                                  >({
                                      mutation: DraftConfiguratorApplicationDocument,
                                      variables: {
                                          moduleId: endpoint.configuratorApplicationModule.id,
                                          vehicle,
                                          customer: {
                                              newLocalCustomer: {
                                                  fields: isDraft
                                                      ? [
                                                            {
                                                                key: LocalCustomerFieldKey.Email,
                                                                stringValue: values.email,
                                                                source: LocalCustomerFieldSource.UserInput,
                                                            },
                                                        ]
                                                      : [],
                                              },
                                          },
                                          financing:
                                              endpoint.configuratorApplicationModule.bankModuleId &&
                                              values.calculator.financeProduct
                                                  ? getFinancingFromCalculatorValues(values.calculator, promoCode)
                                                  : null,
                                          insurancing:
                                              values.configuration.withInsurance &&
                                              endpoint.configuratorApplicationModule.insurerIds?.length &&
                                              values.calculator.insurerId
                                                  ? getInsuranceValueFromCalculator(values.calculator)
                                                  : null,
                                          configuration: values.configuration,
                                          endpointId: endpoint.id,
                                          dealerId: values.dealerId,
                                          configuratorId: values.configuratorId,
                                          tradeInVehicle: [],
                                          configuratorBlocks: getConfiguratorBlocks(
                                              values.configuratorBlocks,
                                              values.options
                                          ),
                                          isDraft,
                                          promoCodeId: promoCode?.id,
                                          languageId: currentLanguageId,
                                      },
                                  })
                                  .finally(() => {
                                      notification.destroy('primary');
                                  });

                    if (data) {
                        persistConfiguratorJourneyValue({
                            ...persistedValue,
                            ...values,
                            token: data.result.token,
                        });
                    }

                    if (!isDraft) {
                        // go to the journey
                        navigate('../apply', { state: { token: data.result.token } });
                    } else {
                        setEmailSent(true);
                    }
                } catch (error) {
                    if (error instanceof ApolloError) {
                        const promoCodeError: { title: string; description: string } = error.graphQLErrors.find(
                            ({ extensions }) => extensions.code === 'BAD_USER_INPUT' && extensions.$promoCodeError
                        )?.extensions?.$promoCodeError as { title: string; description: string };

                        if (promoCodeError) {
                            setPromoCodeError(promoCodeError.description);
                            setPromoCode(null);
                            showPromoCodeErrorModal(promoCodeError.title);

                            return;
                        }
                    }

                    throw error;
                }
            },
            [
                notification,
                apolloClient,
                t,
                applicationId,
                isDraft,
                token,
                endpoint.configuratorApplicationModule.bankModuleId,
                endpoint.configuratorApplicationModule.id,
                endpoint.configuratorApplicationModule.insurerIds?.length,
                endpoint.id,
                promoCode,
                currentLanguageId,
                navigate,
                persistConfiguratorJourneyValue,
                setEmailSent,
                setPromoCodeError,
                setPromoCode,
                showPromoCodeErrorModal,
            ]
        )
    );
};

export default useSubmitDraft;
