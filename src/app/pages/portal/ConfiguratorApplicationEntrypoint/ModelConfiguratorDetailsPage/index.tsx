import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
// eslint-disable-next-line max-len
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { DebugJourneyDataFragment } from '../../../../api/fragments/DebugJourneyData';
import { useGetApplicationJourneyQuery } from '../../../../api/queries/getApplicationJourney';
import { FinancingPreferenceValue, LocalCustomerFieldKey, Maybe } from '../../../../api/types';
import { GenericCalculatorValues } from '../../../../calculator/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    getCalculatorValuesFromApplication,
    getInitialCalculatorValues,
    getInitialInsuranceCalculatorValues,
    getInitialWithFinancing,
    getInitialWithInsurancing,
    getInsuranceValuesFromApplication,
} from '../../../../utilities/journeys/calculator';
import useDefaultMarketTypeValue, { DefaultMarketTypeValue } from '../../../../utilities/useDefaultMarketTypeValue';
import { hasInsuranceScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import NotFoundPage from '../../NotFoundPage';
import { usePersistConfiguratorJourneyValues } from '../usePersistConfiguratorJourneyValues';
import ModelConfiguratorDetailsInnerPage, {
    ConfiguratorContainer,
    ModelConfiguratorDetailsInnerPageProps,
} from './ModelConfiguratorDetailsInnerPage';
import { parseBlocksAndOptions } from './helpers';
import { ConfiguratorFormValues } from './shared';

export type ConfiguratorResumeState = {
    token: string;
    isFromApplyNew?: boolean;
};

const Inner = ({ endpoint }: ModelConfiguratorDetailsPageProps) => {
    const state = useLocation().state as Maybe<ConfiguratorResumeState>;
    const { persistedValue } = usePersistConfiguratorJourneyValues();

    const token = state?.token || persistedValue?.token;

    const { data, loading, called } = useGetApplicationJourneyQuery({
        fetchPolicy: 'network-only',
        variables: {
            token,
            refreshToken: true,
        },
        skip: !token,
    });

    const marketTypeValue = useDefaultMarketTypeValue();

    const initialValues = useMemo((): InitialValues => {
        if (!data) {
            return getDefaultValues(endpoint, marketTypeValue);
        }

        const { application } = data.result;

        if (application.__typename !== 'ConfiguratorApplication') {
            return null;
        }

        return getInitialFormValuesFromApplication(endpoint, application, marketTypeValue);
    }, [data, endpoint, marketTypeValue]);

    if (called && loading) {
        return <PortalLoadingElement />;
    }

    if (called && !initialValues) {
        return <NotFoundPage />;
    }

    return (
        <ModelConfiguratorDetailsInnerPage
            applyNewApplication={
                state?.isFromApplyNew &&
                (data?.result?.application as ModelConfiguratorDetailsInnerPageProps['applyNewApplication'])
            }
            endpoint={endpoint}
            initialCalculatorValues={initialValues.calculator}
            initialFormValues={initialValues.formValues}
            initialShowSummary={initialValues.showSummary}
            overrideCalculator={initialValues.overrideCalculator}
            token={token}
        />
    );
};

type InitialValues = {
    calculator: Partial<GenericCalculatorValues>;
    formValues: Partial<ConfiguratorFormValues>;
    showSummary: boolean;
    overrideCalculator: boolean;
};

const getDefaultValues = (
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment,
    marketTypeValue: DefaultMarketTypeValue
): InitialValues => ({
    calculator: {
        ...getInitialCalculatorValues(marketTypeValue, endpoint.configuratorApplicationModule.marketType),
        ...getInitialInsuranceCalculatorValues(hasInsuranceScenario(endpoint.configuratorApplicationModule.scenarios)),
    },
    formValues: {
        configuration: {
            testDrive: false,
            tradeIn: false,
            withFinancing:
                getInitialWithFinancing(endpoint.configuratorApplicationModule.scenarios) &&
                endpoint.configuratorApplicationModule.financingPreference !== FinancingPreferenceValue.Request,
            withInsurance: getInitialWithInsurancing(endpoint.configuratorApplicationModule.scenarios),
            requestForFinancing:
                endpoint.configuratorApplicationModule.financingPreference === FinancingPreferenceValue.Request,
        },
        configuratorId: null,
        configuratorBlocks: [],
        options: [],
        dealerId: null,
        email: null,
    },
    showSummary: false,
    overrideCalculator: true,
});

type Application = Extract<DebugJourneyDataFragment['application'], { __typename: 'ConfiguratorApplication' }>;

export const getApplicantEmail = (application: Application) => {
    const email = application.applicant.fields.find(field => field.key === LocalCustomerFieldKey.Email);

    if (email && email.__typename === 'LocalCustomerStringField') {
        return email.stringValue;
    }

    return '';
};

const getInitialFormValuesFromApplication = (
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment,
    application: Application,
    marketTypeValue: DefaultMarketTypeValue
): InitialValues => {
    const { dealer, configuration, configurator, configuratorBlocks } = application;

    return {
        calculator: application.financing
            ? {
                  ...getCalculatorValuesFromApplication(
                      endpoint.configuratorApplicationModule.marketType,
                      marketTypeValue,
                      application
                  ),
                  ...getInsuranceValuesFromApplication(configuration.withInsurance, application),
              }
            : getInitialCalculatorValues(marketTypeValue, endpoint.configuratorApplicationModule.marketType),
        formValues: {
            configuration: {
                testDrive: configuration.testDrive,
                tradeIn: configuration.tradeIn,
                withFinancing: configuration.withFinancing,
                withInsurance: configuration.withInsurance,
                requestForFinancing: configuration.requestForFinancing,
            },
            configuratorId: configurator.id,
            dealerId: dealer.id,
            email: getApplicantEmail(application),
            ...parseBlocksAndOptions(configuratorBlocks),
        },
        // Initial page for resumed link is the summary
        // might want to change this if it's not the case
        showSummary: true,
        overrideCalculator: false,
    };
};

export type ModelConfiguratorDetailsPageProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
};

const ModelConfiguratorDetailsPage = ({ endpoint }: ModelConfiguratorDetailsPageProps) => {
    const { ConfiguratorLayout } = useThemeComponents();

    return (
        <ConfiguratorContainer>
            <ConfiguratorLayout>
                <Inner endpoint={endpoint} />
            </ConfiguratorLayout>
        </ConfiguratorContainer>
    );
};

export default ModelConfiguratorDetailsPage;
