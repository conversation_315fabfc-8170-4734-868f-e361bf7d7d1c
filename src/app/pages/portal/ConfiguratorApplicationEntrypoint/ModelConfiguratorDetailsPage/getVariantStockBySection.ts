import { useFormikContext } from 'formik';
import { head, isEmpty } from 'lodash/fp';
import { useMemo } from 'react';
import { InventoryDetailsPublicDataFragment } from '../../../../api/fragments/InventoryDetailsPublicData';
import { BlockType } from '../../../../api/types';
import { ConfiguratorFormValues } from './shared';

const getVariantStockBySection = (block: BlockType, stock: InventoryDetailsPublicDataFragment[]) => {
    const { values } = useFormikContext<ConfiguratorFormValues>();

    const colorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);
    const trimBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);
    const packageBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Package);

    return useMemo(() => {
        switch (block) {
            case BlockType.Color:
                return stock.filter(
                    invStock =>
                        invStock.__typename === 'ConfiguratorInventory' &&
                        invStock.availableStock > 0 &&
                        invStock.trimSetting.id === head(trimBlock?.ids) &&
                        ((!packageBlock && isEmpty(invStock.packageSetting)) ||
                            (invStock.packageSetting && invStock.packageSetting.id === head(packageBlock?.ids)))
                );

            case BlockType.Trim:
                return stock.filter(
                    invStock =>
                        invStock.__typename === 'ConfiguratorInventory' &&
                        invStock.colorSetting.id === head(colorBlock?.ids) &&
                        invStock.availableStock > 0 &&
                        ((!packageBlock && isEmpty(invStock.packageSetting)) ||
                            (invStock.packageSetting && invStock.packageSetting.id === head(packageBlock?.ids)))
                );

            case BlockType.Package:
                return stock.filter(
                    invStock =>
                        invStock.__typename === 'ConfiguratorInventory' &&
                        invStock.trimSetting.id === head(trimBlock?.ids) &&
                        invStock.colorSetting.id === head(colorBlock?.ids) &&
                        invStock.availableStock > 0
                );

            default:
                return stock;
        }
    }, [colorBlock?.ids, packageBlock, block, stock, trimBlock?.ids]);
};

export default getVariantStockBySection;
