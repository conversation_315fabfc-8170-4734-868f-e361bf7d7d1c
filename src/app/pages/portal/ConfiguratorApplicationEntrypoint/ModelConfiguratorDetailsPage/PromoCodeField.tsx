/* eslint-disable max-len */
import { useApolloClient, ApolloError } from '@apollo/client';
import { InputProps } from 'antd';
import { useFormikContext } from 'formik';
import { isString, trim } from 'lodash/fp';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { PromoCodeDataFragment } from '../../../../api/fragments/PromoCodeData';
import {
    GetValidPromoCodeByCodeQuery,
    GetValidPromoCodeByCodeQueryVariables,
    GetValidPromoCodeByCodeDocument,
} from '../../../../api/queries/getValidPromoCodeByCode';
import { CalculatorContext } from '../../../../calculator/computing';
import { VehicleFieldContext } from '../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../calculator/types';
import ApplyPromoCode from '../../StandardApplicationEntrypoint/shared/ApplyPromoCode';
import { ActionsProps, State, ConfiguratorFormValues } from './shared';

type PromoCodeFieldProps = {
    isSummaryContent: boolean;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    state: State;
    actions: ActionsProps;
};

const PromoCodeField = ({
    isSummaryContent = true,
    calculatorContext,
    endpoint,
    state,
    actions,
}: PromoCodeFieldProps) => {
    const apolloClient = useApolloClient();
    const { t } = useTranslation(['configuratorDetails']);
    const { values: calculatorValues, getFieldContext, latestErrorMessage, setLatestErrorMessage } = calculatorContext;
    const { values } = useFormikContext<ConfiguratorFormValues>();

    const { setPromoCode, setPromoCodeError, setPromoCodeInput } = actions;
    const { promoCode, promoCodeInput, promoCodeError } = state;

    const getValidPromoCode = useCallback(
        async event => {
            if (event) {
                event.stopPropagation();
                event.preventDefault();

                event.currentTarget.blur();
            }

            const promoCodeStr = state.promoCodeInput;

            const { selectedVehicle } = getFieldContext<VehicleFieldContext>('vehicle');
            setLatestErrorMessage(latestErrorMessage?.fieldName, null);
            if (isString(promoCodeStr) && trim(promoCodeStr) && values.dealerId) {
                try {
                    const promoCode = await apolloClient.query<
                        GetValidPromoCodeByCodeQuery,
                        GetValidPromoCodeByCodeQueryVariables
                    >({
                        query: GetValidPromoCodeByCodeDocument,
                        variables: {
                            promoCode: promoCodeStr,
                            dealerId: values.dealerId,
                            moduleId: endpoint.configuratorApplicationModule.promoCodeModule?.id,
                            variantId: selectedVehicle.versioning.suiteId,
                            applicationModuleId: endpoint.configuratorApplicationModule.id,
                        },
                        fetchPolicy: 'network-only',
                    });

                    setPromoCode(promoCode?.data?.promoCode);
                } catch (error) {
                    if (error instanceof ApolloError) {
                        const promoCodeError: { title: string; description: string } = error.graphQLErrors.find(
                            ({ extensions }) => extensions.code === 'BAD_USER_INPUT' && extensions.$promoCodeError
                        )?.extensions?.$promoCodeError as { title: string; description: string };

                        if (promoCodeError) {
                            setPromoCodeError(promoCodeError.description);
                        }
                    }
                    setPromoCode(null);
                }
            }
        },
        [
            apolloClient,
            endpoint.configuratorApplicationModule.id,
            endpoint.configuratorApplicationModule.promoCodeModule?.id,
            getFieldContext,
            latestErrorMessage?.fieldName,
            setLatestErrorMessage,
            setPromoCode,
            setPromoCodeError,
            state.promoCodeInput,
            values.dealerId,
        ]
    );

    useEffect(() => {
        // re-try to call the api if the variant changes
        if (calculatorValues.vehicle) {
            getValidPromoCode(null);
        }
    }, [calculatorValues.vehicle]);

    const onFocus = useCallback(() => {
        setPromoCodeInput('');
        setPromoCodeError('');
    }, [setPromoCodeError, setPromoCodeInput]);

    const removePromoCode = useCallback(() => {
        setPromoCodeInput('');
        setPromoCodeError('');
        setPromoCode(null);

        return undefined;
    }, [setPromoCode, setPromoCodeError, setPromoCodeInput]);

    const updatePromoCodeInput = useCallback<InputProps['onChange']>(
        event => {
            setPromoCodeInput(event.target.value);
        },
        [setPromoCodeInput]
    );

    const inputField = useMemo(
        () => ({
            onChange: updatePromoCodeInput,
            onFocus,
            onPressEnter: getValidPromoCode,
            placeholder: t('configuratorDetails:labels.addPromoCode'),
            value: promoCodeInput,
        }),
        [getValidPromoCode, onFocus, promoCodeInput, t, updatePromoCodeInput]
    );

    return (
        <ApplyPromoCode
            inputField={inputField}
            onPromoCodeApply={getValidPromoCode}
            onPromoCodeRemove={removePromoCode}
            price={calculatorValues.totalPrice}
            promoCode={promoCode}
            promoCodeError={promoCodeError}
        />
    );
};

export default PromoCodeField;
