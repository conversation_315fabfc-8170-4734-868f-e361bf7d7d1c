import { ConfiguratorJourneyBlocksDataFragment } from '../../../../api/fragments/ConfiguratorJourneyBlocksData';
import { BlockType, OptionKind, DealershipMarketOverride } from '../../../../api/types';
import { ConfiguratorFormValues } from './shared';

export type DealerMarketOverride = Omit<DealershipMarketOverride, 'dealer'>;

export type DealershipMarket = {
    defaultValue: number;
    editable: boolean;
    overrides: DealerMarketOverride[];
};

export const parseBlocksAndOptions = (
    blocks: ConfiguratorJourneyBlocksDataFragment[]
): {
    configuratorBlocks: ConfiguratorFormValues['configuratorBlocks'];
    options: ConfiguratorFormValues['options'];
} => {
    const configuratorBlocks: ConfiguratorFormValues['configuratorBlocks'] = [];
    const options: ConfiguratorFormValues['options'] = [];

    blocks.forEach(block => {
        switch (block.type) {
            case BlockType.Color:
            case BlockType.Trim:
                configuratorBlocks.push({
                    blockId: block.blockId,
                    blockType: block.type,
                    combo: [],
                    ids: block.value.__typename === 'ColorAndTrimSettings' ? [block.value.id] : [],
                });
                break;

            case BlockType.Package:
                configuratorBlocks.push({
                    blockId: block.blockId,
                    blockType: block.type,
                    combo: [],
                    ids: block.value.__typename === 'PackageSettings' ? [block.value.id] : [],
                });
                break;

            case BlockType.Options:
                switch (block.value.__typename) {
                    case 'SingleSelectOptionSettings':
                        configuratorBlocks.push({
                            blockId: block.blockId,
                            blockType: block.type,
                            combo: [],
                            ids: block.value.options.map(option => option.id),
                        });
                        options.push({
                            id: block.blockId,
                            kind: OptionKind.SingleSelect,
                            values: block.value.options[0].id,
                        });
                        break;

                    case 'MultiSelectOptionSettings':
                        configuratorBlocks.push({
                            blockId: block.blockId,
                            blockType: block.type,
                            combo: [],
                            ids: block.value.options.map(option => option.id),
                        });
                        options.push({
                            id: block.blockId,
                            kind: OptionKind.MultiSelect,
                            values: block.value.options.map(option => option.id),
                        });
                        break;

                    case 'ApplicationComboSettings':
                        configuratorBlocks.push({
                            blockId: block.blockId,
                            blockType: block.type,
                            combo: block.value.comboOptions.map(option => ({
                                id: option.id,
                                value: option.value,
                            })),
                            ids: block.value.comboOptions.map(option => option.id),
                        });
                        options.push({
                            id: block.blockId,
                            kind: OptionKind.Combo,
                            values: block.value.comboOptions.map(option => ({
                                id: option.id,
                                value: option.value,
                            })),
                        });
                        break;

                    default:
                        throw new Error('Unknown option type');
                }
                break;

            default:
                throw new Error('Unrecognized block type');
        }
    });

    return {
        configuratorBlocks,
        options,
    };
};

export type ApplicationScenarioConfig = {
    withFinancing: boolean;
    withInsurance: boolean;
    isFinancingOptional: boolean;
    isInsuranceOptional: boolean;
};

export const scrollToInputField = (fieldName: string) => {
    const inputSelector = `input[name="${fieldName}"]`;
    const firstInputElement = document.querySelector(inputSelector);

    if (!firstInputElement) {
        return;
    }

    firstInputElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
    });
};
