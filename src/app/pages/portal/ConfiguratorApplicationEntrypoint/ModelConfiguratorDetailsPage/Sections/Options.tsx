import { Col, Grid, Row } from 'antd';
import { useFormikContext } from 'formik';
import { findIndex } from 'lodash/fp';
import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { BlockDetailsFragment } from '../../../../../api/fragments/BlockDetails';
import { InventoryDetailsPublicDataFragment } from '../../../../../api/fragments/InventoryDetailsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../../api/fragments/ModelConfiguratorDetails';
import { OptionSettingDetailsFragment } from '../../../../../api/fragments/OptionSettingDetails';
import { UploadFileWithPreviewFormDataFragment } from '../../../../../api/fragments/UploadFileWithPreviewFormData';
import { VariantConfiguratorDetailsFragment } from '../../../../../api/fragments/VariantConfiguratorDetails';
import { BlockType, ComboType, OptionKind } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import renderMarkdown from '../../../../../utilities/renderMarkdown';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import Media from '../components/Media';
import OptionCheckboxField from '../components/OptionCheckboxField';
import OptionComboField from '../components/OptionComboField';
import OptionRadioField from '../components/OptionRadioField';
import ProceedToSummary, { ButtonContainer } from '../components/ProceedToSummary';
import SlideShow from '../components/SlideShow';
import { ConfiguratorFormValues } from '../shared';
import { SectionTitle, ImageContainer, SelectionContainer } from '../ui';
import { StyledCombinationErrorMessage } from './Packages';

const FieldContainer = styled.div`
    text-align: left;
    margin-bottom: 3rem;
`;

const Description = styled.div`
    font-size: 16px;
    white-space: pre-line;
`;

export type OptionsProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    block: BlockDetailsFragment;
    isLastBlock: boolean;
    openWidgetDrawer: boolean;
    setShowSummary: (value: boolean) => void;
    setHasError: (hasError: boolean) => void;
    hasError: boolean;
    stockInventory: InventoryDetailsPublicDataFragment[];
};

export type OptionFieldType = {
    optionSetting: OptionSettingDetailsFragment;
    openWidgetDrawer: boolean;
    index: number;
};

export const OptionField = ({ index, optionSetting, openWidgetDrawer }: OptionFieldType) => {
    switch (optionSetting.__typename) {
        case 'SingleSelectOptionSettings':
            return <OptionRadioField name={`options[${index}].values`} optionSetting={optionSetting} />;

        case 'MultiSelectOptionSettings':
            return <OptionCheckboxField name={`options[${index}].values`} optionSetting={optionSetting} />;

        case 'ComboOptionSettings':
            return (
                <OptionComboField
                    name={`options[${index}].values`}
                    openWidgetDrawer={openWidgetDrawer}
                    optionSetting={optionSetting}
                />
            );

        default:
            throw new Error('Invalid option setting type');
    }
};

const Options = ({
    block,
    isLastBlock,
    openWidgetDrawer,
    setShowSummary,
    hasError,
    variantConfigurator,
    modelConfigurator,
    setHasError,
    stockInventory,
}: OptionsProps) => {
    const { t } = useTranslation('configuratorDetails');
    const screens = Grid.useBreakpoint();
    const isMobile = useMemo(() => !screens.lg, [screens]);

    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();

    const [selectedImage, setSelectedImage] = useState<UploadFileWithPreviewFormDataFragment | null>(null);

    const { Button } = useThemeComponents();

    const translatedString = useTranslatedString();

    // This selected image, only applied for mobile (responsive) one
    // For desktop, it's handled outside
    // ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/ViewContainer.tsx
    const singleSelectedOptionImage = useMemo(() => {
        if (block.__typename === 'OptionsBlock' && block.optionSettings.__typename === 'SingleSelectOptionSettings') {
            const filtered = values.options.find(option => option.id === block.id);

            // Since for single select is just one, it's not an array
            if (typeof filtered?.values === 'string') {
                const selectedOption = block.optionSettings.options.find(option => option.id === filtered.values);

                return selectedOption?.image;
            }
        }

        return null;
    }, [values, block]);

    useEffect(() => {
        if (singleSelectedOptionImage) {
            setSelectedImage(singleSelectedOptionImage);
        } else {
            setSelectedImage(null);
        }
    }, [singleSelectedOptionImage]);

    const onAdd = useCallback(() => {
        setFieldValue(
            'configuratorBlocks',
            [
                ...values.configuratorBlocks.filter(
                    currentBlock => currentBlock.blockType !== BlockType.Options && currentBlock.blockId !== block.id
                ),
                values.configuratorBlocks.every(
                    currentBlock => currentBlock.blockType !== BlockType.Options && currentBlock.blockId !== block.id
                ) && {
                    blockId: block.id,
                    blockType: BlockType.Options,
                    ids: [],
                    combo: [],
                },
            ].filter(Boolean)
        );
    }, [block, setFieldValue, values.configuratorBlocks]);

    const optionSetting = useMemo(() => (block?.__typename === 'OptionsBlock' ? block.optionSettings : null), [block]);

    const index = useMemo(() => findIndex({ id: block.id }, values.options), [block.id, values.options]);

    const disabled = useMemo(() => {
        if (optionSetting.__typename === 'ComboOptionSettings') {
            const dropdownOptions = optionSetting.options.filter(option => option.comboType === ComboType.Dropdown);

            const valueOptions = values.options.find(data => data.id === block.id);

            if (valueOptions?.kind === OptionKind.Combo) {
                return valueOptions.values.some(
                    option => !option.value && dropdownOptions.some(dropdownOption => dropdownOption.id === option.id)
                );
            }
        }

        return false;
    }, [block, optionSetting, values]);

    if (block.__typename !== 'OptionsBlock') {
        return null;
    }

    const showError = useMemo(() => {
        if (hasError && optionSetting.__typename === 'SingleSelectOptionSettings') {
            const filtered = values.options.find(option => option.id === block.id);

            return !filtered?.values;
        }

        return false;
    }, [hasError, optionSetting.__typename, values.options, block.id]);

    return (
        <SelectionContainer>
            {isMobile && (
                <ImageContainer>
                    {selectedImage ? (
                        <Media fileName={selectedImage?.filename} source={selectedImage?.url} />
                    ) : (
                        <SlideShow
                            images={block.sectionImages.map(image => ({ fileName: image.filename, source: image.url }))}
                        />
                    )}
                </ImageContainer>
            )}
            <div className="configurator">
                <SectionTitle>{translatedString(block.sectionTitle)}</SectionTitle>
                <FieldContainer>
                    <OptionField index={index} openWidgetDrawer={openWidgetDrawer} optionSetting={optionSetting} />
                    <Description>{renderMarkdown(translatedString(block.sectionDescription))}</Description>
                    {showError && (
                        <StyledCombinationErrorMessage>
                            {t('configuratorDetails:messages.pleaseSelect')}
                        </StyledCombinationErrorMessage>
                    )}
                </FieldContainer>
                {optionSetting.__typename === 'ComboOptionSettings' && (
                    <ButtonContainer>
                        <Row align="middle" gutter={[16, 16]} justify="center">
                            <Col flex="1 0 11.25rem">
                                <Button
                                    disabled={disabled}
                                    onClick={onAdd}
                                    style={{ marginBottom: '1rem' }}
                                    type="tertiary"
                                    block
                                >
                                    {t(
                                        `configuratorDetails:actions.${
                                            values.configuratorBlocks.some(
                                                block => block.blockType === BlockType.Options
                                            )
                                                ? 'added'
                                                : 'add'
                                        }`
                                    )}
                                </Button>
                            </Col>
                        </Row>
                    </ButtonContainer>
                )}

                {isLastBlock && (
                    <ProceedToSummary
                        modelConfigurator={modelConfigurator}
                        setHasError={setHasError}
                        setShowSummary={setShowSummary}
                        stockInventory={stockInventory}
                        variantConfigurator={variantConfigurator}
                    />
                )}
            </div>
        </SelectionContainer>
    );
};

export default Options;
