import { head, isEmpty } from 'lodash/fp';
import { InventoryDetailsPublicDataFragment } from '../../../../../api/fragments/InventoryDetailsPublicData';
import { BlockType } from '../../../../../api/types';
import type { ConfiguratorBlock } from '../shared';

const getProceedToSummary = (
    stockInventory: InventoryDetailsPublicDataFragment[],
    configuratorBlocks: ConfiguratorBlock[]
) => {
    const colorBlock = configuratorBlocks.find(block => block.blockType === BlockType.Color);
    const trimBlock = configuratorBlocks.find(block => block.blockType === BlockType.Trim);
    const packageBlock = configuratorBlocks.find(block => block.blockType === BlockType.Package);

    return stockInventory.some(
        stock =>
            stock.__typename === 'ConfiguratorInventory' &&
            stock.colorSetting.id === head(colorBlock?.ids) &&
            stock.trimSetting.id === head(trimBlock?.ids) &&
            ((!packageBlock && isEmpty(stock.packageSetting)) ||
                (stock.packageSetting && stock.packageSetting.id === head(packageBlock?.ids))) &&
            stock.availableStock > 0
    );
};

export default getProceedToSummary;
