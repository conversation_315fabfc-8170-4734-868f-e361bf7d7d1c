import { Button as Antd<PERSON><PERSON><PERSON>, Col, Grid, Row } from 'antd';
import { useFormikContext } from 'formik';
import { head } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { BlockDetailsFragment } from '../../../../../api/fragments/BlockDetails';
import { InventoryDetailsPublicDataFragment } from '../../../../../api/fragments/InventoryDetailsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../../api/fragments/ModelConfiguratorDetails';
import { PackageSettingsSpecsFragment } from '../../../../../api/fragments/PackageSettingsSpecs';
import { VariantConfiguratorDetailsFragment } from '../../../../../api/fragments/VariantConfiguratorDetails';
import { BlockType } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import Media from '../components/Media';
import ProceedToSummary, { ButtonContainer } from '../components/ProceedToSummary';
import { SelectedSectionOption, ConfiguratorFormValues } from '../shared';
import { ImageContainer, SelectionContainer } from '../ui';
import getProceedToSummary from './getProceedToSummary';
import LeftIcon from '../../../../../assets/ci/configurator/leftIcon.svg';
import RightIcon from '../../../../../assets/ci/configurator/rightIcon.svg';

const PackageContainer = styled.div`
    position: relative;
`;

const Package = styled.div`
    padding: 0 60px;
`;

const PackageTitle = styled.div`
    font-size: 1.375rem;
    font-weight: 900;
    color: var(--configurator-text-color, var(--ant-primary-color));
`;

const Description = styled.div`
    font-size: 18px;
    color: #646464;
    font-weight: 400;
    padding-bottom: 28px;
`;

const Ul = styled.ul`
    padding-left: 24px;
    font-size: 18px;
    color: #000;
    text-align: left;
    margin-bottom: 3rem;

    & > li {
        list-style-type: disc;
        &::marker {
            color: var(--ant-primary-color);
            font-size: 1.5rem;
        }
    }
`;

const Features = styled.div``;

const StyledButton = styled(AntdButton)<{ position: 'left' | 'right' }>`
    &.ant-btn {
        position: absolute;
        z-index: 10;
        border: none;
        background-color: transparent;
        margin-left: 0;
        padding: 0;
        top: 50%;

        &.ant-btn[disabled]:hover {
            background-color: transparent;
            border: none;
        }
    }

    &:hover {
        background-color: transparent;
    }

    ${({ position }) => position === 'left' && 'left: 0;'}
    ${({ position }) => position === 'right' && 'right: 0;'}
`;

export const StyledCombinationErrorMessage = styled.div`
    color: var(--ant-error-color);
    text-align: left;
    font-size: 16px;
    font-style: italic;
`;

export const StyledPackageCombinationErrorMessage = styled.div`
    color: var(--ant-error-color);
    text-align: center;
    font-size: 16px;
    font-style: italic;
`;

export type PackagesProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    block: BlockDetailsFragment;
    packageIndex: number;
    setPackageIndex: Dispatch<SetStateAction<number>>;
    isLastBlock: boolean;
    setShowSummary: (value: boolean) => void;
    stockInventory?: InventoryDetailsPublicDataFragment[];
    setSelectedSection: (selectedSection: SelectedSectionOption) => void;
    setHasError: (hasError: boolean) => void;
    filteredStockInventoryBySelectedSection: InventoryDetailsPublicDataFragment[];
    hasError: boolean;
};

const PackageDetails = ({
    stockInventory,
    item,
}: {
    stockInventory: InventoryDetailsPublicDataFragment[];
    item: PackageSettingsSpecsFragment;
}) => {
    const companyFormats = useCompanyFormats();
    const availableStock = stockInventory.some(
        stock =>
            stock.__typename === 'ConfiguratorInventory' &&
            stock.availableStock > 0 &&
            stock.packageSetting &&
            stock.packageSetting.id === item.id
    );

    const translatedString = useTranslatedString();

    if (!availableStock) {
        return null;
    }

    return (
        <Package key={`package-${item.id}`}>
            <PackageTitle>
                {translatedString(item.packageName)}
                <Description>
                    {item.packageType.__typename === 'PackageTypeWithDescription'
                        ? item.packageType.description
                        : companyFormats.formatAmountWithCurrency(item.packageType.price)}
                </Description>
            </PackageTitle>
            <Features>
                <Ul>
                    {item.features.map(feature => (
                        <li key={feature.defaultValue}>{translatedString(feature)}</li>
                    ))}
                </Ul>
            </Features>
        </Package>
    );
};

const Packages = ({
    block,
    packageIndex,
    isLastBlock,
    setPackageIndex,
    setShowSummary,
    stockInventory,
    setSelectedSection,
    setHasError,
    hasError,
    filteredStockInventoryBySelectedSection,
    variantConfigurator,
    modelConfigurator,
}: PackagesProps) => {
    const { t } = useTranslation(['configuratorDetails']);
    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();
    const [modelDetailsId, setModalDetailsId] = useState<string>(null);
    const [detailsModalVisible, setDetailsModalVisible] = useState<boolean>(false);
    const screens = Grid.useBreakpoint();
    const isMobile = useMemo(() => !screens.lg, [screens]);
    const { Button, PackageDetailsModal } = useThemeComponents();

    const openModalDetails = useCallback((id: string) => {
        setModalDetailsId(id);
        setDetailsModalVisible(true);
    }, []);

    const [packageName, details] = useMemo(() => {
        if (block.__typename !== 'PackageBlock') {
            return null;
        }

        const packageItem = block.packageSettings.find(item => item.id === modelDetailsId);

        return [packageItem?.packageName, packageItem?.additionalDetails];
    }, [block, modelDetailsId]);

    const onAdd = useCallback(
        (value: string) => {
            const latestBlock = [
                ...values.configuratorBlocks.filter(block => block.blockType !== BlockType.Package),
                value !== head(values.configuratorBlocks.find(block => block.blockType === BlockType.Package)?.ids) && {
                    blockId: block.id,
                    blockType: BlockType.Package,
                    ids: [value],
                    combo: [],
                },
            ].filter(Boolean);
            setFieldValue('configuratorBlocks', latestBlock);
            setSelectedSection(SelectedSectionOption.Package);
        },
        [block.id, setFieldValue, setSelectedSection, values.configuratorBlocks]
    );

    if (block.__typename !== 'PackageBlock') {
        return null;
    }

    const showError = useMemo(() => {
        if (hasError) {
            return !getProceedToSummary(stockInventory, values.configuratorBlocks);
        }

        return false;
    }, [hasError, stockInventory, values.configuratorBlocks]);

    const instockPackageSettings = useMemo(
        () =>
            block.packageSettings.filter(item =>
                stockInventory.some(
                    stock =>
                        stock.__typename === 'ConfiguratorInventory' &&
                        stock.availableStock > 0 &&
                        stock.packageSetting?.id === item.id
                )
            ),
        [block.packageSettings, stockInventory]
    );

    const currentPackageSettings: PackageSettingsSpecsFragment = useMemo(() => {
        if (instockPackageSettings.length === 0) {
            return null;
        }

        switch (instockPackageSettings[packageIndex].__typename) {
            case 'PackageSettings':
                return instockPackageSettings[packageIndex];

            default:
                return null;
        }
    }, [instockPackageSettings, packageIndex]);

    if (!currentPackageSettings) {
        return null;
    }

    return (
        <>
            <SelectionContainer>
                {isMobile && (
                    <ImageContainer key={`image-${currentPackageSettings.id}`}>
                        <Media
                            fileName={currentPackageSettings.sectionImage?.filename}
                            source={currentPackageSettings.sectionImage?.url}
                        />
                    </ImageContainer>
                )}
                <div className="configurator">
                    <PackageContainer>
                        <PackageDetails item={currentPackageSettings} stockInventory={stockInventory} />
                        {instockPackageSettings.length > 1 && (
                            <>
                                <StyledButton
                                    disabled={instockPackageSettings.length <= packageIndex + 1}
                                    onClick={() => setPackageIndex(prevState => prevState + 1)}
                                    position="right"
                                >
                                    <RightIcon
                                        opacity={instockPackageSettings.length > packageIndex + 1 ? '0.4' : '0.1'}
                                    />
                                </StyledButton>
                                <StyledButton
                                    disabled={packageIndex === 0}
                                    onClick={() => setPackageIndex(prevState => prevState - 1)}
                                    position="left"
                                >
                                    <LeftIcon opacity={packageIndex > 0 ? '0.4' : '0.1'} />
                                </StyledButton>
                            </>
                        )}
                    </PackageContainer>
                    <ButtonContainer>
                        <Row align="middle" gutter={[16, 16]} justify="center" style={{ marginBottom: '1rem' }}>
                            {currentPackageSettings.additionalDetails &&
                                currentPackageSettings.additionalDetails.length > 0 && (
                                    <Col flex="1 0 11.25rem">
                                        <Button
                                            onClick={() => openModalDetails(currentPackageSettings.id)}
                                            type="tertiary"
                                            block
                                        >
                                            {t('configuratorDetails:actions.moreDetails')}
                                        </Button>
                                    </Col>
                                )}
                            <Col flex="1 0 11.25rem">
                                <Button
                                    htmlType="button"
                                    onClick={() => onAdd(currentPackageSettings.id)}
                                    type="tertiary"
                                    block
                                >
                                    {t(
                                        `configuratorDetails:actions.${
                                            currentPackageSettings.id ===
                                            head(
                                                values.configuratorBlocks.find(
                                                    block => block.blockType === BlockType.Package
                                                )?.ids
                                            )
                                                ? 'added'
                                                : 'add'
                                        }`
                                    )}
                                </Button>
                            </Col>
                        </Row>
                    </ButtonContainer>
                    {showError && (
                        <StyledPackageCombinationErrorMessage>
                            {t('configuratorDetails:messages.noCombinationConfiguratorVariant')}
                        </StyledPackageCombinationErrorMessage>
                    )}
                    {isLastBlock && (
                        <ProceedToSummary
                            modelConfigurator={modelConfigurator}
                            setHasError={setHasError}
                            setShowSummary={setShowSummary}
                            stockInventory={stockInventory}
                            variantConfigurator={variantConfigurator}
                        />
                    )}
                </div>
            </SelectionContainer>
            <PackageDetailsModal
                details={details}
                modalVisible={detailsModalVisible}
                packageName={packageName}
                setModalVisible={setDetailsModalVisible}
            />
        </>
    );
};

export default Packages;
