import { Grid } from 'antd';
import { useFormikContext } from 'formik';
import { head } from 'lodash/fp';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BlockDetailsFragment } from '../../../../../api/fragments/BlockDetails';
import { InventoryDetailsPublicDataFragment } from '../../../../../api/fragments/InventoryDetailsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../../api/fragments/VariantConfiguratorDetails';
import { BlockType } from '../../../../../api/types';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import ColorOptionBoxField from '../components/ColorOptionBoxField';
import Media from '../components/Media';
import ProceedToSummary from '../components/ProceedToSummary';
import { SelectedSectionOption, ConfiguratorFormValues } from '../shared';
import { SectionTitle, ImageContainer, ColorDescription, SelectionContainer } from '../ui';
import { StyledPackageCombinationErrorMessage } from './Packages';
import getProceedToSummary from './getProceedToSummary';

export type ExteriorProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    block: BlockDetailsFragment;
    isLastBlock: boolean;
    setShowSummary: (value: boolean) => void;
    stockInventory?: InventoryDetailsPublicDataFragment[];
    filteredStockInventoryBySelectedSection: InventoryDetailsPublicDataFragment[];
    setSelectedSection: (selectedSection: SelectedSectionOption) => void;
    setHasError: (hasError: boolean) => void;
    hasError: boolean;
};

const Exterior = ({
    variantConfigurator,
    block,
    isLastBlock,
    setShowSummary,
    stockInventory,
    setSelectedSection,
    setHasError,
    hasError,
    filteredStockInventoryBySelectedSection,
    modelConfigurator,
}: ExteriorProps) => {
    const { t } = useTranslation('configuratorDetails');
    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();
    const screens = Grid.useBreakpoint();
    const isMobile = useMemo(() => !screens.lg, [screens]);

    const translatedString = useTranslatedString();

    // filter out what color is available based on the trim selected
    const colorBlockInventoryList = stockInventory.filter(stock => stock.availableStock > 0);

    const variantColorBlock = variantConfigurator.blocks.find(block => block.__typename === 'ColorBlock');
    if (variantColorBlock.__typename !== 'ColorBlock') {
        throw new Error('Color Block is missing.');
    }

    const { colorBlock, matrix } = useMemo(() => {
        const colorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);
        const trimBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);

        const matrix = variantConfigurator.matrix.find(
            matrix => matrix.colorId === head(colorBlock?.ids) && matrix.trimId === head(trimBlock?.ids)
        );

        return { colorBlock, matrix };
    }, [values.configuratorBlocks, variantConfigurator.matrix]);

    const onSelect = useCallback(
        (value: string) => {
            const latestBlock = [
                ...values.configuratorBlocks.filter(block => block.blockType !== BlockType.Color),
                {
                    blockId: block.id,
                    blockType: BlockType.Color,
                    ids: [value],
                    combo: [],
                },
            ];
            setFieldValue('configuratorBlocks', latestBlock);
            setSelectedSection(SelectedSectionOption.Color);
        },
        [block.id, setFieldValue, setSelectedSection, values.configuratorBlocks]
    );

    if (block?.__typename !== 'ColorBlock') {
        return null;
    }

    const showError = useMemo(() => {
        if (hasError) {
            return !getProceedToSummary(stockInventory, values.configuratorBlocks);
        }

        return false;
    }, [hasError, stockInventory, values.configuratorBlocks]);

    return (
        <SelectionContainer>
            {isMobile && (
                <ImageContainer>
                    <Media fileName={matrix?.variantColorImage?.filename} source={matrix?.variantColorImage?.url} />
                </ImageContainer>
            )}
            <div className="configurator">
                <SectionTitle>{translatedString(block.sectionTitle)}</SectionTitle>
                <ColorOptionBoxField
                    filteredStockInventoryBySelectedSection={filteredStockInventoryBySelectedSection}
                    inventoryList={colorBlockInventoryList}
                    onClick={onSelect}
                    options={block.colorSettings.map(colorSetting => ({
                        id: colorSetting.id,
                        image: colorSetting.image?.url,
                        color: colorSetting.hex,
                        name: colorSetting.code,
                    }))}
                    selectedId={head(colorBlock?.ids)}
                />
                <ColorDescription>
                    {translatedString(
                        block.colorSettings.find(colorSetting => colorSetting.id === head(colorBlock?.ids))?.name
                    )}
                </ColorDescription>
                {showError && (
                    <StyledPackageCombinationErrorMessage>
                        {t('configuratorDetails:messages.noCombinationConfiguratorVariant')}
                    </StyledPackageCombinationErrorMessage>
                )}
                {isLastBlock && (
                    <ProceedToSummary
                        modelConfigurator={modelConfigurator}
                        setHasError={setHasError}
                        setShowSummary={setShowSummary}
                        stockInventory={stockInventory}
                        variantConfigurator={variantConfigurator}
                    />
                )}
            </div>
        </SelectionContainer>
    );
};

export default Exterior;
