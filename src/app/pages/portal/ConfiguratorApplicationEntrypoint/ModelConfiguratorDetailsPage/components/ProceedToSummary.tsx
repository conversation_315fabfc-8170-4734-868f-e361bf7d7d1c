import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { InventoryDetailsPublicDataFragment } from '../../../../../api/fragments/InventoryDetailsPublicData';
import type { ModelConfiguratorDetailsFragment } from '../../../../../api/fragments/ModelConfiguratorDetails';
import type { VariantConfiguratorDetailsFragment } from '../../../../../api/fragments/VariantConfiguratorDetails';
import { CompanyTheme, OptionKind } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import type { ButtonProps } from '../../../../../themes/types';
import { GTMEvents, useConfiguratorTagManagerArguments } from '../../../../../utilities/googleTagManager';
import getProceedToSummary from '../Sections/getProceedToSummary';
import { scrollToInputField } from '../helpers';
import type { ConfiguratorFormValues } from '../shared';

export const ButtonContainer = styled.div`
    width: 60%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 16px;
`;

export type ProceedToSummaryProps = {
    isPreSummary?: boolean;
    setShowSummary: (value: boolean) => void;
    setHasError: (hasError: boolean) => void;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    variantConfigurator: VariantConfiguratorDetailsFragment;
    stockInventory: InventoryDetailsPublicDataFragment[];
    onProceedToSummary?: () => void;
    buttonType?: ButtonProps['type'];
};

const ProceedToSummary = ({
    isPreSummary,
    setShowSummary,
    setHasError,
    modelConfigurator,
    variantConfigurator,
    stockInventory,
    onProceedToSummary,
    buttonType,
}: ProceedToSummaryProps) => {
    const { t } = useTranslation('configuratorDetails');
    const { Button, theme } = useThemeComponents();
    const { values } = useFormikContext<ConfiguratorFormValues>();

    const variantName = useMemo(() => {
        switch (variantConfigurator.variant.__typename) {
            case 'LocalVariant':
                return variantConfigurator.variant.name.defaultValue;

            default:
                return '';
        }
    }, [variantConfigurator]);

    const tagManagerArguments = useConfiguratorTagManagerArguments(
        values,
        variantConfigurator,
        modelConfigurator.model.name.defaultValue,
        GTMEvents.ProceedToSummaryButtonClick
    );

    const isValid = useMemo(
        () => !values.options.some(option => option.kind === OptionKind.SingleSelect && !option.values),
        [values.options]
    );

    const getFieldNameWithError = useCallback(() => {
        const index = values.options.findIndex(option => option.kind === OptionKind.SingleSelect && !option.values);

        if (index === -1) {
            return null;
        }

        return `options[${index}].values`;
    }, [values.options]);

    const validate = useCallback(() => {
        const hasErr = !(isValid && getProceedToSummary(stockInventory, values.configuratorBlocks));

        setHasError(hasErr);

        return !hasErr;
    }, [setHasError, isValid, stockInventory, values.configuratorBlocks]);

    const onClick = useCallback(() => {
        if (onProceedToSummary) {
            onProceedToSummary();
        }

        if (validate()) {
            setShowSummary(true);
            window.dataLayer?.push(tagManagerArguments);
            window.scrollTo(0, 0);

            return;
        }

        const fieldNameWithError = getFieldNameWithError();
        if (fieldNameWithError) {
            scrollToInputField(fieldNameWithError);
        }
    }, [validate, getFieldNameWithError, setShowSummary, tagManagerArguments]);

    return (
        <ButtonContainer style={{ width: theme === CompanyTheme.PorscheV3 && isPreSummary ? '100%' : '60%' }}>
            <Button
                className="ProceedSummary"
                data-model={modelConfigurator.model.name.defaultValue}
                data-variant={variantName}
                htmlType="button"
                onClick={onClick}
                type={buttonType ?? 'primary'}
                block
            >
                {t('configuratorDetails:actions.proceed')}
            </Button>
        </ButtonContainer>
    );
};

export default ProceedToSummary;
