import React, { Dispatch, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import JourneySteps from '../../../shared/JourneyPage/JourneySteps';
import { JourneyStepsProvider } from '../../../shared/JourneyPage/JourneyStepsContext';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import useConfiguratorLayout from '../useConfiguratorLayout';
import type { ConfiguratorApplicationState } from './shared';

type JourneyLayoutProps = PropsWithChildren & {
    state: State<ConfiguratorApplicationState>;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
};

const JourneyLayout = ({ state, dispatch, children }: JourneyLayoutProps) => {
    const { t } = useTranslation(['customerDetails']);
    const { ConfiguratorLayout, BackButton } = useThemeComponents();
    const { title, onBack, shouldShowBackIcon, useJourneyLayout } = useConfiguratorLayout(dispatch, state);

    if (!useJourneyLayout) {
        // eslint-disable-next-line react/jsx-no-useless-fragment
        return <>{children}</>;
    }

    return (
        <ConfiguratorLayout
            backIcon={
                shouldShowBackIcon ? <BackButton type="link">{t('customerDetails:backButton')}</BackButton> : null
            }
            onBack={onBack}
            title={title}
            hasFooterBar
        >
            <BasicProLayoutContainer>
                <JourneyStepsProvider
                    value={{
                        onBack,
                        shouldAllowBack: shouldShowBackIcon,
                    }}
                >
                    <JourneySteps currentStage={state.stage} stages={state?.stages} />
                    {children}
                </JourneyStepsProvider>
            </BasicProLayoutContainer>
        </ConfiguratorLayout>
    );
};

export default JourneyLayout;
