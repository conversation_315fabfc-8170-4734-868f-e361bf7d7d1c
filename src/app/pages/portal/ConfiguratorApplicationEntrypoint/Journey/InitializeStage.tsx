import { Dispatch, useEffect } from 'react';
import { getPorscheIDConfigByApplicationJourney } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { State, Action, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { ConfiguratorApplicationState } from './shared';

export const getStageFromApplication = (
    application: State<ConfiguratorApplicationState>['application'],
    initialized = true
) => {
    const { draftFlow, deposit, signing, module, insuranceSigning } = application;

    if (module.__typename !== 'ConfiguratorModule') {
        throw new Error('ModuleType not supported');
    }

    const { isPorscheIdLoginMandatory } = getPorscheIDConfigByApplicationJourney(application);

    if (!initialized && application.withCustomerDevice) {
        // todo render calculator
    }

    if (!draftFlow.isApplicantKYCCompleted && !draftFlow.isPorscheIdLoginCompleted && isPorscheIdLoginMandatory) {
        return JourneyStage.PorscheIdLoginRegister;
    }

    if (!draftFlow.isApplicantKYCCompleted) {
        return JourneyStage.ApplicantKYC;
    }

    if (!draftFlow.areGlobalAgreementsCompleted) {
        return JourneyStage.Unknown;
    }

    if (draftFlow.hasGuarantor && !draftFlow.isGuarantorCompleted) {
        return JourneyStage.GuarantorKYC;
    }

    if (deposit && !draftFlow.isDepositCompleted && !draftFlow.isDepositSkipped) {
        return JourneyStage.Deposit;
    }

    if (signing && !draftFlow.isSigningCompleted) {
        // this will only have value if there's signing
        switch (signing.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.Namirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.Otp;

            default:
                return JourneyStage.Unknown;
        }
    }

    if (insuranceSigning && !draftFlow.isInsuranceApplicantSigningCompleted) {
        switch (insuranceSigning.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.InsuranceNamirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.InsuranceOtp;

            default:
                return JourneyStage.Unknown;
        }
    }

    return JourneyStage.Unknown;
};

export type InitializeStageProps = {
    state: State<ConfiguratorApplicationState>;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
};

const InitializeStage = ({ state, dispatch }: InitializeStageProps) => {
    const { application } = state;

    useEffect(() => {
        dispatch({ type: 'goTo', stage: getStageFromApplication(application) });
    }, [application, dispatch]);

    return <PortalLoadingElement />;
};

export default InitializeStage;
