import { useApolloClient } from '@apollo/client';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../api/queries';
import { getPorscheIDConfigByApplicationJourney } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useThemeComponents } from '../../../../themes/hooks';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import { useParseJWTPayload } from '../../../../utilities/parseJWTPayload';
import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import JourneyController from './JourneyController';

export type JourneyBootstrapProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    initialToken: string;
    initialStage: JourneyStage;
};

type TokenPayload = {
    applicationModuleId: { $oid: string };
    origin: 'draft' | 'remote-applicant' | 'remote-guarantor';
};

const JourneyBootstrap = ({ initialToken, initialStage, endpoint }: JourneyBootstrapProps) => {
    const apolloClient = useApolloClient();
    const { applicationModuleId } = useParseJWTPayload<TokenPayload>(initialToken);
    const [state, setInitialState] = useState<GetApplicationJourneyQuery['result'] | null>(null);

    const isInitialized = !!state;
    const isValid = applicationModuleId.$oid === endpoint.configuratorApplicationModule.id;

    const { notification } = useThemeComponents();
    const navigate = useNavigate();

    useEffect(() => {
        if (isInitialized) {
            // already initialized, we skip it
            return;
        }

        apolloClient
            .query<GetApplicationJourneyQuery, GetApplicationJourneyQueryVariables>({
                query: GetApplicationJourneyDocument,
                fetchPolicy: 'no-cache',
                variables: { token: initialToken, refreshToken: true },
            })
            .then(result => setInitialState(result.data.result))
            .catch(error => {
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors !== null) {
                    const { $root: rootError } = apolloErrors;

                    if (rootError) {
                        notification.error(rootError);
                    }
                } else {
                    console.error(error);
                }
                // Navigate back to previous page ("configurator detail")
                navigate('..');
            });
    }, [isInitialized, isValid, apolloClient, initialToken, navigate, notification]);

    if (!isValid) {
        // things are not as expected
        return <NotFoundResult />;
    }

    if (!state) {
        return <PortalLoadingElement />;
    }

    const { application } = state;

    if (application.__typename !== 'ConfiguratorApplication') {
        // things are not as expected (invalid app type)
        return <NotFoundResult />;
    }

    const { isPorscheIdLoginMandatory } = getPorscheIDConfigByApplicationJourney(application);

    const stages = [
        ...(isPorscheIdLoginMandatory ? [JourneyStage.PorscheIdLoginRegister] : []),
        ...getApplicationJourneyStages(application),
    ];

    return (
        <JourneyController
            endpoint={endpoint}
            initialApplication={application}
            initialStage={initialStage}
            initialToken={state.token}
            journeyStages={stages}
        />
    );
};

export default JourneyBootstrap;
