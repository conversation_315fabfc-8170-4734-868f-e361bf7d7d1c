import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getStageFromApplication } from './InitializeStage';
import type { ConfiguratorApplicationState } from './shared';

// eslint-disable-next-line import/prefer-default-export
export const reducer = (
    state: State<ConfiguratorApplicationState>,
    action: Action<ConfiguratorApplicationState>
): State<ConfiguratorApplicationState> => {
    switch (action.type) {
        case 'refresh':
            return { ...state, token: action.token, application: action.application };

        case 'next':
            return {
                ...state,
                stage: getStageFromApplication(action.application),
                token: action.token,
                application: action.application,
                stages: getApplicationJourneyStages(action.application),
            };

        case 'goTo':
            return { ...state, stage: action.stage };

        default:
            return state;
    }
};
