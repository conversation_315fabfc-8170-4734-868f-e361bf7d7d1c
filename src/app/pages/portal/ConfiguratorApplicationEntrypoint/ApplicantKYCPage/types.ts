import { ApplicationAgreementDataFragment, KycFieldSpecsFragment } from '../../../../api/fragments';
import {
    AffinAutoFinanceCentre,
    ConfiguratorApplicationConfigurationPayload,
    CustomerKind,
    TradeInVehiclePayload,
} from '../../../../api/types';
import { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { QuotationFormValues } from '../../../shared/JourneyPage/QuotationDetails/types';
import type { AppointmentValues } from '../../StandardApplicationEntrypoint/AppointmentPage/appointmentValues';

export type FormValues = { fields: KYCPresetFormFields };

export type AgreementAndKycStateData = {
    applicantKYC: KycFieldSpecsFragment[];
    corporateKYC: KycFieldSpecsFragment[];
    applicantAgreements: ApplicationAgreementDataFragment[];
    corporateAgreements: ApplicationAgreementDataFragment[];
};

export type ApplicantFormValues = QuotationFormValues & {
    agreements: AgreementValues;
    customer: FormValues;
    guarantor?: FormValues;
    configuration: ConfiguratorApplicationConfigurationPayload;
    vehicleInterest: {
        model: string;
        subModel: string;
        variant: string;
    };
    tradeInVehicle: TradeInVehiclePayload[];
    isCorporateCustomer: boolean;
    hasGuarantor: boolean;
    dealerId: string;
    remarks: string;
    commentsToInsurer?: string;
    uploadDocuments?: {
        [CustomerKind.Guarantor]?: File[];
        [CustomerKind.Local]?: File[];
        [CustomerKind.Corporate]?: File[];
    };
    prefill: boolean;

    financing?: {
        affinAutoFinanceCentre?: AffinAutoFinanceCentre;
    };

    appointment?: AppointmentValues;
    customerCiamId?: string;
};

export type Agreements = ApplicationAgreementDataFragment[];
