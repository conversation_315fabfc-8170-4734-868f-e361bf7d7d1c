/* eslint-disable max-len */
import { Col, Row } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { Container, Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import VehicleInfo from '../ThankYou/VehicleInfo';
import calculateTotalPrice from '../helper';

export const CheckboxContainer = styled.div`
    display: flex;
    align-items: center;

    & .ant-form-item {
        margin-bottom: 0;
    }

    & .ant-typography {
        font-size: 16px;
    }
`;

export type VehicleInterestProps = {
    configurator: ConfiguratorApplicationState;
};

const colSpan = { span: 24 };

const VehicleInterest = ({ configurator }: VehicleInterestProps) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();

    const vehicleData = useMemo(() => {
        if (configurator.__typename === 'ConfiguratorApplication') {
            switch (configurator.vehicle.__typename) {
                case 'LocalVariant':
                    return {
                        variant: translatedString(configurator.vehicle.name),
                        totalPrice: calculateTotalPrice(configurator),
                        filename: configurator.vehicleImage?.filename,
                        source: configurator.vehicleImage?.url,
                    };

                default:
                    throw new Error('not implemented');
            }
        }
        throw new Error('Invalid application');
    }, [configurator, translatedString]);

    return (
        <Container>
            <Title size="large">{t('configuratorJourney:applicantkyc.selectedVehicle.title')}</Title>
            <Row gutter={[24, 24]}>
                <Col {...colSpan}>
                    <VehicleInfo {...vehicleData} />
                </Col>
            </Row>
        </Container>
    );
};

export default VehicleInterest;
