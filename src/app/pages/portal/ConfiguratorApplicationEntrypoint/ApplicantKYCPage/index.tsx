import { Formik, FormikHelpers } from 'formik';
import { sumBy } from 'lodash/fp';
import { Dispatch, SetStateAction, useMemo } from 'react';
import { ApplicationJourneyDepositFragment } from '../../../../api/fragments/ApplicationJourneyDeposit';
// eslint-disable-next-line max-len
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import { CustomerKind, LocalCustomerManagementModule, ModuleType } from '../../../../api/types';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useValidator from '../../../../utilities/useValidator';
import { LocalVariant } from '../../../../utilities/useVehicleOptions';
import validators from '../../../../utilities/validators';
import { hasAppointmentScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import { useQuotationValidator } from '../../../shared/JourneyPage/QuotationDetails';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { StyleContainer } from '../../StandardApplicationEntrypoint/styledComponents';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import KYCPageInner from './KYCPageInner';
import { AgreementAndKycStateData, Agreements, ApplicantFormValues } from './types';

export type ApplicantFormProps = {
    configurator: ConfiguratorApplicationState;
    variants: LocalVariant[];
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    applicationAgreements: Agreements;
    initialValues: ApplicantFormValues;
    onSubmit: (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => void;
    proceedWithCustomer: (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => void;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
    state?: State<ConfiguratorApplicationState>;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    deposit: ApplicationJourneyDepositFragment;
    setIsCorporate?: Dispatch<SetStateAction<boolean>>;
    customerKind: CustomerKind;
    isCorporate?: boolean;
    showTabs: boolean;
    hasGuarantorPreset: boolean;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    prefill: boolean;
    setAgreementAndKyc?: Dispatch<SetStateAction<AgreementAndKycStateData>>;
    token?: string;
    shouldIncludeLayout?: boolean;
} & UploadDocumentProp;

const ApplicantForm = ({
    initialValues,
    kycPresets,
    kycExtraSettings,
    applicationAgreements,
    dispatch,
    state,
    onSubmit,
    customerKind,
    isCorporate,
    prefill,
    setAgreementAndKyc,
    token,
    uploadDocument,
    removeDocument,
    endpoint,
    ...props
}: ApplicantFormProps) => {
    const { application } = state;

    const { module } = application;

    if (module.__typename !== 'ConfiguratorModule') {
        return null;
    }
    const agreementValidators = useAgreementsValidator(applicationAgreements, 'agreements');
    const applicantsValidator = useKYCFormValidator({
        field: kycPresets,
        extraSettings: kycExtraSettings,
        moduleCountryCode: endpoint.configuratorApplicationModule.company.countryCode,
        prefix: 'customer.fields',
        saveDraft: false,
        bankProvider: application.bank?.integration.provider,
    });

    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );

    const showResetKYCButton = useMemo(() => {
        switch (application.module.__typename) {
            case ModuleType.ConfiguratorModule:
                return application.module.showResetKYCButton;

            default:
                return null;
        }
    }, [application.module]);

    const quotationValidator = useQuotationValidator(
        application.bank?.integration.__typename === 'EnbdBankIntegration' && application.configuration.withFinancing,
        sumBy('amount', application.financing?.dealerOptions ?? []),
        'quotation'
    );

    const validations = useMemo(
        () =>
            validators.compose(
                applicantsValidator,
                agreementValidators,
                quotationValidator,
                validators.only(
                    () => showUploadDocument && !isCorporate,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Local}`)
                ),
                validators.only(
                    () => showUploadDocument && isCorporate,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Corporate}`)
                ),
                validators.only(
                    () => showUploadDocument && customerKind === CustomerKind.Guarantor,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Guarantor}`)
                ),
                validators.only(
                    (values, error, { prefix }) =>
                        customerKind !== CustomerKind.Guarantor &&
                        values.configuration.testDrive &&
                        hasAppointmentScenario(endpoint.configuratorApplicationModule.scenarios) &&
                        endpoint.configuratorApplicationModule.appointmentModule &&
                        endpoint.configuratorApplicationModule.displayAppointmentDatepicker &&
                        !values.appointment?.useCurrentDateTime,
                    validators.compose(
                        validators.requiredDate('appointment.date'),
                        validators.requiredString('appointment.time')
                    )
                ),
                validators.only(
                    values => !!values.configuration.isAffinAutoFinanceCentreRequired,
                    validators.compose(validators.requiredString('financing.affinAutoFinanceCentre'))
                )
            ),
        [
            agreementValidators,
            applicantsValidator,
            customerKind,
            isCorporate,
            quotationValidator,
            showUploadDocument,
            endpoint.configuratorApplicationModule,
        ]
    );
    const validate = useValidator(validations, { prefill });

    return (
        <StyleContainer>
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                <KYCPageInner
                    {...props}
                    applicationAgreements={applicationAgreements}
                    customerKind={customerKind}
                    dispatch={dispatch}
                    endpoint={endpoint}
                    kycExtraSettings={kycExtraSettings}
                    kycPresets={kycPresets}
                    removeDocument={removeDocument}
                    setAgreementAndKyc={setAgreementAndKyc}
                    showResetButton={showResetKYCButton}
                    state={state}
                    token={token}
                    uploadDocument={uploadDocument}
                />
            </Formik>
        </StyleContainer>
    );
};

export default ApplicantForm;
