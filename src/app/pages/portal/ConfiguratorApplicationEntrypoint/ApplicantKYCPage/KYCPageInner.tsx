import { Col, Row } from 'antd';
import { FormikHelpers, useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import type { ApplicationJourneyDepositFragment } from '../../../../api/fragments/ApplicationJourneyDeposit';
// eslint-disable-next-line max-len
import type { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import type { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import {
    CompanyTheme,
    CustomerKind,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
    ModuleType,
    PorscheIdData,
} from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import MandatoryPorscheIDLogin from '../../../../components/PorscheID/MandatoryPorscheIDLogin';
import getKYCDataFromPorscheId from '../../../../components/PorscheID/getKYCDataFromPorscheId';
import { getPorscheIDConfigByApplicationJourney } from '../../../../components/PorscheID/getPorscheIDConfig';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import Form from '../../../../components/fields/Form';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import { LocalVariant } from '../../../../utilities/useVehicleOptions';
import { hasAppointmentScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import CustomerDetails from '../../../shared/JourneyPage/CustomerDetails';
import AffinAutoFinanceCentreSelect from '../../../shared/JourneyPage/CustomerDetails/AffinAutoFinanceCentreSelect';
import ResetKYCButtonPorsche from '../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import { QuotationDetails } from '../../../shared/JourneyPage/QuotationDetails';
import ProceedWithCustomerButton from '../../../shared/ProceedWithCustomerButton';
import AppointmentDetailsSection, {
    AppointmentType,
} from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import { Action, State, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import { SectionDivider } from '../../StandardApplicationEntrypoint/KYCPage/shared';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import { Backdrop, StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import { usePersistConfiguratorJourneyValues } from '../usePersistConfiguratorJourneyValues';
import ConsentsAndDeclarations from './ConsentsAndDeclarations';
import { useMyInfoAndOcrContent } from './MyInfoContent';
import VehicleInterest from './VehicleInterest';
import { AgreementAndKycStateData, Agreements, ApplicantFormValues } from './types';

const leftColSpan = { xl: 8, lg: 12, md: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24 };

export type KYCPageInnerProps = {
    configurator: ConfiguratorApplicationState;
    variants: LocalVariant[];
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    applicationAgreements: Agreements;
    proceedWithCustomer: (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => void;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
    state?: State<ConfiguratorApplicationState>;
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    deposit: ApplicationJourneyDepositFragment;
    setIsCorporate?: Dispatch<SetStateAction<boolean>>;
    customerKind: CustomerKind;
    showTabs: boolean;
    hasGuarantorPreset: boolean;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    showResetButton?: boolean;
    setAgreementAndKyc?: Dispatch<SetStateAction<AgreementAndKycStateData>>;
    token?: string;
} & UploadDocumentProp;

const KYCPageInner = ({
    configurator,
    kycPresets,
    kycExtraSettings,
    applicationAgreements,
    dispatch,
    state,
    proceedWithCustomer,
    endpoint,
    deposit,
    setIsCorporate,
    customerKind,
    hasGuarantorPreset,
    showTabs,
    setPrefill,
    showResetButton = false,
    uploadDocument,
    removeDocument,
}: KYCPageInnerProps) => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isPorscheIDFetchLoading, setIsPorscheIDFetchLoading] = useState<boolean>(false);
    const application = state?.application;
    const { values, isSubmitting, handleSubmit, ...rest } = useFormikContext<ApplicantFormValues>();
    const { resetForm, initialValues, validateForm, submitForm, setValues } = rest;

    const { save: persistConfiguratorJourneyValue, persistedValue } = usePersistConfiguratorJourneyValues();

    const { theme } = useThemeComponents();
    const myinfoContent = useMyInfoAndOcrContent();
    const user = useAccount(true);
    const router = useRouter();

    useEffect(() => {
        persistConfiguratorJourneyValue({
            ...persistedValue,
            kyc: values,
        });
    }, [values]);

    const showRemoteFlowButtonInKYCPage = useMemo(() => {
        switch (configurator.module.__typename) {
            case ModuleType.ConfiguratorModule:
                return configurator.module.showRemoteFlowButtonInKYCPage;

            default:
                return null;
        }
    }, [configurator.module]);

    const proceedWithCustomerButton = useMemo((): boolean => {
        const keys = kycPresets.map(preset => preset.key);

        if (
            (keys.includes(LocalCustomerFieldKey.FirstName) ||
                keys.includes(LocalCustomerFieldKey.LastName) ||
                keys.includes(LocalCustomerFieldKey.FullName)) &&
            keys.includes(LocalCustomerFieldKey.Email) &&
            keys.includes(LocalCustomerFieldKey.IdentityNumber)
        ) {
            return !!showRemoteFlowButtonInKYCPage && user && !application.withCustomerDevice;
        }

        return false;
    }, [kycPresets, showRemoteFlowButtonInKYCPage, user, application.withCustomerDevice]);

    const isApplyingFromDetails = useMemo(
        () =>
            !isNil(application.draftFlow.isApplyingForFinanceCompleted) ||
            !isNil(application.draftFlow.isApplyingForInsuranceCompleted) ||
            !isNil(application.draftFlow.isApplyingForReservationCompleted),
        [
            application.draftFlow.isApplyingForFinanceCompleted,
            application.draftFlow.isApplyingForInsuranceCompleted,
            application.draftFlow.isApplyingForReservationCompleted,
        ]
    );

    const isApplyingFromApplyNew = useMemo(
        () =>
            application.draftFlow.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application.draftFlow.isApplyNewForInsuranceReceived ||
                !application.draftFlow.isApplyNewForAppointmentReceived),
        [
            application.draftFlow.isReceived,
            application.draftFlow.isApplyNewForAppointmentReceived,
            application.draftFlow.isApplyNewForFinancingReceived,
            application.draftFlow.isApplyNewForInsuranceReceived,
        ]
    );

    const isAppointmentSectionVisible = useMemo(
        () =>
            customerKind !== CustomerKind.Guarantor &&
            values.configuration.testDrive &&
            hasAppointmentScenario(endpoint.configuratorApplicationModule.scenarios) &&
            endpoint.configuratorApplicationModule.appointmentModule &&
            endpoint.configuratorApplicationModule.displayAppointmentDatepicker,
        [
            customerKind,
            endpoint.configuratorApplicationModule.appointmentModule,
            endpoint.configuratorApplicationModule.displayAppointmentDatepicker,
            endpoint.configuratorApplicationModule.scenarios,
            values.configuration.testDrive,
        ]
    );

    const { porscheIdIntegrationEnabled, isPorscheIdLoginMandatory } = useMemo(
        () => getPorscheIDConfigByApplicationJourney(application),
        [application]
    );

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    const sectionDivider = useMemo(
        () =>
            application.configuration.isAffinAutoFinanceCentreRequired || isAppointmentSectionVisible ? (
                <Col span={24}>
                    <SectionDivider />
                </Col>
            ) : null,
        [application.configuration.isAffinAutoFinanceCentreRequired, isAppointmentSectionVisible]
    );

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);
    const showCommentsToBank = user && configurator?.bank?.showCommentsField;

    const { insurers } = endpoint.configuratorApplicationModule;
    const showCommentsToInsurer = useMemo(() => {
        if (!user) {
            return false;
        }

        const insurer = insurers.find(insurer => insurer.id === application?.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [user, insurers, application?.insurancing?.insurerId]);

    const hasCurrentVehiclePreset = useMemo(() => {
        const keys = kycPresets.map(preset => preset.key);

        return keys.includes(LocalCustomerFieldKey.CurrentVehicleVin);
    }, [kycPresets]);

    const onPorscheIDCustomerFetched = useCallback(
        (porscheIdData: PorscheIdData) => {
            getKYCDataFromPorscheId(porscheIdData, setValues, hasCurrentVehiclePreset);

            if (state?.stage === JourneyStage.PorscheIdLoginRegister) {
                dispatch({
                    type: 'goTo',
                    stage: JourneyStage.ApplicantKYC,
                });
            }
        },
        [dispatch, setValues, state?.stage, hasCurrentVehiclePreset]
    );

    useEffect(() => {
        if (
            isPorscheIdLoginMandatory &&
            values.customerCiamId &&
            state?.stage === JourneyStage.PorscheIdLoginRegister
        ) {
            dispatch({
                type: 'goTo',
                stage: JourneyStage.ApplicantKYC,
            });
        }
    }, [dispatch, isPorscheIdLoginMandatory, state?.stage, values.customerCiamId]);

    return (
        <>
            <FormAutoTouch />
            <Form id="applicantForm" name="applicantForm" onSubmitCapture={handleSubmit}>
                <Row gutter={[24, 50]}>
                    <Col {...leftColSpan}>
                        <Row gutter={[50, 40]}>
                            <Col span={24}>
                                <VehicleInterest configurator={configurator} />
                            </Col>
                            {application.configuration.isAffinAutoFinanceCentreRequired && (
                                <Col span={24}>
                                    <AffinAutoFinanceCentreSelect />
                                </Col>
                            )}
                            {isAppointmentSectionVisible && (
                                <Col span={24}>
                                    <AppointmentDetailsSection
                                        application={application}
                                        applicationModule={endpoint.configuratorApplicationModule}
                                        appointmentType={AppointmentType.Appointment}
                                        showSkipValidation={false}
                                    />
                                </Col>
                            )}

                            {myinfoContent.render({
                                configurator,
                                endpoint,
                                token: state.token,
                                onLoading: setIsLoading,
                                kycPresets,
                                topDivider: sectionDivider,
                            })}
                        </Row>
                    </Col>
                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            extra={
                                proceedWithCustomerButton && (
                                    <ProceedWithCustomerButton onClick={() => proceedWithCustomer(values, rest)} />
                                )
                            }
                            isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                            stage={state.stage}
                            stages={state.stages}
                        >
                            <Row gutter={[16, 16]}>
                                {state.stage === JourneyStage.PorscheIdLoginRegister && (
                                    <Col span={24}>
                                        <MandatoryPorscheIDLogin
                                            applicationId={application.id}
                                            endpointId={endpoint.id}
                                            onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                                            routerId={router.id}
                                            setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                                        />
                                    </Col>
                                )}
                                {state.stage === JourneyStage.ApplicantKYC && (
                                    <>
                                        <Col span={24}>
                                            <CustomerDetails
                                                applicationId={application.id}
                                                customerKind={customerKind}
                                                endpointId={endpoint.id}
                                                hasGuarantorPreset={hasGuarantorPreset}
                                                hasUploadDocuments={application.bank?.hasUploadDocuments}
                                                hasVSOUpload={false}
                                                isApplyingFromApplyNew={isApplyingFromApplyNew}
                                                isApplyingFromDetails={isApplyingFromDetails}
                                                isGuarantorCompleted={application.draftFlow.isGuarantorCompleted}
                                                isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                                                kycExtraSettings={kycExtraSettings}
                                                kycPresets={kycPresets}
                                                onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                                                porscheIdIntegrationEnabled={porscheIdIntegrationEnabled}
                                                removeDocument={removeDocument}
                                                resetFormHandler={resetFormHandler}
                                                routerId={router.id}
                                                setIsCorporate={setIsCorporate}
                                                setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                                                setPrefill={setPrefill}
                                                showCommentsToInsurer={showCommentsToInsurer}
                                                showRemarks={showCommentsToBank}
                                                showResetButton={showResetButton && !myinfoContent.withMyInfo}
                                                showTabs={showTabs}
                                                showTitle={false}
                                                uploadDocument={uploadDocument}
                                                withFinancing={application.configuration.withFinancing}
                                            />
                                        </Col>
                                        <Col span={24}>
                                            <ConsentsAndDeclarations applicationAgreements={applicationAgreements} />
                                        </Col>
                                        {values.quotation?.source && (
                                            <Col span={24}>
                                                <QuotationDetails source={values.quotation?.source} />
                                            </Col>
                                        )}
                                    </>
                                )}
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>
            <StyledJourneyToolbar>
                {showResetButton &&
                    !myinfoContent.withMyInfo &&
                    (theme === CompanyTheme.Porsche || theme === CompanyTheme.PorscheV3) && (
                        <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                    )}
                <NextButton
                    disabled={isSubmitting || (isPorscheIdLoginMandatory && !values.customerCiamId)}
                    form="applicantForm"
                    onSubmit={onSubmit}
                />
            </StyledJourneyToolbar>
            {(isLoading || isPorscheIDFetchLoading) && <Backdrop />}
        </>
    );
};

export default KYCPageInner;
