import { useApolloClient } from '@apollo/client';
import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { KycFieldSpecsFragment } from '../../../../api/fragments';
// eslint-disable-next-line max-len
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import {
    UpdateConfiguratorApplicationConfigurationDocument,
    UpdateConfiguratorApplicationConfigurationMutation,
    UpdateConfiguratorApplicationConfigurationMutationVariables,
} from '../../../../api/mutations/updateConfiguratorApplicationConfiguration';
import {
    GetMyInfoAuthorizeUrlDocument,
    GetMyInfoAuthorizeUrlQuery,
    GetMyInfoAuthorizeUrlQueryVariables,
} from '../../../../api/queries/getMyInfoAuthorizeUrl';
import { CustomerKind } from '../../../../api/types';
import MyInfo from '../../../../components/MyInfo';
import { useRouter } from '../../../../components/contexts/shared';
import { setMyInfoTemporaryValues } from '../../EventApplicationEntrypoint/ApplicantForm/CustomerDetails/utils';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import OcrContent from './OcrContent';
import { ApplicantFormValues } from './types';

export type MyInfoContentProps = UseMyinfoContentProps & {
    withMyInfo: boolean;
    setWithMyInfo: (value: boolean) => void;
    kycPresets: KycFieldSpecsFragment[];
    onLoading?: (isLoading: boolean) => void;
    topDivider?: React.ReactNode;
};

const MyInfoAndOcrContent = ({
    endpoint,
    configurator,
    token,
    withMyInfo,
    setWithMyInfo,
    onLoading,
    kycPresets,
    topDivider = null,
}: MyInfoContentProps) => {
    const client = useApolloClient();
    const router = useRouter();
    const { values } = useFormikContext<ApplicantFormValues>();

    const { isOcrEnabled, myInfoSettingId } = endpoint.configuratorApplicationModule;
    const myInfoEnabled = !!myInfoSettingId;
    const showMyInfoContent = !withMyInfo && myInfoEnabled;
    const showOcr = !withMyInfo && isOcrEnabled;

    const myInfoOnClick = useCallback(
        async configuration => {
            await client.mutate<
                UpdateConfiguratorApplicationConfigurationMutation,
                UpdateConfiguratorApplicationConfigurationMutationVariables
            >({
                mutation: UpdateConfiguratorApplicationConfigurationDocument,
                variables: {
                    token,
                    configuration,
                },
            });

            const { data } = await client.query<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>({
                query: GetMyInfoAuthorizeUrlDocument,
                variables: {
                    applicationId: configurator.id,
                    routerId: router.id,
                    endpointId: endpoint.id,
                    customerKind: CustomerKind.Local,
                    withTradeIn: values.configuration.tradeIn,
                    withTestDrive: values.configuration.testDrive,
                },
                fetchPolicy: 'no-cache',
            });

            setMyInfoTemporaryValues(configurator.id, {
                appointment: values.appointment,
            });

            if (data?.authorizeUrl) {
                globalThis.location.href = data.authorizeUrl;
            }
        },
        [
            configurator.id,
            client,
            endpoint.id,
            router.id,
            token,
            values.configuration.tradeIn,
            values.configuration.testDrive,
            values.appointment,
        ]
    );

    const tradeInVehicle = useMemo(
        () => ({
            withTradeIn: values.configuration.tradeIn,
            withTestDrive: values.configuration.testDrive,
            name: 'tradeInVehicle',
        }),
        [values]
    );

    if (!showMyInfoContent && !showOcr) {
        return null;
    }

    return (
        <>
            {topDivider}
            <Col span={24}>
                {showMyInfoContent && (
                    <MyInfo
                        applicationId={configurator.id}
                        customerFieldPrefix="customer.fields"
                        customerKind={CustomerKind.Local}
                        onClick={() => myInfoOnClick(values.configuration)}
                        onLoading={onLoading}
                        setWithMyInfo={setWithMyInfo}
                        tradeInVehicle={tradeInVehicle}
                    />
                )}
                {showOcr && <OcrContent endpoint={endpoint} kycPresets={kycPresets} withMyInfo={withMyInfo} />}
            </Col>
        </>
    );
};

export default MyInfoAndOcrContent;

export type UseMyinfoContentProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    configurator: ConfiguratorApplicationState;
    token: string;
    kycPresets: KycFieldSpecsFragment[];
    onLoading?: (isLoading: boolean) => void;
    topDivider?: React.ReactNode;
};

export const useMyInfoAndOcrContent = () => {
    const [withMyInfo, setWithMyInfo] = useState(false);

    return {
        withMyInfo,
        render: (props: UseMyinfoContentProps) => (
            <MyInfoAndOcrContent {...props} setWithMyInfo={setWithMyInfo} withMyInfo={withMyInfo} />
        ),
    };
};
