import type { ConfiguratorApplicationState } from './Journey/shared';

const getVehiclePrice = (vehicle: ConfiguratorApplicationState['vehicle']) => {
    if (vehicle.__typename !== 'LocalVariant') {
        return null;
    }

    return vehicle.vehiclePrice;
};

const getConfiguratorBlocksPrice = (blocks: ConfiguratorApplicationState['configuratorBlocks']) =>
    blocks.map(block => {
        switch (block.__typename) {
            case 'ApplicationConfiguratorColorSetting':
            case 'ApplicationConfiguratorTrimSetting':
                return block.value.price;

            case 'ApplicationConfiguratorOptionSetting': {
                switch (block.value.__typename) {
                    case 'SingleSelectOptionSettings':
                    case 'MultiSelectOptionSettings':
                        return block.value.options
                            .map(option => option.valuePrice)
                            .filter(Boolean)
                            .reduce<number>((total, value) => total + (value || 0), 0);

                    case 'ApplicationComboSettings':
                        return block.value.price;

                    default:
                        return null;
                }
            }

            case 'ApplicationConfiguratorPackageSetting': {
                if (block.value.packageType.__typename === 'PackageTypeWithPrice') {
                    return block.value.packageType.price;
                }

                return null;
            }

            default:
                return null;
        }
    });

const calculateTotalPrice = (configurator: ConfiguratorApplicationState) => {
    if (configurator.financing) {
        return configurator.financing.totalPrice;
    }

    const amounts = [
        getVehiclePrice(configurator.vehicle),
        ...getConfiguratorBlocksPrice(configurator.configuratorBlocks),
    ].filter(Boolean);

    return amounts.reduce<number>((total, value) => total + (value || 0), 0);
};

export default calculateTotalPrice;
