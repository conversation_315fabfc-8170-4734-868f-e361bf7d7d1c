import { head } from 'lodash/fp';
import { useMemo } from 'react';
import { DebugJourneyDataFragment } from '../../../api/fragments';
import { BlockType, OptionKind } from '../../../api/types';
import { GTMEvents, mapConfiguratorToGTMState } from '../../../utilities/googleTagManager';
import useCompanyFormats from '../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../utilities/useTranslatedString';
import { OptionFormValues } from './ModelConfiguratorDetailsPage/shared';
import calculateTotalPrice from './helper';

const useConfiguratorTagManagerArgumentsFromApplication = (
    application: Extract<DebugJourneyDataFragment['application'], { __typename: 'ConfiguratorApplication' }>,
    gtmEvent: GTMEvents.PlaceYourOrderSuccessfully | GTMEvents.KycNextButtonClick
) => {
    if (application.__typename !== 'ConfiguratorApplication') {
        throw new Error('Incorrect application type');
    }

    const { dealer, financing } = application;
    const translatedString = useTranslatedString();
    const companyFormats = useCompanyFormats();

    const vehicleData = useMemo(() => {
        switch (application.vehicle.__typename) {
            case 'LocalVariant':
                return {
                    make: translatedString(
                        application.vehicle.model.parentModel
                            ? application.vehicle.model.parentModel.make.name
                            : application.vehicle.model.make.name
                    ),
                    variant: translatedString(application.vehicle.name),
                    defaultVariantName: application.vehicle.name.defaultValue,
                    defaultModelName: application.vehicle.model.parentModel
                        ? application.vehicle.model.parentModel.name.defaultValue
                        : application.vehicle.model.name.defaultValue,
                    totalPrice: calculateTotalPrice(application),
                    filename: application.vehicleImage?.filename,
                    source: application.vehicleImage?.url,
                    carPrice: application.vehicle.vehiclePrice,
                };

            default:
                throw new Error('not implemented');
        }
    }, [application, translatedString]);

    const configuration = useMemo(
        () =>
            application.configuratorBlocks
                .filter(block => block.__typename !== 'ApplicationConfiguratorOptionSetting')
                .map(block => {
                    switch (block.__typename) {
                        case 'ApplicationConfiguratorColorSetting': {
                            return {
                                blockId: block.blockId,
                                blockType: BlockType.Color,
                                ids: [block.value.id],
                                combo: [],
                            };
                        }

                        case 'ApplicationConfiguratorTrimSetting': {
                            return {
                                blockId: block.blockId,
                                blockType: BlockType.Trim,
                                ids: [block.value.id],
                                combo: [],
                            };
                        }

                        case 'ApplicationConfiguratorPackageSetting': {
                            return {
                                blockId: block.blockId,
                                blockType: BlockType.Package,
                                ids: [block.value.id],
                                combo: [],
                            };
                        }

                        default:
                            throw new Error('not supported');
                    }
                }),
        [application.configuratorBlocks]
    );

    const optionValues: OptionFormValues[] = useMemo(
        () =>
            application.configuratorBlocks
                .filter(block => block.__typename === 'ApplicationConfiguratorOptionSetting')
                .map(block => {
                    switch (block.__typename) {
                        case 'ApplicationConfiguratorOptionSetting':
                            switch (block.value.__typename) {
                                case 'SingleSelectOptionSettings': {
                                    return {
                                        id: block.blockId,
                                        values: head(block.value.options)?.id,
                                        kind: OptionKind.SingleSelect,
                                    };
                                }

                                case 'MultiSelectOptionSettings': {
                                    return {
                                        id: block.blockId,
                                        kind: OptionKind.MultiSelect,
                                        values: block.value.options.map(({ id }) => id),
                                    };
                                }

                                case 'ApplicationComboSettings': {
                                    return {
                                        id: block.blockId,
                                        kind: OptionKind.Combo,
                                        values: block.value.comboOptions,
                                    };
                                }

                                default:
                                    throw new Error('not supported');
                            }

                        default:
                            throw new Error('not supported');
                    }
                }),
        [application.configuratorBlocks]
    );

    const configuratorDetails = mapConfiguratorToGTMState(
        configuration,
        optionValues,
        application.configurator.blocks,
        translatedString
    );

    const tagManagerArgs = useMemo(
        () => ({
            event: gtmEvent,
            configuratorDetails,
            dealer: dealer.displayName,
            totalPrice: companyFormats.formatAmountWithCurrency(vehicleData.totalPrice),
            carPrice: companyFormats.formatAmountWithCurrency(vehicleData.carPrice),
            monthlyPayment: companyFormats.formatAmountWithCurrency(head(financing?.monthlyInstalment)?.amount),
            model: vehicleData.defaultModelName,
            variant: vehicleData.defaultVariantName,
        }),
        [gtmEvent, configuratorDetails, dealer.displayName, companyFormats, vehicleData, financing?.monthlyInstalment]
    );

    return tagManagerArgs;
};

export default useConfiguratorTagManagerArgumentsFromApplication;
