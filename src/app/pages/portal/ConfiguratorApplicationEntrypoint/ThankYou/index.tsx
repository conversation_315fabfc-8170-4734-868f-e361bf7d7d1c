import { Col, Row, Space } from 'antd';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { ApplicationStage } from '../../../../api';
import { Maybe } from '../../../../api/types';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getApplicationIdentifier } from '../../../../utilities/application';
import { GTMEvents } from '../../../../utilities/googleTagManager';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import FullJourneySteps from '../../../shared/JourneyPage/FullJourneySteps';
import BookingDeposit from '../../../shared/ThankYou/BookingDeposit';
import DealerInfo from '../../../shared/ThankYou/DealerInfo';
import SubmittedContent from '../../../shared/ThankYou/SubmittedContent';
import VehicleDetails from '../../../shared/ThankYou/VehicleDetails';
import type { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import useConfiguratorTagManagerArgumentsFromApplication from '../useConfiguratorTagManagerArgumentsFromApplication';

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };

type ContentsTranslation = {
    lead: {
        title: string;
        description: string;
    };
    reservation: {
        title: string;
        description: string;
    };
    emailDescription: string;
};

export type ThankYouPageProps = {
    state: State<ConfiguratorApplicationState>;
};

const ThankYouPageContent = ({ state }: ThankYouPageProps) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();
    const { ConfiguratorLayout, Button } = useThemeComponents();

    const { application } = state;

    const tagManagerArguments = useConfiguratorTagManagerArgumentsFromApplication(
        application,
        GTMEvents.PlaceYourOrderSuccessfully
    );

    useEffect(() => {
        window.dataLayer?.push(tagManagerArguments);
    }, [tagManagerArguments]);

    const identifier = useMemo(
        () =>
            getApplicationIdentifier(application, [
                ApplicationStage.Financing,
                ApplicationStage.Reservation,
                ApplicationStage.Lead,
            ]),
        [application]
    );

    const hasDeposit = !!application.deposit;

    const make = useMemo(() => {
        if (application.__typename === 'ConfiguratorApplication' && application.vehicle.__typename === 'LocalVariant') {
            return translatedString(
                application.vehicle.model.parentModel
                    ? application.vehicle.model.parentModel.make.name
                    : application.vehicle.model.make.name
            );
        }

        return '';
    }, [application, translatedString]);
    const contents: ContentsTranslation = t<string, { returnObjects: true; make: string }, ContentsTranslation>(
        'configuratorJourney:thankyou.contents',
        { returnObjects: true, make }
    );
    const contentProps = useMemo(
        () => ({
            title: hasDeposit
                ? t('configuratorJourney:thankyou.deposit.message')
                : t('configuratorJourney:thankyou.message'),
            reference: t('configuratorJourney:thankyou.deposit.reference', {
                reference: identifier,
            }),
            description: hasDeposit ? contents.reservation?.description : contents.lead?.description,
            subDescription: t('configuratorJourney:thankyou.deposit.emailDescription'),
        }),
        [contents.lead?.description, contents.reservation?.description, hasDeposit, identifier, t]
    );
    const onDone = useCallback(() => {
        const { externalUrl } = application.configurator?.modelConfigurator || {};
        if (externalUrl) {
            globalThis.location.replace(`https://${externalUrl}`);
        }
    }, [application.configurator?.modelConfigurator]);

    return (
        <ConfiguratorLayout title={t('configuratorJourney:thankyou.title')}>
            <BasicProLayoutContainer>
                <FullJourneySteps stages={state?.stages} />
                <Row gutter={[24, 24]} justify="center" style={{ marginBottom: '24px' }}>
                    <Col {...colSpan}>
                        <Space direction="vertical" size={24} style={{ width: '100%' }}>
                            <VehicleDetails state={state} />
                            {hasDeposit && <BookingDeposit depositAmount={application.deposit.amount} />}
                        </Space>
                    </Col>
                    <Col {...colSpan}>
                        <Space direction="vertical" size={32} style={{ width: '100%' }}>
                            <SubmittedContent {...contentProps} />
                            <Space direction="vertical" size={24} style={{ width: '100%' }}>
                                {application?.dealer && <DealerInfo dealer={application?.dealer} />}
                                <Button key="done" htmlType="button" onClick={onDone} type="primary" block>
                                    {t('configuratorJourney:thankyou.actions.continue')}
                                </Button>
                            </Space>
                        </Space>
                    </Col>
                </Row>
            </BasicProLayoutContainer>
        </ConfiguratorLayout>
    );
};

const ThankYouPage = () => {
    const state = useLocation().state as Maybe<State<ConfiguratorApplicationState>>;

    if (!state) {
        return <NotFoundResult />;
    }

    return <ThankYouPageContent state={state} />;
};

export default ThankYouPage;
