/* eslint-disable max-len */
import { ApolloError } from '@apollo/client';
import { FormikHelpers } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { useListLocalVariantsForCalculatorQuery } from '../../../../api/queries/listLocalVariantsForCalculator';
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import { CustomerKind, LocalCustomerFieldKey, Purpose } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues, getKycInitialValuesFromInsurance } from '../../../../utilities/kycPresets';
import useProceedWithCustomerValidators from '../../../../utilities/kycPresets/useProceedWithCustomerValidators';
import useActiveState from '../../../../utilities/useActiveStage';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { getInitUploadDocuments } from '../../../shared/JourneyPage/CustomerDetails/shared';
import useKYCData from '../../../shared/JourneyPage/CustomerDetails/useKYCData';
import { useGetInitialQuotationValues } from '../../../shared/JourneyPage/QuotationDetails';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import {
    getMyInfoTemporaryValues,
    removeMyInfoTemporaryValues,
} from '../../EventApplicationEntrypoint/ApplicantForm/CustomerDetails/utils';
import TimeoutPage from '../../EventApplicationEntrypoint/TimeoutPage';
import { useFirstAvailableSlot } from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentAvailability';
import { Action, State, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import useProceedWithCustomerDevice from '../../StandardApplicationEntrypoint/KYCPage/useProceedWithCustomerDevice';
import ApplicantForm from '../ApplicantKYCPage';
import { AgreementAndKycStateData, ApplicantFormValues } from '../ApplicantKYCPage/types';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import { usePersistConfiguratorJourneyValues } from '../usePersistConfiguratorJourneyValues';
import useNoInventoryModal from './useNoInventoryModal';
import useSubmitApplication from './useSubmitApplication';

export type ConfiguratorApplicationProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    state: State<ConfiguratorApplicationState>;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
};

const Inner = ({ endpoint, state, dispatch }: ConfiguratorApplicationProps) => {
    const { t } = useTranslation('eventApplicantForm');
    const { notification } = useThemeComponents();
    const { isActive, reset } = useActiveState();
    const user = useAccount(true);
    const proceedWithCustomerDevices = useProceedWithCustomerDevice();

    const { token, application } = state;
    const [isCorporate, setIsCorporate] = useState<boolean>(false);
    const [prefill, setPrefill] = useState<boolean>(false);

    // Since configuration test-drive and trade-in can be changed in KYC, we need to use local state,
    // It's possible that there is KYC preset or c&d with condition "testDrive" or "tradeIn" applied
    // only applicable to configurator, event, and standard application
    const [agreementAndKyc, setAgreementAndKyc] = useState<AgreementAndKycStateData>({
        applicantAgreements: application.applicantAgreements,
        corporateAgreements: application.corporateAgreements,
        applicantKYC: application.applicantKYC,
        corporateKYC: application.corporateKYC,
    });

    const { guarantorKYC } = application;
    const { kycFields, kycAgreements } = useKYCData(
        isCorporate,
        agreementAndKyc.applicantKYC,
        agreementAndKyc.corporateKYC,
        agreementAndKyc.applicantAgreements,
        agreementAndKyc.corporateAgreements
    );

    const kycExtraSettings = useMemo(
        () =>
            endpoint?.configuratorApplicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.configuratorApplicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const isApplyingFromDetails = useMemo(
        () =>
            (application.configuration.withFinancing && application.configuration.requestForFinancing) ||
            !isNil(application.draftFlow.isApplyingForFinanceCompleted) ||
            !isNil(application.draftFlow.isApplyingForInsuranceCompleted) ||
            !isNil(application.draftFlow.isApplyingForReservationCompleted),
        [
            application.configuration.requestForFinancing,
            application.configuration.withFinancing,
            application.draftFlow.isApplyingForFinanceCompleted,
            application.draftFlow.isApplyingForInsuranceCompleted,
            application.draftFlow.isApplyingForReservationCompleted,
        ]
    );

    const isApplyingFromApplyNew = useMemo(
        () =>
            application.draftFlow.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application.draftFlow.isApplyNewForInsuranceReceived ||
                !application.draftFlow.isApplyNewForAppointmentReceived),
        [
            application.draftFlow.isReceived,
            application.draftFlow.isApplyNewForAppointmentReceived,
            application.draftFlow.isApplyNewForFinancingReceived,
            application.draftFlow.isApplyNewForInsuranceReceived,
        ]
    );

    const applicantKycAgreements = kycAgreements.map(agreement => ({
        ...agreement,
        isAgreed: isApplyingFromDetails || isApplyingFromApplyNew ? agreement.isAgreed : false,
    }));

    const agreements = useAgreementsValues(applicantKycAgreements);

    const { persistedValue } = usePersistConfiguratorJourneyValues();

    const getInitialQuotationValues = useGetInitialQuotationValues();

    const { data: variantData, loading: loadingVariant } = useListLocalVariantsForCalculatorQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                purpose: Purpose.Production,
                moduleId: endpoint.configuratorApplicationModule.vehicleModuleId,
            },
            bankModuleId: endpoint.configuratorApplicationModule.bankModuleId,
        },
    });

    const appointmentModule = useMemo(
        () =>
            endpoint.configuratorApplicationModule?.appointmentModule &&
            endpoint.configuratorApplicationModule.appointmentModule?.__typename === 'AppointmentModule'
                ? endpoint.configuratorApplicationModule.appointmentModule
                : null,
        [endpoint.configuratorApplicationModule?.appointmentModule]
    );

    const { data: bookedListing, loading: loadingInitialAppointment } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: isNil(appointmentModule?.id),
    });

    const bookedAppointmentTimeSlots = useMemo(
        () => bookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [bookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const datePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedAppointmentTimeSlots,
        endpoint.configuratorApplicationModule.company.timeZone,
        application.appointmentStage?.bookingTimeSlot?.slot
    );

    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );

    const initialValues: ApplicantFormValues = useMemo(() => {
        const { vehicle } = application;

        if (vehicle.__typename !== 'LocalVariant') {
            throw new Error('Invalid Variant');
        }

        const initialFields = application.applicant ? application.applicant.fields : [];
        const myInfoTemporaryValues = getMyInfoTemporaryValues(application.id);

        return {
            agreements,
            customer: kycFields
                ? {
                      fields: {
                          ...getInitialValues(initialFields, kycFields),
                          ...(!isCorporate && getKycInitialValuesFromInsurance(application.insurancing, kycFields)),
                      },
                  }
                : null,
            configuration: application.configuration,
            vehicleInterest: {
                model: vehicle.model.parentModel ? vehicle.model.parentModelId : vehicle.model.id,
                subModel: vehicle.model.parentModel ? vehicle.model.id : null,
                variant: vehicle.id,
            },
            isCorporateCustomer: isCorporate,
            tradeInVehicle: application.tradeInVehicle,
            dealerId: application.dealerId,
            hasGuarantor: false,
            remarks: application?.remarks ?? '',
            prefill,
            quotation: getInitialQuotationValues({
                bank: application.bank,
                financing: application.financing,
                isFinancingEnabled: application.configuration.withFinancing,
            }),

            appointment: {
                date: myInfoTemporaryValues?.appointment?.date ?? datePrefill?.date,
                time: myInfoTemporaryValues?.appointment?.time ?? datePrefill?.firstSlot?.value,
                useCurrentDateTime: false,
            },

            uploadDocuments: getInitUploadDocuments(
                application.documents,
                showUploadDocument,
                application.applicant.kind
            ),
            ...persistedValue?.kyc,
        };
    }, [
        application,
        agreements,
        kycFields,
        isCorporate,
        prefill,
        getInitialQuotationValues,
        datePrefill,
        showUploadDocument,
        persistedValue?.kyc,
    ]);

    const noInventoryModal = useNoInventoryModal();

    const proceedWithCustomerValidator = useProceedWithCustomerValidators(
        agreementAndKyc.applicantKYC,
        'customer.fields'
    );
    const proceedWithCustomerValidations = useMemo(
        () => validators.compose(proceedWithCustomerValidator),
        [proceedWithCustomerValidator]
    );
    const proceedWithCustomerValidate = useValidator(proceedWithCustomerValidations);

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const proceedWithCustomer = useCallback(
        async (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => {
            try {
                const customKYCPresets = {};
                const customKYCFieldKeys = Object.keys(values.customer.fields);

                // extract mandatory fields from KYC preset
                customKYCFieldKeys.forEach((key: LocalCustomerFieldKey) => {
                    if (
                        [
                            LocalCustomerFieldKey.FirstName,
                            LocalCustomerFieldKey.LastName,
                            LocalCustomerFieldKey.FullName,
                            LocalCustomerFieldKey.Email,
                            LocalCustomerFieldKey.Phone,
                        ].includes(key)
                    ) {
                        customKYCPresets[key] = values.customer.fields[key];
                    }
                });

                const validationResult = proceedWithCustomerValidate(values);

                if (!validationResult) {
                    notification.loading({
                        content: t('customerDetails:messages.sendEmailToCustomer'),
                        duration: 0,
                        key: 'primary',
                    });

                    await proceedWithCustomerDevices(state.token, customKYCPresets);

                    notification.destroy('primary');

                    removeMyInfoTemporaryValues(application.id);

                    dispatch({
                        type: 'goTo',
                        stage: JourneyStage.ConfirmEmailSend,
                    });
                } else {
                    // Set errors based on validator, since it's the same format, we can just use it
                    helpers.setErrors(validationResult);

                    // Set touched to true for all error fields
                    // although it should be in boolean instead of string, but formik can handle it
                    // and make sure to set the validate parameter to be false
                    // so it won't conflict with applicant validation
                    helpers.setTouched(validationResult, false);
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                }
            }
        },
        [proceedWithCustomerValidate, notification, t, proceedWithCustomerDevices, state.token, application, dispatch]
    );

    const showCommentsToBank = user && application?.bank?.showCommentsField;

    const { insurers } = endpoint.configuratorApplicationModule;
    const showCommentsToInsurer = useMemo(() => {
        if (!user) {
            return false;
        }

        const insurer = insurers.find(insurer => insurer.id === application?.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [user, insurers, application?.insurancing?.insurerId]);

    const onSubmit = useSubmitApplication({
        state,
        initialValues,
        isCorporate,
        dispatch,
        showNoInventoryModal: noInventoryModal.open,
        showCommentsToBank,
        showCommentsToInsurer,
    });

    const variants = useMemo(() => {
        if (!variantData) {
            return null;
        }

        return variantData.list.items.filter(item => item.__typename === 'LocalVariant');
    }, [variantData]);

    if (!isActive && !user) {
        return <TimeoutPage onBack={reset} />;
    }

    if (loadingVariant || loadingInitialAppointment) {
        return <PortalLoadingElement />;
    }

    return (
        <>
            <ApplicantForm
                applicationAgreements={applicantKycAgreements}
                configurator={application}
                customerKind={isCorporate ? CustomerKind.Corporate : CustomerKind.Local}
                deposit={application.deposit}
                dispatch={dispatch}
                endpoint={endpoint}
                hasGuarantorPreset={guarantorKYC.length > 0}
                initialValues={initialValues}
                isCorporate={isCorporate}
                kycExtraSettings={kycExtraSettings}
                kycPresets={kycFields}
                onSubmit={onSubmit}
                prefill={prefill}
                proceedWithCustomer={proceedWithCustomer}
                removeDocument={removeDocument}
                setAgreementAndKyc={setAgreementAndKyc}
                setIsCorporate={setIsCorporate}
                setPrefill={setPrefill}
                showTabs={agreementAndKyc.corporateKYC.length > 0}
                state={state}
                token={state.token}
                uploadDocument={uploadDocument}
                variants={variants}
            />
            {noInventoryModal.render()}
        </>
    );
};

const ConfiguratorApplication = (props: ConfiguratorApplicationProps) => (
    <OcrFilesManager>
        <Inner {...props} />
    </OcrFilesManager>
);

export default ConfiguratorApplication;
