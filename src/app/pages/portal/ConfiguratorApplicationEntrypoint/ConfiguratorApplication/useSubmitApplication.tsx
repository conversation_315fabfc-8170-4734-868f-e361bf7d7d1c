/* eslint-disable max-len */
import { ApolloError } from '@apollo/client';
import { Dispatch } from 'react';
import { useTranslation } from 'react-i18next';
import { CustomerKind } from '../../../../api';
import { useUploadOcrFiles } from '../../../../components/ocr';
import { useThemeComponents } from '../../../../themes/hooks';
import { BAD_USER_INPUT, EMPTY_INVENTORY_ERROR } from '../../../../utilities/constants';
import { GTMEvents } from '../../../../utilities/googleTagManager';
import useInsuranceKyc from '../../../../utilities/kycPresets/useInsuranceKyc';
import useHandleError from '../../../../utilities/useHandleError';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import { useSubmitApplicationQuotation } from '../../../shared/JourneyPage/QuotationDetails';
import refineCustomerValues from '../../../shared/refineCustomerValues';
import { removeMyInfoTemporaryValues } from '../../EventApplicationEntrypoint/ApplicantForm/CustomerDetails/utils';
import useAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentSubmission';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { ApplicantFormValues } from '../ApplicantKYCPage/types';
import type { ConfiguratorApplicationState } from '../Journey/shared';
import useConfiguratorTagManagerArgumentsFromApplication from '../useConfiguratorTagManagerArgumentsFromApplication';
import { usePersistConfiguratorJourneyValues } from '../usePersistConfiguratorJourneyValues';
import useUpdateConfiguratorApplication from '../useUpdateConfiguratorApplication';

type Props = {
    state: State<ConfiguratorApplicationState>;
    initialValues: ApplicantFormValues;
    isCorporate: boolean;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
    showNoInventoryModal: () => void;
    showCommentsToBank?: boolean;
    showCommentsToInsurer?: boolean;
};

const useSubmitApplication = ({
    state,
    initialValues,
    isCorporate,
    dispatch,
    showNoInventoryModal,
    showCommentsToBank,
    showCommentsToInsurer,
}: Props) => {
    const { t } = useTranslation('eventApplicantForm');
    const { notification } = useThemeComponents();

    const { token, application } = state;

    const updateConfigurator = useUpdateConfiguratorApplication();
    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();
    const submitQuotation = useSubmitApplicationQuotation();
    const submitAppointment = useAppointmentSubmission();
    const uploadOcrFiles = useUploadOcrFiles();
    const tagManagerArguments = useConfiguratorTagManagerArgumentsFromApplication(
        application,
        GTMEvents.KycNextButtonClick
    );

    const { persistedValue, save: persistJourneyValues } = usePersistConfiguratorJourneyValues();

    const { birthdayConfirmation } = useInsuranceKyc(application.configuration.withInsurance, {
        createdAt: application.versioning?.createdAt,
    });

    return useHandleError(
        async ({ appointment, ...values }: ApplicantFormValues) => {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            window.dataLayer?.push(tagManagerArguments);

            try {
                await birthdayConfirmation(
                    initialValues.customer.fields.Birthday?.value,
                    values.customer.fields.Birthday?.value,
                    async () => {
                        const { applicantFields } = refineCustomerValues(values.customer.fields);
                        const updateConfiguratorResult = await updateConfigurator(
                            token,
                            values,
                            isCorporate ? CustomerKind.Corporate : CustomerKind.Local
                        );

                        const updatedToken = await submitQuotation(updateConfiguratorResult.token, values);
                        const submitAgreementResult = await submitAgreements(
                            updatedToken,
                            values.agreements,
                            isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                            values.hasGuarantor
                        );

                        if (showCommentsToBank || showCommentsToInsurer) {
                            // we wait to update remarks first
                            await updateApplicationFields(
                                submitAgreementResult.token,
                                values.remarks,
                                values.commentsToInsurer
                            );
                            // then we later call the KYC
                            // as calling submit customer will immediately call the next step and will have not the remarks
                        }

                        const submitCustomerDetailsResult = await submitCustomerDetails({
                            token: submitAgreementResult.token,
                            fields: applicantFields,
                            customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                            sameCorrespondenceAddress: values.prefill,
                            customerCiamId: values.customerCiamId,
                        });

                        // Before next process
                        // See if there is appointment scenario with test drive applied
                        // If yes, then we need to update the application with the appointment
                        const module = application.module.__typename === 'ConfiguratorModule' && application.module;

                        const { appointmentModule } = module;
                        const finalResult =
                            values.configuration.testDrive &&
                            module?.displayAppointmentDatepicker &&
                            appointmentModule?.__typename === 'AppointmentModule' &&
                            !application.draftFlow.isAppointmentCompleted
                                ? await submitAppointment(
                                      submitCustomerDetailsResult.token,
                                      appointment,
                                      appointmentModule,
                                      module.displayAppointmentDatepicker,
                                      module.company.timeZone
                                  )
                                : submitCustomerDetailsResult;

                        await uploadOcrFiles(finalResult.token);

                        notification.destroy('primary');

                        if (finalResult.__typename === 'GiftVoucherJourneyContext') {
                            throw new Error('unsupported journey context');
                        }
                        const { application: newApplication } = finalResult;

                        if (newApplication.__typename !== 'ConfiguratorApplication') {
                            throw new Error('unexpected type');
                        }

                        removeMyInfoTemporaryValues(application.id);

                        persistJourneyValues({
                            ...persistedValue,
                            token: finalResult.token,
                            kyc: {
                                ...values,
                                appointment,
                            },
                        });

                        dispatch({
                            type: 'next',
                            token: finalResult.token,
                            application: newApplication,
                        });
                    }
                );
            } catch (error) {
                notification.destroy('primary');

                if (error instanceof ApolloError) {
                    const emptyInventoryExtension = error.graphQLErrors.find(
                        ({ extensions }) => extensions.code === BAD_USER_INPUT && extensions[EMPTY_INVENTORY_ERROR]
                    );

                    const emptyInventoryError = emptyInventoryExtension?.extensions?.[EMPTY_INVENTORY_ERROR] as {
                        message: string;
                    };

                    if (emptyInventoryError) {
                        showNoInventoryModal();

                        return;
                    }
                }

                throw error;
            }
        },
        [
            notification,
            t,
            tagManagerArguments,
            birthdayConfirmation,
            initialValues.customer.fields.Birthday?.value,
            updateConfigurator,
            token,
            isCorporate,
            submitQuotation,
            submitAgreements,
            showCommentsToBank,
            showCommentsToInsurer,
            submitCustomerDetails,
            application,
            submitAppointment,
            uploadOcrFiles,
            persistJourneyValues,
            persistedValue,
            dispatch,
            updateApplicationFields,
            showNoInventoryModal,
        ]
    );
};

export default useSubmitApplication;
