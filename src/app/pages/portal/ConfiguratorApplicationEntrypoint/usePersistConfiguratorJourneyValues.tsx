import dayjs from 'dayjs';
import { isEmpty, isEqual, omit } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { usePersistentData, usePersistentDataListener } from '../../../utilities/usePersistData';
import { ApplicantFormValues } from './ApplicantKYCPage/types';
import { ConfiguratorFormValues } from './ModelConfiguratorDetailsPage/shared';

export const CONFIGURATOR_JOURNEY_TEMPORARY_KEY = 'configuratorJourneyTemporaryKey';

export type ConfiguratorJourneyTemporaryValue = {
    token: string;
    sessionId: string;
    expiresAt?: Date | string | null;
    draftCreated: boolean;
    kyc?: ApplicantFormValues;
} & ConfiguratorFormValues;

export const usePersistConfiguratorJourneyValues = () => {
    const { sessionTimeout } = useCompany();
    const { setItem, deleteItem } = usePersistentData();

    const expireAt = useMemo(() => sessionTimeout * 60, [sessionTimeout]);

    const persistedValue = usePersistentDataListener<ConfiguratorJourneyTemporaryValue>(
        CONFIGURATOR_JOURNEY_TEMPORARY_KEY
    );

    const save = useCallback(
        (newValue?: ConfiguratorJourneyTemporaryValue) => {
            const updated: ConfiguratorJourneyTemporaryValue = {
                ...newValue,
                expiresAt: dayjs().add(expireAt, 'seconds').toDate(),
            };

            if (!isEqual(omit('expireAt', persistedValue), omit('expireAt', updated))) {
                setItem(CONFIGURATOR_JOURNEY_TEMPORARY_KEY, updated, expireAt, '');
            }
        },
        [expireAt, persistedValue, setItem]
    );

    const remove = useCallback(() => {
        deleteItem(CONFIGURATOR_JOURNEY_TEMPORARY_KEY);
    }, [deleteItem]);

    /**
     * transform calculator values stored in session storage to be used in calculator form
     * @param temporaryValue StandardJourneyTemporaryValue
     * @returns GenericCalculatorValues
     */
    const transformTemporaryCalculatorValues = useCallback(
        (temporaryValue: ConfiguratorJourneyTemporaryValue): ConfiguratorFormValues['calculator'] => {
            if (isEmpty(temporaryValue?.calculator)) {
                return null;
            }

            const { calculator } = temporaryValue;
            const { dateOfBirth, dateOfRegistration } = calculator;

            return {
                ...calculator,
                dateOfBirth: dateOfBirth ? dayjs(dateOfBirth) : null,
                dateOfRegistration: dateOfRegistration ? dayjs(dateOfRegistration) : null,
            };
        },
        []
    );

    return { save, remove, persistedValue, transformTemporaryCalculatorValues };
};
