/* eslint-disable max-len */
import { ApolloError } from '@apollo/client';
import { FormikHelpers } from 'formik';
import { Dispatch, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ConfiguratorApplicationEntrypointContextDataFragment,
    useListLocalVariantsForCalculatorQuery,
} from '../../../../api';
import { CustomerKind, LocalCustomerFieldKey, Purpose } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useProceedWithCustomerValidators from '../../../../utilities/kycPresets/useProceedWithCustomerValidators';
import useActiveState from '../../../../utilities/useActiveStage';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { getApplicantAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { getInitUploadDocuments } from '../../../shared/JourneyPage/CustomerDetails/shared';
import refineCustomerValues from '../../../shared/refineCustomerValues';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import TimeoutPage from '../../EventApplicationEntrypoint/TimeoutPage';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import { Action, State, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import useProceedWithCustomerDevice from '../../StandardApplicationEntrypoint/KYCPage/useProceedWithCustomerDevice';
import ApplicantForm from '../ApplicantKYCPage';
import { ApplicantFormValues } from '../ApplicantKYCPage/types';
import type { ConfiguratorApplicationState } from '../Journey/shared';

export type EventApplicationProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
    state: State<ConfiguratorApplicationState>;
    dispatch: Dispatch<Action<ConfiguratorApplicationState>>;
};

const GuarantorInner = ({ endpoint, state, dispatch }: EventApplicationProps) => {
    const { t } = useTranslation('eventApplicantForm');
    const { notification } = useThemeComponents();
    const { isActive, reset } = useActiveState();
    const user = useAccount(true);
    const proceedWithCustomerDevices = useProceedWithCustomerDevice();

    const { token, application } = state;
    const { guarantorKYC, guarantorAgreements } = application;
    const kycExtraSettings = useMemo(
        () =>
            endpoint?.configuratorApplicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.configuratorApplicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const applicantAgreements = useMemo(() => getApplicantAgreements(guarantorAgreements), [guarantorAgreements]);
    const agreements = useAgreementsValues(applicantAgreements);

    const applicantKyc = useMemo(() => getApplicantKyc(guarantorKYC), [guarantorKYC]);
    const [prefill, setPrefill] = useState<boolean>(false);

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const { data: variantData, loading: loadingVaraiant } = useListLocalVariantsForCalculatorQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                purpose: Purpose.Production,
                moduleId: endpoint.configuratorApplicationModule.vehicleModuleId,
            },
            bankModuleId: endpoint.configuratorApplicationModule.bankModuleId,
        },
    });
    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );
    const initialValues: ApplicantFormValues = useMemo(() => {
        const { vehicle } = application;

        if (vehicle.__typename !== 'LocalVariant') {
            throw new Error('Invalid Variant');
        }

        const initialFields = application.applicant ? application.applicant.fields : [];

        return {
            agreements,
            customer: guarantorKYC ? { fields: getInitialValues(initialFields, guarantorKYC) } : null,
            configuration: application.configuration,
            vehicleInterest: {
                model: vehicle.model.parentModel ? vehicle.model.parentModelId : vehicle.model.id,
                subModel: vehicle.model.parentModel ? vehicle.model.id : null,
                variant: vehicle.id,
            },
            isCorporateCustomer: false,
            tradeInVehicle: application.tradeInVehicle,
            dealerId: application.dealerId,
            hasGuarantor: false,
            remarks: application?.remarks ?? '',
            prefill: false,
            uploadDocuments: getInitUploadDocuments(application.documents, showUploadDocument, CustomerKind.Guarantor),
        };
    }, [application, agreements, guarantorKYC, showUploadDocument]);

    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const uploadOcrFiles = useUploadOcrFiles();

    const proceedWithCustomerValidator = useProceedWithCustomerValidators(applicantKyc, 'customer.fields');
    const proceedWithCustomerValidations = useMemo(
        () => validators.compose(proceedWithCustomerValidator),
        [proceedWithCustomerValidator]
    );
    const proceedWithCustomerValidate = useValidator(proceedWithCustomerValidations);

    const proceedWithCustomer = useCallback(
        async (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => {
            try {
                const customKYCPresets = {};
                const customKYCFieldKeys = Object.keys(values.customer.fields);

                // extract mandatory fields from KYC preset
                customKYCFieldKeys.forEach((key: LocalCustomerFieldKey) => {
                    if (
                        [
                            LocalCustomerFieldKey.FirstName,
                            LocalCustomerFieldKey.LastName,
                            LocalCustomerFieldKey.FullName,
                            LocalCustomerFieldKey.Email,
                            LocalCustomerFieldKey.IdentityNumber,
                        ].includes(key)
                    ) {
                        customKYCPresets[key] = values.customer.fields[key];
                    }
                });

                const validationResult = proceedWithCustomerValidate(values);

                if (!validationResult) {
                    notification.loading({
                        content: t('customerDetails:messages.sendEmailToCustomer'),
                        duration: 0,
                        key: 'primary',
                    });

                    await proceedWithCustomerDevices(state.token, customKYCPresets);

                    notification.destroy('primary');

                    dispatch({
                        type: 'goTo',
                        stage: JourneyStage.ConfirmEmailSend,
                    });
                } else {
                    // Set errors based on validator, since it's the same format, we can just use it
                    helpers.setErrors(validationResult);

                    // Set touched to true for all error fields
                    // although it should be in boolean instead of string, but formik can handle it
                    // and make sure to set the validate parameter to be false
                    // so it won't conflict with applicant validation
                    helpers.setTouched(validationResult, false);
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                }
            }
        },
        [proceedWithCustomerValidate, notification, t, proceedWithCustomerDevices, state.token, dispatch]
    );

    const onSubmit = useHandleError(
        async (values: ApplicantFormValues) => {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const { applicantFields } = refineCustomerValues(values.customer.fields);

            // we need to update KYC to get Guarantor ID first before submit agreements
            const submitAgreementResult = await submitAgreements(
                state.token,
                values.agreements,
                CustomerKind.Guarantor,
                false
            );

            const [submitCustomerDetailsResult] = await Promise.all([
                submitCustomerDetails({
                    token: submitAgreementResult.token,
                    fields: applicantFields,
                    customerKind: CustomerKind.Guarantor,
                    sameCorrespondenceAddress: values.prefill,
                }),
            ]);

            await uploadOcrFiles(submitCustomerDetailsResult.token);

            notification.destroy('primary');

            if (submitCustomerDetailsResult.__typename === 'GiftVoucherJourneyContext') {
                throw new Error('unsupported journey context');
            }
            const { application: newApplication } = submitCustomerDetailsResult;

            if (newApplication.__typename !== 'ConfiguratorApplication') {
                throw new Error('unexpected type');
            }

            dispatch({
                type: 'next',
                token: submitAgreementResult.token,
                application: newApplication,
            });
        },
        [notification, t, submitAgreements, state.token, submitCustomerDetails, uploadOcrFiles, dispatch]
    );

    const variants = useMemo(() => {
        if (!variantData) {
            return null;
        }

        return variantData.list.items.filter(item => item.__typename === 'LocalVariant');
    }, [variantData]);

    if (!isActive && !user) {
        return <TimeoutPage onBack={reset} />;
    }

    if (loadingVaraiant) {
        return <PortalLoadingElement />;
    }

    return (
        <ApplicantForm
            applicationAgreements={applicantAgreements}
            configurator={application}
            customerKind={CustomerKind.Guarantor}
            deposit={application.deposit}
            dispatch={dispatch}
            endpoint={endpoint}
            hasGuarantorPreset={false}
            initialValues={initialValues}
            kycExtraSettings={kycExtraSettings}
            kycPresets={applicantKyc}
            onSubmit={onSubmit}
            prefill={prefill}
            proceedWithCustomer={proceedWithCustomer}
            removeDocument={removeDocument}
            setPrefill={setPrefill}
            showTabs={false}
            state={state}
            uploadDocument={uploadDocument}
            variants={variants}
        />
    );
};

const GuarantorKYC = (props: EventApplicationProps) => (
    <OcrFilesManager>
        <GuarantorInner {...props} />
    </OcrFilesManager>
);

export default GuarantorKYC;
