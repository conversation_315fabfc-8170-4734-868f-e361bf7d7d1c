import { Route, Routes } from 'react-router-dom';
// eslint-disable-next-line max-len
import { ConfiguratorApplicationEntrypointContextDataFragment } from '../../../api/fragments/ConfiguratorApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import NotFoundPage from '../NotFoundPage';
import CarListingPage from './CarListingPage';
import { EndpointProvider } from './ConfiguratorEndpointContext';
import Journey from './Journey';
import ModelConfiguratorDetailsPage from './ModelConfiguratorDetailsPage';
import PublicAccessEntrypoint from './PublicAccessEntrypoint';
import ThankYouPage from './ThankYou';

export type ConfiguratorApplicationEntrypointProps = {
    endpoint: ConfiguratorApplicationEntrypointContextDataFragment;
};

const ConfiguratorApplicationEntrypoint = ({ endpoint }: ConfiguratorApplicationEntrypointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <EndpointProvider endpoint={endpoint}>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key="list" element={<CarListingPage endpoint={endpoint} />} path="" />
                <Route
                    key="configuratorDetails"
                    element={<ModelConfiguratorDetailsPage endpoint={endpoint} />}
                    path="details/:urlIdentifier"
                />
                <Route key="apply" element={<Journey endpoint={endpoint} />} path="apply" />
                <Route key="thankyou" element={<ThankYouPage />} path="thankyou" />
                <Route key="404" element={<NotFoundPage />} path="*" />
                <Route key="public" element={<PublicAccessEntrypoint endpoint={endpoint} />} path="remote/*" />
            </Routes>
        </EndpointProvider>
    );
};

export default ConfiguratorApplicationEntrypoint;
