import { useMemo } from 'react';
import { useRouter } from '../../../../components/contexts/shared';
// eslint-disable-next-line max-len
import { eventPortalPath } from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import { CustomerJourneyStepProps } from '../../../shared/CustomerDetailsPage/CustomerJourneyStep';
import JourneySteps from '../../../shared/CustomerDetailsPage/JourneySteps';
import { getApplicationReferenceLink, getLeadReferenceLink } from '../CustomerListPage/CustomerListCI';

const CustomerJourneyStep = ({ application }: CustomerJourneyStepProps) => {
    const router = useRouter(true);

    const eventDetailEndpoint = useMemo(() => {
        const prefix = router.endpoints.find(
            item =>
                item.__typename === 'EventApplicationEntrypoint' &&
                application.__typename === 'EventApplication' &&
                item.eventApplicationModule?.id === application.moduleId
        )?.pathname;

        return eventPortalPath(prefix);
    }, [router.endpoints, application]);

    return (
        <JourneySteps
            application={application}
            getApplicationReferenceLinkCI={getApplicationReferenceLink}
            getEventDetail={eventDetailEndpoint}
            getLeadReferenceLinkCI={getLeadReferenceLink}
        />
    );
};

export default CustomerJourneyStep;
