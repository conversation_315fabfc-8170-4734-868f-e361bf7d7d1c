import { Route, Routes } from 'react-router-dom';
import { CustomerListEndpointContextDataFragment } from '../../../api/fragments/CustomerListEndpointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import withPrivateAccess from '../../../utilities/withPrivateAccess';
import MetaData from '../../shared/MetaData';
import NotFoundPage from '../NotFoundPage';
import CustomerDetailsPage from './CustomerDetailsPage';
import CustomerListPage from './CustomerListPage';

export type CustomerListEndpointProps = {
    endpoint: CustomerListEndpointContextDataFragment;
};

const CustomerListEndpoint = ({ endpoint }: CustomerListEndpointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.title}`} />
            <Routes>
                <Route element={<CustomerListPage endpoint={endpoint} />} index />
                <Route element={<CustomerDetailsPage />} path=":id" />
                <Route key="404" element={<NotFoundPage />} path="*" />
            </Routes>
        </>
    );
};

export default withPrivateAccess(CustomerListEndpoint);
