import { useApolloClient } from '@apollo/client';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../api/queries/getApplicationJourney';
import { ApplicationSigningPurpose, ApplicationSigningStatus } from '../../../../api/types';
import type { State } from '../Journey/shared';

const maxRetries = 80;
const retryGapInMilliseconds = 1500;

const useSigningUrl = <TApplicationState,>(state: State<TApplicationState>, purpose: ApplicationSigningPurpose) => {
    const { token } = state;
    const { t } = useTranslation();
    const apolloClient = useApolloClient();
    const location = useLocation();

    // initial path where the request is initiated
    const path = location.pathname;

    useEffect(() => {
        let tries = 0;

        let timeoutId: NodeJS.Timeout;

        const handler = async () => {
            if (tries > maxRetries) {
                clearInterval(timeoutId);
                throw new Error('fail to create signing');
            }

            const response = await apolloClient.query<GetApplicationJourneyQuery, GetApplicationJourneyQueryVariables>({
                query: GetApplicationJourneyDocument,
                variables: { token },
                fetchPolicy: 'network-only',
            });

            // prevent starting namirial session if path different
            if (path !== location.pathname) {
                return;
            }

            const { application } = response.data.result;

            switch (purpose) {
                case ApplicationSigningPurpose.TestDrive: {
                    if (
                        (application.__typename === 'StandardApplication' ||
                            application.__typename === 'ConfiguratorApplication' ||
                            application.__typename === 'EventApplication' ||
                            application.__typename === 'FinderApplication' ||
                            application.__typename === 'LaunchpadApplication') &&
                        application.testDriveSigning.__typename === 'ApplicationNamirialSigning' &&
                        application.testDriveSigning?.envelopeId &&
                        application.testDriveSigning?.redirectionUrl
                    ) {
                        if (
                            application.draftFlow.isTestDriveProcessStarted &&
                            application.testDriveSigning?.__typename === 'ApplicationNamirialSigning' &&
                            !!application.testDriveSigning?.envelopeId &&
                            !!application.testDriveSigning?.redirectionUrl
                        ) {
                            window.location.replace(application.testDriveSigning.redirectionUrl);
                        }
                    } else {
                        tries += 1;
                        timeoutId = setTimeout(handler, retryGapInMilliseconds);
                    }

                    break;
                }

                case ApplicationSigningPurpose.Finance: {
                    if (
                        (application.__typename === 'StandardApplication' ||
                            application.__typename === 'ConfiguratorApplication' ||
                            application.__typename === 'EventApplication' ||
                            application.__typename === 'FinderApplication') &&
                        application.signing.__typename === 'ApplicationNamirialSigning' &&
                        application.signing?.envelopeId &&
                        application.signing?.redirectionUrl
                    ) {
                        if (
                            application.signing?.status === ApplicationSigningStatus.Completed &&
                            application.guarantorSigning?.__typename === 'ApplicationNamirialSigning' &&
                            !!application.guarantorSigning?.envelopeId &&
                            !!application.guarantorSigning?.redirectionUrl
                        ) {
                            window.location.replace(application.guarantorSigning.redirectionUrl);
                        } else {
                            window.location.replace(application.signing.redirectionUrl);
                        }
                    } else {
                        tries += 1;
                        timeoutId = setTimeout(handler, retryGapInMilliseconds);
                    }
                    break;
                }

                case ApplicationSigningPurpose.Insurance: {
                    if (
                        (application.__typename === 'StandardApplication' ||
                            application.__typename === 'ConfiguratorApplication' ||
                            application.__typename === 'FinderApplication') &&
                        application.insuranceSigning.__typename === 'ApplicationNamirialSigning' &&
                        application.insuranceSigning?.envelopeId &&
                        application.insuranceSigning?.redirectionUrl
                    ) {
                        window.location.replace(application.insuranceSigning.redirectionUrl);
                    } else {
                        tries += 1;
                        timeoutId = setTimeout(handler, retryGapInMilliseconds);
                    }
                    break;
                }

                case ApplicationSigningPurpose.Mobility: {
                    if (
                        application.__typename === 'MobilityApplication' &&
                        application.signing.__typename === 'ApplicationNamirialSigning' &&
                        application.signing?.envelopeId &&
                        application.signing?.redirectionUrl
                    ) {
                        window.location.replace(application.signing.redirectionUrl);
                    } else {
                        tries += 1;
                        timeoutId = setTimeout(handler, retryGapInMilliseconds);
                    }
                    break;
                }
            }
        };

        timeoutId = setTimeout(handler, retryGapInMilliseconds);

        return () => {
            clearTimeout(timeoutId);
        };
    }, [location.pathname, path, token, t, apolloClient, purpose]);
};

export default useSigningUrl;
