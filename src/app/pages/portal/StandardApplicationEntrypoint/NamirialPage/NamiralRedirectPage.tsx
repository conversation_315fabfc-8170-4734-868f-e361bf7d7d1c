import { useApolloClient } from '@apollo/client';
import { Dispatch, useCallback, useEffect, useRef } from 'react';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../api/queries/getApplicationJourney';
import { ApplicationSigningPurpose, ApplicationSigningStatus } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { AllowedApplicationForSigning, ensureApplicationForSigning } from '../../../../utilities/journeys/signing';
import { State, Action, JourneyStage } from '../Journey/shared';

export type NamirialWaitingProps = {
    state: State<AllowedApplicationForSigning>;
    dispatch: Dispatch<Action<AllowedApplicationForSigning>>;
    purpose: ApplicationSigningPurpose;
};

const maxRetries = 10;
const timeoutInterval = 2000;

const checkSigningStatus = (application, purpose) => {
    switch (purpose) {
        case ApplicationSigningPurpose.TestDrive:
            return application.__typename !== 'MobilityApplication' &&
                application.testDriveSigning?.__typename === 'ApplicationNamirialSigning' &&
                (application.testDriveSigning?.status === ApplicationSigningStatus.Rejected ||
                    application.testDriveSigning?.status === ApplicationSigningStatus.Completed)
                ? application.testDriveSigning?.status
                : null;

        case ApplicationSigningPurpose.Finance:
            if (
                application.__typename !== 'MobilityApplication' &&
                application.signing?.__typename === 'ApplicationNamirialSigning' &&
                application.signing?.status === ApplicationSigningStatus.Completed &&
                application.guarantorSigning?.__typename === 'ApplicationNamirialSigning' &&
                (application.guarantorSigning?.status === ApplicationSigningStatus.Rejected ||
                    application.guarantorSigning?.status === ApplicationSigningStatus.Completed)
            ) {
                return application.guarantorSigning?.status;
            }

            return application.signing?.__typename === 'ApplicationNamirialSigning' &&
                (application.signing?.status === ApplicationSigningStatus.Rejected ||
                    application.signing?.status === ApplicationSigningStatus.Completed)
                ? application.signing?.status
                : null;

        case ApplicationSigningPurpose.Insurance:
            return application.__typename !== 'EventApplication' &&
                application.__typename !== 'MobilityApplication' &&
                application.insuranceSigning?.__typename === 'ApplicationNamirialSigning' &&
                (application.insuranceSigning?.status === ApplicationSigningStatus.Rejected ||
                    application.insuranceSigning?.status === ApplicationSigningStatus.Completed)
                ? application.insuranceSigning?.status
                : null;

        case ApplicationSigningPurpose.Mobility:
            return application.signing?.__typename === 'ApplicationNamirialSigning' &&
                (application.signing?.status === ApplicationSigningStatus.Rejected ||
                    application.signing?.status === ApplicationSigningStatus.Completed)
                ? application.signing?.status
                : null;

        default:
            return null;
    }
};

const NamirialRedirectPage = ({ state, dispatch, purpose }: NamirialWaitingProps) => {
    const apolloClient = useApolloClient();
    const { token } = state;
    const retriesRef = useRef(1);

    const callback = useCallback(async () => {
        const pollingEnvelopeStatus = async () => {
            while (retriesRef.current <= maxRetries) {
                try {
                    // we want to wait for the response and will exit once it is ok
                    // eslint-disable-next-line no-await-in-loop
                    const response = await apolloClient.query<
                        GetApplicationJourneyQuery,
                        GetApplicationJourneyQueryVariables
                    >({
                        query: GetApplicationJourneyDocument,
                        variables: { token },
                        fetchPolicy: 'network-only',
                    });

                    const { application } = response.data.result;
                    const ensuredApplication = ensureApplicationForSigning(application);
                    const signingStatus = checkSigningStatus(ensuredApplication, purpose);

                    if (signingStatus) {
                        return { signingStatus, result: response.data.result };
                    }

                    retriesRef.current++;
                    // 2 seconds delay timer so we don't spam the server
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => {
                        setTimeout(resolve, timeoutInterval);
                    });
                } catch (error) {
                    return { signingStatus: ApplicationSigningStatus.Error, result: null };
                }
            }

            return { signingStatus: ApplicationSigningStatus.Timeout, result: null };
        };

        const response = (await pollingEnvelopeStatus()) as {
            signingStatus: ApplicationSigningStatus;
            result: GetApplicationJourneyQuery['result'];
        };

        if (!response) {
            return;
        }

        const { signingStatus, result } = response;

        if (signingStatus === ApplicationSigningStatus.Completed) {
            const ensuredApplicationForSigning = ensureApplicationForSigning(result.application);
            dispatch({ type: 'next', token: result.token, application: ensuredApplicationForSigning });
        } else if (signingStatus === ApplicationSigningStatus.Rejected) {
            dispatch({ type: 'goTo', stage: JourneyStage.NamirialReject });
        } else if (signingStatus === ApplicationSigningStatus.Error) {
            dispatch({ type: 'goTo', stage: JourneyStage.Unknown });
        } else {
            dispatch({ type: 'goTo', stage: JourneyStage.NamirialTimeout });
        }
    }, [apolloClient, dispatch, purpose, token]);

    useEffect(() => {
        callback();
    }, [callback]);

    return <PortalLoadingElement />;
};

export default NamirialRedirectPage;
