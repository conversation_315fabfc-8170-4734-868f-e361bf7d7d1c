import { ApplicationSigningPurpose } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import type { State } from '../Journey/shared';
import useSigningUrl from './useSigningUrl';

export type NamirialProps<TApplicationState> = {
    state: State<TApplicationState>;
    purpose: ApplicationSigningPurpose;
};

const NamirialPage = <TApplicationState,>({ state, purpose }: NamirialProps<TApplicationState>) => {
    useSigningUrl(state, purpose);

    return <PortalLoadingElement />;
};

export default NamirialPage;
