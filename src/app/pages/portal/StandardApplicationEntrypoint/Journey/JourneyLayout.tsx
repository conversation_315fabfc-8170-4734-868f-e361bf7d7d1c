import React, { Dispatch, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import JourneySteps from '../../../shared/JourneyPage/JourneySteps';
import { JourneyStepsProvider } from '../../../shared/JourneyPage/JourneyStepsContext';
import useStandardLayout from '../useStandardLayout';
import type { State, Action, StandardApplicationState } from './shared';

type JourneyLayoutProps = PropsWithChildren & {
    state: State<StandardApplicationState>;
    dispatch: Dispatch<Action<StandardApplicationState>>;
};

const JourneyLayout = ({ state, dispatch, children }: JourneyLayoutProps) => {
    const { t } = useTranslation(['customerDetails']);
    const { StandardLayout, BackButton } = useThemeComponents();
    const { title, onBack, shouldShowBackIcon, useJourneyLayout } = useStandardLayout(dispatch, state);

    if (!useJourneyLayout) {
        // eslint-disable-next-line react/jsx-no-useless-fragment
        return <>{children}</>;
    }

    return (
        <StandardLayout
            backIcon={
                shouldShowBackIcon ? <BackButton type="link">{t('customerDetails:backButton')}</BackButton> : null
            }
            onBack={onBack}
            title={title}
            hasFooterBar
        >
            <BasicProLayoutContainer>
                <JourneyStepsProvider
                    value={{
                        onBack,
                        shouldAllowBack: shouldShowBackIcon,
                    }}
                >
                    <JourneySteps currentStage={state.stage} stages={state?.stages} />
                    {children}
                </JourneyStepsProvider>
            </BasicProLayoutContainer>
        </StandardLayout>
    );
};

export default JourneyLayout;
