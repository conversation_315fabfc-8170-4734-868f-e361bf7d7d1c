import { useEffect, useMemo, useReducer, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { DebugJourneyDataFragment } from '../../../../api/fragments/DebugJourneyData';
import { LeadDataFragment } from '../../../../api/fragments/LeadData';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import { ApplicationSigningPurpose } from '../../../../api/types';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useThemeComponents } from '../../../../themes/hooks';
import useApplicationConcurrencyRedirect from '../../../../utilities/useApplicationConcurrencyRedirect';
import RedirectAndReplace from '../../../shared/RedirectAndReplace';
import AdyenPaymentPage from '../AdyenPaymentPage';
import ConfirmationPage from '../ConfirmationPage';
import FiservPaymentPage from '../FiservPaymentPage';
import GuarantorKYCPage from '../GuarantorKYCPage';
import KYCPage from '../KYCPage';
import { NamiralRedirectPage, NamirialPage, NamirialRejectPage, NamirialTimeoutPage } from '../NamirialPage';
import OTPPage from '../OTPPage';
import PayGatePaymentPage from '../PayGatePaymentPage';
import PorschePaymentPage from '../PorschePaymentPage';
import { useStandardApplicationContext } from '../StandardApplicationContext';
import TtbPaymentPage from '../TtbPaymentPage';
import { mayProceedToThanksYou } from '../shared/util';
import InitializeStage from './InitializeStage';
import JourneyLayout from './JourneyLayout';
import reducer from './reducer';
import { JourneyStage, type StandardApplicationState } from './shared';

export type JourneyControllerProps = {
    initialToken: string;
    initialApplication: StandardApplicationState;
    initialLead: LeadDataFragment;
    initialStage: JourneyStage;
    endpoint: StandardApplicationEntrypointContextDataFragment;
    journeyStages: JourneyStage[];
};

const JourneyController = ({
    initialToken,
    initialApplication,
    initialLead,
    initialStage,
    endpoint,
    journeyStages,
}: JourneyControllerProps) => {
    const initialState = useRef({
        token: initialToken,
        stage: initialStage,
        application: initialApplication,
        lead: initialLead,
        stages: journeyStages,
    });
    const navigate = useNavigate();
    const { setLaunchpadLead } = useStandardApplicationContext();
    const [state, dispatch] = useReducer(reducer, initialState.current);
    const locationState = useLocation().state as { token?: string; myInfoAuthorizationCode?: string; linkId?: string };
    const { myInfoAuthorizationCode, linkId } = locationState;

    useApplicationConcurrencyRedirect(initialToken);

    const { StandardLayout } = useThemeComponents();

    useEffect(() => {
        // ensure we locally persist the latest token as well
        navigate({ pathname: '.' }, { state: { myInfoAuthorizationCode, linkId, token: state.token }, replace: true });
    }, [state.token, myInfoAuthorizationCode, linkId, navigate]);

    useEffect(() => {
        if (state?.application?.lead?.__typename === 'LaunchpadLead') {
            setLaunchpadLead(state.application.lead);
        }
    }, [setLaunchpadLead, state?.application]);

    const content = useMemo(() => {
        switch (state.stage) {
            case JourneyStage.Initialize:
                return <InitializeStage dispatch={dispatch} state={state} />;

            case JourneyStage.ApplicantKYC:
                return <KYCPage dispatch={dispatch} endpoint={endpoint} state={state} />;

            case JourneyStage.GuarantorKYC:
                return <GuarantorKYCPage dispatch={dispatch} endpoint={endpoint} state={state} />;

            case JourneyStage.Deposit: {
                switch (state.application.deposit?.__typename) {
                    case 'ApplicationAdyenDeposit':
                        return (
                            <AdyenPaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationPorscheDeposit':
                        return (
                            <PorschePaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationFiservDeposit':
                        return (
                            <FiservPaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationPayGateDeposit':
                        return (
                            <PayGatePaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationTtbDeposit':
                        return (
                            <TtbPaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    default:
                        return <NotFoundResult />;
                }
            }

            case JourneyStage.Otp:
                return <OTPPage dispatch={dispatch} purpose={ApplicationSigningPurpose.Finance} state={state} />;

            case JourneyStage.InsuranceOtp:
                return (
                    <OTPPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Insurance}
                        shouldIncludeLayout={false}
                        state={state}
                    />
                );

            case JourneyStage.Namirial:
            case JourneyStage.GuarantorNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.Finance} state={state} />;

            case JourneyStage.NamirialRedirect:
            case JourneyStage.GuarantorNamirialRedirect:
                return (
                    <NamiralRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Finance}
                        state={state}
                    />
                );

            case JourneyStage.InsuranceNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.Insurance} state={state} />;

            case JourneyStage.InsuranceNamirialRedirect:
                return (
                    <NamiralRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Insurance}
                        state={state}
                    />
                );

            case JourneyStage.NamirialReject:
            case JourneyStage.GuarantorNamirialReject:
                return <NamirialRejectPage />;

            case JourneyStage.NamirialTimeout:
            case JourneyStage.GuarantorNamirialTimeout:
                return <NamirialTimeoutPage />;

            case JourneyStage.ConfirmEmailSend:
                return <ConfirmationPage />;

            case JourneyStage.Unknown:
            default:
                return <NotFoundResult />;
        }
    }, [StandardLayout, endpoint, state]);

    const canProceedToThankYou = mayProceedToThanksYou(state.application);

    if (canProceedToThankYou) {
        return <RedirectAndReplace path={{ pathname: '../thankyou' }} state={state} />;
    }

    return (
        <JourneyLayout dispatch={dispatch} state={state}>
            {content}
        </JourneyLayout>
    );
};

export default JourneyController;
