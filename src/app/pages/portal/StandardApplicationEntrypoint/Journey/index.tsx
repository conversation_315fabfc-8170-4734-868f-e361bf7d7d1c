import { useLocation } from 'react-router-dom';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import { Maybe } from '../../../../api/types';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import JourneyBootstrap from './JourneyBootstrap';
import { JourneyStage } from './shared';

export type JourneyLocationState = {
    token: string;
    stage?: JourneyStage;
};

export type JourneyProps = {
    endpoint: StandardApplicationEntrypointContextDataFragment;
};

const Journey = ({ endpoint }: JourneyProps) => {
    const state = useLocation().state as Maybe<JourneyLocationState>;

    if (!state) {
        return <NotFoundResult />;
    }

    const { token, stage = JourneyStage.Initialize } = state;

    if (!token) {
        // that shouldn't happen as well
        return <NotFoundResult />;
    }

    return <JourneyBootstrap endpoint={endpoint} initialStage={stage} initialToken={token} />;
};

export default Journey;
