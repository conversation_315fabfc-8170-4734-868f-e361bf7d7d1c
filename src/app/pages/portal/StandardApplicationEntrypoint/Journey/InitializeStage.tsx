import { Dispatch, useEffect } from 'react';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import type { StandardApplicationState } from './shared';
import { Action, JourneyStage, State } from './shared';

export type InitializeStageProps = {
    state: State<StandardApplicationState>;
    dispatch: Dispatch<Action<StandardApplicationState>>;
};

export const getStageFromApplication = (
    application: State<StandardApplicationState>['application'],
    initialized = true
) => {
    const { draftFlow, deposit, signing, module, insuranceSigning, guarantorSigning, guarantor } = application;

    if (module.__typename !== 'StandardApplicationModule') {
        throw new Error('ModuleType not supported');
    }

    if (!initialized && application.withCustomerDevice) {
        // todo render calculator
    }

    if (!draftFlow.isApplicantKYCCompleted) {
        return JourneyStage.ApplicantKYC;
    }

    if (draftFlow.hasGuarantor && !draftFlow.isGuarantorCompleted) {
        return JourneyStage.GuarantorKYC;
    }

    if (deposit && !draftFlow.isDepositCompleted && !draftFlow.isDepositSkipped) {
        return JourneyStage.Deposit;
    }

    if (signing && !draftFlow.isSigningCompleted) {
        // this will only have value if there's signing
        switch (signing.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.Namirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.Otp;

            default:
                return JourneyStage.Unknown;
        }
    }

    if (guarantor && guarantorSigning && !draftFlow.isGuarantorSigningCompleted) {
        switch (guarantorSigning.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.GuarantorNamirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.Otp;

            default:
                return JourneyStage.Unknown;
        }
    }

    if (insuranceSigning && !draftFlow.isInsuranceApplicantSigningCompleted) {
        switch (insuranceSigning.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.InsuranceNamirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.InsuranceOtp;

            default:
                return JourneyStage.Unknown;
        }
    }

    return JourneyStage.Unknown;
};

const InitializeStage = ({ state, dispatch }: InitializeStageProps) => {
    const { application } = state;

    useEffect(() => {
        dispatch({ type: 'goTo', stage: getStageFromApplication(application, false) });
    }, [application, dispatch]);

    return <PortalLoadingElement />;
};

export default InitializeStage;
