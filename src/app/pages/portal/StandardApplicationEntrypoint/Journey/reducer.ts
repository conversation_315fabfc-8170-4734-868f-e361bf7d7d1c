import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import { getStageFromApplication } from './InitializeStage';
import type { Action, State, StandardApplicationState } from './shared';

const reducer = <TApplicationState extends StandardApplicationState>(
    state: State<TApplicationState>,
    action: Action<TApplicationState>
): State<TApplicationState> => {
    switch (action.type) {
        case 'refresh':
            return { ...state, token: action.token, application: action.application };

        case 'next':
            return {
                ...state,
                stage: getStageFromApplication(action.application),
                token: action.token,
                application: action.application,
                stages: getApplicationJourneyStages(action.application),
            };

        case 'goTo':
            return { ...state, stage: action.stage };

        default:
            return state;
    }
};

export default reducer;
