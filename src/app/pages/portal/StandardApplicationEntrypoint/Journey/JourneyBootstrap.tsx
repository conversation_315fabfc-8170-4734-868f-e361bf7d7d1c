/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { useEffect, useState } from 'react';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../api/queries/getApplicationJourney';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useThemeComponents } from '../../../../themes/hooks';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import { useParseJWTPayload } from '../../../../utilities/parseJWTPayload';
import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import JourneyController from './JourneyController';
import { JourneyStage } from './shared';

export type JourneyBootstrapProps = {
    endpoint: StandardApplicationEntrypointContextDataFragment;
    initialToken: string;
    initialStage: JourneyStage;
};

type TokenPayload = {
    applicationModuleId: { $oid: string };
    origin: 'draft' | 'remote-applicant' | 'remote-guarantor';
};

const JourneyBootstrap = ({ initialToken, initialStage, endpoint }: JourneyBootstrapProps) => {
    const apolloClient = useApolloClient();

    const { applicationModuleId } = useParseJWTPayload<TokenPayload>(initialToken);
    const [state, setInitialState] = useState<GetApplicationJourneyQuery['result'] | null>(null);
    const [error, setError] = useState<Error | null>(null);

    const { notification } = useThemeComponents();

    const isInitialized = !!state;
    const isValid = applicationModuleId.$oid === endpoint.applicationModule.id;

    useEffect(() => {
        if (isInitialized) {
            // already initialized, we skip it
            return;
        }

        apolloClient
            .query<GetApplicationJourneyQuery, GetApplicationJourneyQueryVariables>({
                query: GetApplicationJourneyDocument,
                fetchPolicy: 'no-cache',
                variables: { token: initialToken, refreshToken: true },
            })
            .then(result => setInitialState(result.data.result))
            .catch(error => {
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors !== null) {
                    const { $root: rootError } = apolloErrors;

                    if (rootError) {
                        notification.error(rootError);
                    }
                } else {
                    console.error(error);
                }

                setError(error);
            });
    }, [isInitialized, isValid, apolloClient, initialToken, notification]);

    if (!isValid || !!error) {
        // things are not as expected
        return <NotFoundResult />;
    }

    if (!state) {
        return <PortalLoadingElement />;
    }

    const { application, token, lead } = state;

    if (application.__typename !== 'StandardApplication') {
        // things are not as expected (invalid app type)
        return <NotFoundResult />;
    }

    const stages = getApplicationJourneyStages(application);

    return (
        <JourneyController
            endpoint={endpoint}
            initialApplication={application}
            initialLead={lead}
            initialStage={initialStage}
            initialToken={token}
            journeyStages={stages}
        />
    );
};

export default JourneyBootstrap;
