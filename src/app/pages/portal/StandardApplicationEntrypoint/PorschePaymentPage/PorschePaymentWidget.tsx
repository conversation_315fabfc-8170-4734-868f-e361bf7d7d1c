import { useFormikContext } from 'formik';
import { pick } from 'lodash/fp';
import { useMemo, useEffect, useRef, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { ApplicationPorscheDepositDataFragment } from '../../../../api/fragments/ApplicationPorscheDepositData';
import { LayoutType } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../components/contexts/shared';

export enum TransactionStatus {
    AUTHORISED = 'AUTHORISED',
    CAPTURED = 'CAPTURED',
    CAPTURE_REQUEST_SENT = 'CAPTURE_REQUEST_SENT',
    PREPARED = 'PREPARED',
    FAILED = 'FAILED',
    ERROR = 'ERROR',
    CANCELLED = 'CANCELLED',
    REFUNDED = 'REFUNDED',
}

type PorschePaymentWidgetProps = {
    deposit: ApplicationPorscheDepositDataFragment;
    hasSinglePaymentMethod: boolean;
};

type PaymentMethod = {
    typeDisplayName: string;
    encryptedCardNumber: string;
    encryptedExpiryMonth: string;
    encryptedExpiryYear: string;
    encryptedSecurityCode: string;
};

type StrongCustomerAuthenticationData = {
    browserInfo: {
        acceptHeader: string;
        colorDepth: number;
        javaEnabled: boolean;
        language: string;
        screenHeight: number;
        screenWidth: number;
        timeZoneOffset: number;
        userAgent: string;
    };
};

export type PaymentData = {
    paymentMethod: PaymentMethod;
    strongCustomerAuthenticationData: StrongCustomerAuthenticationData;
};

const defaultLanguageCode = 'en';

const PaymentWidgetContainer = styled.div<{ hasSinglePaymentMethod: boolean }>`
    guest-payment-widget .payment-widget.padding {
        padding: 0;
        margin-top: 16px;
    }

    ${props =>
        props.hasSinglePaymentMethod &&
        css`
            .payment-list {
                slpayment-p-radio-button-wrapper {
                    display: none;
                }

                .new-credit-card-wrapper {
                    margin-left: 0;
                }
            }
        `}

    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        css`
            margin-left: -32px;
            margin-right: -32px;
        `}
`;

const PorschePaymentWidget = ({ deposit, hasSinglePaymentMethod }: PorschePaymentWidgetProps) => {
    // get language pack
    const { i18n } = useTranslation();

    const company = useCompany();

    const router = useRouter(false);

    const widget = useRef<HTMLElement>();

    const { setFieldValue } = useFormikContext();

    const languageCode = useMemo(() => {
        if (!router) {
            return defaultLanguageCode;
        }

        return router.languages.find(({ id }) => id === i18n.language)?.code || defaultLanguageCode;
    }, [i18n.language, router]);

    const assortments = useMemo(
        () =>
            JSON.stringify([
                {
                    assortment: deposit.assortment,
                    amount: deposit.minorUnitsAmount,
                },
            ]),
        [deposit]
    );

    const paymentMethodSelectedHandler = useCallback(
        event => {
            // publish only when detail has value
            // detail will only a value when all fields are satisfied
            if (event.detail?.paymentMethodDetails) {
                setFieldValue(
                    'paymentData',
                    pick(['paymentMethod', 'strongCustomerAuthenticationData'], event.detail.paymentMethodDetails)
                );
            }
        },
        [setFieldValue]
    );

    useEffect(() => {
        let widgetRefValue: HTMLElement | null = null;

        if (widget.current) {
            widget.current.addEventListener('paymentMethodSelectedHandler', paymentMethodSelectedHandler);

            widgetRefValue = widget.current;
        }

        return () => {
            widgetRefValue &&
                widgetRefValue.removeEventListener('paymentMethodSelectedHandler', paymentMethodSelectedHandler);
        };
    }, [paymentMethodSelectedHandler]);

    return (
        <>
            <PaymentWidgetContainer hasSinglePaymentMethod={hasSinglePaymentMethod}>
                <guest-payment-widget
                    ref={widget}
                    apikey={deposit.apiKey}
                    assortmentswithamounts={assortments}
                    countrycode={company.countryCode}
                    environment={deposit.environment}
                    locale={languageCode}
                    redirecturl={deposit.redirectUrl}
                />
            </PaymentWidgetContainer>

            <Helmet>
                <script src={deposit.widgetUrl} />
            </Helmet>
        </>
    );
};

export default PorschePaymentWidget;
