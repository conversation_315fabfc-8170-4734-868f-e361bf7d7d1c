import Icon from '@ant-design/icons';
import { Typography, Form, Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { ComponentType, Dispatch, PropsWithChildren, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetPorschePaymentMethodsQuery } from '../../../../api/queries/getPorschePaymentMethods';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../components/ScrollToTop';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { DefaultLayoutProps } from '../../../../themes/default/Standard/Layout';
import { useThemeComponents } from '../../../../themes/hooks';
import { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import AgreementField from '../../../shared/CIPage/ConsentAndDeclarations/AgreementField';
import { getPaymentAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import BookingDeposit from '../../EventApplicationEntrypoint/ThankYou/BookingDeposit';
import LocationInfo from '../../MobilityApplicationEntrypoint/Components/LocationInfo';
import OrderSummary from '../../MobilityApplicationEntrypoint/Components/OrderSummary';
import Stepper, {
    MobilityPageKind,
    AvailableSteps as MobilitySteps,
} from '../../MobilityApplicationEntrypoint/Components/Stepper';
import { calculateTotalPrice } from '../../MobilityApplicationEntrypoint/helper';
import { getMandatory } from '../AdyenPaymentPage/Inner';
import { SkipForDepositButtonProps } from '../AdyenPaymentPage/SkipForDepositButton';
import { type Action, type State, JourneyStage } from '../Journey/shared';
import JourneyToolbar from '../shared/JourneyToolbar';
import MobilityPromoCode from '../shared/MobilityPromoCode';
import NextButton from '../shared/NextButton';
import VehicleWithDealer from '../shared/VehicleWithDealer';
import useMobilityPaymentHooks from '../shared/useMobilityPaymentHooks';
import usePaymentSectionColOffset from '../shared/usePaymentSectionColOffset';
import usePaymentSectionColSpans from '../shared/usePaymentSectionColSpans';
import PaymentSuccess from './PaymentSuccess';
import PorschePaymentWidget from './PorschePaymentWidget';
import BackIcon from '../../../../icons/mobility/back.svg';

export type PorschePaymentInnerProps = {
    state: State<AllowedApplicationForPayment>;
    dispatch: Dispatch<Action<AllowedApplicationForPayment>>;
    CustomLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    SkipDepositButton: (props: Omit<SkipForDepositButtonProps, 'setSkipDeposit'>) => JSX.Element;
    canSkipDeposit?: boolean;
    isSuccessful: boolean;
    promoCodeModuleId?: string;
    giftVoucherModuleId?: string;
    applicationModuleId?: string;
    shouldIncludeLayout?: boolean;
};

export type PorschePaymentFormValues = AgreementValues & { promoCodeId?: string };

const Inner = ({
    state,
    dispatch,
    CustomLayout,
    SkipDepositButton,
    isSuccessful,
    canSkipDeposit = false,
    promoCodeModuleId,
    applicationModuleId,
    giftVoucherModuleId,
    shouldIncludeLayout = true,
}: PorschePaymentInnerProps) => {
    const { t } = useTranslation(['paymentDetails']);
    const { application, token } = state;
    const { deposit } = application;

    const { BackButton } = useThemeComponents();

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.application.applicantAgreements),
        [state.application.applicantAgreements]
    );

    const { values, isSubmitting, handleSubmit, validateForm, submitForm, setFieldValue } =
        useFormikContext<PorschePaymentFormValues>();

    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const onSkipDeposit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [validateForm, submitForm]);

    const { Button } = useThemeComponents();

    const { data, loading } = useGetPorschePaymentMethodsQuery({ variables: { token: state.token } });

    const hasSinglePaymentMethod = data?.result.length === 1;

    const { onDiscountCodeChange } = useMobilityPaymentHooks(token, setFieldValue, dispatch);

    const showDealerInfo = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            return application.event.showDealership && !!application.dealer;
        }

        if (application.__typename === 'MobilityApplication') {
            return false;
        }

        return !!application.dealer;
    }, [application]);

    const paymentSectionColOffset = usePaymentSectionColOffset({
        application,
        paymentAgreements,
    });

    const [halfColSpan, colSpan, rightColSpan] = usePaymentSectionColSpans();

    if (deposit.__typename !== 'ApplicationPorscheDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    const content = (
        <>
            <FormAutoTouch />
            <Form
                id="completePorschePaymentForm"
                layout="vertical"
                name="completePorschePaymentForm"
                onSubmitCapture={handleSubmit}
            >
                <ScrollToTop />
                {application.__typename === 'MobilityApplication' && (
                    <Stepper
                        currentPage={MobilityPageKind.Mobility}
                        currentStep={MobilitySteps.Payment}
                        dispatchMobility={dispatch}
                    />
                )}
                <Row
                    gutter={[24, 24]}
                    style={router?.layout?.__typename === 'PorscheV3Layout' ? { marginLeft: 0, marginRight: 0 } : {}}
                >
                    <Col className="paymentDiv" offset={paymentSectionColOffset} {...colSpan}>
                        <Row gutter={[16, 40]}>
                            {application.__typename === 'MobilityApplication' && (
                                <Col xs={24}>
                                    <Title>{t('paymentDetails:titles.orderSummary')}</Title>
                                    <OrderSummary application={application} />
                                    <MobilityPromoCode
                                        application={application}
                                        applicationModuleId={applicationModuleId}
                                        giftVoucher={application?.giftVoucher}
                                        giftVoucherModuleId={giftVoucherModuleId}
                                        onDiscountCodeChange={onDiscountCodeChange}
                                        price={calculateTotalPrice(application)}
                                        promoCode={application.promoCode}
                                        promoCodeModuleId={promoCodeModuleId}
                                    />
                                    <LocationInfo application={application} />
                                </Col>
                            )}
                            {application.__typename !== 'MobilityApplication' && (
                                <VehicleWithDealer shouldShowDealerInfo={showDealerInfo} state={state} />
                            )}
                        </Row>
                    </Col>
                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            stage={state.stage}
                            stages={state.stages}
                        >
                            <Row gutter={[24, 24]}>
                                {deposit.amount > 0 && paymentAgreements.length > 0 && (
                                    <Col {...halfColSpan}>
                                        <Title>{t('paymentDetails:messages.checkAgreements')}</Title>
                                        <Row gutter={[16, 16]}>
                                            {paymentAgreements.map(agreement => (
                                                <Col xs={24}>
                                                    <AgreementField
                                                        key={agreement.id}
                                                        agreement={agreement}
                                                        bordered={application.__typename !== 'MobilityApplication'}
                                                    />
                                                </Col>
                                            ))}
                                        </Row>
                                    </Col>
                                )}
                                {deposit.amount > 0 && (
                                    <Col {...halfColSpan}>
                                        <Title>{t('paymentDetails:titles.paymentTitle')}</Title>
                                        <div style={{ marginBottom: '15px' }}>
                                            <BookingDeposit application={application} depositAmount={deposit.amount} />
                                        </div>
                                        <div
                                            style={
                                                paymentAgreements.some(
                                                    agreement =>
                                                        !values[agreement.id].isAgreed && getMandatory(agreement)
                                                )
                                                    ? {
                                                          opacity: '30%',
                                                          pointerEvents: 'none',
                                                      }
                                                    : {}
                                            }
                                        >
                                            {isSuccessful ? (
                                                <PaymentSuccess />
                                            ) : (
                                                !loading && (
                                                    <PorschePaymentWidget
                                                        deposit={deposit}
                                                        hasSinglePaymentMethod={hasSinglePaymentMethod}
                                                    />
                                                )
                                            )}
                                        </div>
                                    </Col>
                                )}
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>
            <JourneyToolbar>
                {canSkipDeposit && (
                    <SkipDepositButton key="skipForNextButton" disabled={false} onClick={onSkipDeposit} />
                )}
                {application.__typename === 'MobilityApplication' && (
                    <Button
                        key="back"
                        form="bookingForm"
                        htmlType="button"
                        icon={<Icon className="porsche-arrow" component={BackIcon} />}
                        onClick={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
                        porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="tertiary"
                    >
                        {t('paymentDetails:actions.back')}
                    </Button>
                )}
                <NextButton
                    key="nextButton"
                    disabled={isSubmitting}
                    form="completePorschePaymentForm"
                    htmlType="submit"
                    porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                    type="primary"
                >
                    {t('paymentDetails:actions.submit')}
                </NextButton>
            </JourneyToolbar>
        </>
    );

    if (!shouldIncludeLayout) {
        return content;
    }

    return (
        <CustomLayout
            backIcon={
                application.__typename !== 'MobilityApplication' && !application.withCustomerDevice ? (
                    <BackButton type="link">{t('paymentDetails:actions.back')}</BackButton>
                ) : null
            }
            onBack={
                application.__typename !== 'MobilityApplication' && !application.withCustomerDevice
                    ? () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })
                    : null
            }
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={<Typography style={{ alignItems: 'center', width: '100%' }}>{t('paymentDetails:title')}</Typography>}
            hasFooterBar
        >
            <BasicProLayoutContainer>{content}</BasicProLayoutContainer>
        </CustomLayout>
    );
};

export default Inner;
