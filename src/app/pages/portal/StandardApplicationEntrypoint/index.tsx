import { Route, Routes } from 'react-router-dom';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../api/fragments/StandardApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import PrivateAccessEntrypoint from './PrivateAccessEntrypoint';
import PublicAccessEntrypoint from './PublicAccessEntrypoint';

export type StandardApplicationEntrypointProps = {
    endpoint: StandardApplicationEntrypointContextDataFragment;
};

const StandardApplicationEntrypoint = ({ endpoint }: StandardApplicationEntrypointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key="private" element={<PrivateAccessEntrypoint endpoint={endpoint} />} path="*" />
                <Route key="public" element={<PublicAccessEntrypoint endpoint={endpoint} />} path="remote/*" />
            </Routes>
        </>
    );
};

export default StandardApplicationEntrypoint;
