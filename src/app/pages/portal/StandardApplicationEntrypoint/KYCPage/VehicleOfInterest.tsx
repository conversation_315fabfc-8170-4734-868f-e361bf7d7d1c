import { Col, Row } from 'antd';
import { head, isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import usePublic from '../../../../utilities/usePublic';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import VehicleInfo from '../../ConfiguratorApplicationEntrypoint/ThankYou/VehicleInfo';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import type { StandardApplicationState } from '../Journey/shared';

const colSpan = { span: 24 };

type VehicleOfInterestProps = { application: StandardApplicationState };

const VehicleOfInterest = ({ application }: VehicleOfInterestProps) => {
    const { t } = useTranslation('customerDetails');

    const translate = useTranslatedString();
    const empty = usePublic('empty.svg');

    const values = useMemo(() => {
        if (application.__typename !== 'StandardApplication') {
            return undefined;
        }

        switch (application.vehicle.__typename) {
            case 'LocalVariant': {
                const image = head(application.vehicle.images);

                return {
                    make: translate(
                        application.vehicle.model.parentModel
                            ? application.vehicle.model.parentModel.make.name
                            : application.vehicle.model.make.name
                    ),
                    variant: translate(application.vehicle.name),
                    totalPrice: application.financing?.totalPrice,
                    filename: image?.filename ?? 'empty.svg',
                    source: image?.url ?? empty,
                };
            }

            default:
                throw new Error('not implemented');
        }
    }, [application, translate, empty]);

    // nothing could be rendered
    if (isNil(values)) {
        return null;
    }

    return (
        <Col span={24}>
            <Row className="v3-layout-card">
                <Col {...colSpan}>
                    <Title size="large">{t('customerDetails:sectionTitles.vehicleOfInterest')}</Title>
                </Col>

                <Col {...colSpan}>
                    <VehicleInfo {...values} />
                </Col>
            </Row>
        </Col>
    );
};

export default VehicleOfInterest;
