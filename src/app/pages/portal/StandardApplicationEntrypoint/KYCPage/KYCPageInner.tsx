import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import {
    ApplicationQuotationSource,
    CompanyTheme,
    CustomerKind,
    LocalCustomerManagementModule,
    ModuleType,
} from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import { type SelectedCapValues, ViewState } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import Form from '../../../../components/fields/Form';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import { hasAppointmentScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import { SearchCapCustomerContextManager } from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/ContextManager';
import getKYCDataFromCap, {
    getCurrentVehicleFromCap,
} from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/getKYCDataFromCap';
// eslint-disable-next-line max-len
import useCapSearchCustomerAction from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/useCapSearchCustomerAction';
import CustomerDetails from '../../../shared/JourneyPage/CustomerDetails';
import AffinAutoFinanceCentreSelect from '../../../shared/JourneyPage/CustomerDetails/AffinAutoFinanceCentreSelect';
import ResetKYCButtonPorsche from '../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import { QuotationDetails } from '../../../shared/JourneyPage/QuotationDetails';
import ProceedWithCustomerButton from '../../../shared/ProceedWithCustomerButton';
import { Agreements } from '../../EventApplicationEntrypoint/ApplicantForm';
import AppointmentDetailsSection, {
    AppointmentType,
} from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import ConsentsAndDeclarations from '../../EventApplicationEntrypoint/ApplicantForm/ConsentsAndDeclarations';
import { usePersistStandardJourneyValues } from '../CarDetailsPage/usePersistStandardJourneyValues';
import { useStandardApplicationContext } from '../StandardApplicationContext';
import { NextButton } from '../shared/JourneyButton';
import { Backdrop, StyledJourneyToolbar } from '../styledComponents';
import OcrAndMyinfo from './OcrAndMyinfo';
import SaveDraftButton, { canSaveDraft } from './SaveDraftButton';
import VehicleOfInterest from './VehicleOfInterest';
import { SectionDivider, type KYCJourneyValues, type KYCPageProps } from './shared';
import useProceedWithCustomerDeviceButton from './useProceedWithCustomerDeviceButton';

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

type KYCPageInnerProps = KYCPageProps & {
    setSaveDraft: (value: boolean) => void;
    isCorporate: boolean;
    setIsCorporate: Dispatch<SetStateAction<boolean>>;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    kycAgreements: Agreements;
    hasCorporatePreset: boolean;
} & UploadDocumentProp;

const KYCPageInner = ({
    state,
    endpoint,
    dispatch,
    setSaveDraft,
    isCorporate,
    setIsCorporate,
    setPrefill,
    kycPresets,
    kycExtraSettings,
    kycAgreements,
    hasCorporatePreset,
    uploadDocument,
    removeDocument,
}: KYCPageInnerProps) => {
    const { application } = state;
    const { guarantorKYC, draftFlow } = application;

    const {
        values,
        handleSubmit,
        validateForm,
        submitForm,
        resetForm,
        initialValues,
        isSubmitting,
        setSubmitting,
        setFieldValue,
    } = useFormikContext<KYCJourneyValues>();
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const { theme } = useThemeComponents();

    const { lead } = useStandardApplicationContext();
    const { save: persistStandardJourneyValue, persistedValue } = usePersistStandardJourneyValues();

    const [withMyInfo, setWithMyInfo] = useState(persistedValue?.withMyInfo ?? false);
    const [capModalHasError, setCapModalHasError] = useState(false);

    const onSaveDraft = useCallback(async () => {
        try {
            setSaveDraft(true);
            await validateForm();
            await submitForm();
        } finally {
            setSaveDraft(false);
        }
    }, [submitForm, validateForm, setSaveDraft]);

    const onSubmit = useCallback(async () => {
        try {
            setSubmitting(true);
            await validateForm();
            await submitForm();
        } finally {
            setSubmitting(false);
        }
    }, [setSubmitting, submitForm, validateForm]);

    const showResetKYCButton = useMemo(() => {
        switch (application.module.__typename) {
            case ModuleType.StandardApplicationModule:
                return application.module.showResetKYCButton;

            default:
                return null;
        }
    }, [application.module]);

    const { applicationModuleId, capModuleId, isSearchCapOptional, dealerId } = useMemo(() => {
        if (application?.module?.__typename === 'StandardApplicationModule') {
            return {
                applicationModuleId: application.module.id,
                capModuleId: application.module.capModuleId,
                isSearchCapOptional: application.module.isSearchCapCustomerOptional,
                dealerId: application.dealerId,
            };
        }

        return { applicationModuleId: null, capModuleId: null, isSearchCapOptional: true, dealerId: null };
    }, [application]);

    const user = useAccount(true);

    const showCommentsToBank = user && application?.bank?.showCommentsField;

    const { insurers } = endpoint?.applicationModule || {};
    const showCommentsToInsurer = useMemo(() => {
        if (!user) {
            return false;
        }

        const insurer = insurers?.find(insurer => insurer.id === application?.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [user, insurers, application?.insurancing?.insurerId]);

    const isApplyingFromDetails = useMemo(
        () =>
            !isNil(draftFlow.isApplyingForFinanceCompleted) ||
            !isNil(draftFlow.isApplyingForInsuranceCompleted) ||
            !isNil(draftFlow.isApplyingForReservationCompleted),
        [
            draftFlow.isApplyingForFinanceCompleted,
            draftFlow.isApplyingForInsuranceCompleted,
            draftFlow.isApplyingForReservationCompleted,
        ]
    );

    const isApplyingFromApplyNew = useMemo(
        () =>
            draftFlow.isReceived &&
            (!draftFlow.isApplyNewForFinancingReceived ||
                !draftFlow.isApplyNewForInsuranceReceived ||
                !draftFlow.isApplyNewForAppointmentReceived),
        [
            draftFlow.isReceived,
            draftFlow.isApplyNewForAppointmentReceived,
            draftFlow.isApplyNewForFinancingReceived,
            draftFlow.isApplyNewForInsuranceReceived,
        ]
    );

    const { proceedWithCustomerButton, proceedWithCustomer } = useProceedWithCustomerDeviceButton(
        kycPresets,
        state,
        dispatch
    );

    const isAppointmentSectionVisible = useMemo(
        () =>
            values.configuration.testDrive &&
            hasAppointmentScenario(endpoint.applicationModule.scenarios) &&
            !isNil(endpoint.applicationModule.appointmentModule) &&
            endpoint.applicationModule.displayAppointmentDatepicker,
        [
            endpoint.applicationModule.appointmentModule,
            endpoint.applicationModule.displayAppointmentDatepicker,
            endpoint.applicationModule.scenarios,
            values.configuration.testDrive,
        ]
    );
    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    const sectionDivider = useMemo(
        () =>
            application.configuration.isAffinAutoFinanceCentreRequired || isAppointmentSectionVisible ? (
                <Col span={24}>
                    <SectionDivider />
                </Col>
            ) : null,
        [application.configuration.isAffinAutoFinanceCentreRequired, isAppointmentSectionVisible]
    );

    const { showSearchCapCustomerButton, immediateOpenCapCustomerSearch } = useCapSearchCustomerAction({
        kycHasCompleted: application.draftFlow?.isApplicantKYCCompleted,
        requireLogin: true,
        isCapEnabled: !!capModuleId,
        isOptional: isSearchCapOptional,
        hasLeadGuid: !!values.capValues?.leadGuid,
        capModalHasError,
        isLead: !!lead,
    });

    useEffect(() => {
        persistStandardJourneyValue({
            ...persistedValue,
            kyc: values,
            withMyInfo,
        });
    }, [persistedValue, persistStandardJourneyValue, values, withMyInfo]);

    const onCapValuesChanged = useCallback(
        (capValues: SelectedCapValues) => {
            switch (capValues.selectedValue) {
                case ViewState.BusinessPartner: {
                    const { selectedBusinessPartner } = capValues;

                    if (selectedBusinessPartner.currentVehicle && values.tradeInVehicle?.length) {
                        setFieldValue(
                            'tradeInVehicle.0',
                            getCurrentVehicleFromCap(selectedBusinessPartner, values.tradeInVehicle)
                        );
                    }

                    setFieldValue(
                        'customer.fields',
                        getKYCDataFromCap(selectedBusinessPartner, kycPresets, values.customer)
                    );
                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                    });

                    break;
                }

                default: {
                    const { selectedBusinessPartner, selectedLead } = capValues;
                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                        leadGuid: selectedLead?.leadGuid,
                        leadId: selectedLead?.leadId,
                    });
                }
            }
        },
        [kycPresets, setFieldValue, values.customer, values.tradeInVehicle]
    );

    const onCapSearchErrorConfirmed = useCallback(() => {
        setCapModalHasError(true);
    }, []);

    return (
        <SearchCapCustomerContextManager
            applicationModuleId={applicationModuleId}
            capModuleId={capModuleId}
            dealerId={dealerId}
            kycHasCompleted={application.draftFlow?.isApplicantKYCCompleted}
            lead={application.lead}
            onCapModalErrorConfirmed={onCapSearchErrorConfirmed}
            onCapValuesChanged={onCapValuesChanged}
            applicationRequireLogin
        >
            <FormAutoTouch />
            <Form id="kycJourneyForm" name="kycJourneyForm" onSubmitCapture={handleSubmit}>
                <Row gutter={[24, 50]}>
                    <Col {...leftColSpan}>
                        <Row gutter={[50, 40]}>
                            <VehicleOfInterest application={application} />
                            {application.configuration.isAffinAutoFinanceCentreRequired && (
                                <Col span={24}>
                                    <AffinAutoFinanceCentreSelect />
                                </Col>
                            )}
                            {isAppointmentSectionVisible && (
                                <Col span={24}>
                                    <AppointmentDetailsSection
                                        application={application}
                                        applicationModule={endpoint.applicationModule}
                                        appointmentType={AppointmentType.Appointment}
                                        showSkipValidation
                                    />
                                </Col>
                            )}

                            {!isCorporate && (
                                <OcrAndMyinfo
                                    application={application}
                                    endpoint={endpoint}
                                    kycPresets={kycPresets}
                                    onLoading={setIsLoading}
                                    setWithMyInfo={setWithMyInfo}
                                    state={state}
                                    topDivider={sectionDivider}
                                    withMyInfo={withMyInfo}
                                />
                            )}
                        </Row>
                    </Col>

                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            extra={
                                proceedWithCustomerButton && (
                                    <ProceedWithCustomerButton onClick={() => proceedWithCustomer(values)} />
                                )
                            }
                            stage={state.stage}
                            stages={state.stages}
                            withCapSearchButton={showSearchCapCustomerButton}
                        >
                            <Row gutter={[16, 16]}>
                                <Col span={24}>
                                    <CustomerDetails
                                        customerKind={isCorporate ? CustomerKind.Corporate : CustomerKind.Local}
                                        hasGuarantorPreset={guarantorKYC.length > 0}
                                        hasUploadDocuments={application.bank?.hasUploadDocuments}
                                        hasVSOUpload={application.bank?.hasVSOUpload}
                                        immediateOpenCapCustomerSearch={immediateOpenCapCustomerSearch}
                                        isApplyingFromApplyNew={isApplyingFromApplyNew}
                                        isApplyingFromDetails={isApplyingFromDetails}
                                        isGuarantorCompleted={application.draftFlow.isGuarantorCompleted}
                                        kycExtraSettings={kycExtraSettings}
                                        kycPresets={kycPresets}
                                        removeDocument={removeDocument}
                                        resetFormHandler={resetFormHandler}
                                        setIsCorporate={setIsCorporate}
                                        setPrefill={setPrefill}
                                        showCommentsToInsurer={showCommentsToInsurer}
                                        showRemarks={showCommentsToBank}
                                        showResetButton={showResetKYCButton && !withMyInfo}
                                        showTabs={hasCorporatePreset}
                                        showTitle={false}
                                        uploadDocument={uploadDocument}
                                        withFinancing={application.configuration.withFinancing}
                                    />
                                </Col>
                                <Col span={24}>
                                    <ConsentsAndDeclarations
                                        applicationAgreements={kycAgreements}
                                        hideDivider={values?.quotation?.source !== ApplicationQuotationSource.Enbd}
                                    />
                                </Col>
                                {values?.quotation?.source && (
                                    <Col span={24}>
                                        <QuotationDetails source={values?.quotation?.source} />
                                    </Col>
                                )}
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>
            {isLoading && <Backdrop />}

            <StyledJourneyToolbar>
                {showResetKYCButton &&
                    !withMyInfo &&
                    (theme === CompanyTheme.Porsche || theme === CompanyTheme.PorscheV3) && (
                        <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                    )}
                {canSaveDraft(application, endpoint.applicationModule) && (
                    <SaveDraftButton
                        applicantKYC={kycPresets}
                        bankProvider={application.bank?.integration.provider}
                        kycExtraSettings={kycExtraSettings}
                        moduleCountryCode={endpoint.applicationModule.company.countryCode}
                        onSaveDraft={onSaveDraft}
                        values={values}
                    />
                )}
                <NextButton key="nextButton" disabled={isSubmitting} form="kycJourneyForm" onSubmit={onSubmit} />
            </StyledJourneyToolbar>
        </SearchCapCustomerContextManager>
    );
};

export default KYCPageInner;
