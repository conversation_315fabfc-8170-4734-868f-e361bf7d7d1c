import { Col, ColProps, Divider, Row, RowProps, Space, Typography } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { Dispatch, ReactNode, SetStateAction, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { StyledComponent } from 'styled-components';
import { KycFieldSpecsFragment } from '../../../../api/fragments';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import {
    KycFieldPurpose,
    AffinAutoFinanceCentre,
    LocalCustomerManagementModule,
    CapValuesInput,
    CustomerKind,
    LocalCustomerFieldKey,
    StandardApplicationConfiguration,
    TradeInVehiclePayload,
} from '../../../../api/types';
import CheckboxField from '../../../../components/fields/CheckboxField';
import { KycPresetField, KYCPresetFormFields } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import { SubTitle } from '../../../shared/ApplicationDetailsPage/standard/sharedUI';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { QuotationFormValues } from '../../../shared/JourneyPage/QuotationDetails/types';
import type { AppointmentValues } from '../AppointmentPage/appointmentValues';
import type { Action, State, StandardApplicationState } from '../Journey/shared';

export const StyledContainer = styled(SubTitle)``;
export const CheckboxContainer = styled.div`
    display: flex;
    align-items: center;

    & .ant-form-item {
        margin-bottom: 0;
    }

    & .ant-typography {
        font-size: 16px;
    }
`;

const SubHeader = styled(Typography.Paragraph)`
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
`;

export const SectionDivider = styled(Divider)`
    margin-top: 0px;
    margin-bottom: 0;
    border: 1px solid #e3e4e5;
`;

export const PanelContainer = styled(Space)`
    width: 100%;
`;

export const defaultFieldsColSpan = { md: 12, sm: 24, xs: 24 };

export const fullWidthColSpan = { xxl: 6, xl: 6, lg: 8, md: 8, sm: 12, xs: 24 };

export const CustomerDetailsKey = [
    LocalCustomerFieldKey.Title,
    LocalCustomerFieldKey.Salutation,
    LocalCustomerFieldKey.SalutationBmw,
    LocalCustomerFieldKey.FirstName,
    LocalCustomerFieldKey.LastName,
    LocalCustomerFieldKey.FullName,
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Birthday,
    LocalCustomerFieldKey.Nationality,
    LocalCustomerFieldKey.Citizenship,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Race,
    LocalCustomerFieldKey.Gender,
    LocalCustomerFieldKey.MaritalStatus,
    LocalCustomerFieldKey.Education,
    LocalCustomerFieldKey.ResidentialStatus,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.RelationshipWithApplicant,
    LocalCustomerFieldKey.Passport,
];

export const IdentityDetailsKey = [LocalCustomerFieldKey.UaeIdentitySet];

export const CorrespondenceKey = [
    LocalCustomerFieldKey.CorrespondenceAddress,
    LocalCustomerFieldKey.CorrespondenceCity,
    LocalCustomerFieldKey.CorrespondenceDistrict,
];

export const CorporateInformationKey = [
    LocalCustomerFieldKey.CorporateName,
    LocalCustomerFieldKey.CorporateIdentityNumber,
    LocalCustomerFieldKey.CorporateRegistrationDate,
    LocalCustomerFieldKey.CorporateIndustryCategory,
    LocalCustomerFieldKey.CorporateRegisteredCapital,
    LocalCustomerFieldKey.CorporateAnnualRevenue,
    LocalCustomerFieldKey.CorporateNumberOfEmployee,
    LocalCustomerFieldKey.CorporatePhone,
];

export const AddressDetailsKey = [
    LocalCustomerFieldKey.ResidentialStatusVwfs,
    LocalCustomerFieldKey.ResidenceType,
    LocalCustomerFieldKey.Country,
    LocalCustomerFieldKey.Region,
    LocalCustomerFieldKey.City,
    LocalCustomerFieldKey.District,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.Address,
    LocalCustomerFieldKey.Road,
    LocalCustomerFieldKey.UnitNumber,
    LocalCustomerFieldKey.TimeOfAddress,
    LocalCustomerFieldKey.AddressType,
    LocalCustomerFieldKey.Emirate,
    LocalCustomerFieldKey.DeliveryAddress,
];

export const EmploymentDetailsKey = [
    LocalCustomerFieldKey.JobTitle,
    LocalCustomerFieldKey.Occupation,
    LocalCustomerFieldKey.EmploymentStatus,
    LocalCustomerFieldKey.TimeOfEmployment,
    LocalCustomerFieldKey.CompanyName,
    LocalCustomerFieldKey.CompanyPhoneticName,
    LocalCustomerFieldKey.CompanyCity,
    LocalCustomerFieldKey.CompanyDistrict,
    LocalCustomerFieldKey.CompanyAddress,
    LocalCustomerFieldKey.IncomeType,
    LocalCustomerFieldKey.MonthlyIncome,
    LocalCustomerFieldKey.OtherIncome,
    LocalCustomerFieldKey.DateOfJoining,
    LocalCustomerFieldKey.PreferredFirstPaymentDate,
    LocalCustomerFieldKey.SalaryTransferredBankSet,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.BusinessTitle,
];

export const ReferenceDetailsKey = [LocalCustomerFieldKey.ReferenceDetailSet];

export const OthersKey = [
    LocalCustomerFieldKey.NoClaimDiscount,
    LocalCustomerFieldKey.DriverLicensePassDate,
    LocalCustomerFieldKey.DrivingLicense,
    LocalCustomerFieldKey.DrivingLicenseTh,
    LocalCustomerFieldKey.DrivingLicenseMy,
    LocalCustomerFieldKey.UaeDrivingLicense,
    LocalCustomerFieldKey.PurchaseIntention,
    LocalCustomerFieldKey.Comments,
];

export type KYCPageProps = {
    state: State<StandardApplicationState>;
    dispatch: Dispatch<Action<StandardApplicationState>>;
    endpoint: StandardApplicationEntrypointContextDataFragment;
};

export type GroupedCustomerDetailsProps = {
    customerFields: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    customerKind: CustomerKind;
    prefix: string;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    SectionHeader?: React.FC | StyledComponent<any, any, {}, any>;
    hasUpdatePermission?: boolean;
    colSpan?: ColProps;
    contentGutter?: RowProps['gutter'];
    enableMobileVerification?: boolean;
} & UploadDocumentProp;

type GroupedCustomerDataItem = KycFieldSpecsFragment & {
    index: number;
};

type GroupedCustomerData = {
    customerDetails: GroupedCustomerDataItem[];
    addressDetails: GroupedCustomerDataItem[];
    correspondenceAddress: GroupedCustomerDataItem[];
    corporateInformation: GroupedCustomerDataItem[];
    employmentDetails: GroupedCustomerDataItem[];
    identityDetails: GroupedCustomerDataItem[];
    referenceDetails: GroupedCustomerDataItem[];
    others: GroupedCustomerDataItem[];
};

export type ApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type KYCJourneyValues = QuotationFormValues & {
    agreements: AgreementValues;
    customer: ApplicantFormValues;
    configuration: StandardApplicationConfiguration;
    tradeInVehicle?: TradeInVehiclePayload[];
    hasGuarantor: boolean;
    prefix?: string;
    vsoUpload?: File[];
    uploadDocuments?: {
        [CustomerKind.Local]?: File[];
        [CustomerKind.Corporate]?: File[];
    };
    prefill: boolean;

    appointment?: AppointmentValues;
    visitAppointment?: AppointmentValues;

    financing?: {
        affinAutoFinanceCentre?: AffinAutoFinanceCentre;
    };

    capValues?: CapValuesInput;
    customerCiamId?: string;
};

enum SectionKey {
    BasicInformation = 'basicInformation',
    CorporateInformation = 'corporateInformation',
    AddressDetails = 'addressDetails',
    CorrespondenceAddress = 'correspondenceAddress',
    EmploymentDetails = 'employmentDetails',
    IdentityDetails = 'identityDetails',
    ReferenceDetails = 'referenceDetails',
    Others = 'others',
}

type PanelSectionProps = {
    data: GroupedCustomerDataItem[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    colSpan?: ColProps;
    key: SectionKey;
    hasUpdatePermission: boolean;
    header: ReactNode;
    uploadDocument: UploadDocumentProp['uploadDocument'];
    removeDocument: UploadDocumentProp['removeDocument'];
    SectionHeader?: React.FC | StyledComponent<any, any, {}, any>;
    contentGutter?: RowProps['gutter'];
    enableMobileVerification?: boolean;
};

// eslint-disable-next-line max-len
const getPanelSection = ({
    data,
    kycExtraSettings,
    key,
    header,
    uploadDocument,
    removeDocument,
    SectionHeader = SubHeader,
    hasUpdatePermission,
    colSpan = defaultFieldsColSpan,
    contentGutter = [24, 0],
    enableMobileVerification,
}: PanelSectionProps) =>
    data.length > 0 && (
        <Row key={key} gutter={[24, 0]}>
            <Col span={24}>
                <SectionHeader>{header}</SectionHeader>
            </Col>
            <Col span={24}>
                <Row gutter={contentGutter}>
                    {data.map(field => (
                        <KycPresetField
                            key={field.key}
                            colSpan={colSpan}
                            customerType="customer"
                            enableMobileVerification={enableMobileVerification}
                            extraSettings={kycExtraSettings}
                            field={field}
                            isDisabled={!hasUpdatePermission}
                            markMyinfo={false}
                            prefix="customer.fields"
                            removeDocument={removeDocument}
                            uploadDocument={uploadDocument}
                        />
                    ))}
                </Row>
            </Col>
        </Row>
    );

export const GroupedCustomerDetails = ({
    customerFields,
    customerKind,
    kycExtraSettings,
    prefix,
    setPrefill,
    uploadDocument,
    removeDocument,
    colSpan = defaultFieldsColSpan,
    SectionHeader,
    hasUpdatePermission = true,
    contentGutter,
    enableMobileVerification,
}: GroupedCustomerDetailsProps) => {
    const { t } = useTranslation('customerDetails');

    const onChange = useCallback(
        (e: CheckboxChangeEvent) => {
            setPrefill(e.target.checked);
            e.stopPropagation();
            e.preventDefault();
        },
        [setPrefill]
    );
    const groupByCustomerData = useMemo(() => {
        const data: GroupedCustomerData = {
            customerDetails: [],
            addressDetails: [],
            correspondenceAddress: [],
            corporateInformation: [],
            employmentDetails: [],
            identityDetails: [],
            referenceDetails: [],
            others: [],
        };

        (customerFields ?? []).forEach(({ key, purpose, ...rest }, index) => {
            if (purpose.includes(KycFieldPurpose.Kyc)) {
                const formatData = { ...rest, key, index, purpose };
                if (CustomerDetailsKey.includes(key)) {
                    if (key === LocalCustomerFieldKey.RelationshipWithApplicant) {
                        if (customerKind === CustomerKind.Guarantor) {
                            data.customerDetails.push(formatData);

                            return;
                        }

                        return;
                    }

                    data.customerDetails.push(formatData);

                    return;
                }

                if (CorrespondenceKey.includes(key)) {
                    data.correspondenceAddress.push(formatData);

                    return;
                }

                if (CorporateInformationKey.includes(key)) {
                    data.corporateInformation.push(formatData);

                    return;
                }

                if (AddressDetailsKey.includes(key)) {
                    data.addressDetails.push(formatData);

                    return;
                }

                if (EmploymentDetailsKey.includes(key)) {
                    data.employmentDetails.push(formatData);

                    return;
                }

                if (IdentityDetailsKey.includes(key)) {
                    data.identityDetails.push(formatData);

                    return;
                }

                if (ReferenceDetailsKey.includes(key)) {
                    data.referenceDetails.push(formatData);

                    return;
                }

                if (OthersKey.includes(key)) {
                    data.others.push(formatData);
                }
            }
        });

        return data;
    }, [customerFields, customerKind]);

    return (
        <Col span={24}>
            <PanelContainer direction="vertical" size={16}>
                {getPanelSection({
                    data: groupByCustomerData.customerDetails,
                    kycExtraSettings,
                    key: SectionKey.BasicInformation,
                    header: t('customerDetails:subHeaders.basicInformation'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                    enableMobileVerification,
                })}

                {getPanelSection({
                    data: groupByCustomerData.corporateInformation,
                    kycExtraSettings,
                    key: SectionKey.CorporateInformation,
                    header: t('customerDetails:subHeaders.corporateInformation'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}

                {getPanelSection({
                    data: groupByCustomerData.addressDetails,
                    kycExtraSettings,
                    key: SectionKey.AddressDetails,
                    header: t('customerDetails:subHeaders.addressDetails'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}

                {getPanelSection({
                    data: groupByCustomerData.correspondenceAddress,
                    kycExtraSettings,
                    key: SectionKey.CorrespondenceAddress,
                    header: (
                        <Row>
                            <StyledContainer>{t('customerDetails:subHeaders.correspondenceAddress')}</StyledContainer>
                            <CheckboxContainer>
                                <CheckboxField
                                    name="prefill"
                                    onChange={onChange}
                                    style={{ verticalAlign: 'super', paddingLeft: '15px', marginBottom: '1rem' }}
                                >
                                    <Typography>{t('customerDetails:prefill')}</Typography>
                                </CheckboxField>
                            </CheckboxContainer>
                        </Row>
                    ),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}

                {getPanelSection({
                    data: groupByCustomerData.employmentDetails,
                    kycExtraSettings,
                    key: SectionKey.EmploymentDetails,
                    header: t('customerDetails:subHeaders.employmentDetails'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}

                {getPanelSection({
                    data: groupByCustomerData.identityDetails,
                    kycExtraSettings,
                    key: SectionKey.IdentityDetails,
                    header: t('customerDetails:subHeaders.identityDetails'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}

                {getPanelSection({
                    data: groupByCustomerData.referenceDetails,
                    kycExtraSettings,
                    key: SectionKey.ReferenceDetails,
                    header: t('customerDetails:subHeaders.referenceDetails'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}

                {getPanelSection({
                    data: groupByCustomerData.others,
                    kycExtraSettings,
                    key: SectionKey.Others,
                    header: t('customerDetails:subHeaders.others'),
                    hasUpdatePermission,
                    uploadDocument,
                    removeDocument,
                    colSpan,
                    SectionHeader,
                    contentGutter,
                })}
            </PanelContainer>
        </Col>
    );
};

export default GroupedCustomerDetails;
