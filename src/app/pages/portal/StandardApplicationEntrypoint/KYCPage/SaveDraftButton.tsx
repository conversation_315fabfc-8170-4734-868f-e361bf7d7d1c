import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import { KycField, LocalCustomerManagementModule } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import type { GuarantorKYCJourneyValues } from '../GuarantorKYCPage/shared';
import { StandardApplicationState } from '../Journey/shared';
import type { KYCJourneyValues } from './shared';

/**
 *  only when there is scenario ‘Financings’ and/or ‘Insurance’
 *  & checked the checkbox of ‘Financings’ and/or ‘Insurance’ if they are optional
 */
export const canSaveDraft = (
    application: StandardApplicationState,
    module: StandardApplicationEntrypointContextDataFragment['applicationModule']
) =>
    !application.withCustomerDevice &&
    ((hasFinancingScenario(module.scenarios) && application.configuration.withFinancing) ||
        (hasInsuranceScenario(module.scenarios) && application.configuration.withInsurance)) &&
    (!application.draftFlow?.isReceived ||
        (application.draftFlow?.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application?.draftFlow.isApplyNewForInsuranceReceived)));

const SaveDraftButton = ({
    values,
    onSaveDraft,
    applicantKYC,
    kycExtraSettings,
    moduleCountryCode,
    bankProvider,
}: {
    values: KYCJourneyValues | GuarantorKYCJourneyValues;
    onSaveDraft: () => void;
    applicantKYC: Array<KycField>;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    moduleCountryCode: string;
    bankProvider?: string;
}) => {
    const { t } = useTranslation('customerDetails');
    const { Button } = useThemeComponents();
    const { layout } = useRouter();

    const applicantsValidator = useKYCFormValidator({
        field: applicantKYC,
        extraSettings: kycExtraSettings,
        moduleCountryCode,
        prefix: 'customer.fields',
        saveDraft: true,
        bankProvider,
    });

    const disabled = useMemo(() => !isNil(applicantsValidator.validate(values)), [applicantsValidator, values]);

    return (
        <Button
            key="saveDraft"
            disabled={disabled}
            htmlType="button"
            onClick={onSaveDraft}
            porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
            type="tertiary"
        >
            {t('customerDetails:saveDraftButton')}
        </Button>
    );
};

export default SaveDraftButton;
