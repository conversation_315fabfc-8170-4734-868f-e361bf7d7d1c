import { ApolloError } from '@apollo/client';
import { Formik } from 'formik';
import { isEmpty, isNil, sumBy } from 'lodash/fp';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import { ApplicationAgreement, CustomerKind, LocalCustomerFieldKey } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    KYCPresetFormFields,
    getInitialValues,
    getKycInitialValuesFromInsurance,
} from '../../../../utilities/kycPresets';
import useInsuranceKyc from '../../../../utilities/kycPresets/useInsuranceKyc';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import {
    hasAppointmentScenario,
    hasVisitAppointmentScenario,
} from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues, {
    AgreementValues,
} from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { getInitUploadDocuments } from '../../../shared/JourneyPage/CustomerDetails/shared';
import useKYCData from '../../../shared/JourneyPage/CustomerDetails/useKYCData';
import {
    useGetInitialQuotationValues,
    useQuotationValidator,
    useSubmitApplicationQuotation,
} from '../../../shared/JourneyPage/QuotationDetails';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import { AgreementAndKycStateData } from '../../ConfiguratorApplicationEntrypoint/ApplicantKYCPage/types';
// eslint-disable-next-line max-len
import { retrieveAppointmentModuleFromEntrypoint } from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import {
    getMyInfoTemporaryValues,
    removeMyInfoTemporaryValues,
} from '../../EventApplicationEntrypoint/ApplicantForm/CustomerDetails/utils';
import { useFirstAvailableSlot } from '../AppointmentPage/useAppointmentAvailability';
import useAppointmentSubmission from '../AppointmentPage/useAppointmentSubmission';
import useVisitAppointmentSubmission from '../AppointmentPage/useVisitAppointmentSubmission';
import { usePersistStandardJourneyValues } from '../CarDetailsPage/usePersistStandardJourneyValues';
import useCustomerDetailsSubmission from '../CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../CustomerDetailsPage/useUpdateApplicationFields';
import type { State, StandardApplicationState } from '../Journey/shared';
import { useStandardApplicationContext } from '../StandardApplicationContext';
import { StyleContainer } from '../styledComponents';
import useUpdateStandardApplication from '../useUpdateStandardApplication';
import KYCPageInner from './KYCPageInner';
import type { KYCJourneyValues, KYCPageProps } from './shared';

export type ApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type CustomKYCFormFields = Pick<
    KYCPresetFormFields,
    | LocalCustomerFieldKey.FirstName
    | LocalCustomerFieldKey.LastName
    | LocalCustomerFieldKey.FullName
    | LocalCustomerFieldKey.Email
    | LocalCustomerFieldKey.IdentityNumber
>;

export type Agreements = Omit<ApplicationAgreement, 'displayName' | 'orderNumber'>[];

export type KYCPageFormProps = {
    state: State<StandardApplicationState>;
    setSaveDraft: (value: boolean) => void;
};

const KYCPageForm = ({ state, endpoint, dispatch }: KYCPageProps) => {
    const { application, token } = state;
    const { lead } = useStandardApplicationContext();

    const { t } = useTranslation('customerDetails');
    const { notification } = useThemeComponents();

    const [saveDraft, setSaveDraft] = useState(false);
    const [prefill, setPrefill] = useState(false);
    const { persistedValue, save: persistStandardJourneyValues } = usePersistStandardJourneyValues();

    // Since configuration test-drive and trade-in can be changed in KYC, we need to use local state,
    // It's possible that there is KYC preset or c&d with condition "testDrive" or "tradeIn" applied
    // only applicable to configurator and standard application
    const agreementAndKyc: AgreementAndKycStateData = useMemo(
        () => ({
            applicantAgreements: application.applicantAgreements,
            corporateAgreements: application.corporateAgreements,
            applicantKYC: application.applicantKYC,
            corporateKYC: application.corporateKYC,
        }),
        [application]
    );

    const { draftFlow } = application;

    const { applicant, documents } = useMemo(() => {
        if (lead) {
            return { applicant: lead.customer, documents: lead.documents };
        }

        return { applicant: application.applicant, documents: application.documents };
    }, [application, lead]);

    const [isCorporate, setIsCorporate] = useState<boolean>(
        !lead ? application.applicant.__typename === 'CorporateCustomer' : false
    );

    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const submitAppointment = useAppointmentSubmission();
    const submitVisitAppointment = useVisitAppointmentSubmission();
    const updateApplicationFields = useUpdateApplicationFields();

    const isEditableField = useMemo(() => (applicant ? applicant.fields : []), [applicant]);

    const { kycAgreements, kycFields } = useKYCData(
        isCorporate,
        agreementAndKyc.applicantKYC,
        agreementAndKyc.corporateKYC,
        agreementAndKyc.applicantAgreements,
        agreementAndKyc.corporateAgreements
    );

    const kycExtraSettings = useMemo(
        () =>
            endpoint?.applicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.applicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const isApplyingFromApplyNew = useMemo(
        () =>
            application.draftFlow.isReceived &&
            (!application.draftFlow.isApplyNewForFinancingReceived ||
                !application.draftFlow.isApplyNewForInsuranceReceived ||
                !application.draftFlow.isApplyNewForAppointmentReceived),
        [
            application.draftFlow.isReceived,
            application.draftFlow.isApplyNewForAppointmentReceived,
            application.draftFlow.isApplyNewForFinancingReceived,
            application.draftFlow.isApplyNewForInsuranceReceived,
        ]
    );

    const agreementsKYC = useMemo(
        () =>
            kycAgreements.map(agreement => ({
                ...agreement,
                isAgreed:
                    !isNil(draftFlow.isApplyingForFinanceCompleted) ||
                    !isNil(draftFlow.isApplyingForInsuranceCompleted) ||
                    !isNil(draftFlow.isApplyingForReservationCompleted) ||
                    isApplyingFromApplyNew
                        ? agreement.isAgreed
                        : false,
            })),
        [
            draftFlow.isApplyingForFinanceCompleted,
            draftFlow.isApplyingForInsuranceCompleted,
            draftFlow.isApplyingForReservationCompleted,
            kycAgreements,
            isApplyingFromApplyNew,
        ]
    );
    const agreementsValidator = useAgreementsValidator(agreementsKYC, 'agreements');
    const agreements = useAgreementsValues(agreementsKYC);

    const applicants = useMemo(() => {
        const fields = {
            ...getInitialValues(isEditableField, kycFields, documents),
            ...(!isCorporate && getKycInitialValuesFromInsurance(application.insurancing, kycFields)),
        };

        return { fields };
    }, [isEditableField, kycFields, documents, isCorporate, application.insurancing]);

    const applicantsValidator = useKYCFormValidator({
        field: kycFields,
        extraSettings: kycExtraSettings,
        moduleCountryCode: endpoint.applicationModule.company.countryCode,
        prefix: 'customer.fields',
        saveDraft,
        bankProvider: application.bank?.integration.provider,
    });

    const { showVSOUpload, showUploadDocument } = useMemo(
        () => ({
            showVSOUpload: application.configuration.withFinancing && application.bank?.hasVSOUpload,
            showUploadDocument: application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        }),
        [application.configuration.withFinancing, application.bank]
    );

    const quotationValidator = useQuotationValidator(
        application.bank?.integration.__typename === 'EnbdBankIntegration' && application.configuration.withFinancing,
        sumBy('amount', application.financing?.dealerOptions ?? []),
        'quotation'
    );

    const validations = useMemo(
        () =>
            validators.compose(
                applicantsValidator,
                agreementsValidator,
                validators.only(() => !saveDraft, quotationValidator),
                validators.only(() => showVSOUpload, validators.requiredUploadFile('vsoUpload')),
                validators.only(
                    () => showUploadDocument && !isCorporate,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Local}`)
                ),
                validators.only(
                    () => showUploadDocument && isCorporate,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Corporate}`)
                ),
                validators.only(
                    (values, error, { prefix }) =>
                        values.configuration.testDrive &&
                        hasAppointmentScenario(endpoint.applicationModule.scenarios) &&
                        endpoint.applicationModule.appointmentModule &&
                        endpoint.applicationModule.displayAppointmentDatepicker &&
                        !values.appointment?.useCurrentDateTime,
                    validators.compose(
                        validators.requiredDate('appointment.date'),
                        validators.requiredString('appointment.time')
                    )
                ),
                validators.only(
                    (values, error, { prefix }) =>
                        values.configuration.visitAppointment &&
                        endpoint.applicationModule.appointmentModule &&
                        hasVisitAppointmentScenario(endpoint.applicationModule.scenarios),
                    validators.compose(
                        validators.requiredDate('visitAppointment.date'),
                        validators.requiredString('visitAppointment.time')
                    )
                ),
                validators.only(
                    values => !!values.configuration.isAffinAutoFinanceCentreRequired,
                    validators.compose(validators.requiredString('financing.affinAutoFinanceCentre'))
                )
            ),
        [
            agreementsValidator,
            applicantsValidator,
            isCorporate,
            quotationValidator,
            saveDraft,
            showUploadDocument,
            showVSOUpload,
            endpoint.applicationModule,
        ]
    );

    const validate = useValidator(validations, { prefill });

    // Added insurance kyc handler in KYC Page
    const { birthdayConfirmation } = useInsuranceKyc(application.configuration.withInsurance, {
        createdAt: application.versioning?.createdAt,
    });

    const getInitialQuotationValues = useGetInitialQuotationValues();

    const { appointmentModule, hasShowroomVisit, hasTestDrive, visitAppointmentModule } =
        retrieveAppointmentModuleFromEntrypoint(endpoint);

    const { data: bookedListing, loading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: isEmpty(appointmentModule?.id),
    });
    const bookedAppointmentTimeSlots = useMemo(
        () => bookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [bookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const datePrefill = useFirstAvailableSlot(
        appointmentModule,
        bookedAppointmentTimeSlots,
        endpoint.applicationModule.company.timeZone,
        application.appointmentStage?.bookingTimeSlot?.slot
    );

    const { data: visitbookedListing, loading: visitLoading } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: visitAppointmentModule?.id,
        },
        skip: isEmpty(visitAppointmentModule?.id),
    });
    const bookedVisitAppointmentTimeSlots = useMemo(
        () => visitbookedListing?.retrieveBlockedAppointmentTimeSlot || [],
        [visitbookedListing?.retrieveBlockedAppointmentTimeSlot]
    );

    const visitDatePrefill = useFirstAvailableSlot(
        visitAppointmentModule,
        bookedVisitAppointmentTimeSlots,
        endpoint.applicationModule.company.timeZone,
        application.appointmentStage?.bookingTimeSlot?.slot
    );

    const initialValues: KYCJourneyValues = useMemo(() => {
        const myInfoTemporaryValues = getMyInfoTemporaryValues(application.id);

        return {
            customer: applicants,
            agreements,
            configuration: application.configuration,
            tradeInVehicle: application.tradeInVehicle,
            hasGuarantor: application.draftFlow.hasGuarantor,
            remarks: application?.remarks ?? '',
            vsoUpload: [],
            prefill: false,
            quotation: getInitialQuotationValues({
                bank: application.bank,
                financing: application.financing,
                isFinancingEnabled: application.configuration.withFinancing,
            }),
            appointment: {
                date: myInfoTemporaryValues?.appointment?.date ?? datePrefill?.date,
                time: myInfoTemporaryValues?.appointment?.time ?? datePrefill?.firstSlot?.value,
                useCurrentDateTime: false,
            },
            visitAppointment: {
                date: visitDatePrefill?.date,
                time: visitDatePrefill?.firstSlot?.value,
                useCurrentDateTime: false,
            },
            uploadDocuments: getInitUploadDocuments(
                application.documents,
                showUploadDocument,
                application.applicant.kind
            ),
            ...persistedValue?.kyc,
        };
    }, [
        application,
        applicants,
        agreements,
        getInitialQuotationValues,
        datePrefill,
        visitDatePrefill,
        showUploadDocument,
        persistedValue?.kyc,
    ]);

    const updateStandardApplication = useUpdateStandardApplication();
    const submitQuotation = useSubmitApplicationQuotation();
    const uploadOcrFiles = useUploadOcrFiles();

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const user = useAccount(true);

    const showCommentsToBank = user && application?.bank?.showCommentsField;

    const { insurers } = endpoint.applicationModule;
    const showCommentsToInsurer = useMemo(() => {
        if (!user) {
            return false;
        }

        const insurer = insurers.find(insurer => insurer.id === application?.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [user, insurers, application?.insurancing?.insurerId]);

    const onSubmit = useHandleError(
        async ({
            appointment,
            visitAppointment,
            ...values
        }: KYCJourneyValues & { remarks: string; commentsToInsurer?: string }) =>
            new Promise((resolve, reject) => {
                birthdayConfirmation(
                    applicants.fields.Birthday?.value,
                    values.customer.fields.Birthday?.value,
                    async () => {
                        try {
                            notification.loading({
                                content: t('customerDetails:messages.creationSubmitting'),
                                duration: 0,
                                key: 'primary',
                            });

                            const updateApplicationResult = await updateStandardApplication(
                                state.token,
                                values,
                                isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                                saveDraft
                            );

                            const updatedToken = await submitQuotation(updateApplicationResult.token, values);

                            const submitAgreementKYC = await submitAgreements(
                                updatedToken,
                                values.agreements,
                                isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                                values.hasGuarantor
                            );

                            if (showCommentsToBank || showCommentsToInsurer) {
                                // we wait to update remarks first
                                await updateApplicationFields(
                                    submitAgreementKYC.token,
                                    values.remarks,
                                    values.commentsToInsurer
                                );
                                /* 
                                then we later call the KYC as calling submit customer will immediately 
                                call the next step and will have not the remarks
                                */
                            }

                            const submitApplicantKYC = await submitCustomerDetails({
                                token: submitAgreementKYC.token,
                                fields: values.customer.fields,
                                customerKind: isCorporate ? CustomerKind.Corporate : CustomerKind.Local,
                                sameCorrespondenceAddress: values.prefill,
                                saveDraft,
                                capValues: values.capValues,
                            });

                            // Before next process
                            // See if there is appointment scenario with test drive applied
                            // If yes, then we need to update the application with the appointment
                            const module =
                                application.module.__typename === 'StandardApplicationModule' && application.module;

                            let temporarilyJourneyResult = submitApplicantKYC;

                            if (
                                values.configuration.testDrive &&
                                module?.displayAppointmentDatepicker &&
                                hasTestDrive
                            ) {
                                const result = await submitAppointment(
                                    submitApplicantKYC.token,
                                    appointment,
                                    appointmentModule,
                                    module.displayAppointmentDatepicker,
                                    module.company.timeZone
                                );
                                temporarilyJourneyResult = result;
                            }

                            if (hasShowroomVisit) {
                                const result = await submitVisitAppointment(
                                    submitApplicantKYC.token,
                                    appointment,
                                    visitAppointmentModule,
                                    module.displayAppointmentDatepicker,
                                    module.company.timeZone
                                );
                                temporarilyJourneyResult = result;
                            }

                            await uploadOcrFiles(temporarilyJourneyResult.token);

                            notification.destroy('primary');

                            if (temporarilyJourneyResult.__typename !== 'ApplicationJourney') {
                                throw new Error('unexpected journey context');
                            }
                            // go to the journey
                            if (temporarilyJourneyResult.application.__typename !== 'StandardApplication') {
                                throw new Error('unexpected type');
                            }

                            removeMyInfoTemporaryValues(application.id);

                            persistStandardJourneyValues({
                                ...persistedValue,
                                token: temporarilyJourneyResult.token,
                                kyc: {
                                    ...values,
                                    appointment,
                                },
                            });

                            if (!saveDraft) {
                                dispatch({
                                    type: 'next',
                                    token: temporarilyJourneyResult.token,
                                    application: temporarilyJourneyResult.application,
                                });
                            } else {
                                notification.success({
                                    content: t('customerDetails:messages.draftSaved'),
                                    key: 'secondary',
                                });
                            }
                            resolve();
                        } catch (error) {
                            if (error instanceof ApolloError) {
                                notification.error(error.graphQLErrors[0].message);
                            } else {
                                console.error(error);
                            }
                            reject(error);
                        } finally {
                            notification.destroy('primary');
                        }
                    }
                );
            }),
        [
            birthdayConfirmation,
            applicants,
            notification,
            t,
            updateStandardApplication,
            state.token,
            isCorporate,
            saveDraft,
            submitQuotation,
            submitAgreements,
            showCommentsToBank,
            showCommentsToInsurer,
            submitCustomerDetails,
            application.module,
            application.id,
            hasTestDrive,
            hasShowroomVisit,
            uploadOcrFiles,
            persistStandardJourneyValues,
            persistedValue,
            updateApplicationFields,
            submitAppointment,
            appointmentModule,
            submitVisitAppointment,
            visitAppointmentModule,
            dispatch,
        ]
    );

    if (loading) {
        return <PortalLoadingElement />;
    }

    return (
        <StyleContainer>
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} validateOnMount>
                <KYCPageInner
                    dispatch={dispatch}
                    endpoint={endpoint}
                    hasCorporatePreset={agreementAndKyc.corporateKYC?.length > 0}
                    isCorporate={isCorporate}
                    kycAgreements={kycAgreements}
                    kycExtraSettings={kycExtraSettings}
                    kycPresets={kycFields}
                    removeDocument={removeDocument}
                    setIsCorporate={setIsCorporate}
                    setPrefill={setPrefill}
                    setSaveDraft={setSaveDraft}
                    state={state}
                    uploadDocument={uploadDocument}
                />
            </Formik>
        </StyleContainer>
    );
};

const KYCPage = (props: KYCPageProps) => (
    <OcrFilesManager>
        <KYCPageForm {...props} />
    </OcrFilesManager>
);

export default KYCPage;
