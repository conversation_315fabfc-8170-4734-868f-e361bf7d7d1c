import { ApolloError } from '@apollo/client';
import { useFormikContext } from 'formik';
import { Dispatch, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { KycFieldSpecsFragment } from '../../../../api/fragments';
import { LocalCustomerFieldKey } from '../../../../api/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import useProceedWithCustomerValidators from '../../../../utilities/kycPresets/useProceedWithCustomerValidators';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { JourneyAction, JourneyState } from '../../FinderApplicationPublicAccessEntrypoint/types';
import type { TestDriveJourneyState } from '../../TestDriveEntrypoint/Journey/shared';
import type { TestDriveKYCJourneyValues } from '../../TestDriveEntrypoint/TestDriveKYC/shared';
import { JourneyStage, type StandardApplicationState, type Action, type State } from '../Journey/shared';
import type { KYCJourneyValues } from './shared';
import useProceedWithCustomerDevice from './useProceedWithCustomerDevice';

const useProceedWithCustomerDeviceButton = (
    kycPresets: KycFieldSpecsFragment[],
    state: State<StandardApplicationState> | JourneyState | State<TestDriveJourneyState>,
    dispatch:
        | Dispatch<Action<StandardApplicationState>>
        | Dispatch<JourneyAction>
        | Dispatch<Action<TestDriveJourneyState>>
) => {
    const { application } = state;
    const { notification } = useThemeComponents();

    const { setErrors, setTouched } = useFormikContext<KYCJourneyValues | TestDriveKYCJourneyValues>();

    const { t } = useTranslation('customerDetails');

    const user = useAccount(true);
    const proceedWithCustomerDevices = useProceedWithCustomerDevice();

    const showRemoteFlowButtonInKYCPage = useMemo(() => {
        if (
            application.__typename === 'StandardApplication' &&
            application.module.__typename === 'StandardApplicationModule'
        ) {
            return application.module.showRemoteFlowButtonInKYCPage;
        }
        if (
            application.__typename === 'FinderApplication' &&
            application.module.__typename === 'FinderApplicationPrivateModule'
        ) {
            return application.module.showRemoteFlowButtonInKYCPage;
        }
        if (
            application.__typename === 'EventApplication' &&
            application.module.__typename === 'EventApplicationModule'
        ) {
            return application.module.showRemoteFlowButtonInKYCPage;
        }

        return null;
    }, [application]);

    const proceedWithCustomerValidator = useProceedWithCustomerValidators(kycPresets, 'customer.fields');
    const proceedWithCustomerValidations = useMemo(
        () => validators.compose(proceedWithCustomerValidator),
        [proceedWithCustomerValidator]
    );
    const proceedWithCustomerValidate = useValidator(proceedWithCustomerValidations);

    const proceedWithCustomerButton = useMemo((): boolean => {
        const keys = kycPresets.map(preset => preset.key);

        if (
            application.__typename !== 'LaunchpadApplication' &&
            (keys.includes(LocalCustomerFieldKey.FirstName) ||
                keys.includes(LocalCustomerFieldKey.LastName) ||
                keys.includes(LocalCustomerFieldKey.FullName)) &&
            keys.includes(LocalCustomerFieldKey.Email) &&
            keys.includes(LocalCustomerFieldKey.Phone)
        ) {
            return !!showRemoteFlowButtonInKYCPage && user && !application.withCustomerDevice;
        }

        return false;
    }, [kycPresets, showRemoteFlowButtonInKYCPage, user, application]);

    const proceedWithCustomer = useCallback(
        async (currentValues: KYCJourneyValues | TestDriveKYCJourneyValues) => {
            try {
                const customKYCPresets = {};
                const customKYCFieldKeys = Object.keys(currentValues.customer.fields);

                // extract mandatory fields from KYC preset
                customKYCFieldKeys.forEach((key: LocalCustomerFieldKey) => {
                    if (
                        [
                            LocalCustomerFieldKey.FirstName,
                            LocalCustomerFieldKey.LastName,
                            LocalCustomerFieldKey.FullName,
                            LocalCustomerFieldKey.Email,
                            LocalCustomerFieldKey.Phone,
                        ].includes(key)
                    ) {
                        customKYCPresets[key] = currentValues.customer.fields[key];
                    }
                });

                const validationResult = proceedWithCustomerValidate(currentValues);

                if (!validationResult) {
                    notification.loading({
                        content: t('customerDetails:messages.sendEmailToCustomer'),
                        duration: 0,
                        key: 'primary',
                    });

                    await proceedWithCustomerDevices(state.token, customKYCPresets);

                    notification.destroy('primary');

                    dispatch({
                        type: 'goTo',
                        stage: JourneyStage.ConfirmEmailSend,
                    });
                } else {
                    // Set errors based on validator, since it's the same format, we can just use it
                    setErrors(validationResult);

                    // Set touched to true for all error fields
                    // although it should be in boolean instead of string, but formik can handle it
                    // and make sure to set the validate parameter to be false
                    // so it won't conflict with applicant validation
                    setTouched(validationResult, false);
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                }
            }
        },
        [
            proceedWithCustomerValidate,
            notification,
            t,
            proceedWithCustomerDevices,
            state.token,
            dispatch,
            setErrors,
            setTouched,
        ]
    );

    return useMemo(
        () => ({
            proceedWithCustomerButton,
            proceedWithCustomer,
        }),
        [proceedWithCustomer, proceedWithCustomerButton]
    );
};

export default useProceedWithCustomerDeviceButton;
