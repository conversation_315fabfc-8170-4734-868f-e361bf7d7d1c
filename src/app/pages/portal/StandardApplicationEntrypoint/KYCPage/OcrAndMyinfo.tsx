import { ApolloClient, NormalizedCacheObject, useApolloClient } from '@apollo/client';
import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import styled, { css } from 'styled-components';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import {
    GetMyInfoAuthorizeUrlDocument,
    GetMyInfoAuthorizeUrlQuery,
    GetMyInfoAuthorizeUrlQueryVariables,
} from '../../../../api/queries/getMyInfoAuthorizeUrl';
import { CustomerKind, LayoutType } from '../../../../api/types';
import MyInfo from '../../../../components/MyInfo';
import { useRouter } from '../../../../components/contexts/shared';
import Ocr, { useOcrDetectedHandler } from '../../../../components/ocr';
import { setMyInfoTemporaryValues } from '../../EventApplicationEntrypoint/ApplicantForm/CustomerDetails/utils';
import { State, StandardApplicationState } from '../Journey/shared';
import useUpdateStandardApplicationConfiguration from '../useUpdateStandardApplicationConfiguration';
import type { KYCJourneyValues } from './shared';

const Container = styled(Col)`
    ${props =>
        props.theme.layoutType === LayoutType.PorscheV3 &&
        css`
            display: flex;
            flex-direction: column;
            gap: 24px;
        `}
`;

type OcrAndMyinfoProps = {
    state: State<StandardApplicationState>;
    application: StandardApplicationState;
    kycPresets: KycFieldSpecsFragment[];
    endpoint: StandardApplicationEntrypointContextDataFragment;
    onLoading: (isLoading: boolean) => void;
    withMyInfo: boolean;
    setWithMyInfo: (value: boolean) => void;
    topDivider?: React.ReactNode;
};

const OcrAndMyinfo = ({
    state,
    application,
    endpoint,
    kycPresets,
    onLoading,
    withMyInfo,
    setWithMyInfo,
    topDivider = null,
}: OcrAndMyinfoProps) => {
    const client = useApolloClient() as ApolloClient<NormalizedCacheObject>;
    const router = useRouter();
    const { values } = useFormikContext<KYCJourneyValues>();
    const updateStandardApplicationConfiguration = useUpdateStandardApplicationConfiguration();
    const { token } = state;

    const myInfoEnabled = !!endpoint.applicationModule.myInfoSettingId;
    const { isOcrEnabled } = endpoint.applicationModule;
    const showMyInfoContent = !withMyInfo && myInfoEnabled;
    const showOcr = !withMyInfo && isOcrEnabled;

    const myInfoOnClick = useCallback(async () => {
        await updateStandardApplicationConfiguration(token, values);

        // Persist appointment data in local storage
        setMyInfoTemporaryValues(application.id, {
            appointment: values.appointment,
        });

        const { data } = await client.query<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>({
            query: GetMyInfoAuthorizeUrlDocument,
            variables: {
                applicationId: application.id,
                routerId: router.id,
                endpointId: endpoint.id,
                customerKind: CustomerKind.Local,
                withTradeIn: values.configuration.tradeIn,
                withTestDrive: values.configuration.testDrive,
            },
            fetchPolicy: 'no-cache',
        });

        if (data?.authorizeUrl) {
            globalThis.location.href = data.authorizeUrl;
        }
    }, [updateStandardApplicationConfiguration, token, values, client, application.id, router.id, endpoint.id]);

    const onOcrDetected = useOcrDetectedHandler(kycPresets);

    const tradeInVehicle = useMemo(
        () => ({
            withTradeIn: values.configuration.tradeIn,
            withTestDrive: values.configuration.testDrive,
            name: 'tradeInVehicle',
        }),
        [values]
    );

    if (!showOcr && !showMyInfoContent) {
        return null;
    }

    return (
        <>
            {topDivider}
            <Container span={24}>
                {showMyInfoContent && (
                    <MyInfo
                        applicationId={application.id}
                        customerFieldPrefix="customer.fields"
                        customerKind={CustomerKind.Local}
                        onClick={myInfoOnClick}
                        onLoading={onLoading}
                        setWithMyInfo={setWithMyInfo}
                        tradeInVehicle={tradeInVehicle}
                    />
                )}
                {true && <Ocr onDetected={onOcrDetected} />}
            </Container>
        </>
    );
};

export default OcrAndMyinfo;
