import { useApolloClient } from '@apollo/client';
import { useCallback } from 'react';
import {
    UpdateStandardApplicationConfigurationDocument,
    UpdateStandardApplicationConfigurationMutation,
    UpdateStandardApplicationConfigurationMutationVariables,
} from '../../../api/mutations/updateStandardApplicationConfiguration';
import type { KYCJourneyValues } from './KYCPage/shared';

const useUpdateStandardApplicationConfiguration = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (token: string, values: KYCJourneyValues) => {
            const { data } = await apolloClient.mutate<
                UpdateStandardApplicationConfigurationMutation,
                UpdateStandardApplicationConfigurationMutationVariables
            >({
                mutation: UpdateStandardApplicationConfigurationDocument,
                variables: {
                    token,
                    configuration: values.configuration,
                },
            });

            return data.result;
        },
        [apolloClient]
    );
};

export default useUpdateStandardApplicationConfiguration;
