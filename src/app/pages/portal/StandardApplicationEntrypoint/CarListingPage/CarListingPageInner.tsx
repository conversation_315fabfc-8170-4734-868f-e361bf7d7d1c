import { Typography } from 'antd';
import { useCallback, useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FinanceProductSortingField, LayoutType, SortingOrder } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import breakpoints from '../../../../utilities/breakpoints';
import useLocalFinanceProducts, { ApplicationModules } from '../../../shared/CIPage/useLocalFinanceProducts';
import { usePersistStandardJourneyValues } from '../CarDetailsPage/usePersistStandardJourneyValues';
import { StyledJourneyToolbar } from '../styledComponents';
import CarFilterButton from './CarFilter/CarFilterButton';
import CarListingContent from './CarListingContent';
import CarListingPageDisclaimer from './CarListingPageDisclaimer';
import { VehicleCount } from './CarSelection/ui';
import useFilteredVariants from './CarSelection/useFilteredVariants';
import { useCarListingState } from './useCarListingState';
import CompareIcon from '../../../../../../public/compare.svg';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0;
    margin: 0;

    @media screen and (min-width: ${breakpoints.md}) {
        flex-direction: row;
        justify-content: space-between;
        margin: ${props => (props.theme.layoutType === LayoutType.PorscheV3 ? '0' : '0 36px')};
    }
`;

export const HeaderButtonContainer = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;

    // V2 style
    & .ant-btn.ant-btn-link {
        padding: 0;
        height: 2rem;
        font-size: var(--button-font-size, 1rem);
        margin-left: 20px;

        > span.anticon-mail {
            height: var(--button-font-size);
        }

        > svg {
            fill: currentColor;
        }

        > svg + span {
            margin-left: 8px;
            margin-top: 0;
        }
    }

    // V3 style
    & .p-button {
        margin-left: 20px;
    }
`;

const CarListingTitle = ({ title }: { title: string }) => {
    const { layout } = useRouter();

    if (layout.__typename !== 'PorscheV3Layout') {
        return <Typography.Title level={5}>{title}</Typography.Title>;
    }

    // eslint-disable-next-line react/jsx-no-useless-fragment
    return <>{title}</>;
};

const useHasFinanceProduct = (applicationModule: ApplicationModules, dealerIds: string[]) => {
    const { financeProducts } = useLocalFinanceProducts({
        module: applicationModule,
        sort: { field: FinanceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds,
    });

    return useCallback(
        (variantSuiteId: string) => financeProducts?.some(fp => fp.allVariantSuiteIds.includes(variantSuiteId)),
        [financeProducts]
    );
};

type CarListingPageInnerProps = {
    dealerIds: string[];
};
const CarListingPageInner = ({ dealerIds }: CarListingPageInnerProps) => {
    const { t } = useTranslation(['carList', 'comparison']);
    const { Button, StandardLayout } = useThemeComponents();
    const { layout } = useRouter();

    const { remove: removePersistedStandardJourneyValue } = usePersistStandardJourneyValues();

    const [vehicleIds, setVehicleIds] = useState<string[]>([]);
    const [isComparisonSelection, setIsComparisonSelection] = useState(false);
    // header
    const navigate = useNavigate();

    const title = isComparisonSelection ? t('comparison:title') : t('carList:title');

    useEffect(() => {
        removePersistedStandardJourneyValue();
    }, [removePersistedStandardJourneyValue]);

    const {
        meta: { endpoint },
        actions: { setOpenFilterDrawer },
        state: { openFilterDrawer },
    } = useCarListingState();

    const variants = useFilteredVariants();

    const extra = useMemo(() => {
        // hide compare button when it's comparison selection
        if (isComparisonSelection) {
            return null;
        }

        return (
            <HeaderButtonContainer>
                <Button
                    icon={<CompareIcon fill="var(--ant-primary-color)" />}
                    onClick={() => setIsComparisonSelection(true)}
                    porscheFallbackIconSource="compare"
                    type={layout?.__typename === 'PorscheV3Layout' ? 'primary' : 'link'}
                >
                    {t('carList:compareButton')}
                </Button>
                {layout?.__typename === 'PorscheV3Layout' && (
                    <CarFilterButton openFilterDrawer={openFilterDrawer} setOpenFilterDrawer={setOpenFilterDrawer} />
                )}
            </HeaderButtonContainer>
        );
    }, [Button, isComparisonSelection, layout?.__typename, openFilterDrawer, setOpenFilterDrawer, t]);

    const compare = useCallback(() => {
        navigate('./comparison', { state: { vehicleIds } });
    }, [navigate, vehicleIds]);

    const cancel = useCallback(() => {
        setIsComparisonSelection(false);
        setVehicleIds([]);
    }, []);

    const hasFinanceProduct = useHasFinanceProduct(endpoint.applicationModule, dealerIds);

    return (
        <StandardLayout
            extra={extra}
            headerHeadingAlign={layout?.__typename === 'PorscheV3Layout' ? 'flex-start' : undefined}
            title={<CarListingTitle title={title} />}
        >
            {layout?.__typename === 'PorscheV3Layout' && (
                <VehicleCount>{t('carList:noOfAvailable.vehicle', { count: variants.length })}</VehicleCount>
            )}
            <Container>
                <CarListingContent
                    hasFinanceProduct={hasFinanceProduct}
                    isComparisonSelection={isComparisonSelection}
                    setVehicleIds={setVehicleIds}
                    vehicleIds={vehicleIds}
                />
            </Container>
            {dealerIds.length === 1 && (
                <CarListingPageDisclaimer applicationModule={endpoint.applicationModule} dealerIds={dealerIds} />
            )}
            {isComparisonSelection && (
                <StyledJourneyToolbar>
                    <Button
                        className="cancel-left-button"
                        onClick={cancel}
                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        style={{ marginRight: 'auto' }}
                        type="tertiary"
                    >
                        {t('comparison:action.cancel')}
                    </Button>
                    <Button
                        disabled={vehicleIds.length < 2}
                        onClick={compare}
                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="primary"
                    >
                        {t('comparison:action.compare')}
                    </Button>
                </StyledJourneyToolbar>
            )}
        </StandardLayout>
    );
};

export default CarListingPageInner;
