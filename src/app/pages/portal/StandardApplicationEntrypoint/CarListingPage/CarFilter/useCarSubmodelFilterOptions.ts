import { uniqBy } from 'lodash/fp';
import { useMemo } from 'react';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import type { Variants, CarListingContext } from '../shared';

const useCarSubmodelFilterOptions = (state: CarListingContext['state']) => {
    const { variants: allVariants, selectedModels } = state;
    const translateString = useTranslatedString();

    const variants = useMemo((): Variants => {
        if (!selectedModels.length) {
            return allVariants;
        }

        return allVariants.filter(variant => selectedModels.includes(variant.modelId));
    }, [allVariants, selectedModels]);

    return useMemo(() => {
        if (variants.length === 0) {
            return [];
        }

        return uniqBy(
            submodel => submodel?.id,
            variants.map(variant => variant.submodel)
        )
            .filter(Boolean)
            .sort((a, b) => a.order - b.order)
            .map(submodel => ({
                label: translateString(submodel.name),
                value: submodel.id,
            }));
    }, [variants, translateString]);
};

export default useCarSubmodelFilterOptions;
