import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../../components/contexts/shared';
import { useCarListingState } from '../useCarListingState';
import CarFilterButton from './CarFilterButton';
import CarFilterContent from './CarFilterContent';
import { FilterButtonContainer, SidePanel, Source } from './ui';

export type CarFilterOnLargeScreenContainerProps = {
    openFilterDrawer: boolean;
    setOpenFilterDrawer: (value: boolean) => void;
    children: ReactElement;
    source?: Source;
    showFilterDrawer: boolean;
};

export const CarFilterOnLargeScreenContainer = ({
    openFilterDrawer,
    setOpenFilterDrawer,
    children,
    source = 'default',
    showFilterDrawer = true,
}: CarFilterOnLargeScreenContainerProps) => {
    const { t } = useTranslation(['core']);
    const { layout } = useRouter();

    if (!showFilterDrawer) {
        return null;
    }

    return (
        <SidePanel $direction={t('core:orientation') as 'ltr' | 'rtl'} $isPanelVisible={openFilterDrawer}>
            {layout?.__typename !== 'PorscheV3Layout' && (
                <FilterButtonContainer $showFilter={openFilterDrawer} $source={source}>
                    <CarFilterButton openFilterDrawer={openFilterDrawer} setOpenFilterDrawer={setOpenFilterDrawer} />
                </FilterButtonContainer>
            )}
            {children}
        </SidePanel>
    );
};

const CarFilterOnLargeScreen = () => {
    const { t } = useTranslation(['core']);
    const { state, actions } = useCarListingState();

    const direction = t('core:orientation');

    const bordered = direction === 'rtl' ? { rightBordered: true } : { leftBordered: true };

    return (
        <CarFilterOnLargeScreenContainer
            openFilterDrawer={state.openFilterDrawer}
            setOpenFilterDrawer={actions.setOpenFilterDrawer}
            showFilterDrawer={state.showFilterDrawer}
        >
            {state.openFilterDrawer && <CarFilterContent actions={actions} state={state} {...bordered} />}
        </CarFilterOnLargeScreenContainer>
    );
};

export default CarFilterOnLargeScreen;
