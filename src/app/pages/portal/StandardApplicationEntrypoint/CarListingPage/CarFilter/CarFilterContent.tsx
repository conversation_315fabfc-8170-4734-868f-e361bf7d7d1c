import type { CarListingContext } from '../shared';
import CarModelFilter from './CarModelFilter';
import CarSubmodelFilter from './CarSubmodelFilter';
import MonthlyPaymentFilter from './MonthlyPaymentFilter';
import { FilterContainer, FilterGroupContainer } from './ui';

type CarFilterContentProps = {
    leftBordered?: boolean;
    rightBordered?: boolean;
    state: CarListingContext['state'];
    actions: CarListingContext['actions'];
};

const CarFilterContent = ({ leftBordered, rightBordered, state, actions }: CarFilterContentProps) => (
    <FilterContainer $leftBordered={leftBordered} $rightBordered={rightBordered}>
        <FilterGroupContainer>
            <CarModelFilter actions={actions} state={state} />
        </FilterGroupContainer>
        <FilterGroupContainer>
            <CarSubmodelFilter actions={actions} state={state} />
        </FilterGroupContainer>
        <FilterGroupContainer>
            <MonthlyPaymentFilter actions={actions} state={state} />
        </FilterGroupContainer>
    </FilterContainer>
);

export default CarFilterContent;
