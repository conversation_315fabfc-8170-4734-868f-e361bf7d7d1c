import isEqual from 'fast-deep-equal';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import type { CarListingContext } from '../shared';
import FilterGroup from './FilterGroup';
import useMonthlyPaymentFilterOptions from './useMonthlyPaymentFilterOptions';

export type MonthlyPaymentFilterProps = {
    state: CarListingContext['state'];
    actions: CarListingContext['actions'];
};

const MonthlyPaymentFilter = ({ state, actions }: MonthlyPaymentFilterProps) => {
    const { t } = useTranslation(['carList']);
    const options = useMonthlyPaymentFilterOptions(state);

    const previousOptionsRef = useRef(options);

    useEffect(() => {
        if (!isEqual(previousOptionsRef.current, options)) {
            // reset selected models because option changed
            actions.setSelectedMonthlyPayments([]);
            previousOptionsRef.current = options;
        }
    }, [options, actions, previousOptionsRef]);

    if (!options?.length || options.length === 1) {
        return null;
    }

    return (
        <FilterGroup
            onChange={actions.setSelectedMonthlyPayments}
            options={options}
            title={t('carList:filter.monthlyPaymentTitle')}
            value={state.selectedMonthlyPayments}
        />
    );
};

export default MonthlyPaymentFilter;
