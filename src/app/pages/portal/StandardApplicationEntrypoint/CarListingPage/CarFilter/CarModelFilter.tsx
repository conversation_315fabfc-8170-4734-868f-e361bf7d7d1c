import isEqual from 'fast-deep-equal';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import type { CarListingContext } from '../shared';
import FilterGroup from './FilterGroup';
import useCarModelFilterOptions from './useCarModelFilterOptions';

export type CarModelFilterProps = {
    state: CarListingContext['state'];
    actions: CarListingContext['actions'];
};

const CarModelFilter = ({ state, actions }: CarModelFilterProps) => {
    const { t } = useTranslation(['carList']);

    const options = useCarModelFilterOptions(state);
    const previousOptionsRef = useRef(options);

    useEffect(() => {
        if (!isEqual(previousOptionsRef.current, options)) {
            // reset selected models because option changed
            actions.setSelectedModels([]);
            previousOptionsRef.current = options;
        }
    }, [options, actions, previousOptionsRef]);

    if (!options?.length || options.length === 1) {
        return null;
    }

    return (
        <FilterGroup
            onChange={actions.setSelectedModels}
            options={options}
            title={t('carList:filter.carModelTitle')}
            value={state.selectedModels}
        />
    );
};

export default CarModelFilter;
