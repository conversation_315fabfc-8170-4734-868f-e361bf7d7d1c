import { uniqBy } from 'lodash/fp';
import { useMemo } from 'react';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import type { CarListingContext } from '../shared';

const useCarModelFilterOptions = (state: CarListingContext['state']) => {
    const translateString = useTranslatedString();
    const { variants: allVariants } = state;

    return useMemo(
        () =>
            uniqBy(
                model => model.id,
                allVariants.map(variant => variant.model)
            )
                .sort((a, b) => a.order - b.order)
                .map(model => ({
                    label: translateString(model.name),
                    value: model.id,
                })),
        [allVariants, translateString]
    );
};

export default useCarModelFilterOptions;
