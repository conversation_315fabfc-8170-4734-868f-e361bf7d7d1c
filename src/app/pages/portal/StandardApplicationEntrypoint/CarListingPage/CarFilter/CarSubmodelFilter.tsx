import isEqual from 'fast-deep-equal';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import type { CarListingContext } from '../shared';
import FilterGroup from './FilterGroup';
import useCarSubmodelFilterOptions from './useCarSubmodelFilterOptions';

export type CarSubmodelFilterProps = {
    state: CarListingContext['state'];
    actions: CarListingContext['actions'];
};

const CarSubmodelFilter = ({ state, actions }: CarSubmodelFilterProps) => {
    const { t } = useTranslation(['carList']);

    const options = useCarSubmodelFilterOptions(state);

    const previousOptionsRef = useRef(options);

    useEffect(() => {
        if (!isEqual(previousOptionsRef.current, options)) {
            // reset selected models because option changed
            actions.setSelectedSubmodels([]);
            previousOptionsRef.current = options;
        }
    }, [options, actions, previousOptionsRef]);

    if (!options?.length || options.length === 1) {
        return null;
    }

    return (
        <FilterGroup
            onChange={actions.setSelectedSubmodels}
            options={options}
            title={t('carList:filter.carSubmodelTitle')}
            value={state.selectedSubmodels}
        />
    );
};

export default CarSubmodelFilter;
