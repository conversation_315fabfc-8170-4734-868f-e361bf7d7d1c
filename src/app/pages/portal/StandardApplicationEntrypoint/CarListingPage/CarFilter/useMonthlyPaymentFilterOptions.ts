import { CheckboxOptionType } from 'antd';
import { isEmpty, isNumber, max, min } from 'lodash/fp';
import { useMemo } from 'react';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import type { Variants, CarListingContext } from '../shared';

const stepValue = (num: number) => {
    if (Math.abs(num) >= 1) {
        return 1;
    }

    return 1 / 10 ** Math.ceil(Math.abs(Math.log10(num)));
};

const useMonthlyPaymentFilterOptions = (state: CarListingContext['state']) => {
    const { formatAmountWithCurrency } = useCompanyFormats();
    const { variants: allVariants, selectedModels, selectedSubmodels } = state;

    const filteredVariants = useMemo((): Variants => {
        const filterdBySelectedModels = !selectedModels?.length
            ? allVariants
            : allVariants.filter(variant => selectedModels.includes(variant.modelId));

        return !selectedSubmodels?.length
            ? filterdBySelectedModels
            : allVariants.filter(variant => selectedSubmodels.includes(variant.submodelId));
    }, [allVariants, selectedModels, selectedSubmodels]);

    return useMemo(() => {
        if (filteredVariants.length === 0) {
            return [];
        }

        // how many options do we want for the monthly payment
        const optionCount = filteredVariants.length > 5 ? 3 : 2;

        // get the minimum and maximum monthly payments
        const amounts = filteredVariants.map(variant => variant.monthlyInstalment);

        if (isEmpty(amounts)) {
            return [];
        }

        const monthlyPayments: number[] = [...new Set(amounts.filter(value => isNumber(value)).filter(Boolean))];

        if (isEmpty(monthlyPayments)) {
            return [];
        }

        if (monthlyPayments.length === 1) {
            return [
                {
                    value: `${monthlyPayments[0]}-${monthlyPayments[0]}`,
                    label: `${formatAmountWithCurrency(monthlyPayments[0])} - ${formatAmountWithCurrency(
                        monthlyPayments[0]
                    )}`,
                },
            ];
        }

        const minPayment = min(monthlyPayments);
        const maxPayment = max(monthlyPayments);

        // divide it by the number of options
        const range = (maxPayment - minPayment) / optionCount;

        // create the option array
        const options: CheckboxOptionType[] = [];

        // loop to create options
        let nextMinimum = minPayment;
        for (let index = 0; index < optionCount; index++) {
            // compute min & max
            const minValue = nextMinimum;
            const maxValue = index === optionCount - 1 ? maxPayment : minValue + range;

            // prepare the next minimum value
            // nextMinimum should not be greater than next maxValue or maxPayment
            nextMinimum = maxValue + stepValue(range);

            // build option
            options.push({
                value: `${minValue}-${maxValue}`,
                label: `${formatAmountWithCurrency(minValue)} - ${formatAmountWithCurrency(maxValue)}`,
            });
        }

        return options;
    }, [filteredVariants, formatAmountWithCurrency]);
};

export default useMonthlyPaymentFilterOptions;
