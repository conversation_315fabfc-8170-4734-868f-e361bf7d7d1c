import { useMemo } from 'react';
import type { Variants } from '../shared';
import { useCarListingState } from '../useCarListingState';

const useFilteredVariants = (): Variants => {
    const { state } = useCarListingState();
    const { variants, selectedModels, selectedSubmodels, selectedMonthlyPayments } = state;

    const filteredVariants = useMemo((): Variants => {
        const filterdBySelectedModels = !selectedModels?.length
            ? variants
            : variants.filter(variant => selectedModels.includes(variant.modelId));

        return !selectedSubmodels?.length
            ? filterdBySelectedModels
            : variants.filter(variant => selectedSubmodels.includes(variant.submodelId));
    }, [variants, selectedModels, selectedSubmodels]);

    return useMemo((): Variants => {
        if (!selectedMonthlyPayments.length) {
            return filteredVariants;
        }

        const testFunctions = selectedMonthlyPayments.map(value => {
            const [min, max] = value.split('-').map(value => parseFloat(value));

            return (price: number) => min <= price && max >= price;
        });

        return filteredVariants.filter(variant => testFunctions.some(test => test(variant.monthlyInstalment)));
    }, [filteredVariants, selectedMonthlyPayments]);
};

export default useFilteredVariants;
