import { Row } from 'antd';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../../components/contexts/shared';
import type { Variants } from '../shared';
import { useCarListingState } from '../useCarListingState';
import CarSelectionNoResult from './CarSelectionNoResult';
import { CarSelectionContainer, Title } from './ui';
import useFilteredVariants from './useFilteredVariants';

type CarSelectionContentProps = {
    children: (variants: Variants) => any;
};

const CarSelectionContent = ({ children }: CarSelectionContentProps) => {
    const { t } = useTranslation(['carList']);
    const { state } = useCarListingState();
    const variants = useFilteredVariants();
    const { layout } = useRouter();

    if (variants.length === 0) {
        return (
            <CarSelectionContainer $showFilter={state.openFilterDrawer}>
                <CarSelectionNoResult />
            </CarSelectionContainer>
        );
    }

    return (
        <CarSelectionContainer $showFilter={state.openFilterDrawer}>
            {layout.__typename !== 'PorscheV3Layout' && (
                <Title level={5}>{t('carList:noOfAvailable.vehicle', { count: variants.length })}</Title>
            )}
            <Row gutter={[24, 36]} style={{ marginTop: '15px' }}>
                {children(variants)}
            </Row>
        </CarSelectionContainer>
    );
};

export default CarSelectionContent;
