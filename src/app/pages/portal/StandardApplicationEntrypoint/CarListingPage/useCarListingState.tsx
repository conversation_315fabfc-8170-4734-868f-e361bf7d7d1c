import { uniq } from 'lodash/fp';
import { createContext, useContext, useEffect, useMemo, useReducer } from 'react';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
// eslint-disable-next-line max-len
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { useListLocalVariantsForSelectionQuery } from '../../../../api/queries/listLocalVariantsForSelection';
import { LocalVariantSortingField, Purpose, SortingOrder } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useRouter } from '../../../../components/contexts/shared';
import useCarModelFilterOptions from './CarFilter/useCarModelFilterOptions';
import useCarSubmodelFilterOptions from './CarFilter/useCarSubmodelFilterOptions';
import useMonthlyPaymentFilterOptions from './CarFilter/useMonthlyPaymentFilterOptions';
import type { ActionMethods, CarListingContext, State, Variants } from './shared';

type Action =
    | { type: 'setSelectedModels'; models: string[] }
    | { type: 'setSelectedSubmodels'; submodels: string[] }
    | { type: 'setSelectedMonthlyPayments'; monthlyPayments: string[] }
    | { type: 'setShowFilterDrawer'; showFilterDrawer: boolean }
    | { type: 'setOpenFilterDrawer'; openFilterDrawer: boolean }
    | { type: 'setVariants'; variants: Variants };

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setSelectedModels':
            return { ...state, selectedModels: action.models };

        case 'setSelectedSubmodels':
            return { ...state, selectedSubmodels: action.submodels };

        case 'setSelectedMonthlyPayments':
            return { ...state, selectedMonthlyPayments: action.monthlyPayments };

        case 'setShowFilterDrawer':
            return { ...state, showFilterDrawer: action.showFilterDrawer };

        case 'setOpenFilterDrawer':
            return { ...state, openFilterDrawer: action.openFilterDrawer };

        case 'setVariants':
            return { ...state, variants: action.variants };

        default:
            return state;
    }
};

const initialState: State = {
    variants: [],
    selectedModels: [],
    selectedSubmodels: [],
    selectedMonthlyPayments: [],
    showFilterDrawer: true,
    openFilterDrawer: true,
};

const Context = createContext<CarListingContext | null>(null);

export const useCarListingState = () => {
    const context = useContext(Context);

    if (!context) {
        throw new Error('useCarListingState must be used within a CarListingStateProvider');
    }

    return context;
};

const showFilter = <T extends {}>(filters: T[]) => filters && uniq(filters)?.filter(Boolean).length > 1;

const useShowFilterDrawer = (state: State) => {
    const models = useCarModelFilterOptions(state);
    const subModels = useCarSubmodelFilterOptions(state);
    const monthlyInstalments = useMonthlyPaymentFilterOptions(state);

    return [models, subModels, monthlyInstalments].some(showFilter);
};

export type CarListingStateProviderProps = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    children: JSX.Element | React.ReactNode;
    dealerIds: string[];
};

const CarListingStateProvider = ({ children, endpoint, dealerIds }: CarListingStateProviderProps) => {
    const { layout } = useRouter();

    const enhancedInitialState = useMemo(
        () => ({
            ...initialState,
            showFilterDrawer: layout?.__typename !== 'PorscheV3Layout',
            openFilterDrawer: layout?.__typename !== 'PorscheV3Layout',
        }),
        [layout]
    );

    const [state, dispatch] = useReducer(reducer, enhancedInitialState);

    const actions = useMemo(
        (): ActionMethods => ({
            setSelectedModels: models => dispatch({ type: 'setSelectedModels', models }),
            setSelectedSubmodels: submodels => dispatch({ type: 'setSelectedSubmodels', submodels }),
            setSelectedMonthlyPayments: (monthlyPayments: string[]) =>
                dispatch({ type: 'setSelectedMonthlyPayments', monthlyPayments }),
            setOpenFilterDrawer: (open: boolean) => dispatch({ type: 'setOpenFilterDrawer', openFilterDrawer: open }),
        }),
        [dispatch]
    );

    const context = useMemo(
        (): CarListingContext => ({ state, actions, meta: { dealerIds, endpoint } }),
        [state, actions, dealerIds, endpoint]
    );

    // get all local variant the for the given module ID
    const { data, loading } = useListLocalVariantsForSelectionQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                moduleId: endpoint.applicationModule.vehicleModuleId,
                purpose: Purpose.Production,
                applicationModuleId: endpoint.applicationModule.id,
                dealerIds,
            },
            sort: { field: LocalVariantSortingField.ModelOrderAndVariantOrder, order: SortingOrder.Asc },
            bankModuleId: endpoint.applicationModule.bankModuleId,
            applicationModuleIds: [endpoint.applicationModule.id],
            dealerId: dealerIds.length > 1 ? null : dealerIds[0],
        },
        // Make sure that it load when dealer is ready
        skip: dealerIds.length === 0,
    });

    // ensure we have an array pf items
    const variants = useMemo(() => data?.list?.items || [], [data]);

    useEffect(() => {
        dispatch({ type: 'setVariants', variants });
    }, [variants, dispatch]);

    const showFilter = useShowFilterDrawer(state);
    useEffect(() => {
        dispatch({ type: 'setShowFilterDrawer', showFilterDrawer: showFilter });
    }, [showFilter]);

    const isLoading = !data && loading;

    return <Context.Provider value={context}>{isLoading ? <PortalLoadingElement /> : children}</Context.Provider>;
};

export default CarListingStateProvider;
