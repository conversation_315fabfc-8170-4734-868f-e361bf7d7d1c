import { isNil } from 'lodash/fp';
import { Dispatch, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { JourneyStage, type Action, type State, type StandardApplicationState } from './Journey/shared';

const useStandardLayout = (
    dispatch: Dispatch<Action<StandardApplicationState>>,
    state: State<StandardApplicationState>
): {
    title: string;
    onBack: () => void;
    shouldShowBackIcon?: boolean;
    /**
     * This is used to determine whether to wrap the content page with JourneyLayout. default is true.
     */
    useJourneyLayout?: boolean;
} => {
    const { t } = useTranslation([
        'customerDetails',
        'journey',
        'paymentDetails',
        'confirmRemoteFlow',
        'otpPage',
        'appointmentPage',
    ]);
    const navigate = useNavigate();

    const { application, stage: journeyStage } = state;
    const { draftFlow } = application;

    const shouldShowBackIcon = !application.withCustomerDevice;
    const isApplyingFromDetails =
        !isNil(draftFlow.isApplyingForFinanceCompleted) ||
        !isNil(draftFlow.isApplyingForInsuranceCompleted) ||
        !isNil(draftFlow.isApplyingForReservationCompleted);

    const isApplyingFromApplyNew =
        draftFlow.isReceived &&
        (!draftFlow.isApplyNewForFinancingReceived ||
            !draftFlow.isApplyNewForInsuranceReceived ||
            !draftFlow.isApplyNewForAppointmentReceived);

    const useJourneyLayout = true;

    const canGoBackInKYC = shouldShowBackIcon && !isApplyingFromDetails && !isApplyingFromApplyNew;

    const vehicleId = application.vehicle.__typename === 'LocalVariant' ? application.vehicle.id : '';

    return useMemo(() => {
        switch (journeyStage) {
            case JourneyStage.ApplicantKYC:
                return {
                    title: t('customerDetails:title'),
                    // use concrete url instead of `-1`
                    // when redirected back from Myinfo, `-1` points to the redirection link
                    onBack: canGoBackInKYC && vehicleId ? () => navigate(`../details/${vehicleId}`) : () => {},
                    shouldShowBackIcon: canGoBackInKYC,
                    useJourneyLayout,
                };

            case JourneyStage.GuarantorKYC:
                return {
                    title: t('customerDetails:title'),
                    onBack: () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC }),
                    shouldShowBackIcon: true,
                    useJourneyLayout,
                };

            case JourneyStage.Deposit:
                return {
                    title: t('paymentDetails:title'),
                    onBack: () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC }),
                    shouldShowBackIcon,
                    useJourneyLayout,
                };

            case JourneyStage.ConfirmEmailSend:
                return {
                    title: t('confirmRemoteFlow:title'),
                    onBack: () => {},
                    shouldShowBackIcon,
                    useJourneyLayout: false,
                };

            case JourneyStage.Otp:
                return {
                    title: t('otpPage:title'),
                    onBack: () => navigate('..'),
                    shouldShowBackIcon,
                    useJourneyLayout,
                };

            default:
                return {
                    title: '',
                    onBack: () => {},
                    shouldShowBackIcon,
                };
        }
    }, [canGoBackInKYC, dispatch, journeyStage, navigate, shouldShowBackIcon, t, useJourneyLayout, vehicleId]);
};

export default useStandardLayout;
