import { LeftOutlined } from '@ant-design/icons';
import { PageContainerProps } from '@ant-design/pro-layout';
import { Col, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { ComponentType, PropsWithChildren, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import Form from '../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import useAssetCondition from '../../../../utilities/useAssetCondition';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import DealerInfo from '../../ConfiguratorApplicationEntrypoint/DealerInfo';
import ConfiguratorVehicleInfo from '../../ConfiguratorApplicationEntrypoint/ThankYou/VehicleInfo';
import calculateTotalPrice from '../../ConfiguratorApplicationEntrypoint/helper';
// eslint-disable-next-line max-len
import { retrieveAppointmentModuleFromEntrypoint } from '../../EventApplicationEntrypoint/ApplicantForm/AppointmentDetailsSection';
import Content from '../../EventApplicationEntrypoint/ThankYou/Content';
import EventVehicleInfo from '../../EventApplicationEntrypoint/ThankYou/VehicleInfo';
import FinderVehicleInfo from '../../FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInfo';
import { JourneyStage } from '../Journey/shared';
import { NextButton } from '../shared/JourneyButton';
import JourneyToolbar from '../shared/JourneyToolbar';
import { FooterButtonContainer, StyledBackButton } from '../styledComponents';
import AppointmentDatepicker from './AppointmentDatepicker';
import type { AppointmentValues } from './appointmentValues';
import type { AllowedApplicationForAppointment, AppointmentPageProps } from './shared';

const StyledCol = styled(Col)`
    width: 100%;
`;

const StyledDealerDiv = styled.div`
    padding-top: 20px;
`;

type VehicleInfoProps = {
    application: AllowedApplicationForAppointment;
};

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

const StandardApplicationVehicleInfo = ({ application }: VehicleInfoProps) => {
    const translatedString = useTranslatedString();
    if (application.__typename !== 'StandardApplication') {
        return null;
    }
    const { vehicle, financing, dealer } = application;

    const vehicleData = useMemo(() => {
        switch (vehicle.__typename) {
            case 'LocalVariant':
                return {
                    make: translatedString(
                        vehicle.model.parentModel ? vehicle.model.parentModel.make.name : vehicle.model.make.name
                    ),
                    variant: translatedString(vehicle.name),
                    totalPrice: financing?.financedAmount ? financing?.financedAmount : null,
                    filename: vehicle.images?.length > 0 ? vehicle.images[0].filename : '',
                    source: vehicle.images?.length > 0 ? vehicle.images[0]?.url : '',
                };

            default:
                throw new Error('not implemented');
        }
    }, [financing?.financedAmount, translatedString, vehicle]);

    return (
        <>
            <ConfiguratorVehicleInfo {...vehicleData} />
            <StyledDealerDiv>
                <DealerInfo dealer={dealer} />
            </StyledDealerDiv>
        </>
    );
};
const EventApplicationVehicleInfo = ({ application }: VehicleInfoProps) => {
    const translatedString = useTranslatedString();
    if (application.__typename !== 'EventApplication') {
        return null;
    }
    const { __typename, configuration, vehicle } = application;
    const assetCondition = useAssetCondition(configuration.assetCondition);

    const vehicleData = useMemo(() => {
        if (__typename === 'EventApplication') {
            switch (vehicle.__typename) {
                case 'LocalVariant':
                    return {
                        carType: assetCondition,
                        make: translatedString(
                            vehicle.model.parentModel ? vehicle.model.parentModel.make.name : vehicle.model.make.name
                        ),
                        model: translatedString(
                            vehicle.model.parentModel ? vehicle.model.parentModel.name : vehicle.model.name
                        ),
                        subModel: vehicle.model.parentModel ? translatedString(vehicle.model.name) : null,
                        variant: translatedString(vehicle.name),
                    };

                default:
                    throw new Error('not implemented');
            }
        }

        throw new Error('Invalid application');
    }, [__typename, vehicle, assetCondition, translatedString]);

    return (
        <>
            <EventVehicleInfo {...vehicleData} />
            <StyledDealerDiv>
                <Content
                    dealer={application?.dealer}
                    hasDeposit={false}
                    make={vehicleData.make}
                    showDealerInfo={application.event?.showDealership}
                />
            </StyledDealerDiv>
        </>
    );
};
const FinderApplicationVehicleInfo = ({ application }: VehicleInfoProps) => {
    if (application.__typename !== 'FinderApplication') {
        return null;
    }
    const { dealer } = application;

    return (
        <>
            <FinderVehicleInfo application={application} />
            <StyledDealerDiv>
                <DealerInfo dealer={dealer} />
            </StyledDealerDiv>
        </>
    );
};

const ConfiguratorApplicationVehicleInfo = ({ application }: VehicleInfoProps) => {
    const translatedString = useTranslatedString();

    if (application.__typename !== 'ConfiguratorApplication') {
        return null;
    }
    const { vehicle, vehicleImage, dealer } = application;

    const vehicleData = useMemo(() => {
        switch (vehicle.__typename) {
            case 'LocalVariant':
                return {
                    make: translatedString(
                        vehicle.model.parentModel ? vehicle.model.parentModel.make.name : vehicle.model.make.name
                    ),
                    variant: translatedString(vehicle.name),
                    defaultVariantName: vehicle.name.defaultValue,
                    defaultModelName: vehicle.model.parentModel
                        ? translatedString(vehicle.model.parentModel.name)
                        : translatedString(vehicle.model.name),
                    totalPrice: calculateTotalPrice(application),
                    filename: vehicleImage?.filename,
                    source: vehicleImage?.url,
                    carPrice: vehicle.vehiclePrice,
                };

            default:
                throw new Error('not implemented');
        }
    }, [application, translatedString, vehicle, vehicleImage?.filename, vehicleImage?.url]);

    return (
        <>
            <ConfiguratorVehicleInfo {...vehicleData} />
            <StyledDealerDiv>
                <DealerInfo dealer={dealer} />
            </StyledDealerDiv>
        </>
    );
};
const VehicleInfo = ({ application }: VehicleInfoProps) => {
    const { __typename } = application;
    switch (__typename) {
        case 'ConfiguratorApplication':
            return <ConfiguratorApplicationVehicleInfo application={application} />;

        case 'EventApplication':
            return <EventApplicationVehicleInfo application={application} />;

        case 'FinderApplication':
            return <FinderApplicationVehicleInfo application={application} />;

        case 'StandardApplication':
            return <StandardApplicationVehicleInfo application={application} />;

        default:
            throw new Error('Application not support');
    }
};
type InnerProps = AppointmentPageProps & {
    bookedAppointmentTimeSlots: (string | Date)[];
    bookedVisitAppointmentTimeSlots: (string | Date)[];

    CustomLayout?: ComponentType<PropsWithChildren<PageContainerProps>>;
};

export const Inner = ({
    dispatch,
    state,
    endpoint,
    bookedAppointmentTimeSlots,
    bookedVisitAppointmentTimeSlots,
    CustomLayout,
    shouldIncludeLayout = true,
}: InnerProps) => {
    const { application } = state;
    const { handleSubmit, isSubmitting, validateForm, submitForm } = useFormikContext<AppointmentValues>();
    const { module } = application;
    const navigate = useNavigate();

    const { appointmentModule, hasShowroomVisit, hasTestDrive, visitAppointmentModule } =
        retrieveAppointmentModuleFromEntrypoint(endpoint);

    const { t } = useTranslation('customerDetails');
    if (
        module.__typename !== 'StandardApplicationModule' &&
        module.__typename !== 'EventApplicationModule' &&
        module.__typename !== 'FinderApplicationPublicModule' &&
        module.__typename !== 'FinderApplicationPrivateModule' &&
        module.__typename !== 'ConfiguratorModule'
    ) {
        return null;
    }

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const onBack = useCallback(() => {
        if (application.withCustomerDevice) {
            return;
        }

        if (application.__typename === 'EventApplication') {
            dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC });

            return;
        }

        navigate('..');
    }, [application.__typename, application.withCustomerDevice, dispatch, navigate]);

    const showSkipValidation =
        module.__typename === 'FinderApplicationPrivateModule' || module.__typename === 'StandardApplicationModule';

    const title = <Typography style={{ alignItems: 'center', width: '100%' }}>{t('appointmentPage:title')}</Typography>;

    const content = (
        <Form id="appointmentForm" name="appointmentForm" onSubmitCapture={handleSubmit}>
            <Row gutter={[24, 50]}>
                <Col {...leftColSpan}>
                    <VehicleInfo application={application} />
                </Col>
                <StyledCol {...rightColSpan}>
                    <JourneySectionWrapper
                        applicationType={application.__typename}
                        stage={state.stage}
                        stages={state.stages}
                    >
                        {hasTestDrive && (
                            <AppointmentDatepicker
                                appointmentModule={appointmentModule}
                                bookedAppointmentTimeSlots={bookedAppointmentTimeSlots}
                                showSkipValidation={showSkipValidation}
                                showTitle={shouldIncludeLayout}
                            />
                        )}
                        {hasShowroomVisit && (
                            <AppointmentDatepicker
                                appointmentModule={visitAppointmentModule}
                                bookedAppointmentTimeSlots={bookedVisitAppointmentTimeSlots}
                                showSkipValidation={showSkipValidation}
                                showTitle={shouldIncludeLayout}
                            />
                        )}
                    </JourneySectionWrapper>
                </StyledCol>
            </Row>
        </Form>
    );

    if (!shouldIncludeLayout) {
        return (
            <>
                {content}
                <JourneyToolbar>
                    <NextButton disabled={isSubmitting} onSubmit={onSubmit} />
                </JourneyToolbar>
            </>
        );
    }

    return (
        <CustomLayout
            backIcon={
                !application.withCustomerDevice ? (
                    <StyledBackButton>
                        <LeftOutlined /> {t('appointmentPage:actions.back')}
                    </StyledBackButton>
                ) : null
            }
            footer={[
                <FooterButtonContainer>
                    <NextButton key="nextButton" disabled={isSubmitting} onSubmit={onSubmit} />
                </FooterButtonContainer>,
            ]}
            onBack={onBack}
            style={{ background: '#fff', height: '100%', padding: '0px' }}
            title={title}
        >
            <BasicProLayoutContainer>{content}</BasicProLayoutContainer>
        </CustomLayout>
    );
};

export default Inner;
