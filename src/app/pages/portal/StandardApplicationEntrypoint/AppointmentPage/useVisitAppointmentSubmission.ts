import { useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { VisitAppointmentModuleSpecsFragment } from '../../../../api/fragments/VisitAppointmentModuleSpecs';
import { SubmitApplicantAppointmentMutationVariables } from '../../../../api/mutations/submitApplicantAppointment';
import {
    SubmitApplicantVisitAppointmentDocument,
    SubmitApplicantVisitAppointmentMutation,
    SubmitApplicantVisitAppointmentMutationVariables,
} from '../../../../api/mutations/submitApplicantVisitAppointment';
import type { AppointmentValues } from './appointmentValues';

export const getSlotFromVisitAppointmentDateTime = (
    appointmentModule: Pick<VisitAppointmentModuleSpecsFragment, 'bookingTimeSlot'>,
    datePickerEnabled: boolean,
    values?: AppointmentValues,
    timeZone?: string
): SubmitApplicantAppointmentMutationVariables['bookingTimeSlot'] | undefined => {
    if (!values.time || !values.date) {
        return undefined;
    }

    const { time } = values;
    const splitTime = datePickerEnabled ? time.toString().split(':') : '00:00:00';

    const dayjsDate = datePickerEnabled ? dayjs(values.date) : dayjs();
    // the reason why we need set seconds to be 0 due to the comparison logic always fail when comparing
    // in minutes
    const bookingTimeSlot = dayjsDate
        .set('hour', parseInt(splitTime[0], 10))
        .set('minute', parseInt(splitTime[1], 10))
        .set('second', 0)
        .tz(timeZone, true);
    const systemBookingTimeSlot = appointmentModule.bookingTimeSlot.find(timeSlot => {
        // system slot, should be based on value date instead
        // Otherwise, the comparison will fail
        const slot = dayjs(dayjsDate)
            .set('hour', dayjs(timeSlot.slot).hour())
            .set('minute', dayjs(timeSlot.slot).minute())
            .tz(timeZone)
            .format('HH:mm');

        return slot === time;
    });

    return {
        // Make it the same with: src/server/journeys/common/AppointmentStep.ts
        bookingLimit: datePickerEnabled ? systemBookingTimeSlot.bookingLimit : 0,
        slot: datePickerEnabled ? bookingTimeSlot.toDate() : null,
        useCurrentDateTime: values?.useCurrentDateTime ?? false,
    };
};

export const useVisitAppointmentSubmission = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (
            token: string,
            values: AppointmentValues,
            appointmentModule: Pick<VisitAppointmentModuleSpecsFragment, 'bookingTimeSlot'>,
            datePickerEnabled: boolean,
            timeZone?: string
        ) => {
            const bookingTimeSlot = getSlotFromVisitAppointmentDateTime(
                appointmentModule,
                datePickerEnabled,
                values,
                timeZone
            );

            const { data } = await apolloClient.mutate<
                SubmitApplicantVisitAppointmentMutation,
                SubmitApplicantVisitAppointmentMutationVariables
            >({
                mutation: SubmitApplicantVisitAppointmentDocument,
                variables: {
                    bookingTimeSlot,
                    token,
                },
            });

            return data.result;
        },
        [apolloClient]
    );
};

export default useVisitAppointmentSubmission;
