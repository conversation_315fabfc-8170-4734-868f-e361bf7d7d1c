import dayjs, { Dayjs } from 'dayjs';
import { isEmpty, isEqual, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { TimeSlotDataFragment } from '../../../../api';
import { AppointmentModuleSpecsFragment } from '../../../../api/fragments/AppointmentModuleSpecs';
import { AppointmentTimeSlotDataFragment } from '../../../../api/fragments/AppointmentTimeSlotData';
import { VisitAppointmentModuleSpecsFragment } from '../../../../api/fragments/VisitAppointmentModuleSpecs';
import { getUnavailableDayofWeek } from '../../MobilityApplicationEntrypoint/helper';
import type { AppointmentValues } from './appointmentValues';

export type MinimumAppointmentModuleSpecs =
    | Pick<
          AppointmentModuleSpecsFragment,
          'advancedBookingLimit' | 'bookingTimeSlot' | 'unavailableDayOfWeek' | 'maxAdvancedBookingLimit'
      >
    | Pick<
          VisitAppointmentModuleSpecsFragment,
          'advancedBookingLimit' | 'bookingTimeSlot' | 'unavailableDayOfWeek' | 'maxAdvancedBookingLimit'
      >;

type BlockedTimeSlot = {
    date: dayjs.Dayjs;
    time: string;
    count: number;
    isFull: boolean;
    limit: number;
};

type MappedBlockedTimeSlot = {
    [dateKey: string]: {
        limit: number;
        isFull: boolean;
        count: number;
        slot: {
            [timeKey: string]: BlockedTimeSlot;
        };
    };
};

type CleanedTimeSlot = Pick<AppointmentTimeSlotDataFragment | TimeSlotDataFragment, 'bookingLimit' | 'slot'>[];

const mapBookedTimeSlot = (
    bookedTimeSlot: (string | Date)[],
    appointmentModule: MinimumAppointmentModuleSpecs,
    timeZone?: string
): MappedBlockedTimeSlot => {
    if (isEmpty(bookedTimeSlot)) {
        return {};
    }
    const timeSlots: CleanedTimeSlot = appointmentModule?.bookingTimeSlot || [];

    // Map time slot setting with it's booking limit
    const slotLimit = timeSlots.reduce(
        (prev, timeSlot) => {
            const time = dayjs(timeSlot.slot).tz(timeZone, true).format('HH:mm');

            return {
                ...prev,
                times: {
                    ...prev.times,
                    [time]: timeSlot.bookingLimit,
                },
                total: (prev.total ?? 0) + timeSlot.bookingLimit,
            };
        },
        {
            times: {},
            total: 0,
        } as {
            times: {
                [timeSlotString: string]: number;
            };
            total: number;
        }
    );

    const blockedTimeSlot: MappedBlockedTimeSlot = {};

    // From booked time slot
    // Map to key values, to give easier access to data, rather than find inside array
    // Which currently used to validate current date or time slot is full or not
    bookedTimeSlot.forEach(app => {
        const timeSlot = dayjs(app).tz(timeZone, true);

        const dateKey = timeSlot.format('YYYY-MM-DD');
        const timeKey = timeSlot.format('HH:mm');
        if (blockedTimeSlot[dateKey]?.slot?.[timeKey]) {
            blockedTimeSlot[dateKey].slot[timeKey].count++;
            blockedTimeSlot[dateKey].slot[timeKey].isFull =
                slotLimit.times[timeSlot.format('HH:mm')] <= blockedTimeSlot[dateKey].slot[timeKey].count;

            blockedTimeSlot[dateKey].count++;
            blockedTimeSlot[dateKey].isFull = blockedTimeSlot[dateKey].count >= blockedTimeSlot[dateKey].limit;
        } else {
            const timeLimit = slotLimit.times[timeSlot.format('HH:mm')] ?? 0;

            if (!blockedTimeSlot[dateKey]) {
                blockedTimeSlot[dateKey] = {
                    isFull: false,
                    slot: {},
                    limit: slotLimit.total ?? 0,
                    count: 0,
                };
            }

            blockedTimeSlot[dateKey].slot[timeKey] = {
                date: timeSlot,
                time: timeSlot.format('HH:mma'),
                count: 1,
                limit: timeLimit,
                isFull: timeLimit <= 1,
            };
            blockedTimeSlot[dateKey].count++;
            blockedTimeSlot[dateKey].isFull = blockedTimeSlot[dateKey].count >= blockedTimeSlot[dateKey].limit;
        }
    });

    return blockedTimeSlot;
};

export const retrieveAvailableAppointmentSlot = (
    bookingTimeSlot: AppointmentTimeSlotDataFragment[] | TimeSlotDataFragment[],
    bookedTimeSlot: MappedBlockedTimeSlot,
    selectedDate: string | Date | dayjs.Dayjs,
    timeZone?: string,
    initialDateBooked?: string | Date | dayjs.Dayjs
) => {
    const availableTimeSlots = (bookingTimeSlot || [])
        .map(timeSlotSetting => {
            if (!selectedDate) {
                return null;
            }

            const slot = dayjs(timeSlotSetting.slot);
            const availableSlot = dayjs(selectedDate).set('hour', slot.hour()).set('minute', slot.minute());

            const dateKey = availableSlot.format('YYYY-MM-DD');
            const timeKey = availableSlot.format('HH:mm');

            // If date and time is in the past or is already booked, return null
            if (dayjs().isAfter(availableSlot, 'minute') || bookedTimeSlot?.[dateKey]?.slot[timeKey]?.isFull) {
                return null;
            }

            // Return timeslot if it is not booked
            return {
                label: availableSlot.tz(timeZone).format('HH:mm'),
                value: availableSlot.tz(timeZone).format('HH:mm'),
            };
        })
        .filter(Boolean);

    const initialDateBookedOption = {
        label: dayjs(initialDateBooked).tz(timeZone).format('HH:mm'),
        value: dayjs(initialDateBooked).tz(timeZone).format('HH:mm'),
    };

    // If there is initialDateBooked, then the booking slot must be available
    // Since it is reserved by the current application
    if (initialDateBooked && !availableTimeSlots.some(slot => isEqual(slot.value, initialDateBookedOption.value))) {
        return availableTimeSlots.concat(initialDateBookedOption).sort((a, b) => a?.value.localeCompare(b?.value));
    }

    return availableTimeSlots.sort((a, b) => a?.value.localeCompare(b?.value));
};

const isBeforeAvailabilityStartDate = (today: dayjs.Dayjs, date: dayjs.Dayjs, advancedBookingLimit: number) =>
    date.isBefore(today.add(advancedBookingLimit, 'day'), 'day');

const isAfterAvailabilityEndDate = (today: dayjs.Dayjs, date: dayjs.Dayjs, maxAdvancedBookingLimit?: number) =>
    !!maxAdvancedBookingLimit && date.isAfter(today.add(maxAdvancedBookingLimit, 'day'), 'day');

const isUnavailableDayOfWeek = (date: dayjs.Dayjs, unavailableDayOfWeek: number[]) =>
    unavailableDayOfWeek.includes(date.day());

export type BookedTimeSlots = (string | Date)[];

type FilteredAvailableSlot = {
    date: string;
    firstSlot: {
        value: string;
        label: string;
    };
};

const retrieveFirstSlot = (
    appointmentModule: MinimumAppointmentModuleSpecs,
    bookedAppointmentTimeSlots: (string | Date)[],
    timeZone?: string
) => {
    // prefilling the fields
    // get next available date
    const mappedBlockedTimeSlot = mapBookedTimeSlot(bookedAppointmentTimeSlots, appointmentModule, timeZone);
    const unavailableDayOfWeek = getUnavailableDayofWeek(appointmentModule.unavailableDayOfWeek);
    const today = dayjs();

    const getFirstSlot = (nextDate?: string | Date) => {
        // if there is max advanced booking limit, we need to validate the condition below
        // if advance booking limit is greater than max, then it is a configuration issue
        // max advance booking should be within the range of advance booking limit
        if (
            !isNil(appointmentModule.maxAdvancedBookingLimit) &&
            appointmentModule.advancedBookingLimit > appointmentModule.maxAdvancedBookingLimit
        ) {
            return null;
        }

        // stop once we are the same or passed the date
        if (
            !isNil(nextDate) &&
            dayjs(nextDate).isSameOrAfter(dayjs().add(appointmentModule.maxAdvancedBookingLimit, 'day'))
        ) {
            return null;
        }

        const dateToPrefill = !isNil(nextDate)
            ? dayjs(nextDate).format('YYYY-MM-DD')
            : dayjs().add(appointmentModule.advancedBookingLimit).format('YYYY-MM-DD');

        if (
            isBeforeAvailabilityStartDate(today, dayjs(dateToPrefill), appointmentModule?.advancedBookingLimit) ||
            isAfterAvailabilityEndDate(today, dayjs(dateToPrefill), appointmentModule?.maxAdvancedBookingLimit) ||
            isUnavailableDayOfWeek(dayjs(dateToPrefill), unavailableDayOfWeek)
        ) {
            return getFirstSlot(dayjs(dateToPrefill).add(1, 'day').format('YYYY-MM-DD'));
        }

        const availableTimeSlots = retrieveAvailableAppointmentSlot(
            appointmentModule?.bookingTimeSlot,
            mappedBlockedTimeSlot,
            dateToPrefill,
            timeZone
        );

        if (!availableTimeSlots?.length || mappedBlockedTimeSlot[dateToPrefill]?.isFull) {
            return getFirstSlot(dayjs(dateToPrefill).add(1, 'day').format('YYYY-MM-DD'));
        }

        return {
            availableTimeSlots,
            dateToPrefill,
        };
    };

    const { availableTimeSlots, dateToPrefill } = getFirstSlot() || {};

    return {
        availableTimeSlots,
        dateToPrefill,
    };
};
export const useFirstAvailableSlot = (
    appointmentModule: MinimumAppointmentModuleSpecs | null | undefined,
    bookedAppointmentTimeSlots: (string | Date)[] | null | undefined,
    timeZone?: string,
    initialValue?: string | Date
): FilteredAvailableSlot =>
    useMemo(() => {
        if (initialValue) {
            return {
                date: dayjs(initialValue).tz(timeZone).format('YYYY-MM-DD'),
                firstSlot: {
                    value: dayjs(initialValue).tz(timeZone).format('HH:mm'),
                    label: dayjs(initialValue).tz(timeZone).format('HH:mm'),
                },
            };
        }
        const result =
            isNil(appointmentModule) || !appointmentModule.bookingTimeSlot?.length
                ? null
                : retrieveFirstSlot(appointmentModule, bookedAppointmentTimeSlots ?? [], timeZone);

        return {
            date: isNil(result) ? null : result?.dateToPrefill,
            firstSlot: isNil(result) || isNil(result?.availableTimeSlots) ? null : result?.availableTimeSlots[0],
        };
        /**
         * in order to get rid of deadloop, we might not include `bookedAppointmentSlots` as one of the dependencies
         * this is because it keep loop multiple times when useFirstAvailableSlot
         * render the `KYC Page` and re-render the `useAvailableFirstSlot` again
         */
    }, [appointmentModule, initialValue, timeZone]);

const useAppointmentAvailability = (
    appointmentModule: MinimumAppointmentModuleSpecs,
    bookedAppointmentTimeSlots: (string | Date)[],
    selectedDate: AppointmentValues['date'],
    timeZone?: string,
    initialDateBooked?: string | Date | dayjs.Dayjs
) => {
    const [availableTimeSlots, bookedTimeSlots, unavailableDayOfWeek] = useMemo(() => {
        const bookedSlots = mapBookedTimeSlot(bookedAppointmentTimeSlots, appointmentModule, timeZone);

        return [
            retrieveAvailableAppointmentSlot(
                appointmentModule?.bookingTimeSlot,
                bookedSlots,
                selectedDate,
                timeZone,
                initialDateBooked
            ),
            bookedSlots,
            getUnavailableDayofWeek(appointmentModule?.unavailableDayOfWeek ?? []),
        ];
    }, [appointmentModule, bookedAppointmentTimeSlots, initialDateBooked, selectedDate, timeZone]);

    const today = dayjs();

    const isDisabledDate = useCallback(
        (value: Dayjs): boolean =>
            isBeforeAvailabilityStartDate(today, dayjs(value), appointmentModule?.advancedBookingLimit) ||
            isAfterAvailabilityEndDate(today, dayjs(value), appointmentModule?.maxAdvancedBookingLimit) ||
            isUnavailableDayOfWeek(dayjs(value), unavailableDayOfWeek) ||
            (!isNil(selectedDate) &&
                dayjs(value).isSame(dayjs(selectedDate), 'D') &&
                availableTimeSlots?.length <= 0) ||
            (bookedTimeSlots[value.format('YYYY-MM-DD')]?.isFull ?? false),
        [
            appointmentModule?.advancedBookingLimit,
            appointmentModule?.maxAdvancedBookingLimit,
            availableTimeSlots?.length,
            bookedTimeSlots,
            selectedDate,
            today,
            unavailableDayOfWeek,
        ]
    );

    return useMemo(
        () => ({
            availableTimeSlots,
            isDisabledDate,
        }),
        [availableTimeSlots, isDisabledDate]
    );
};

export default useAppointmentAvailability;
