import { Typography } from 'antd';
import { Dispatch } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useThemeComponents } from '../../../../themes/hooks';
import type { StandardApplicationState } from '../Journey/shared';
import { Action, State } from '../Journey/shared';
import ConfirmEmailSendPage from './ConfirmEmailSendPage';

const Container = styled.div`
    & .ant-page-header {
        display: none;
    }
`;

export type ConfirmationPageProps = {
    state: State<StandardApplicationState>;
    dispatch: Dispatch<Action<StandardApplicationState>>;
};

const ConfirmationPage = () => {
    const { t } = useTranslation('confirmRemoteFlow');
    const { StandardLayout } = useThemeComponents();

    const title = (
        <Typography style={{ alignItems: 'center', width: '100%' }}>{t('confirmRemoteFlow:title')}</Typography>
    );

    return (
        <Container>
            <StandardLayout>
                <ConfirmEmailSendPage />
            </StandardLayout>
        </Container>
    );
};

export default ConfirmationPage;
