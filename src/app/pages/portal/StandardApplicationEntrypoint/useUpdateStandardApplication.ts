import { useApolloClient } from '@apollo/client';
import { useCallback } from 'react';
import {
    UpdateStandardApplicationJourneyDocument,
    UpdateStandardApplicationJourneyMutation,
    UpdateStandardApplicationJourneyMutationVariables,
} from '../../../api/mutations/updateStandardApplicationJourney';
import { CustomerKind } from '../../../api/types';
import type { KYCJourneyValues } from './KYCPage/shared';

const useUpdateStandardApplication = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (token: string, values: KYCJourneyValues, customerKind: CustomerKind, saveDraft = false) => {
            const { data } = await apolloClient.mutate<
                UpdateStandardApplicationJourneyMutation,
                UpdateStandardApplicationJourneyMutationVariables
            >({
                mutation: UpdateStandardApplicationJourneyDocument,
                variables: {
                    token,
                    configuration: values.configuration,
                    tradeInVehicle: values.tradeInVehicle.filter(({ isSelected }) => isSelected),
                    customerKind,
                    saveDraft,
                    financing: values.financing,
                },
            });

            return data.result;
        },
        [apolloClient]
    );
};

export default useUpdateStandardApplication;
