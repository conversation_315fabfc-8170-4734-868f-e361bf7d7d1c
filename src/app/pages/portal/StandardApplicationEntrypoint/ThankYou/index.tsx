import { useLocation } from 'react-router';
import { Maybe } from '../../../../api/types';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import type { State, StandardApplicationState } from '../Journey/shared';
import Content from './Content';

const ThankYouPage = () => {
    const state = useLocation().state as Maybe<State<StandardApplicationState>>;

    if (!state) {
        return <NotFoundResult />;
    }

    return <Content state={state} />;
};

export default ThankYouPage;
