import { Col, Row, Space } from 'antd';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import urljoin from 'url-join';
import { LeadDataFragment } from '../../../../api/fragments/LeadData';
import { ApplicationStage } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getApplicationIdentifier } from '../../../../utilities/application';
import FullJourneySteps from '../../../shared/JourneyPage/FullJourneySteps';
import BookingDeposit from '../../../shared/ThankYou/BookingDeposit';
import DealerInfo from '../../../shared/ThankYou/DealerInfo';
import SubmittedContent from '../../../shared/ThankYou/SubmittedContent';
import VehicleDetails from '../../../shared/ThankYou/VehicleDetails';
import { usePersistStandardJourneyValues } from '../CarDetailsPage/usePersistStandardJourneyValues';
import type { StandardApplicationState, State } from '../Journey/shared';
import { useStandardApplicationContext } from '../StandardApplicationContext';

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };

interface ContentProps {
    state: State<StandardApplicationState>;
}
const Content = ({ state }: ContentProps) => {
    const { t } = useTranslation('standardApplicationThankYou');
    const router = useRouter(false);

    const { StandardLayout, Button } = useThemeComponents();
    const { lead: initialLeadFromLaunchpad } = useStandardApplicationContext();
    const { lead: latestLead } = useLocation().state as { lead: LeadDataFragment };
    const { application } = state;
    const hasDeposit = !!application.deposit;

    const navigate = useNavigate();
    const { remove } = usePersistStandardJourneyValues();
    useEffect(() => {
        // remove persisted values from session storage
        remove();
    }, [remove]);

    const launchpadEndpoint = useMemo(() => {
        if (!router?.endpoints?.length || !(initialLeadFromLaunchpad && latestLead)) {
            return null;
        }

        const launchpadModuleEndpoint = router.endpoints.find(endpoint => {
            if (endpoint.__typename === 'LaunchPadApplicationEntrypoint') {
                return endpoint.launchPadApplicationModule.id === initialLeadFromLaunchpad.moduleId;
            }

            return false;
        });

        return launchpadModuleEndpoint
            ? urljoin(launchpadModuleEndpoint.pathname, 'leads', latestLead.versioning.suiteId)
            : null;
    }, [initialLeadFromLaunchpad, latestLead, router]);

    const redirectAfterThankyou = useCallback(() => {
        if (launchpadEndpoint) {
            navigate(`/${launchpadEndpoint}`, {
                state: { completedExternalJourney: true },
            });
        } else {
            navigate('..');
        }
    }, [launchpadEndpoint, navigate]);

    const identifier = useMemo(
        () =>
            getApplicationIdentifier(application, [
                ApplicationStage.Financing,
                ApplicationStage.Reservation,
                ApplicationStage.Lead,
            ]),
        [application]
    );

    return (
        <StandardLayout title={t('standardApplicationThankYou:title')}>
            <BasicProLayoutContainer>
                <FullJourneySteps stages={state?.stages} />
                <Row gutter={[24, 24]} justify="center" style={{ marginBottom: '24px' }}>
                    <Col {...colSpan}>
                        <Space direction="vertical" size={24} style={{ width: '100%' }}>
                            <VehicleDetails state={state} />
                            {hasDeposit && <BookingDeposit depositAmount={application.deposit.amount} />}
                        </Space>
                    </Col>
                    <Col {...colSpan}>
                        <Space className="v3-layout-card" direction="vertical" size={32} style={{ width: '100%' }}>
                            <SubmittedContent
                                description={t('standardApplicationThankYou:contents.description.emailSendMessage')}
                                reference={t('standardApplicationThankYou:contents.reference', {
                                    reference: identifier,
                                })}
                                title={t('standardApplicationThankYou:contents.description.successMessage')}
                            />
                            <Space direction="vertical" size={24} style={{ width: '100%' }}>
                                {application?.dealer && <DealerInfo dealer={application?.dealer} />}
                                <Button
                                    key="done"
                                    data-cy="eventThankPageButton"
                                    htmlType="button"
                                    onClick={redirectAfterThankyou}
                                    type="primary"
                                    block
                                >
                                    {t(
                                        `standardApplicationThankYou:actions.${initialLeadFromLaunchpad && latestLead ? 'viewAllActivities' : 'continueBrowsing'}`
                                    )}
                                </Button>
                            </Space>
                        </Space>
                    </Col>
                </Row>
            </BasicProLayoutContainer>
        </StandardLayout>
    );
};

export default Content;
