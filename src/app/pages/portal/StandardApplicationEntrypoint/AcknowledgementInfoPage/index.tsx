import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { Maybe } from '../../../../api/types';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import type { StandardApplicationState } from '../Journey/shared';
import { State } from '../Journey/shared';
import Content from './Content';

export type AcknowledgementInfoPageProps = {
    state: State<StandardApplicationState>;
};

const AcknowledgementInfoPageContent = ({ state }: AcknowledgementInfoPageProps) => {
    const { application } = state;

    const translatedString = useTranslatedString();

    const { dealer } = application;
    const dealerInfo = useMemo(
        () => ({
            name: dealer.displayName,
            email: dealer.contact.email,
            phone: dealer.contact.telephone,
            address: translatedString(dealer.contact.address),
        }),
        [dealer, translatedString]
    );

    return (
        <BasicProLayoutContainer>
            <Content acknowledgeContent={application.bank?.remoteFlowAcknowledgmentInfo} dealerInfo={dealerInfo} />
        </BasicProLayoutContainer>
    );
};

const AcknowledgementInfoPage = () => {
    const state = useLocation().state as Maybe<State<StandardApplicationState>>;

    if (!state) {
        return <NotFoundResult />;
    }

    return <AcknowledgementInfoPageContent state={state} />;
};

export default AcknowledgementInfoPage;
