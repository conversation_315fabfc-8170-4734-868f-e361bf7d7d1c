/* eslint-disable max-len */
import { MailOutlined } from '@ant-design/icons';
import { Grid, Typography } from 'antd';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled, { css } from 'styled-components';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import {
    FinanceProductSortingField,
    InsuranceProductSortingField,
    LayoutType,
    SortingOrder,
} from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import BasicProPageWithHeader from '../../../../layouts/BasicProLayout/BasicProPageWithHeader';
import { useThemeComponents } from '../../../../themes/hooks';
import { hasInsuranceScenario } from '../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useLocalFinanceProducts from '../../../shared/CIPage/useLocalFinanceProducts';
import useLocalInsuranceProducts from '../../../shared/CIPage/useLocalInsuranceProduct';
import { useShareModal } from '../CarDetailsPage/CalculatorStage/ShareModal';
import { StyledJourneyToolbar } from '../styledComponents';
import useLocalVariants from '../useLocalVariants';
import ComparisonCalculatorPage from './ComparisonCalculatorPage';

const StyledBasicProLayoutContainer = styled(BasicProLayoutContainer)`
    ${props =>
        props.theme.layoutType !== LayoutType.PorscheV3 &&
        css`
            padding-bottom: 24px;
        `}
`;

const StyledBasicProPageWithHeader = styled(BasicProPageWithHeader)`
    ${props =>
        props.theme.layoutType === LayoutType.PorscheV3
            ? css`
                  min-height: calc(100% + var(--v3-layout-footer-height, 150px));
                  .ant-pro-grid-content {
                      padding: 16px;
                      border-radius: 12px;
                      background-color: #ffffff;
                  }
              `
            : css`
                  background: #fff;
                  min-height: 100%;
              `}
`;

const SpaceBetweenContainer = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    margin-top: -8px;
    padding-bottom: 4px;
    max-height: 34px;

    h5 {
        margin-bottom: 0;
    }
`;

export type ComparisonCalculatorStageProps = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    vehicleIds: string[];
    dealerIds: string[];
};

const ComparisonCalculatorStage = ({ endpoint, vehicleIds, dealerIds }: ComparisonCalculatorStageProps) => {
    const { t } = useTranslation(['comparison']);
    const { Button } = useThemeComponents();
    const screens = Grid.useBreakpoint();

    const [calculatorId, setCalculatorId] = useState<string>();
    const [isValid, setIsValid] = useState<boolean>(false);

    const shareModal = useShareModal();
    const navigate = useNavigate();
    const { layout } = useRouter();

    // load dependencies
    const { loading: loadingVariants, variants: allVariants } = useLocalVariants({
        module: endpoint.applicationModule,
        dealerIds,
    });
    const { loading: loadingFinanceProducts, financeProducts } = useLocalFinanceProducts({
        module: endpoint.applicationModule,
        sort: { field: FinanceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds,
    });

    // filter out the variant with no finance product
    const variants = useMemo(
        () =>
            allVariants?.filter(variant =>
                financeProducts?.some(fp => fp.allVariantSuiteIds.includes(variant.versioning.suiteId))
            ),
        [allVariants, financeProducts]
    );

    const { insuranceProducts, loading: insuranceProductLoading } = useLocalInsuranceProducts({
        module: endpoint.applicationModule,
        sort: { field: InsuranceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds,
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    if (loadingVariants || loadingFinanceProducts || insuranceProductLoading) {
        return <PortalLoadingElement />;
    }

    if (!financeProducts?.length || !variants?.length) {
        throw new Error('No finance products or variants found');
    }

    if (hasInsuranceScenario(endpoint.applicationModule.scenarios) && !insuranceProducts?.length) {
        throw new Error('No Insurance Product found');
    }

    const title =
        layout.__typename === 'PorscheV3Layout' ? (
            t('comparison:title')
        ) : (
            <Typography.Title level={5} style={{ fontSize: '20px' }}>
                {t('comparison:title')}
            </Typography.Title>
        );

    const extra = (
        <Button
            icon={<MailOutlined />}
            onClick={shareModal.open}
            porscheFallbackIcon="email"
            type={layout.__typename === 'PorscheV3Layout' ? 'secondary' : 'link'}
        >
            {t('comparison:action.share')}
        </Button>
    );

    return (
        <StyledBasicProPageWithHeader
            header={{
                ...(screens.md
                    ? { title, extra }
                    : {
                          children: (
                              <SpaceBetweenContainer>
                                  {title}
                                  {extra}
                              </SpaceBetweenContainer>
                          ),
                      }),
            }}
        >
            <StyledBasicProLayoutContainer>
                <ComparisonCalculatorPage
                    calculatorId={calculatorId}
                    dealerId={dealerIds.length > 1 ? null : dealerIds[0]}
                    endpoint={endpoint}
                    financeProducts={financeProducts}
                    insuranceProducts={insuranceProducts}
                    insurers={endpoint.applicationModule.insurers}
                    setCalculatorId={setCalculatorId}
                    setIsSubmitting={setIsSubmitting}
                    setIsValid={setIsValid}
                    shareModalRender={shareModal.render}
                    vehicleIds={vehicleIds.filter(id => variants.some(variant => variant.id === id))}
                    vehicles={variants}
                />
            </StyledBasicProLayoutContainer>
            {endpoint.__typename === 'StandardApplicationEntrypoint' && (
                <StyledJourneyToolbar>
                    <Button
                        className="cancel-left-button"
                        onClick={() => navigate('..')}
                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        style={{ marginRight: 'auto' }}
                        type="tertiary"
                    >
                        {t('comparison:action.cancel')}
                    </Button>
                    <Button
                        disabled={!calculatorId || !isValid || isSubmitting}
                        form="calculatorForm"
                        htmlType="submit"
                        porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="primary"
                    >
                        {t('comparison:action.next')}
                    </Button>
                </StyledJourneyToolbar>
            )}
        </StyledBasicProPageWithHeader>
    );
};

export default ComparisonCalculatorStage;
