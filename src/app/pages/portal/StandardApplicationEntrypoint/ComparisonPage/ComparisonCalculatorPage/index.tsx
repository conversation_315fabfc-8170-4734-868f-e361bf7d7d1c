import { Dispatch, SetStateAction, useCallback, useMemo, useReducer } from 'react';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationEntrypointContextData';
// eslint-disable-next-line max-len
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { GenericComparisonCalculatorProvider } from '../../../../../calculator/ComparisonCalculatorProvider';
import * as computing from '../../../../../calculator/computing';
import { CalculatorContext } from '../../../../../calculator/computing';
import { InsurerType } from '../../../../../calculator/computing/insuranceFields/types';
import type { ConfigurationValues } from '../../../../../calculator/form/types';
import { GenericCalculatorValues } from '../../../../../calculator/types';
import useDefaultMarketTypeValue from '../../../../../utilities/useDefaultMarketTypeValue';
import useInitialValue from '../../../../../utilities/useInitialValue';
import useMarketSpecificCalculatorValues, {
    MarketSpecificCalculatorValues,
} from '../../../../../utilities/useMarketSpecificCalculatorValues';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
} from '../../../../admin/ModuleDetailsPage/modules/implementations/shared';
import getIsDealerOptionsVisible from '../../../../shared/getIsDealerOptionsVisible';
import { reducer } from '../../CarDetailsPage/CalculatorStage';
import CalculatorForm from '../../CarDetailsPage/CalculatorStage/CalculatorForm';
import type { State, ActionsProps } from '../../CarDetailsPage/CalculatorStage/shared';
import ComparisonCalculator from './ComparisonCalculator';

type ComparisonCalculatorPageProps = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    // initial vehicle ids, the selected one
    vehicleIds: string[];

    // required values for the meta/context
    financeProducts: computing.FinanceProduct[];
    insuranceProducts?: computing.InsuranceProduct[];
    insurers: InsurerType[];

    // List of vehicles
    vehicles: computing.Vehicle[];
    calculatorId: string;
    setCalculatorId: Dispatch<SetStateAction<string>>;
    shareModalRender: (
        calculatorContext: CalculatorContext<GenericCalculatorValues>[],
        endpoint:
            | StandardApplicationEntrypointContextDataFragment
            | StandardApplicationPublicAccessEntrypointContextDataFragment,
        withFinancing: boolean,
        withInsurance: boolean,
        tradeIn: boolean,
        testDrive: boolean,
        dealerId: string
    ) => JSX.Element;
    setIsValid: (isValid: boolean) => void;
    setIsSubmitting: (isSubmitting: boolean) => void;
    dealerId: string;
};

type KnownCalculatorValues =
    | computing.DefaultCalculatorValues
    | computing.SingaporeCalculatorValues
    | computing.NewZealandCalculatorValues;

const Inner = ({
    comparisonCalculatorContext,
    marketSpecificCalculatorValues,
    vehicles,
    endpoint,
    calculatorId,
    setCalculatorId,
    actions,
    state,
    setIsValid,
    setIsSubmitting,
    dealerId,
}: {
    comparisonCalculatorContext: computing.ComparisonCalculatorContext<KnownCalculatorValues>;
    marketSpecificCalculatorValues: MarketSpecificCalculatorValues;
    actions: ActionsProps;
    state: State;
    dealerId: string;
} & Pick<
    ComparisonCalculatorPageProps,
    'vehicles' | 'endpoint' | 'calculatorId' | 'setCalculatorId' | 'setIsValid' | 'setIsSubmitting'
>) => {
    const { add } = comparisonCalculatorContext;
    const enhancedAdd = useCallback<computing.ComparisonCalculatorContext<KnownCalculatorValues>['add']>(
        values => {
            add({ ...values, ...marketSpecificCalculatorValues });
        },
        [add, marketSpecificCalculatorValues]
    );

    return (
        <CalculatorForm
            actions={actions}
            application={null}
            calculatorContext={comparisonCalculatorContext.contexts.find(({ uid }) => uid === calculatorId)}
            dealerId={dealerId}
            endpoint={endpoint}
            setIsSubmitting={setIsSubmitting}
            setIsValid={setIsValid}
            setPromoCode={null}
            state={state}
            onComparisonPage
        >
            <ComparisonCalculator
                {...comparisonCalculatorContext}
                add={enhancedAdd}
                calculatorId={calculatorId}
                market={endpoint.applicationModule.market}
                setCalculatorId={setCalculatorId}
                vehicles={vehicles}
            />
        </CalculatorForm>
    );
};

const ComparisonCalculatorPage = ({
    endpoint,
    vehicleIds,
    financeProducts,
    vehicles,
    calculatorId,
    setCalculatorId,
    shareModalRender,
    insuranceProducts,
    insurers,
    setIsValid,
    dealerId,
    setIsSubmitting,
}: ComparisonCalculatorPageProps) => {
    const marketTypeValue = useDefaultMarketTypeValue(dealerId);
    const marketSpecificCalculatorValues = useMarketSpecificCalculatorValues(
        endpoint.applicationModule.marketType,
        marketTypeValue
    );

    const initialCalculatorValues = useInitialValue(
        vehicleIds.map(vehicleId => ({
            ...marketSpecificCalculatorValues,
            vehicle: vehicleId,
            isFinancingEnabled: hasFinancingScenario(endpoint.applicationModule.scenarios),
        }))
    );

    const promoCodeViewable = !!endpoint.applicationModule.promoCodeModule;

    const bankDisplayPreference = useMemo(
        () => endpoint.applicationModule.bankDisplayPreference,
        [endpoint.applicationModule]
    );

    const [state, dispatch] = useReducer(reducer, {
        configuration: {
            withFinancing: hasFinancingScenario(endpoint.applicationModule.scenarios),
            tradeIn: false,
            testDrive: false,
            withInsurance: hasInsuranceScenario(endpoint.applicationModule.scenarios),
        },
    });

    const actions: ActionsProps = useMemo(
        () => ({
            setConfiguration: (configuration: ConfigurationValues) =>
                dispatch({ type: 'setConfiguration', configuration }),
        }),
        [dispatch]
    );

    return (
        <GenericComparisonCalculatorProvider
            bankDisplayPreference={bankDisplayPreference}
            companyId={endpoint.applicationModule.company.id}
            dealerId={dealerId}
            financeProducts={financeProducts}
            hasFinancingCalculator={
                hasFinancingScenario(endpoint.applicationModule.scenarios) ||
                endpoint.applicationModule.showFinanceCalculator
            }
            initialValues={initialCalculatorValues}
            insuranceProducts={insuranceProducts}
            isDealerOptionsVisible={getIsDealerOptionsVisible(endpoint.applicationModule)}
            isFinancingOptional={endpoint.applicationModule.isFinancingOptional}
            market={endpoint.applicationModule.market}
            marketType={endpoint.applicationModule.marketType}
            promoCodeViewable={promoCodeViewable}
            vehicles={vehicles}
        >
            {comparisonCalculatorContext => (
                <>
                    {shareModalRender(
                        comparisonCalculatorContext.contexts,
                        endpoint,
                        state.configuration.withFinancing,
                        state.configuration.withInsurance,
                        state.configuration.tradeIn,
                        state.configuration.testDrive,
                        dealerId
                    )}
                    <Inner
                        actions={actions}
                        calculatorId={calculatorId}
                        comparisonCalculatorContext={comparisonCalculatorContext}
                        dealerId={dealerId}
                        endpoint={endpoint}
                        marketSpecificCalculatorValues={marketSpecificCalculatorValues}
                        setCalculatorId={setCalculatorId}
                        setIsSubmitting={setIsSubmitting}
                        setIsValid={setIsValid}
                        state={state}
                        vehicles={vehicles}
                    />
                </>
            )}
        </GenericComparisonCalculatorProvider>
    );
};

export default ComparisonCalculatorPage;
