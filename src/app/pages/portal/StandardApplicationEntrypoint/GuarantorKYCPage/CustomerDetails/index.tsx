/* eslint-disable max-len */
import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { allowedExtensions } from '../../../../../../server/utils/extensions';
import { KycFieldSpecsFragment } from '../../../../../api/fragments/KYCFieldSpecs';
import {
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    CustomerKind,
    ApplicationDocumentKind,
    LocalCustomerManagementModule,
} from '../../../../../api/types';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import { KycPresetFieldsRenderer } from '../../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../../utilities/kycPresets/shared';
import { allowedCountryForSections } from '../../../../shared/JourneyPage/CustomerDetails/shared';
import type { StandardApplicationState } from '../../Journey/shared';
import GroupedCustomerDetails from '../../KYCPage/shared';
import KycUploadDocumentField from '../../KYCPage/uploadDocuments/KycUploadDocumentField';
import type { GuarantorKYCJourneyValues } from '../shared';

export type GuarantorDetailsProps = {
    application: StandardApplicationState;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    kycPresets: KycFieldSpecsFragment[];
    setPrefill: Dispatch<SetStateAction<boolean>>;
} & UploadDocumentProp;

const fieldsColSpan = { md: 12, sm: 24, xs: 24 };

const CustomerDetails = ({
    kycPresets,
    kycExtraSettings,
    application,
    setPrefill,
    uploadDocument,
    removeDocument,
}: GuarantorDetailsProps) => {
    const { t } = useTranslation('customerDetails');
    const { values, setFieldValue } = useFormikContext<GuarantorKYCJourneyValues>();
    const { FormFields } = useThemeComponents();

    const router = useRouter();
    const company = useCompany();
    const countryCode = useMemo(() => {
        if (company) {
            return company.countryCode;
        }

        if (router) {
            return router.company.countryCode;
        }

        return null;
    }, [company, router]);

    const showRemarks = application?.bank?.showCommentsField;
    const showCommentsToInsurer = useMemo(() => {
        const { insurers } =
            (application.module?.__typename === 'StandardApplicationModule' && application.module) || {};

        const insurer = (insurers ?? []).find(insurer => insurer?.id === application.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [application]);

    const showVSOUpload = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasVSOUpload,
        [application.configuration.withFinancing, application.bank]
    );

    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );

    const uploadVSO = useCallback(
        (file: File) => uploadDocument(ApplicationDocumentKind.VsoUpload, file),
        [uploadDocument]
    );

    const uploadGuarantorDocuments = useCallback(
        (file: File) => uploadDocument(ApplicationDocumentKind.GuarantorIdentity, file),
        [uploadDocument]
    );

    const remove = useCallback(file => removeDocument(file.id), [removeDocument]);

    useEffect(() => {
        if (!values.tradeInVehicle?.length) {
            setFieldValue('tradeInVehicle', [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }]);
        }
        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.DrivingLicense) &&
            !values.customer.fields?.DrivingLicense
        ) {
            setFieldValue('customer.fields.DrivingLicense', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: null }],
            });
        }
        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.DrivingLicenseTh) &&
            !values.customer.fields?.DrivingLicenseTh
        ) {
            setFieldValue('customer.fields.DrivingLicenseTh', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: null }],
            });
        }
        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.DrivingLicenseMy) &&
            !values.customer.fields?.DrivingLicenseMy
        ) {
            setFieldValue('customer.fields.DrivingLicenseMy', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: null }],
            });
        }
        if (
            kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.UaeDrivingLicense) &&
            !values.customer.fields?.UAEDrivingLicense
        ) {
            setFieldValue('customer.fields.UAEDrivingLicense', {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: null }],
            });
        }
    }, [
        setFieldValue,
        values.customer.fields?.DrivingLicense,
        values.tradeInVehicle?.length,
        values.customer.fields?.UAEDrivingLicense,
        kycPresets,
        values.customer.fields?.DrivingLicenseTh,
        values.customer.fields?.DrivingLicenseMy,
    ]);

    return (
        <Row gutter={16}>
            {allowedCountryForSections.includes(countryCode) ? (
                <GroupedCustomerDetails
                    customerFields={kycPresets}
                    customerKind={CustomerKind.Guarantor}
                    kycExtraSettings={kycExtraSettings}
                    prefix="customer.fields"
                    removeDocument={removeDocument}
                    setPrefill={setPrefill}
                    uploadDocument={uploadDocument}
                />
            ) : (
                <KycPresetFieldsRenderer
                    colSpan={fieldsColSpan}
                    customerType="guarantor"
                    extraSettings={kycExtraSettings}
                    fields={kycPresets}
                    gutter={24}
                    markMyinfo={false}
                    prefix="customer.fields"
                    removeDocument={removeDocument}
                    uploadDocument={uploadDocument}
                />
            )}

            {showVSOUpload && (
                <Col span={24}>
                    <FormFields.MultipleDraggerField
                        {...t('customerDetails:fields.uploadVSO', { returnObjects: true })}
                        customRemove={removeDocument ? remove : null}
                        customUpload={uploadDocument ? uploadVSO : null}
                        extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                        maxCount={3}
                        name="vsoUpload"
                        sizeLimitInMiB={20}
                        required
                    />
                </Col>
            )}

            {showUploadDocument && (
                <KycUploadDocumentField
                    customRemove={removeDocument ? remove : null}
                    customUpload={uploadDocument ? uploadGuarantorDocuments : null}
                    label={`uploadDocuments.${CustomerKind.Guarantor}`}
                    name={`uploadDocuments.${CustomerKind.Guarantor}`}
                />
            )}

            {showRemarks && (
                <Col span={24}>
                    <FormFields.InputField
                        {...t('customerDetails:fields.remark', { returnObjects: true })}
                        name="remarks"
                    />
                </Col>
            )}

            {showCommentsToInsurer && (
                <Col span={24}>
                    <FormFields.InputField
                        {...t('customerDetails:fields.commentsToInsurer', { returnObjects: true })}
                        name="commentsToInsurer"
                    />
                </Col>
            )}
        </Row>
    );
};

export default CustomerDetails;
