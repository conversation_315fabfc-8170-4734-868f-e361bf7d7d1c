/* eslint-disable max-len */
import { ApolloError } from '@apollo/client';
import { Col, Row } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import {
    ApplicationAgreement,
    CustomerKind,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
    ModuleType,
    StandardApplicationConfiguration,
    TradeInVehiclePayload,
} from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import Form from '../../../../components/fields/Form';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { KYCPresetFormFields, getInitialValues } from '../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useProceedWithCustomerValidators from '../../../../utilities/kycPresets/useProceedWithCustomerValidators';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import { getApplicantAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues, {
    AgreementValues,
} from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { getInitUploadDocuments } from '../../../shared/JourneyPage/CustomerDetails/shared';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import ProceedWithCustomerButton from '../../../shared/ProceedWithCustomerButton';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import ConsentsAndDeclarations from '../../EventApplicationEntrypoint/ApplicantForm/ConsentsAndDeclarations';
import useCustomerDetailsSubmission from '../CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../CustomerDetailsPage/useUpdateApplicationFields';
import { JourneyStage, type StandardApplicationState } from '../Journey/shared';
import { Action, State } from '../Journey/shared';
import OcrAndMyinfo from '../KYCPage/OcrAndMyinfo';
import SaveDraftButton, { canSaveDraft } from '../KYCPage/SaveDraftButton';
import VehicleOfInterest from '../KYCPage/VehicleOfInterest';
import { getApplicantKyc } from '../KYCPage/getKyc';
import useProceedWithCustomerDevice from '../KYCPage/useProceedWithCustomerDevice';
import { NextButton } from '../shared/JourneyButton';
import { Backdrop, FooterButtonContainer, StyleContainer, StyledJourneyToolbar } from '../styledComponents';
import CustomerDetails from './CustomerDetails';
import type { GuarantorKYCJourneyValues } from './shared';

export type Agreements = Omit<ApplicationAgreement, 'displayName' | 'orderNumber'>[];

export type KYCPageProps = {
    state: State<StandardApplicationState>;
    dispatch: Dispatch<Action<StandardApplicationState>>;
    endpoint: StandardApplicationEntrypointContextDataFragment;
};

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

export type KYCPageFormProps = {
    state: State<StandardApplicationState>;
    setSaveDraft: (value: boolean) => void;
};

const GurantorKYCPageForm = ({ state, endpoint, dispatch }: KYCPageProps) => {
    const { application } = state;
    const { guarantorKYC, guarantorAgreements, guarantor } = application;

    const { t } = useTranslation('customerDetails');
    const { notification } = useThemeComponents();

    const [saveDraft, setSaveDraft] = useState(false);
    const [isCorporate, setIsCorporate] = useState<boolean>(false);
    const [prefill, setPrefill] = useState<boolean>(false);
    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();

    const isEditableField = useMemo(() => (guarantor ? guarantor.fields : []), [guarantor]);
    const gurantorAgreementsKYC = guarantorAgreements.map(agreement => ({ ...agreement, isAgreed: false }));
    const guarantorKycAgreements = useMemo(
        () => getApplicantAgreements(gurantorAgreementsKYC),
        [gurantorAgreementsKYC]
    );
    const agreementsValidator = useAgreementsValidator(guarantorKycAgreements, 'agreements');
    const agreements = useAgreementsValues(guarantorKycAgreements);

    const applicantKycFields = useMemo(() => getApplicantKyc(guarantorKYC), [guarantorKYC]);

    const kycExtraSettings = useMemo(
        () =>
            endpoint?.applicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.applicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const applicants = useMemo(
        () => ({ fields: getInitialValues(isEditableField, applicantKycFields) }),
        [applicantKycFields, isEditableField]
    );
    const applicantsValidator = useKYCFormValidator({
        field: applicantKycFields,
        extraSettings: kycExtraSettings,
        moduleCountryCode: endpoint.applicationModule.company.countryCode,
        prefix: 'customer.fields',
        saveDraft,
    });

    const showVSOUpload = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasVSOUpload,
        [application.configuration.withFinancing, application.bank]
    );

    const showUploadDocument = useMemo(
        () => application.configuration.withFinancing && application.bank?.hasUploadDocuments,
        [application.configuration.withFinancing, application.bank]
    );

    const validations = useMemo(
        () =>
            validators.compose(
                applicantsValidator,
                agreementsValidator,
                validators.only(() => showVSOUpload, validators.requiredUploadFile('vsoUpload')),
                validators.only(
                    () => showUploadDocument,
                    validators.requiredUploadFile(`uploadDocuments.${CustomerKind.Guarantor}`)
                )
            ),
        [agreementsValidator, applicantsValidator, showUploadDocument, showVSOUpload]
    );

    const validate = useValidator(validations, { prefill });

    const initialValues: GuarantorKYCJourneyValues = useMemo(
        () => ({
            agreements,
            customer: applicants,
            configuration: application.configuration,
            tradeInVehicle: application.tradeInVehicle,
            hasGuarantor: false,
            remarks: application?.remarks ?? '',
            vsoUpload: [],
            prefill: false,
            uploadDocuments: getInitUploadDocuments(application.documents, showUploadDocument, CustomerKind.Guarantor),
        }),
        [
            agreements,
            applicants,
            application.configuration,
            application.documents,
            application.tradeInVehicle,
            application.remarks,
            showUploadDocument,
        ]
    );

    const uploadOcrFiles = useUploadOcrFiles();

    const user = useAccount(true);

    const showCommentsToBank = user && application?.bank?.showCommentsField;

    const { insurers } = endpoint.applicationModule;
    const showCommentsToInsurer = useMemo(() => {
        if (!user) {
            return false;
        }

        const insurer = insurers.find(insurer => insurer.id === application?.insurancing?.insurerId);

        return insurer?.showCommentsField;
    }, [user, insurers, application?.insurancing?.insurerId]);

    const onSubmit = useHandleError(
        async (values: GuarantorKYCJourneyValues & { remarks: string; commentsToInsurer?: string }) => {
            try {
                notification.loading({
                    content: t('customerDetails:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                const submitAgreementKYC = await submitAgreements(
                    state.token,
                    values.agreements,
                    CustomerKind.Guarantor,
                    false
                );

                if (showCommentsToBank || showCommentsToInsurer) {
                    // we wait to update remarks first
                    await updateApplicationFields(submitAgreementKYC.token, values.remarks, values.commentsToInsurer);
                    // then we later call the KYC
                    // as calling submit customer will immediately call the next step and will have not the remarks
                }

                const submitApplicantKYC = await submitCustomerDetails({
                    token: submitAgreementKYC.token,
                    fields: values.customer.fields,
                    customerKind: CustomerKind.Guarantor,
                    sameCorrespondenceAddress: values.prefill,
                    saveDraft,
                });

                await uploadOcrFiles(submitApplicantKYC.token);

                notification.destroy('primary');

                if (submitApplicantKYC.__typename !== 'ApplicationJourney') {
                    throw new Error('unexpected journey context');
                }

                // go to the journey
                if (submitApplicantKYC.application.__typename !== 'StandardApplication') {
                    throw new Error('unexpected type');
                }

                if (!saveDraft) {
                    dispatch({
                        type: 'next',
                        token: submitApplicantKYC.token,
                        application: submitApplicantKYC.application,
                    });
                } else {
                    notification.success({
                        content: t('customerDetails:messages.draftSaved'),
                        key: 'secondary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                }
            } finally {
                notification.destroy('primary');
            }
        },
        [
            notification,
            t,
            state.token,
            submitAgreements,
            submitCustomerDetails,
            saveDraft,
            showCommentsToBank,
            showCommentsToInsurer,
            updateApplicationFields,
            uploadOcrFiles,
            dispatch,
        ]
    );

    return (
        <StyleContainer>
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} validateOnMount>
                <GuarantorInner
                    dispatch={dispatch}
                    endpoint={endpoint}
                    isCorporate={isCorporate}
                    kycExtraSettings={kycExtraSettings}
                    prefill={prefill}
                    setIsCorporate={setIsCorporate}
                    setPrefill={setPrefill}
                    setSaveDraft={setSaveDraft}
                    state={state}
                />
            </Formik>
        </StyleContainer>
    );
};

const GuarantorInner = ({
    state,
    endpoint,
    dispatch,
    setSaveDraft,
    setPrefill,
    kycExtraSettings,
}: KYCPageProps & {
    setSaveDraft: (value: boolean) => void;
    isCorporate: boolean;
    setIsCorporate: Dispatch<SetStateAction<boolean>>;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    prefill: boolean;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
}) => {
    const { token, application } = state;

    const { guarantorAgreements, guarantorKYC } = application;

    const { t } = useTranslation('customerDetails');
    const { values, isSubmitting, handleSubmit, validateForm, submitForm, setErrors, setTouched } =
        useFormikContext<GuarantorKYCJourneyValues>();
    const { notification } = useThemeComponents();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [withMyInfo, setWithMyInfo] = useState(false);

    const onSaveDraft = useCallback(async () => {
        setSaveDraft(true);
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm, setSaveDraft]);

    const onSubmit = useCallback(async () => {
        setSaveDraft(false);
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm, setSaveDraft]);

    const user = useAccount(true);
    const proceedWithCustomerDevices = useProceedWithCustomerDevice();

    const proceedWithCustomerValidator = useProceedWithCustomerValidators(guarantorKYC, 'customer.fields');
    const proceedWithCustomerValidations = useMemo(
        () => validators.compose(proceedWithCustomerValidator),
        [proceedWithCustomerValidator]
    );
    const proceedWithCustomerValidate = useValidator(proceedWithCustomerValidations);

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const showRemoteFlowButtonInKYCPage = useMemo(() => {
        switch (application.module.__typename) {
            case ModuleType.StandardApplicationModule:
                return application.module.showRemoteFlowButtonInKYCPage;

            default:
                return null;
        }
    }, [application.module]);

    const agreementsKYC = guarantorAgreements.map(agreement => ({ ...agreement, isAgreed: false }));

    const applicantKycAgreements = useMemo(() => getApplicantAgreements(agreementsKYC), [agreementsKYC]);

    const applicantKycFields = useMemo(() => getApplicantKyc(guarantorKYC), [guarantorKYC]);

    const proceedWithCustomerButton = useMemo((): boolean => {
        const keys = applicantKycFields.map(preset => preset.key);

        if (
            (keys.includes(LocalCustomerFieldKey.FirstName) ||
                keys.includes(LocalCustomerFieldKey.LastName) ||
                keys.includes(LocalCustomerFieldKey.FullName)) &&
            keys.includes(LocalCustomerFieldKey.Email) &&
            keys.includes(LocalCustomerFieldKey.IdentityNumber)
        ) {
            return !!showRemoteFlowButtonInKYCPage && user && !application.withCustomerDevice;
        }

        return false;
    }, [applicantKycFields, showRemoteFlowButtonInKYCPage, user, application.withCustomerDevice]);

    const proceedWithCustomer = useCallback(
        async (currentValues: GuarantorKYCJourneyValues) => {
            try {
                const customKYCPresets = {};
                const customKYCFieldKeys = Object.keys(currentValues.customer.fields);

                // extract mandatory fields from KYC preset
                customKYCFieldKeys.forEach((key: LocalCustomerFieldKey) => {
                    if (
                        [
                            LocalCustomerFieldKey.FirstName,
                            LocalCustomerFieldKey.LastName,
                            LocalCustomerFieldKey.FullName,
                            LocalCustomerFieldKey.Email,
                            LocalCustomerFieldKey.IdentityNumber,
                        ].includes(key)
                    ) {
                        customKYCPresets[key] = currentValues.customer.fields[key];
                    }
                });

                const validationResult = proceedWithCustomerValidate(currentValues);

                if (!validationResult) {
                    notification.loading({
                        content: t('customerDetails:messages.sendEmailToCustomer'),
                        duration: 0,
                        key: 'primary',
                    });

                    await proceedWithCustomerDevices(state.token, customKYCPresets);

                    notification.destroy('primary');

                    dispatch({
                        type: 'goTo',
                        stage: JourneyStage.ConfirmEmailSend,
                    });
                } else {
                    // Set errors based on validator, since it's the same format, we can just use it
                    setErrors(validationResult);

                    // Set touched to true for all error fields
                    // although it should be in boolean instead of string, but formik can handle it
                    // and make sure to set the validate parameter to be false
                    // so it won't conflict with applicant validation
                    setTouched(validationResult, false);
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                }
            }
        },
        [
            proceedWithCustomerValidate,
            notification,
            t,
            proceedWithCustomerDevices,
            state.token,
            dispatch,
            setErrors,
            setTouched,
        ]
    );

    return (
        <>
            <FormAutoTouch />

            <Form id="kycJourneyForm" name="kycJourneyForm" onSubmitCapture={handleSubmit}>
                <Row gutter={[24, 50]}>
                    <Col {...leftColSpan}>
                        <Row gutter={[50, 50]}>
                            <VehicleOfInterest application={application} />
                            <OcrAndMyinfo
                                application={application}
                                endpoint={endpoint}
                                kycPresets={applicantKycFields}
                                onLoading={setIsLoading}
                                setWithMyInfo={setWithMyInfo}
                                state={state}
                                withMyInfo={withMyInfo}
                            />
                        </Row>
                    </Col>

                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            extra={
                                proceedWithCustomerButton && (
                                    <ProceedWithCustomerButton onClick={() => proceedWithCustomer(values)} />
                                )
                            }
                            stage={state.stage}
                            stages={state.stages}
                        >
                            <Row gutter={[16, 16]}>
                                <Col span={24}>
                                    <CustomerDetails
                                        application={application}
                                        kycExtraSettings={kycExtraSettings}
                                        kycPresets={applicantKycFields}
                                        removeDocument={removeDocument}
                                        setPrefill={setPrefill}
                                        uploadDocument={uploadDocument}
                                    />
                                </Col>
                                <Col span={24}>
                                    <ConsentsAndDeclarations applicationAgreements={applicantKycAgreements} />
                                </Col>
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>
            {isLoading && <Backdrop />}

            <StyledJourneyToolbar>
                <FooterButtonContainer>
                    {canSaveDraft(application, endpoint.applicationModule) && (
                        <SaveDraftButton
                            applicantKYC={applicantKycFields}
                            kycExtraSettings={kycExtraSettings}
                            moduleCountryCode={endpoint.applicationModule.company.countryCode}
                            onSaveDraft={onSaveDraft}
                            values={values}
                        />
                    )}
                    <NextButton key="nextButton" disabled={isSubmitting} form="kycJourneyForm" onSubmit={onSubmit} />
                </FooterButtonContainer>
            </StyledJourneyToolbar>
        </>
    );
};

const GuarantorKYCPage = (props: KYCPageProps) => (
    <OcrFilesManager>
        <GurantorKYCPageForm {...props} />
    </OcrFilesManager>
);
export default GuarantorKYCPage;
