import { LeftOutlined } from '@ant-design/icons';
import { Typography } from 'antd';
import { Dispatch } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ApplicationSigningPurpose } from '../../../../api/types';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { AllowedApplicationForSigning } from '../../../../utilities/journeys/signing';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import type { Action, State } from '../Journey/shared';
import { StyledBackButton } from '../styledComponents';
import OTPInnerPage from './OTPInnerPage';

export type OTPPageProps = {
    state: State<AllowedApplicationForSigning>;
    dispatch: Dispatch<Action<AllowedApplicationForSigning>>;
    purpose: ApplicationSigningPurpose;
    shouldIncludeLayout?: boolean;
};

const OTPPage = ({ dispatch, state, purpose, shouldIncludeLayout }: OTPPageProps) => {
    const { t } = useTranslation('otpPage');
    const { StandardLayout } = useThemeComponents();

    const { application } = state;
    const navigate = useNavigate();

    const isNotMobilityAndNotLaunchpad =
        application.__typename !== 'MobilityApplication' && application.__typename !== 'LaunchpadApplication';

    const title = (
        <Typography style={{ alignItems: 'center', width: '100%' }}>
            {isNotMobilityAndNotLaunchpad && application.draftFlow.isSubmittedToBank
                ? t('otpPage:resubmitTitle')
                : t('otpPage:title')}
        </Typography>
    );

    const content = (
        <JourneySectionWrapper applicationType={application.__typename} stage={state.stage} stages={state.stages}>
            <OTPInnerPage dispatch={dispatch} purpose={purpose} state={state} />
        </JourneySectionWrapper>
    );

    if (!shouldIncludeLayout) {
        return content;
    }

    return (
        <StandardLayout
            backIcon={
                isNotMobilityAndNotLaunchpad && !application.withCustomerDevice ? (
                    <StyledBackButton>
                        <LeftOutlined /> {t('otpPage:actions.back')}
                    </StyledBackButton>
                ) : null
            }
            onBack={isNotMobilityAndNotLaunchpad && !application.withCustomerDevice ? () => navigate('..') : null}
            style={{ background: '#fff', height: '100%' }}
            title={title}
        >
            <BasicProLayoutContainer>
                <OTPInnerPage dispatch={dispatch} purpose={purpose} state={state} />
            </BasicProLayoutContainer>
        </StandardLayout>
    );
};

export default OTPPage;
