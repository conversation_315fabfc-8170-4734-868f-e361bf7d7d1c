import { LeftOutlined } from '@ant-design/icons';
import { ApolloClient, NormalizedCacheObject, useApolloClient } from '@apollo/client';
import { Col, Form, Row, Typography } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { Dispatch, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import {
    GetMyInfoAuthorizeUrlDocument,
    GetMyInfoAuthorizeUrlQuery,
    GetMyInfoAuthorizeUrlQueryVariables,
} from '../../../../api/queries/getMyInfoAuthorizeUrl';
import { CustomerKind, LocalCustomerFieldKey, KycFieldPurpose } from '../../../../api/types';
import MyInfo from '../../../../components/MyInfo';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues, KYCPresetFormFields } from '../../../../utilities/kycPresets';
import useHandleError from '../../../../utilities/useHandleError';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import { type StandardApplicationState, JourneyStage } from '../Journey/shared';
import { Action, State } from '../Journey/shared';
import type { KYCJourneyValues } from '../KYCPage/shared';
import { FooterButtonContainer, StyledBackButton } from '../styledComponents';
import useUpdateStandardApplicationConfiguration from '../useUpdateStandardApplicationConfiguration';
import CustomerDetailsFields from './CustomerDetailsFields';
import useCustomerDetailsSubmission from './useCustomerDetailsSubmission';

export type FormValues = {
    fields: KYCPresetFormFields;
    isCorporateCustomer: boolean;
    hasGuarantor: boolean;
    prefill: boolean;
};

export type CustomerDetailsPageProps = {
    state: State<StandardApplicationState>;
    dispatch: Dispatch<Action<StandardApplicationState>>;
    endpoint: StandardApplicationEntrypointContextDataFragment;
};

const myInfoColSpan = { xl: 8, lg: 12, md: 24 };

const CustomerDetailsPage = ({ state, dispatch, endpoint }: CustomerDetailsPageProps) => {
    const { token, application } = state;
    const { t } = useTranslation(['customerDetails']);
    const client = useApolloClient() as ApolloClient<NormalizedCacheObject>;
    const { values } = useFormikContext<KYCJourneyValues>();
    const updateStandardApplicationConfiguration = useUpdateStandardApplicationConfiguration();

    // any icons needed to be here
    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const [withMyInfo, setWithMyInfo] = useState(false);
    const myInfoEnabled = !!endpoint.applicationModule.myInfoSettingId;
    const showMyInfoContent = !withMyInfo && myInfoEnabled;

    const colSpan = useMemo(() => (showMyInfoContent ? { xl: 16, lg: 12, md: 24 } : { span: 24 }), [showMyInfoContent]);

    const { Button, StandardLayout, notification } = useThemeComponents();

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const saveDraftButton = (
        <Button key="saveDraft" form="addCustomerDetailsForm" htmlType="submit">
            {t('customerDetails:saveDraftButton')}
        </Button>
    );

    const nextButton = (
        <Button key="nextButton" form="addCustomerDetailsForm" htmlType="submit">
            {t('customerDetails:nextButton')}
        </Button>
    );

    const kycExtraSettings = useMemo(
        () =>
            endpoint?.applicationModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? endpoint.applicationModule.customerModule.extraSettings
                : null,
        [endpoint]
    );

    const kycPresets = useMemo(() => {
        const citizenship = application.applicantKYC.find(field => field.key === LocalCustomerFieldKey.Citizenship);
        const identityNumber = application.applicantKYC.find(
            field => field.key === LocalCustomerFieldKey.IdentityNumber
        );

        const clonedKYC = [...application.applicantKYC];

        // we push the passport or identity number with isRequired = true at first from BE
        // when we push API , we will check and remove those empty value or it is optional
        if (citizenship && !identityNumber) {
            const citizenshipIndex = clonedKYC.findIndex(field => field.key === LocalCustomerFieldKey.Citizenship);

            clonedKYC.splice(
                citizenshipIndex + 1,
                0,
                // push identity number

                {
                    isRequired: true,
                    key: LocalCustomerFieldKey.IdentityNumber,
                    purpose: [KycFieldPurpose.Kyc],
                    __typename: 'KYCField',
                },
                // push passport
                {
                    isRequired: true,
                    key: LocalCustomerFieldKey.Passport,
                    purpose: [KycFieldPurpose.Kyc],
                    __typename: 'KYCField',
                }
            );
        } else if (citizenship && identityNumber) {
            // push passport
            const identityNumberIndex = application.applicantKYC.findIndex(
                field => field.key === LocalCustomerFieldKey.IdentityNumber
            );

            application.applicantKYC.splice(identityNumberIndex + 1, 0, {
                isRequired: true,
                key: LocalCustomerFieldKey.Passport,
                purpose: [KycFieldPurpose.Kyc],
                __typename: 'KYCField',
            });
        }

        return clonedKYC;
    }, [application.applicantKYC]);

    const initialValues = useMemo(
        (): FormValues => ({
            fields: getInitialValues([], kycPresets),
            isCorporateCustomer: false,
            hasGuarantor: false,
            prefill: false,
        }),
        [kycPresets]
    );

    const submitCustomerDetails = useCustomerDetailsSubmission();
    const onSubmit = useHandleError(
        async (values: FormValues) => {
            notification.loading({
                content: t('customerDetails:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const result = await submitCustomerDetails({
                token,
                fields: values.fields,
                customerKind: values.isCorporateCustomer ? CustomerKind.Corporate : CustomerKind.Local,
                sameCorrespondenceAddress: values.prefill,
                saveDraft: values.hasGuarantor,
            }).finally(() => {
                notification.destroy('primary');
            });

            if (result.__typename !== 'ApplicationJourney') {
                throw new Error('unexpected journey context');
            }

            const { application: newApplication } = result;

            if (newApplication.__typename !== 'StandardApplication') {
                throw new Error('unexpected type');
            }

            dispatch({ type: 'next', token: result.token, application: newApplication });
        },
        [notification, t, submitCustomerDetails, token, dispatch]
    );

    const myInfoOnClick = useCallback(async () => {
        await updateStandardApplicationConfiguration(token, values);

        const { data } = await client.query<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>({
            query: GetMyInfoAuthorizeUrlDocument,
            variables: {
                applicationId: application.id,
                routerId: router.id,
                endpointId: endpoint.id,
                customerKind: CustomerKind.Local,
                withTradeIn: values.configuration.tradeIn,
                withTestDrive: values.configuration.testDrive,
            },
            fetchPolicy: 'no-cache',
        });

        if (data?.authorizeUrl) {
            globalThis.location.href = data.authorizeUrl;
        }
    }, [updateStandardApplicationConfiguration, token, values, client, application.id, router.id, endpoint.id]);

    const tradeInVehicle = useMemo(
        () => ({
            withTradeIn: values.configuration.tradeIn,
            withTestDrive: values.configuration.testDrive,
            name: 'tradeInVehicle',
        }),
        [values]
    );

    return (
        <StandardLayout
            backIcon={
                <StyledBackButton>
                    <LeftOutlined /> {t('customerDetails:backButton')}
                </StyledBackButton>
            }
            footer={[
                <FooterButtonContainer>
                    {saveDraftButton}
                    {nextButton}
                </FooterButtonContainer>,
            ]}
            onBack={() => dispatch({ type: 'goTo', stage: JourneyStage.Initialize })}
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={
                <Typography style={{ alignItems: 'center', width: '100%' }}>{t('customerDetails:title')}</Typography>
            }
            hasFooterBar
        >
            <Formik initialValues={initialValues} onSubmit={onSubmit}>
                {({ handleSubmit }) => (
                    <Form
                        id="addCustomerDetailsForm"
                        layout="vertical"
                        name="addCustomerDetailsForm"
                        onSubmitCapture={handleSubmit}
                    >
                        <BasicProLayoutContainer>
                            <Row gutter={[24, 24]}>
                                {showMyInfoContent && (
                                    <Col {...myInfoColSpan}>
                                        <MyInfo
                                            applicationId={application.id}
                                            customerFieldPrefix="fields"
                                            customerKind={CustomerKind.Local}
                                            onClick={myInfoOnClick}
                                            setWithMyInfo={setWithMyInfo}
                                            tradeInVehicle={tradeInVehicle}
                                        />
                                    </Col>
                                )}
                                <Col {...colSpan}>
                                    <CustomerDetailsFields
                                        fields={application.applicantKYC}
                                        kycExtraSettings={kycExtraSettings}
                                        prefix="fields"
                                        removeDocument={removeDocument}
                                        showMyInfoContent={showMyInfoContent}
                                        uploadDocument={uploadDocument}
                                    />
                                </Col>
                            </Row>
                        </BasicProLayoutContainer>
                    </Form>
                )}
            </Formik>
        </StandardLayout>
    );
};

export default CustomerDetailsPage;
