import {
    ApplicationStage,
    ConfiguratorApplicationConfiguration,
    FinderApplicationConfiguration,
    StandardApplicationConfiguration,
    StandardApplicationDraftFlow,
} from '../../../../api/types';
import type { ConfiguratorApplicationState } from '../../ConfiguratorApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../FinderApplicationPublicAccessEntrypoint/shared';
import type { StandardApplicationState } from '../Journey/shared';

/**
 * to validate whether any incomplete apply new application journey
 * @param stage Application Stage already registered in this user or this same set of document
 * @param draftFlow threads of application journey / applying new application journey progression
 * @returns true :: if there is no incomplete journey.
 */
const retrieveStageApplyNewFeatureReceived = (
    stage: ApplicationStage,
    draftFlow: StandardApplicationDraftFlow,
    configuration:
        | StandardApplicationConfiguration
        | FinderApplicationConfiguration
        | ConfiguratorApplicationConfiguration
) => {
    switch (stage) {
        case ApplicationStage.Financing:
        case ApplicationStage.Insurance: {
            /**
             * initially we check any incompleted journey
             * :: draftFlow.isApplyNewForFinancingReceived = false
             * :: draftFlow.isApplyNewForInsuranceReceived = true
             *
             * we filter out all `true` and remains those incomplete journey
             * therefore,
             * :: applyNewJourneyIncomplete.length > 0 (true) = some incomplete apply new submission  journey
             * :: applyNewJourneyIncomplete.length === 0 (false) = all apply new submission journey completed;
             */
            const applyNewJourneyIncomplete = [
                !draftFlow.isApplyNewForFinancingReceived,
                !draftFlow.isApplyNewForInsuranceReceived,
            ].filter(Boolean);

            return applyNewJourneyIncomplete.length;
        }

        default:
            return false;
    }
};

export const mayProceedToThanksYou = (
    application: StandardApplicationState | FinderApplicationState | ConfiguratorApplicationState
) => {
    const { stages, draftFlow, configuration } = application;

    if (draftFlow.hasApplyNewSubmission) {
        if (configuration.testDrive && !draftFlow.isApplyNewForAppointmentReceived) {
            return false;
        }

        return !stages.some(stage => retrieveStageApplyNewFeatureReceived(stage, draftFlow, configuration));
    }

    return application.draftFlow.isReceived;
};
