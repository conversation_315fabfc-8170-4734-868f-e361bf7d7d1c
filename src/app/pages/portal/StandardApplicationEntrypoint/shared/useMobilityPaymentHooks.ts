import { useApolloClient } from '@apollo/client';
import { useCallback, useMemo, Dispatch } from 'react';
import { GiftVoucherCodeDataFragment, PromoCodeDataFragment } from '../../../../api/fragments';
import {
    UpdateMobilityDepositAmountDocument,
    UpdateMobilityDepositAmountMutation,
    UpdateMobilityDepositAmountMutationVariables,
} from '../../../../api/mutations';
import { AllowedApplicationForPayment, ensureApplicationForPayment } from '../../../../utilities/journeys/payment';
import type { Action } from '../Journey/shared';

const useMobilityPaymentHooks = (
    token: string,
    setFieldValue: (string, any) => void,
    dispatch: Dispatch<Action<AllowedApplicationForPayment>>
) => {
    const apolloClient = useApolloClient();

    const onDiscountCodeChange = useCallback(
        async (promoCode: PromoCodeDataFragment, giftVoucher: GiftVoucherCodeDataFragment) => {
            try {
                const result = await apolloClient.mutate<
                    UpdateMobilityDepositAmountMutation,
                    UpdateMobilityDepositAmountMutationVariables
                >({
                    mutation: UpdateMobilityDepositAmountDocument,
                    variables: {
                        token,
                        promoCodeId: promoCode?.id,
                        giftVoucherId: giftVoucher?.id,
                    },
                    fetchPolicy: 'network-only',
                });

                setFieldValue('promoCodeId', promoCode?.id);

                dispatch({
                    type: 'refresh',
                    token: result.data.result.token,
                    application: ensureApplicationForPayment(result.data.result.application),
                });
            } catch (error) {
                console.warn(error);
            }
        },
        [apolloClient, dispatch, setFieldValue, token]
    );

    return useMemo(
        () => ({
            onDiscountCodeChange,
        }),
        [onDiscountCodeChange]
    );
};

export default useMobilityPaymentHooks;
