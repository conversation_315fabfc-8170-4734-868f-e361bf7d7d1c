import { ButtonProps, InputProps } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { PromoCodeDataFragment } from '../../../../api/fragments';
import { CompanyTheme, DiscountType } from '../../../../api/types';
import { useThemeComponents } from '../../../../themes/hooks';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import {
    promoCodeValue,
    promoCodePercentage,
} from '../../ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/shared';

const PromoCodeMainContainer = styled.div`
    padding-bottom: 10px;
`;

const PromoCodeContainer = styled.div`
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 10px;

    & input {
        border: none;

        &:focus {
            border: none;
            box-shadow: none;
        }

        ::placeholder {
            /* Chrome, Firefox, Opera, Safari 10.1+ */
            color: #b1b1b1;
        }

        :-ms-input-placeholder {
            /* Internet Explorer 10-11 */
            color: #b1b1b1;
        }

        ::-ms-input-placeholder {
            /* Microsoft Edge */
            color: #b1b1b1;
        }
    }
`;

type ApplyPromoCodeProps = {
    promoCode: PromoCodeDataFragment;
    price: number;
    promoCodeError?: string;
    inputField: InputProps;
    onPromoCodeApply: ButtonProps['onClick'];
    onPromoCodeRemove: () => void;
};

export const usePromoCodeAmount = (promoCode: PromoCodeDataFragment, price: number) => {
    const companyFormats = useCompanyFormats();

    return useMemo(() => {
        if (!promoCode || promoCode.promoType.__typename !== 'DiscountPromoType') {
            return null;
        }

        if (promoCode.promoType.discountType === DiscountType.Value) {
            return companyFormats.formatAmountWithCurrency(promoCodeValue(promoCode, price));
        }

        // round down for percentage
        if (promoCode.promoType.discountType === DiscountType.Percentage) {
            return `${companyFormats.formatPercentage(
                promoCodePercentage(promoCode, price)
            )} (${companyFormats.formatAmountRoundDownWithCurrency(promoCodeValue(promoCode, price))})`;
        }

        return null;
    }, [companyFormats, price, promoCode]);
};

const ApplyPromoCode = ({
    promoCode,
    price,
    promoCodeError,
    inputField,
    onPromoCodeApply,
    onPromoCodeRemove,
}: ApplyPromoCodeProps) => {
    const {
        Input,
        Button,
        theme,
        Calculator: { PromoCodeValidation },
    } = useThemeComponents();
    const { t } = useTranslation(['common']);

    const promoCodeAmount = usePromoCodeAmount(promoCode, price);

    const buttonType = theme === CompanyTheme.PorscheV3 ? 'primary' : 'tertiary';

    const validationProps = {
        removePromoCode: onPromoCodeRemove,
        promoCode,
        promoCodeError,
        promoCodeAmount,
    };

    return (
        <PromoCodeMainContainer>
            <PromoCodeContainer>
                <Input {...inputField} />
                <Button onClick={onPromoCodeApply} style={{ height: '48px' }} type={buttonType}>
                    {t('common:actions.apply')}
                </Button>
            </PromoCodeContainer>
            <PromoCodeValidation {...validationProps} />
        </PromoCodeMainContainer>
    );
};

export default ApplyPromoCode;
