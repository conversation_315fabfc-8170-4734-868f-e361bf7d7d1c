import { Col } from 'antd';
import styled from 'styled-components';
import { LayoutType } from '../../../../api/types';
import { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import DealerInfo from '../../ConfiguratorApplicationEntrypoint/DealerInfo';
import VehicleDetails from '../AdyenPaymentPage/VehicleDetails';
import type { State } from '../Journey/shared';

const StyledCol = styled(Col)`
    padding: ${props => (props.theme.layoutType === LayoutType.PorscheV3 ? '0' : 'inherit')};
`;

type VehicleWithDealerProps = {
    state: State<AllowedApplicationForPayment>;
    shouldShowDealerInfo: boolean;
};

/**
 * A combined component that displays both vehicle details and dealer information.
 * This component is used in payment pages to show the selected vehicle and dealer information.
 */
const VehicleWithDealer = ({ state, shouldShowDealerInfo }: VehicleWithDealerProps) => {
    const { application } = state;

    if (application.__typename === 'MobilityApplication') {
        return null;
    }

    return (
        <>
            <VehicleDetails state={state} />
            {shouldShowDealerInfo && (
                <StyledCol xs={24}>
                    <DealerInfo dealer={application.dealer} />
                </StyledCol>
            )}
        </>
    );
};

export default VehicleWithDealer;
