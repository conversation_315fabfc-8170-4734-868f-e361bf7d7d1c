import { useMemo } from 'react';
import { useRouter } from '../../../../components/contexts/shared';

const defaultColSpans = [
    { lg: 12, md: 12, sm: 24, xs: 24 }, // halfColSpan
    { lg: 8, md: 12, sm: 24, xs: 24 }, // colSpan
    { lg: 16, md: 12, sm: 24, xs: 24 }, // rightColSpan
];

const v3LayoutColSpans = [
    { xl: 12, lg: 24, md: 24, sm: 24, xs: 24 }, // halfColSpan
    { lg: 8, md: 8, sm: 24, xs: 24 }, // colSpan
    { lg: 16, md: 16, sm: 24, xs: 24 }, // rightColSpan
];

const usePaymentSectionColSpans = () => {
    const router = useRouter();

    const layoutType = router?.layout?.__typename;

    return useMemo(() => {
        if (layoutType === 'PorscheV3Layout') {
            return v3LayoutColSpans;
        }

        return defaultColSpans;
    }, [layoutType]);
};

export default usePaymentSectionColSpans;
