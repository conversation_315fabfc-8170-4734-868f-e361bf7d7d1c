/* eslint-disable import/prefer-default-export */
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../components/contexts/shared';
import DefaultNextButton from './NextButton';

type NextButtonProps = {
    form?: string;
    onSubmit?: () => void;
    disabled?: boolean;
};

export const NextButton = ({ form, onSubmit, disabled }: NextButtonProps) => {
    const { t } = useTranslation('customerDetails');
    const { layout } = useRouter();

    return (
        <DefaultNextButton
            key="submit"
            disabled={disabled}
            form={form}
            htmlType="button"
            onClick={onSubmit}
            porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
            type="primary"
        >
            {t('customerDetails:nextButton')}
        </DefaultNextButton>
    );
};
