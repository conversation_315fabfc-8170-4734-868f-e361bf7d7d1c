import { Formik } from 'formik';
import { ComponentType, Dispatch, PropsWithChildren, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PaymentStatus } from '../../../../api/types';
import { DefaultLayoutProps } from '../../../../themes/default/Standard/Layout';
import { useThemeComponents } from '../../../../themes/hooks';
import { AllowedApplicationForPayment, ensureApplicationForPayment } from '../../../../utilities/journeys/payment';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues, {
    AgreementValues,
} from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { useSkipForDepositButton } from '../AdyenPaymentPage/SkipForDepositButton';
import usePaymentAgreements from '../AdyenPaymentPage/usePaymentAgreements';
import type { Action, State } from '../Journey/shared';
import Inner from './Inner';
import useTtbDepositSubmission, { submitPayment } from './useTtbDepositSubmission';

export type TtbPaymentPageProps = {
    state: State<AllowedApplicationForPayment>;
    dispatch: Dispatch<Action<AllowedApplicationForPayment>>;
    CustomLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    promoCodeModuleId?: string;
    ModuleId?: string;
    applicationModuleId?: string;
    giftVoucherModuleId?: string;
    shouldIncludeLayout?: boolean;
};

type TtbPaymentFormValues = AgreementValues & { promoCodeId?: string };

const TtbPaymentPage = ({ state, dispatch, CustomLayout, ...props }: TtbPaymentPageProps) => {
    const { token, application } = state;
    const { t } = useTranslation(['paymentDetails']);
    const { notification } = useThemeComponents();

    const paymentAgreements = usePaymentAgreements(state);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitTtbDeposit = useTtbDepositSubmission();
    const { skipDeposit, render, canSkipDeposit } = useSkipForDepositButton(state);

    useEffect(() => {
        if ([PaymentStatus.Failed, PaymentStatus.Error].includes(application.deposit.status)) {
            notification.error(t('paymentDetails:resultCode.error'));
        }
    }, [application.deposit.status, notification, t]);

    const onSubmit = useHandleError(
        async (values: TtbPaymentFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentRedirecting'),
                duration: 0,
                key: 'primary',
            });
            const { promoCodeId, ...agreementValues } = values;

            if (!skipDeposit && application.deposit.amount !== 0) {
                try {
                    // call payment submission api
                    const paymentResult = await submitPayment(agreementValues, token, promoCodeId);
                    const { redirectUrl } = paymentResult;

                    if (redirectUrl) {
                        // redirect to 3DS
                        window.location.replace(redirectUrl);

                        return;
                    }

                    // there's an error
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                } catch {
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                }
            }

            const result = await submitTtbDeposit(token, agreementValues, skipDeposit).finally(() => {
                notification.destroy('primary');
            });

            const { application: newApplication } = result;

            const ensuredApplication = ensureApplicationForPayment(newApplication);

            if (ensuredApplication.draftFlow.isDepositCompleted) {
                dispatch({
                    type: 'next',
                    token: result.token,
                    application: ensuredApplication,
                });
            } else {
                // there's an error
                notification.warn(t('paymentDetails:resultCode.error'));
            }
        },
        [notification, t, skipDeposit, application, submitTtbDeposit, token, dispatch],
        {}
    );

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.application.deposit.amount > 0 ? validation : null}
        >
            <Inner
                CustomLayout={CustomLayout}
                SkipDepositButton={render}
                canSkipDeposit={canSkipDeposit}
                dispatch={dispatch}
                state={state}
                {...props}
            />
        </Formik>
    );
};

export default TtbPaymentPage;
