import Icon from '@ant-design/icons';
import { Col, Form, Grid, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../components/ScrollToTop';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import AgreementField from '../../../shared/CIPage/ConsentAndDeclarations/AgreementField';
import { getPaymentAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import BookingDeposit from '../../EventApplicationEntrypoint/ThankYou/BookingDeposit';
import LocationInfo from '../../MobilityApplicationEntrypoint/Components/LocationInfo';
import OrderSummary from '../../MobilityApplicationEntrypoint/Components/OrderSummary';
import Stepper, {
    MobilityPageKind,
    AvailableSteps as MobilitySteps,
} from '../../MobilityApplicationEntrypoint/Components/Stepper';
import { calculateTotalPrice } from '../../MobilityApplicationEntrypoint/helper';
import { SkipForDepositButtonProps } from '../AdyenPaymentPage/SkipForDepositButton';
import { JourneyStage } from '../Journey/shared';
import JourneyToolbar from '../shared/JourneyToolbar';
import MobilityPromoCode from '../shared/MobilityPromoCode';
import NextButton from '../shared/NextButton';
import VehicleWithDealer from '../shared/VehicleWithDealer';
import useMobilityPaymentHooks from '../shared/useMobilityPaymentHooks';
import usePaymentSectionColSpans from '../shared/usePaymentSectionColSpans';
import { TtbPaymentPageProps } from './index';
import BackIcon from '../../../../icons/mobility/back.svg';

const Inner = ({
    state,
    dispatch,
    CustomLayout,
    SkipDepositButton,
    canSkipDeposit = false,
    promoCodeModuleId,
    applicationModuleId,
    giftVoucherModuleId,
    shouldIncludeLayout = true,
}: TtbPaymentPageProps & {
    SkipDepositButton: (props: Omit<SkipForDepositButtonProps, 'setSkipDeposit'>) => JSX.Element;
    canSkipDeposit?: boolean;
}) => {
    const { t } = useTranslation(['paymentDetails']);
    const { application, token } = state;
    const { deposit } = application;

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.application.applicantAgreements),
        [state.application.applicantAgreements]
    );

    const { isSubmitting, handleSubmit, validateForm, submitForm, setFieldValue } = useFormikContext<AgreementValues>();

    const screens = Grid.useBreakpoint();
    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const { Button, BackButton } = useThemeComponents();

    const onSkipDeposit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [validateForm, submitForm]);

    const { onDiscountCodeChange } = useMobilityPaymentHooks(token, setFieldValue, dispatch);

    const preferredMobileFooterHeight = useMemo(
        () =>
            [application.__typename === 'MobilityApplication', canSkipDeposit, 'hasPayButton'].filter(Boolean).length *
            80,
        [application.__typename, canSkipDeposit]
    );

    const showDealerInfo = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            return application.event.showDealership && !!application.dealer;
        }

        if (application.__typename === 'MobilityApplication') {
            return false;
        }

        return !!application.dealer;
    }, [application]);

    const paymentSectionColOffset = useMemo(() => {
        if (application.__typename === 'MobilityApplication') {
            return screens.lg && paymentAgreements.length === 0 ? 4 : null;
        }

        return null;
    }, [application.__typename, paymentAgreements.length, screens.lg]);

    const [halfColSpan, colSpan, rightColSpan] = usePaymentSectionColSpans();

    if (deposit.__typename !== 'ApplicationTtbDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    const content = (
        <>
            <FormAutoTouch />
            <Form
                id="completeTtbPaymentForm"
                layout="vertical"
                name="completeTtbPaymentForm"
                onSubmitCapture={handleSubmit}
            >
                <ScrollToTop />
                {application.__typename === 'MobilityApplication' && (
                    <Stepper
                        currentPage={MobilityPageKind.Mobility}
                        currentStep={MobilitySteps.Payment}
                        dispatchMobility={dispatch}
                    />
                )}
                <Row gutter={[24, 24]}>
                    <Col className="paymentDiv" offset={paymentSectionColOffset} {...colSpan}>
                        <Row gutter={[16, 40]}>
                            {application.__typename === 'MobilityApplication' && (
                                <Col xs={24}>
                                    <Title>{t('paymentDetails:titles.orderSummary')}</Title>
                                    <OrderSummary application={application} />
                                    <MobilityPromoCode
                                        application={application}
                                        applicationModuleId={applicationModuleId}
                                        giftVoucher={application.giftVoucher}
                                        giftVoucherModuleId={giftVoucherModuleId}
                                        onDiscountCodeChange={onDiscountCodeChange}
                                        price={calculateTotalPrice(application)}
                                        promoCode={application.promoCode}
                                        promoCodeModuleId={promoCodeModuleId}
                                    />
                                    <LocationInfo application={application} />
                                </Col>
                            )}
                            {application.__typename !== 'MobilityApplication' && (
                                <VehicleWithDealer shouldShowDealerInfo={showDealerInfo} state={state} />
                            )}
                        </Row>
                    </Col>
                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            stage={state.stage}
                            stages={state.stages}
                        >
                            <Row gutter={[24, 24]}>
                                {deposit.amount > 0 && paymentAgreements.length > 0 && (
                                    <Col {...halfColSpan}>
                                        <Title>
                                            {t(
                                                `paymentDetails:messages.${
                                                    application.__typename === 'MobilityApplication'
                                                        ? 'paymentAndRefundPolicy'
                                                        : 'checkAgreements'
                                                }`
                                            )}
                                        </Title>
                                        <Row gutter={[16, 16]}>
                                            {paymentAgreements.map(agreement => (
                                                <Col xs={24}>
                                                    <AgreementField
                                                        key={agreement.id}
                                                        agreement={agreement}
                                                        bordered={application.__typename !== 'MobilityApplication'}
                                                    />
                                                </Col>
                                            ))}
                                        </Row>
                                    </Col>
                                )}
                                {deposit.amount > 0 && (
                                    <Col {...halfColSpan}>
                                        <Title>{t('paymentDetails:titles.paymentTitle')}</Title>
                                        <div style={{ marginBottom: '15px' }}>
                                            <BookingDeposit application={application} depositAmount={deposit.amount} />
                                        </div>
                                    </Col>
                                )}
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>

            <JourneyToolbar preferredMobileFooterHeight={preferredMobileFooterHeight}>
                {application.__typename === 'MobilityApplication' && (
                    <Button
                        key="back"
                        form="bookingForm"
                        htmlType="button"
                        icon={<Icon className="porsche-arrow" component={BackIcon} />}
                        onClick={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
                        porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="tertiary"
                    >
                        {t('paymentDetails:actions.back')}
                    </Button>
                )}
                {canSkipDeposit && (
                    <SkipDepositButton key="skipForNextButton" disabled={false} onClick={onSkipDeposit} />
                )}
                <NextButton
                    key="nextButton"
                    disabled={isSubmitting}
                    form="completeTtbPaymentForm"
                    htmlType="submit"
                    type="primary"
                >
                    {deposit.amount === 0 ? t('paymentDetails:actions.submit') : t('paymentDetails:actions.pay')}
                </NextButton>
            </JourneyToolbar>
        </>
    );

    if (!shouldIncludeLayout) {
        return content;
    }

    return (
        <CustomLayout
            backIcon={
                application.__typename !== 'MobilityApplication' && !application.withCustomerDevice ? (
                    <BackButton type="link">{t('paymentDetails:actions.back')}</BackButton>
                ) : null
            }
            onBack={
                application.__typename !== 'MobilityApplication' && !application.withCustomerDevice
                    ? () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })
                    : null
            }
            preferredMobileFooterHeight={preferredMobileFooterHeight}
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={<Typography style={{ alignItems: 'center', width: '100%' }}>{t('paymentDetails:title')}</Typography>}
            hasFooterBar
        >
            <BasicProLayoutContainer>{content}</BasicProLayoutContainer>
        </CustomLayout>
    );
};

export default Inner;
