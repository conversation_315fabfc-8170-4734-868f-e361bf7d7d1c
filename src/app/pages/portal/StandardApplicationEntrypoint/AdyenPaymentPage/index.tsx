import { Formik } from 'formik';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../themes/hooks';
import { ensureApplicationForPayment } from '../../../../utilities/journeys/payment';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import Inner, { AdyenFormValues } from './Inner';
import { useSkipForDepositButton } from './SkipForDepositButton';
import type { AdyenPaymentPageProps } from './shared';
import useAdyenDepositSubmission from './useAdyenDepositSubmission';
import usePaymentAgreements from './usePaymentAgreements';

const AdyenPaymentPage = ({ state, dispatch, CustomLayout, ...props }: AdyenPaymentPageProps) => {
    const { token } = state;
    const { t } = useTranslation(['paymentDetails']);
    const { notification } = useThemeComponents();
    const paymentAgreements = usePaymentAgreements(state);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitAdyenDeposit = useAdyenDepositSubmission();
    const { skipDeposit, render, canSkipDeposit } = useSkipForDepositButton(state);
    const [adyenSessionResult, setSessionResult] = useState(null);

    const onSubmit = useHandleError(
        async (values: AdyenFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const { promoCodeId, ...agreementValues } = values;
            const result = await submitAdyenDeposit(token, agreementValues, skipDeposit, adyenSessionResult).finally(
                () => {
                    notification.destroy('primary');
                }
            );

            const { application: newApplication } = result;

            dispatch({
                type: 'next',
                token: result.token,
                application: ensureApplicationForPayment(newApplication),
            });
        },
        [notification, t, submitAdyenDeposit, token, skipDeposit, adyenSessionResult, dispatch],
        {}
    );

    const onChangeSessionResult = useCallback((newSessionData: string) => {
        setSessionResult(newSessionData);
    }, []);

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.application.deposit.amount > 0 ? validation : null}
        >
            <Inner
                CustomLayout={CustomLayout}
                SkipDepositButton={render}
                adyenSessionResult={adyenSessionResult}
                canSkipDeposit={canSkipDeposit}
                dispatch={dispatch}
                onChangeSessionResult={onChangeSessionResult}
                state={state}
                {...props}
            />
        </Formik>
    );
};

export default AdyenPaymentPage;
