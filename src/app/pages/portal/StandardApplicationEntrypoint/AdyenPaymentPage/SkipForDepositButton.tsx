import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleType } from '../../../../api/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import { State } from '../Journey/shared';

export type SkipForDepositButtonProps = {
    disabled: boolean;
    setSkipDeposit: Dispatch<SetStateAction<boolean>>;
    onClick?: () => void;
};

const SkipForDepositButton = ({ disabled, setSkipDeposit, onClick }: SkipForDepositButtonProps) => {
    const { t } = useTranslation('paymentDetails');
    const { Button } = useThemeComponents();
    const { layout } = useRouter();

    const onEnhancedClick = useCallback(() => {
        setSkipDeposit(true);

        if (onClick) {
            onClick();
        }
    }, [onClick, setSkipDeposit]);

    return (
        <Button
            key="skipForDeposit"
            disabled={disabled}
            htmlType="button"
            onClick={onEnhancedClick}
            porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
            style={{ marginRight: 'auto' }}
            type="primary"
        >
            {t('paymentDetails:actions.skipForDeposit')}
        </Button>
    );
};

export default SkipForDepositButton;

export const useSkipForDepositButton = (state: State<AllowedApplicationForPayment>) => {
    const { application } = state;

    const [skipDeposit, setSkipDeposit] = useState(false);

    const user = useAccount(true);

    const canSkipDeposit = useMemo(() => {
        switch (application.__typename) {
            case 'StandardApplication':
                return (
                    user &&
                    application.module.__typename === ModuleType.StandardApplicationModule &&
                    application.module.skipForDeposit &&
                    !application.withCustomerDevice
                );

            case 'FinderApplication':
                return (
                    user &&
                    application.module.__typename === ModuleType.FinderApplicationPrivateModule &&
                    application.module.skipForDeposit &&
                    !application.withCustomerDevice
                );

            case 'ConfiguratorApplication':
                return (
                    user &&
                    application.module.__typename === ModuleType.ConfiguratorModule &&
                    application.module.skipForDeposit &&
                    !application.withCustomerDevice
                );

            case 'EventApplication':
                return (
                    user &&
                    application.event.privateAccess &&
                    application.event.skipForDeposit &&
                    !application.withCustomerDevice
                );

            default:
                return false;
        }
    }, [application, user]);

    return useMemo(
        () => ({
            skipDeposit,
            canSkipDeposit,
            render: (props: Omit<SkipForDepositButtonProps, 'setSkipDeposit'>) => (
                <SkipForDepositButton setSkipDeposit={setSkipDeposit} {...props} />
            ),
        }),
        [canSkipDeposit, skipDeposit]
    );
};
