import Icon from '@ant-design/icons';
import { Col, Form, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import ScrollToTop from '../../../../components/ScrollToTop';
import { useRouter } from '../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import AgreementField from '../../../shared/CIPage/ConsentAndDeclarations/AgreementField';
import { getPaymentAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import BookingDeposit from '../../EventApplicationEntrypoint/ThankYou/BookingDeposit';
import LocationInfo from '../../MobilityApplicationEntrypoint/Components/LocationInfo';
import OrderSummary from '../../MobilityApplicationEntrypoint/Components/OrderSummary';
import Stepper, {
    MobilityPageKind,
    AvailableSteps as MobilitySteps,
} from '../../MobilityApplicationEntrypoint/Components/Stepper';
import { calculateTotalPrice } from '../../MobilityApplicationEntrypoint/helper';
import { JourneyStage } from '../Journey/shared';
import JourneyToolbar from '../shared/JourneyToolbar';
import MobilityPromoCode from '../shared/MobilityPromoCode';
import NextButton from '../shared/NextButton';
import VehicleWithDealer from '../shared/VehicleWithDealer';
import useMobilityPaymentHooks from '../shared/useMobilityPaymentHooks';
import usePaymentSectionColOffset from '../shared/usePaymentSectionColOffset';
import usePaymentSectionColSpans from '../shared/usePaymentSectionColSpans';
import AdyenPayment from './AdyenPayment';
import { SkipForDepositButtonProps } from './SkipForDepositButton';
import { AgreementTypes, AdyenPaymentPageProps } from './shared';
import { AdyenResult } from './useAdyenPayment';
import BackIcon from '../../../../icons/mobility/back.svg';

export const getMandatory = (agreement: ApplicationAgreementDataFragment) => {
    switch (agreement.__typename) {
        case AgreementTypes.CheckboxApplicationAgreement:
        case AgreementTypes.MarketingApplicationAgreement:
            return agreement.isMandatory;

        default:
            return false;
    }
};

export type AdyenFormValues = AgreementValues & { promoCodeId?: string };

const Inner = ({
    state,
    dispatch,
    CustomLayout,
    SkipDepositButton,
    canSkipDeposit = false,
    promoCodeModuleId,
    applicationModuleId,
    adyenSessionResult,
    onChangeSessionResult,
    giftVoucherModuleId,
    shouldIncludeLayout = true,
}: AdyenPaymentPageProps & {
    SkipDepositButton: (props: Omit<SkipForDepositButtonProps, 'setSkipDeposit'>) => JSX.Element;
    canSkipDeposit?: boolean;
    adyenSessionResult?: string | null;
    onChangeSessionResult: (newSessionData: string) => void;
}) => {
    const { t } = useTranslation(['paymentDetails']);
    const { application, token } = state;
    const { deposit, draftFlow } = application;

    const { Button, BackButton } = useThemeComponents();

    const [allowToSubmit, setAllowToSubmit] = useState(false);

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.application.applicantAgreements),
        [state.application.applicantAgreements]
    );

    const { values, isSubmitting, handleSubmit, validateForm, submitForm, setFieldValue } =
        useFormikContext<AdyenFormValues>();

    const router = useRouter();
    const basicLayoutHeight =
        router === null || (router && router.layout?.__typename === 'BasicProLayout') ? '100%' : '100vh';

    const onPaymentCompleted = useCallback(
        (result: AdyenResult) => {
            // Result code already authorised from the front-end
            const canSubmit = draftFlow.isDepositCompleted || result.resultCode === 'Authorised';

            if (canSubmit) {
                onChangeSessionResult(result.sessionResult);
                setAllowToSubmit(true);
            }
        },
        [draftFlow.isDepositCompleted, onChangeSessionResult]
    );

    useEffect(() => {
        // Why using useEffect?
        // There is race condition when setting up new session data
        // and submitting the form. We need to wait for both
        if (adyenSessionResult && allowToSubmit) {
            submitForm();
        }
    }, [adyenSessionResult, allowToSubmit, submitForm]);

    const onSkipDeposit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [validateForm, submitForm]);

    if (deposit.__typename !== 'ApplicationAdyenDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    const preferredMobileFooterHeight = useMemo(
        () => [application.__typename === 'MobilityApplication', canSkipDeposit].filter(Boolean).length * 80,
        [application.__typename, canSkipDeposit]
    );

    const { onDiscountCodeChange } = useMobilityPaymentHooks(token, setFieldValue, dispatch);
    const showDealerInfo = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            return application.event.showDealership && !!application.dealer;
        }

        if (application.__typename === 'MobilityApplication') {
            return false;
        }

        return !!application.dealer;
    }, [application]);

    const depositAmount = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            const { dealerId, event } = application;

            const { defaultValue, overrides } = event.depositAmount;
            if (event.depositAmount.overrides.length) {
                const dealerSpecific = overrides.find(override => override.dealerId === dealerId);

                const value = !dealerSpecific ? defaultValue : dealerSpecific.value;

                return value;
            }

            return defaultValue;
        }

        return deposit.amount;
    }, [application, deposit.amount]);

    const paymentSectionColOffset = usePaymentSectionColOffset({
        application,
        paymentAgreements,
    });

    const [halfColSpan, colSpan, rightColSpan] = usePaymentSectionColSpans();

    const content = (
        <>
            <FormAutoTouch />
            <Form id="completeAdyenForm" layout="vertical" name="completeAdyenForm" onSubmitCapture={handleSubmit}>
                <ScrollToTop />
                {application.__typename === 'MobilityApplication' && (
                    <Stepper
                        currentPage={MobilityPageKind.Mobility}
                        currentStep={MobilitySteps.Payment}
                        dispatchMobility={dispatch}
                    />
                )}
                <Row gutter={[24, 24]}>
                    <Col className="paymentDiv" offset={paymentSectionColOffset} {...colSpan}>
                        <Row gutter={[16, 40]}>
                            {application.__typename === 'MobilityApplication' && (
                                <Col xs={24}>
                                    <Title>{t('paymentDetails:titles.orderSummary')}</Title>
                                    <OrderSummary application={application} />
                                    <MobilityPromoCode
                                        application={application}
                                        applicationModuleId={applicationModuleId}
                                        giftVoucher={application.giftVoucher}
                                        giftVoucherModuleId={giftVoucherModuleId}
                                        onDiscountCodeChange={onDiscountCodeChange}
                                        price={calculateTotalPrice(application)}
                                        promoCode={application.promoCode}
                                        promoCodeModuleId={promoCodeModuleId}
                                    />
                                    <LocationInfo application={application} />
                                </Col>
                            )}

                            {application.__typename !== 'MobilityApplication' && (
                                <VehicleWithDealer shouldShowDealerInfo={showDealerInfo} state={state} />
                            )}
                        </Row>
                    </Col>
                    <Col {...rightColSpan}>
                        <JourneySectionWrapper
                            applicationType={application.__typename}
                            stage={state.stage}
                            stages={state.stages}
                        >
                            <Row gutter={[24, 24]}>
                                {depositAmount > 0 && paymentAgreements.length > 0 && (
                                    <Col {...halfColSpan}>
                                        <Title>
                                            {t(
                                                `paymentDetails:messages.${
                                                    application.__typename === 'MobilityApplication'
                                                        ? 'paymentAndRefundPolicy'
                                                        : 'checkAgreements'
                                                }`
                                            )}
                                        </Title>
                                        <Row gutter={[16, 16]}>
                                            {paymentAgreements.map(agreement => (
                                                <Col xs={24}>
                                                    <AgreementField
                                                        key={agreement.id}
                                                        agreement={agreement}
                                                        bordered={application.__typename !== 'MobilityApplication'}
                                                    />
                                                </Col>
                                            ))}
                                        </Row>
                                    </Col>
                                )}
                                {depositAmount > 0 && (
                                    <Col {...halfColSpan}>
                                        <Title>{t('paymentDetails:titles.paymentTitle')}</Title>
                                        <div style={{ marginBottom: '15px' }}>
                                            <BookingDeposit application={application} depositAmount={depositAmount} />
                                        </div>
                                        <div
                                            style={
                                                deposit?.sessionId &&
                                                paymentAgreements.some(
                                                    agreement =>
                                                        !values[agreement.id].isAgreed && getMandatory(agreement)
                                                )
                                                    ? {
                                                          opacity: '30%',
                                                          pointerEvents: 'none',
                                                      }
                                                    : {}
                                            }
                                        >
                                            <AdyenPayment deposit={deposit} onPaymentCompleted={onPaymentCompleted} />
                                        </div>
                                    </Col>
                                )}
                            </Row>
                        </JourneySectionWrapper>
                    </Col>
                </Row>
            </Form>

            <JourneyToolbar preferredMobileFooterHeight={preferredMobileFooterHeight}>
                {application.__typename === 'MobilityApplication' && (
                    <Button
                        key="back"
                        form="bookingForm"
                        htmlType="button"
                        icon={<Icon className="porsche-arrow" component={BackIcon} />}
                        onClick={() => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })}
                        porscheTheme={router?.layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                        type="tertiary"
                    >
                        {t('paymentDetails:actions.back')}
                    </Button>
                )}
                {canSkipDeposit && (
                    <SkipDepositButton key="skipForNextButton" disabled={false} onClick={onSkipDeposit} />
                )}
                {deposit?.amount === 0 && (
                    <NextButton
                        key="nextButton"
                        disabled={isSubmitting}
                        form="completeAdyenForm"
                        htmlType="submit"
                        type="primary"
                    >
                        {t('paymentDetails:actions.submit')}
                    </NextButton>
                )}
            </JourneyToolbar>
        </>
    );

    if (!shouldIncludeLayout) {
        return content;
    }

    return (
        <CustomLayout
            backIcon={
                application.__typename !== 'MobilityApplication' && !application.withCustomerDevice ? (
                    <BackButton type="link">{t('paymentDetails:actions.back')}</BackButton>
                ) : null
            }
            onBack={
                application.__typename !== 'MobilityApplication' && !application.withCustomerDevice
                    ? () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC })
                    : null
            }
            preferredMobileFooterHeight={preferredMobileFooterHeight}
            style={{ height: basicLayoutHeight, backgroundColor: '#fff' }}
            title={<Typography style={{ alignItems: 'center', width: '100%' }}>{t('paymentDetails:title')}</Typography>}
        >
            <BasicProLayoutContainer>{content}</BasicProLayoutContainer>
        </CustomLayout>
    );
};

export default Inner;
