import { Col } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../components/contexts/shared';
import { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import VehicleInfo from '../../ConfiguratorApplicationEntrypoint/ThankYou/VehicleInfo';
import calculateTotalPrice from '../../ConfiguratorApplicationEntrypoint/helper';
import { Title } from '../../EventApplicationEntrypoint/ApplicantForm/shared';
import FinderVehicleInfo from '../../FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInfo';
import VehicleInterest from '../../FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInterest';
import LocationInfo from '../../MobilityApplicationEntrypoint/Components/LocationInfo';
import OrderSummary from '../../MobilityApplicationEntrypoint/Components/OrderSummary';
import { State } from '../Journey/shared';

type VehicleDetailsProps = {
    state: State<AllowedApplicationForPayment>;
};

const VehicleDetails = ({ state }: VehicleDetailsProps) => {
    const { application } = state;

    const translatedString = useTranslatedString();
    const { t } = useTranslation(['paymentDetails', 'finderJourney']);
    const { layout } = useRouter();

    const vehicleData = useMemo(() => {
        if (
            application.__typename === 'ConfiguratorApplication' ||
            application.__typename === 'EventApplication' ||
            application.__typename === 'StandardApplication'
        ) {
            switch (application.vehicle.__typename) {
                case 'LocalVariant':
                    return {
                        make: translatedString(
                            application.vehicle.model.parentModel
                                ? application.vehicle.model.parentModel.make.name
                                : application.vehicle.model.make.name
                        ),
                        variant: translatedString(application.vehicle.name),
                        totalPrice:
                            application.__typename === 'ConfiguratorApplication'
                                ? calculateTotalPrice(application)
                                : null,
                        filename:
                            application.__typename === 'ConfiguratorApplication'
                                ? application.vehicleImage?.filename
                                : application.vehicle?.images?.[0]?.filename,
                        source:
                            application.__typename === 'ConfiguratorApplication'
                                ? application.vehicleImage?.url
                                : application.vehicle?.images?.[0]?.url,
                    };

                default:
                    throw new Error('not implemented');
            }
        }

        return null;
    }, [application, translatedString]);

    if (
        !vehicleData &&
        application.__typename !== 'MobilityApplication' &&
        application.__typename !== 'FinderApplication'
    ) {
        return null;
    }

    if (application.__typename === 'MobilityApplication') {
        return (
            <Col xs={24}>
                <Title>{t('paymentDetails:titles.orderSummary')}</Title>
                <OrderSummary application={application} />
                <LocationInfo application={application} />
            </Col>
        );
    }

    if (application.__typename === 'FinderApplication') {
        if (layout?.__typename === 'PorscheV3Layout') {
            return <VehicleInterest application={application} />;
        }

        return (
            <Col xs={24}>
                <Title>{t('finderJourney:payment.title')}</Title>
                <FinderVehicleInfo application={application} />
            </Col>
        );
    }

    return (
        <Col xs={24}>
            <div className="v3-layout-card">
                <Title size="large">
                    {application.__typename === 'EventApplication'
                        ? t('paymentDetails:titles.vehicleOfInterest')
                        : t('paymentDetails:titles.selectedCar')}
                </Title>
                <VehicleInfo {...vehicleData} />
            </div>
        </Col>
    );
};

export default VehicleDetails;
