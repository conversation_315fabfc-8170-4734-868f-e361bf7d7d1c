import { useMemo } from 'react';
import { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import { getPaymentAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import type { State } from '../Journey/shared';

const usePaymentAgreements = (state: State<AllowedApplicationForPayment>) =>
    useMemo(() => getPaymentAgreements(state.application.applicantAgreements), [state.application.applicantAgreements]);

export default usePaymentAgreements;
