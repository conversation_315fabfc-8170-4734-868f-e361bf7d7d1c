/* eslint-disable max-len */
import { useEffect, useMemo } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useDealerContext, useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { useStandardApplicationContext } from '../StandardApplicationContext';
import useLocalVariants from '../useLocalVariants';
import CalculatorStage, { CalculatorStageProps } from './CalculatorStage';
import useApplyNewApplication from './applyNew/useApplyNewApplication';
import type { CarDetailPageProps } from './shared';

// todo later switch with comparison will be done here
const CarDetailPage = ({ endpoint }: CarDetailPageProps) => {
    const locationState = useLocation().state as { leadId?: string };
    const { variantId } = useParams<{ variantId: string }>();
    // for apply new function to get application info
    const [hasToken, isValid, applyNewApplication] = useApplyNewApplication(endpoint);
    const { lead, loadingLead, setLeadId, setIsApplyNew } = useStandardApplicationContext();
    const { dealerId } = useSingleDealerId();
    const { dealersFromApi, setAllVisible, setDealerOptions } = useDealerContext();
    const translated = useTranslatedString();

    useEffect(() => {
        if (dealersFromApi.length > 0) {
            const dealerOption = dealersFromApi.map(dealer => ({
                value: dealer.id,
                label: translated(dealer.legalName),
            }));
            setDealerOptions(dealerOption);
        }
        setAllVisible();
    }, []);

    useEffect(() => {
        if (locationState?.leadId) {
            setLeadId(locationState.leadId);
        }
    }, [locationState, setLeadId]);

    useEffect(() => {
        if (hasToken && applyNewApplication) {
            setIsApplyNew(true);
        }
    }, [applyNewApplication, hasToken, setIsApplyNew]);

    const { loading: loadingVariants, variants } = useLocalVariants({
        module: endpoint.applicationModule,
        // if there is existing dealer from lead, we ensure variants selected are also the same dealer
        dealerIds: [lead?.dealerId ? lead.dealerId : dealerId],
    });

    /* 
        If application coming from F&I Launchpad (Has lead value that being passed),
        If the assigned vehicle on the lead is not exist / not available currently, 
            then just set to the first variant available on the same model. 
        If the first variant from the same model is still not available also, 
            then use the first available variant fro local variant available
    */

    const isVariantIdOnVariantList = useMemo(
        () => variants?.some(item => item.id === variantId),
        [variantId, variants]
    );
    const selectedVariantId = useMemo(() => {
        if ((variants || []).length) {
            if (lead?.vehicle && !isVariantIdOnVariantList) {
                const availableVariantsFromSameModel = variants.filter(
                    variant => lead.vehicle?.__typename === 'LocalVariant' && variant.modelId === lead.vehicle?.modelId
                );

                return availableVariantsFromSameModel.length ? availableVariantsFromSameModel[0].id : variants[0]?.id;
            }
        }

        return variantId;
    }, [isVariantIdOnVariantList, lead, variantId, variants]);

    if ((hasToken && !applyNewApplication) || loadingVariants || (locationState?.leadId && loadingLead)) {
        return <PortalLoadingElement />;
    }

    // from apply new application but not valid (maybe token expired)
    if (
        (hasToken && !isValid) ||
        (locationState?.leadId && !lead && !isVariantIdOnVariantList) ||
        (lead && !selectedVariantId)
    ) {
        return <NotFoundResult />;
    }

    if (!variants?.length || !selectedVariantId) {
        throw new Error('No variants found');
    }

    return (
        <CalculatorStage
            application={applyNewApplication as CalculatorStageProps['application']}
            dealerIds={[dealerId]}
            endpoint={endpoint}
            variantId={selectedVariantId}
            variants={variants}
        />
    );
};

export default CarDetailPage;
