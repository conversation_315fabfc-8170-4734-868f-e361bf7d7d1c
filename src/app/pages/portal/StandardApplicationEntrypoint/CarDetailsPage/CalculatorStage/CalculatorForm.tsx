/* eslint-disable max-len */
import isEqual from 'fast-deep-equal';
import { Formik, useFormikContext } from 'formik';
import { isEmpty, omit } from 'lodash/fp';
import { SetStateAction, ReactNode, useCallback, useEffect, useRef, Dispatch, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { PromoCodeDataFragment } from '../../../../../api/fragments/PromoCodeData';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { CalculatorContext } from '../../../../../calculator/computing';
import { BankFieldContext } from '../../../../../calculator/computing/defaultFields/bankField';
import { GenericCalculatorValues } from '../../../../../calculator/types';
import Form from '../../../../../components/fields/Form';
import useDebounce from '../../../../../utilities/useDebounce';
import useHandleError from '../../../../../utilities/useHandleError';
import useInitialValue from '../../../../../utilities/useInitialValue';
import {
    hasAppointmentScenario,
    hasFinancingScenario,
} from '../../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useApplyNewCondition from '../../../FinderApplicationPublicAccessEntrypoint/OfferPage/applyNew/useApplyNewCondition';
import type { StandardApplicationState } from '../../Journey/shared';
import useApplyNewAction from '../applyNew/useApplyNewAction';
import { usePersistStandardJourneyValues } from '../usePersistStandardJourneyValues';
import useSubmitDraft from '../useSubmitDraft';
import useUpdateDraft from '../useUpdateDraft';
import { usePromoCodeErrorModal } from './PromoCodeErrorModal';
import type { ActionsProps, CalculatorFormValues, State } from './shared';

export type CalculatorFormProps = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    calculatorContext?: CalculatorContext<GenericCalculatorValues>;
    children: JSX.Element | ReactNode;
    setPromoCode: (value: PromoCodeDataFragment) => void;
    actions: ActionsProps;
    state: State;
    setIsValid: (isValid: boolean) => void;
    dealerId: string;
    setIsSubmitting: Dispatch<SetStateAction<Boolean>>;
    application: StandardApplicationState | null;
    onComparisonPage?: boolean;
};

const CalculatorForm = ({
    calculatorContext,
    children,
    endpoint,
    state,
    actions,
    setPromoCode,
    setIsValid,
    dealerId,
    setIsSubmitting,
    application,
    onComparisonPage,
}: CalculatorFormProps) => {
    const bankFieldContext = calculatorContext?.getFieldContext<BankFieldContext>('bank');
    const navigate = useNavigate();

    const { persistedValue: persistedJourneyValues } = usePersistStandardJourneyValues();

    const withFinancing = useMemo(() => {
        /** In comparison page there is no Apply for Financing checkbox to toggle withFinancing. Therefore it does not need to be updated.
         * And bankFieldContext will only be available when there is selected vehicle in comparison.
         */
        if (onComparisonPage) {
            return hasFinancingScenario(endpoint.applicationModule.scenarios);
        }

        return state.configuration.withFinancing ? bankFieldContext?.availableFinanceProducts.length > 0 : false;
    }, [onComparisonPage, endpoint.applicationModule.scenarios, state.configuration.withFinancing, bankFieldContext]);

    const basicInitConfiguration = {
        withFinancing,
        tradeIn: false,
        testDrive: false,
        withInsurance: state.configuration.withInsurance,
    };

    const { canApplyFinancing, canApplyInsurance, canApplyAppointment } = useApplyNewCondition(
        application,
        endpoint.applicationModule
    );
    const applyNewInitConfiguration = application
        ? {
              ...basicInitConfiguration,
              withFinancing: canApplyFinancing,
              tradeIn: application.configuration.tradeIn,
              testDrive: canApplyAppointment,
              withInsurance: canApplyInsurance,
          }
        : persistedJourneyValues?.calculatorValues?.configuration || basicInitConfiguration;

    const initialValues = useInitialValue<CalculatorFormValues>({
        // enforce the type as non-partial
        calculator: calculatorContext?.values as GenericCalculatorValues,
        // application configuration
        configuration: { ...applyNewInitConfiguration },
    });

    const promoCodeErrorModal = usePromoCodeErrorModal();
    const showPromoCodeErrorModal = useCallback(
        (title: string) => {
            promoCodeErrorModal.open();
            promoCodeErrorModal.changeTitle(title);
        },
        [promoCodeErrorModal]
    );

    const navigateToVehicleCalculator = useDebounce(
        useHandleError<CalculatorFormValues>(async () => {
            if (calculatorContext?.values?.vehicle) {
                navigate(`../details/${calculatorContext.values.vehicle}`, {
                    state: {
                        // insurance values should not be passed to the calculator from comparison page
                        calculatorValues: omit(['insuranceProduct', 'insurerId'], calculatorContext.values),
                        vehicleSuiteId: calculatorContext.values.vehicle,
                    },
                });
            }
        }, [calculatorContext, navigate])
    );

    const submitApplyNew = application ? useApplyNewAction(calculatorContext, endpoint, application) : null;
    const submitDraft = useSubmitDraft(
        calculatorContext,
        endpoint,
        showPromoCodeErrorModal,
        setPromoCode,
        dealerId,
        persistedJourneyValues
    );
    const updateDraft = useUpdateDraft(
        calculatorContext,
        showPromoCodeErrorModal,
        setPromoCode,
        persistedJourneyValues
    );

    const onSubmit = useMemo(() => {
        if (application) {
            return submitApplyNew;
        }

        if (persistedJourneyValues) {
            return updateDraft;
        }

        return submitDraft;
    }, [application, persistedJourneyValues, submitApplyNew, submitDraft, updateDraft]);

    useEffect(() => {
        if (
            endpoint.applicationModule.scenarios.length === 1 &&
            hasAppointmentScenario(endpoint.applicationModule.scenarios)
        ) {
            setIsValid(true);
        } else {
            setIsValid(isEmpty(calculatorContext?.errors));
        }
    }, [
        calculatorContext?.errors,
        endpoint.applicationModule.scenarios,
        endpoint.applicationModule.scenarios.length,
        setIsValid,
    ]);

    return (
        <Formik initialValues={initialValues} onSubmit={onComparisonPage ? navigateToVehicleCalculator : onSubmit}>
            {({ handleSubmit, isSubmitting }) => {
                useEffect(() => {
                    setIsSubmitting(isSubmitting);
                }, [isSubmitting]);

                return (
                    <Form id="calculatorForm" name="calculatorForm" onSubmitCapture={handleSubmit}>
                        <ConfigurationSync actions={actions} state={state} />
                        <CalculatorFormSyncLink calculatorContext={calculatorContext} />
                        {promoCodeErrorModal.render(() => {
                            handleSubmit();
                            promoCodeErrorModal.close();
                        })}
                        {children}
                    </Form>
                );
            }}
        </Formik>
    );
};

export default CalculatorForm;

type CalculatorFormSyncLinkProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
};

const CalculatorFormSyncLink = ({ calculatorContext }: CalculatorFormSyncLinkProps) => {
    const { setFieldValue, values } = useFormikContext<CalculatorFormValues>();

    const previousValuesRef = useRef(values.calculator);
    const currentValues = (calculatorContext || {}).values as CalculatorFormValues['calculator'];
    useEffect(() => {
        if (!isEqual(previousValuesRef.current, currentValues)) {
            setFieldValue('calculator', currentValues, false);
            previousValuesRef.current = currentValues;
        }
    }, [previousValuesRef, currentValues, setFieldValue]);

    return null;
};

type ConfigurationSyncProps = {
    state: State;
    actions: ActionsProps;
};

const ConfigurationSync = ({ state, actions }: ConfigurationSyncProps) => {
    const { values } = useFormikContext<CalculatorFormValues>();

    useEffect(() => {
        if (!isEqual(state.configuration, values.configuration)) {
            actions.setConfiguration(values.configuration);
        }
    }, [actions, state.configuration, values.configuration]);

    return null;
};
