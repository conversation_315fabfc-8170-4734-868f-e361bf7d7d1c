/* eslint-disable max-len */
import { Col, Row } from 'antd';
import { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { PromoCodeDataFragment } from '../../../../../../api/fragments/PromoCodeData';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { LayoutType } from '../../../../../../api/types';
import { InsuranceGridCalculator } from '../../../../../../calculator/Implementations';
import { CalculatorContext } from '../../../../../../calculator/computing';
import { BankFieldContext } from '../../../../../../calculator/computing/defaultFields/bankField';
import { InsurerFieldContext } from '../../../../../../calculator/computing/defaultFields/insurerField';
import { VehicleFieldContext } from '../../../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import CheckboxField from '../../../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../../../themes/hooks';
import useDefaultMarketTypeValue from '../../../../../../utilities/useDefaultMarketTypeValue';
import usePlaceholderValues from '../../../../../../utilities/usePlaceholderValues';
import useTranslatedString from '../../../../../../utilities/useTranslatedString';
import PromoCodeField from '../../../../../shared/CIPage/PromoCode';
import TradeIn from '../../../../../shared/CIPage/TradeIn';
import { CheckboxContainer, StyledInfoCircleFilled } from '../../../../../shared/CIPage/ui';
import useValidPromoCode from '../../../../../shared/useValidPromoCode';
import useApplyNewCondition from '../../../../FinderApplicationPublicAccessEntrypoint/OfferPage/applyNew/useApplyNewCondition';
import {
    ApplicationScenarioConfig,
    OfferPageActionStateProps,
} from '../../../../FinderApplicationPublicAccessEntrypoint/types';
import type { StandardApplicationState } from '../../../Journey/shared';
import type { State } from '../shared';
import Financing from './Financing';
import VehicleBrief from './VehicleBrief';
import VehicleImages from './VehicleImages';
import SectionContainer from './shared';

const VehicleBriefContainer = styled.div`
    ${props => props.theme.layoutType !== LayoutType.PorscheV3 && 'margin-bottom: 40px;'};
`;

export type CalculatorPageProps = {
    applicationScenario: ApplicationScenarioConfig;
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    setPromoCode: React.Dispatch<React.SetStateAction<PromoCodeDataFragment | string>>;
    promoCode: PromoCodeDataFragment;
    dealerId: string;
    state: State;
    application: StandardApplicationState | null;
    setSelectVehicleModalVisible: (visible: boolean) => void;
};

const Index = ({
    applicationScenario,
    calculatorContext,
    endpoint,
    setPromoCode,
    promoCode,
    dealerId,
    state,
    application,
    setSelectVehicleModalVisible,
}: CalculatorPageProps) => {
    const [showCarPriceModal, setShowCarPriceModal] = useState(false);
    const { t } = useTranslation(['calculators', 'carDetails']);

    const { getFieldContext } = calculatorContext;

    const { Checkbox, Tooltip } = useThemeComponents();

    const [promoCodeError, setPromoCodeError] = useState<string>('');
    const translateString = useTranslatedString();

    const { selectedBank } = calculatorContext.getFieldContext<BankFieldContext>('bank');

    const { availableInsuranceProducts } = calculatorContext.getFieldContext<InsurerFieldContext>('insurerId');

    // Intentionally froze selected bank, so it won't rerender on bank change
    const initialSelectedBank = useRef(selectedBank);

    const marketTypeValue = useDefaultMarketTypeValue(dealerId, initialSelectedBank.current);
    const placeholders = usePlaceholderValues({ module: endpoint.applicationModule, marketTypeValue });

    const priceDisclaimer = useMemo(() => {
        const data =
            endpoint.applicationModule.dealerPriceDisclaimer?.overrides?.find(
                ({ dealerId: inputId }) => inputId === dealerId
            )?.value ?? endpoint.applicationModule.dealerPriceDisclaimer?.defaultValue;

        return translateString(data, placeholders);
    }, [endpoint, translateString, dealerId, placeholders]);

    const vehicleFieldContext = getFieldContext<VehicleFieldContext>('vehicle');
    const { selectedVehicle } = vehicleFieldContext;

    const hasValidPromoCode = useValidPromoCode({
        dealerId,
        variantSuiteId: selectedVehicle?.versioning.suiteId,
        promoCodeModuleId: endpoint.applicationModule.promoCodeModule?.id,
        applicationModuleId: endpoint.applicationModule.id,
    });

    const { canApplyFinancing, canApplyInsurance, canApplyAppointment } = useApplyNewCondition(
        application,
        endpoint.applicationModule
    );

    const isFinancingSectionVisible = useMemo(
        () => (application && canApplyFinancing) || !application,
        [application, canApplyFinancing]
    );

    const isInsuranceSectionVisible = useMemo(
        () => availableInsuranceProducts.length > 0 && ((application && canApplyInsurance) || !application),
        [application, availableInsuranceProducts, canApplyInsurance]
    );

    const isAppointmentSectionVisible = useMemo(
        () => (application && canApplyAppointment) || !application,
        [application, canApplyAppointment]
    );

    return (
        <Row gutter={40}>
            <Col lg={14} md={12} xs={24}>
                <VehicleImages calculatorContext={calculatorContext} />
            </Col>
            <Col lg={10} md={12} xs={24}>
                <div className="v3-layout-card">
                    <VehicleBriefContainer>
                        <VehicleBrief
                            application={application}
                            calculatorContext={calculatorContext}
                            hasValidPromoCode={!!promoCode}
                            onClickCarPrice={() => setShowCarPriceModal(true)}
                            priceDisclaimer={priceDisclaimer}
                            promoCode={promoCode}
                            setSelectVehicleModalVisible={setSelectVehicleModalVisible}
                        />
                    </VehicleBriefContainer>
                    {isFinancingSectionVisible && (
                        <Financing
                            applicationScenario={applicationScenario}
                            calculatorContext={calculatorContext}
                            dealerId={dealerId}
                            endpoint={endpoint}
                            setShowCarPriceModal={setShowCarPriceModal}
                            showCarPriceModal={showCarPriceModal}
                            state={state}
                        />
                    )}
                    {isInsuranceSectionVisible && (
                        <SectionContainer
                            hidden={
                                !(
                                    state.configuration.withInsurance ||
                                    endpoint.applicationModule.showInsuranceCalculator
                                )
                            }
                            isCheckboxChecked={
                                !endpoint.applicationModule.tradeIn ? true : state.configuration.withInsurance
                            }
                        >
                            {(applicationScenario.withInsurance ||
                                endpoint.applicationModule.showInsuranceCalculator) && (
                                <InsuranceGridCalculator
                                    applicationModule={endpoint.applicationModule}
                                    calculatorContext={calculatorContext}
                                    dealerId={dealerId}
                                    hideCheckbox={!applicationScenario.isInsuranceOptional}
                                    initialInsuranceConfiguration={
                                        (state.configuration.withInsurance &&
                                            endpoint.applicationModule.isInsuranceOptional) ||
                                        (endpoint.applicationModule.showInsuranceCalculator &&
                                            !endpoint.applicationModule.isInsuranceOptional)
                                    }
                                    isPublicAccess={endpoint.__typename === 'StandardApplicationPublicAccessEntrypoint'}
                                />
                            )}
                        </SectionContainer>
                    )}
                    {isAppointmentSectionVisible && (
                        <SectionContainer
                            hidden={!endpoint.applicationModule.testDrive}
                            isCheckboxChecked={!endpoint.applicationModule.tradeIn}
                        >
                            <CheckboxContainer hasPrice={false} hasTooltip={false} hideCheckbox={false}>
                                <CheckboxField customComponent={Checkbox} name="configuration.testDrive">
                                    {t('carDetails:bookTestDrive.label')}
                                    <Tooltip placement="top" title={t('carDetails:bookTestDrive.tooltip')}>
                                        <StyledInfoCircleFilled />
                                    </Tooltip>
                                </CheckboxField>
                            </CheckboxContainer>
                        </SectionContainer>
                    )}
                    {isFinancingSectionVisible && (
                        <SectionContainer hidden={!endpoint.applicationModule.tradeIn} isCheckboxChecked>
                            <TradeIn
                                calculatorContext={calculatorContext}
                                disabledCheckbox={application && application.configuration.tradeIn}
                                isTradeInAmountVisible={endpoint.applicationModule.isTradeInAmountVisible}
                                moduleType={endpoint.applicationModule.__typename}
                                state={state as unknown as OfferPageActionStateProps['state']}
                            />
                        </SectionContainer>
                    )}
                    {!application && (
                        <SectionContainer hidden={!hasValidPromoCode} isCheckboxChecked>
                            <PromoCodeField
                                key="promoCode"
                                applicationModuleId={endpoint.applicationModule.id}
                                calculatorContext={calculatorContext}
                                promoCode={promoCode}
                                promoCodeError={promoCodeError}
                                promoCodeModuleId={endpoint.applicationModule.promoCodeModule?.id}
                                setPromoCode={setPromoCode}
                                setPromoCodeError={setPromoCodeError}
                            />
                        </SectionContainer>
                    )}
                </div>
            </Col>
        </Row>
    );
};

export default Index;
