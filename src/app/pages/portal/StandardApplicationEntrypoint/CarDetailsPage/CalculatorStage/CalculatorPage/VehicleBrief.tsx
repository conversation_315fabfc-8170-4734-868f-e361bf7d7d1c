/* eslint-disable max-len */
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { PromoCodeDataFragment } from '../../../../../../api';
import { ApplicationMarket } from '../../../../../../api/types';
import { CalculatorContext, Vehicle } from '../../../../../../calculator/computing';
import { VehicleFieldContext } from '../../../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import ParagraphEllipsis from '../../../../../../components/ParagraphEllipsis';
import { useRouter } from '../../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../../themes/hooks';
import renderMarkdown from '../../../../../../utilities/renderMarkdown';
import useCompanyFormats from '../../../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../../../utilities/useTranslatedString';
import { Price, PriceContainer } from '../../../../../shared/CIPage/ui';
import { promoCodeValue } from '../../../../ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/shared';
import type { StandardApplicationState } from '../../../Journey/shared';
import ChangeVehicleTrigger from './ChangeVehicleTrigger';

const PriceDisclaimerContainer = styled.div`
    margin: 10px 0px;
    line-height: 1.3;
    opacity: 0.8;

    .ant-typography {
        margin-bottom: 0px;
    }
`;

export const getLocalVariant = (vehicle: Vehicle) => {
    switch (vehicle?.__typename) {
        case 'LocalVariant':
            return vehicle;

        default:
            throw new Error('Vehicle Type not supported');
    }
};

type VehicleBriefProps = {
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    hasValidPromoCode: boolean;
    promoCode?: PromoCodeDataFragment;
    priceDisclaimer?: string;
    onClickCarPrice?: () => void;
    application: StandardApplicationState | null;
    setSelectVehicleModalVisible: (visible: boolean) => void;
};

const VehicleBrief = ({
    calculatorContext,
    hasValidPromoCode,
    promoCode,
    priceDisclaimer,
    onClickCarPrice,
    application,
    setSelectVehicleModalVisible,
}: VehicleBriefProps) => {
    const { t } = useTranslation('finder');
    const translatedString = useTranslatedString();
    const { formatAmountWithCurrency } = useCompanyFormats();
    const { theme } = useThemeComponents();
    const { layout } = useRouter();

    const vehicleFieldContext = calculatorContext.getFieldContext<VehicleFieldContext>('vehicle');

    const selectedVehicleName = vehicleFieldContext.selectedVehicle
        ? getLocalVariant(vehicleFieldContext.selectedVehicle).name
        : null;

    const [isCoeEnabled, coeAmount] = useMemo(() => {
        switch (calculatorContext.values.market) {
            case ApplicationMarket.Singapore:
                return [true, calculatorContext.values.coe];

            default:
                return [false, 0];
        }
    }, [calculatorContext.values]);

    const { carPriceAfterDiscount, totalPrice } = calculatorContext.values;
    const promoAmount = hasValidPromoCode ? promoCodeValue(promoCode, totalPrice) : 0;

    return (
        <div>
            {layout?.__typename !== 'PorscheV3Layout' && (
                <ChangeVehicleTrigger
                    allowChangeVehicle={!application}
                    selectedVehicleName={selectedVehicleName ? translatedString(selectedVehicleName) : ''}
                    setModalVisible={setSelectVehicleModalVisible}
                    variant="default"
                />
            )}
            <PriceContainer>
                <Price
                    className="carPrice"
                    clickable={!!onClickCarPrice}
                    companyTheme={theme}
                    isPromoValid={hasValidPromoCode}
                    onClick={onClickCarPrice}
                    promo={promoAmount}
                >
                    <span>{t('finder:offerPage.price.label')}</span>
                    <span>{formatAmountWithCurrency(carPriceAfterDiscount)}</span>
                </Price>
                {promoAmount && hasValidPromoCode ? (
                    <Price companyTheme={theme} isCoeEnabled={isCoeEnabled}>
                        <span>
                            {t(
                                isCoeEnabled
                                    ? 'finder:offerPage.priceWithoutCoe.label'
                                    : 'finder:offerPage.finalPrice.label'
                            )}
                        </span>
                        <span>
                            {carPriceAfterDiscount && formatAmountWithCurrency(carPriceAfterDiscount - promoAmount)}
                        </span>
                    </Price>
                ) : null}
                {coeAmount ? (
                    <Price companyTheme={theme} isCoeEnabled={isCoeEnabled}>
                        <span>{t('finder:offerPage.estimatedCoe.label')}</span>
                        <span>{formatAmountWithCurrency(coeAmount)}</span>
                    </Price>
                ) : null}
                {coeAmount ? (
                    <Price
                        className="totalPrice"
                        companyTheme={theme}
                        fieldKey="priceIncludeCoe"
                        isCoeEnabled={isCoeEnabled}
                    >
                        <span>{t('finder:offerPage.priceIncludeCoe.label')}</span>
                        <span>{totalPrice && formatAmountWithCurrency(totalPrice)}</span>
                    </Price>
                ) : null}
                {priceDisclaimer && (
                    <PriceDisclaimerContainer>
                        <ParagraphEllipsis
                            collapseText={t('calculators:readLess')}
                            expandText={t('calculators:readMore')}
                            row={1}
                        >
                            {renderMarkdown(priceDisclaimer)}
                        </ParagraphEllipsis>
                    </PriceDisclaimerContainer>
                )}
            </PriceContainer>
        </div>
    );
};

export default VehicleBrief;
