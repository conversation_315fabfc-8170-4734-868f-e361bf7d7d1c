/* eslint-disable max-len */
import { isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { useGetLowestMonthlyInstalmentQuery } from '../../../../../../api/queries/getLowestMonthlyInstalment';
import { LowestMonthlyInstalmentPayload } from '../../../../../../api/types';
import { GenericGridCalculator } from '../../../../../../calculator/Implementations';
import { CalculatorContext } from '../../../../../../calculator/computing';
import { BankFieldContext } from '../../../../../../calculator/computing/defaultFields/bankField';
import { VehicleFieldContext } from '../../../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import CheckboxField from '../../../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../../../themes/hooks';
import getDefaultTotalPrice from '../../../../../../utilities/getDefaultTotalPrice';
import { roundUpWithPrecision } from '../../../../../../utilities/rounding';
import useCompanyFormats from '../../../../../../utilities/useCompanyFormats';
import useDefaultMarketTypeValue from '../../../../../../utilities/useDefaultMarketTypeValue';
import usePlaceholderValues from '../../../../../../utilities/usePlaceholderValues';
import FinancingDisclaimer from '../../../../../shared/CIPage/FinancingDisclaimer';
import { CheckboxContainer } from '../../../../../shared/CIPage/ui';
import { ApplicationScenarioConfig } from '../../../../FinderApplicationPublicAccessEntrypoint/types';
import type { State } from '../shared';
import { VehiclePrice } from './CarPriceField';
import CarPriceFieldModal from './CarPriceFieldModal';
import { getLocalVariant } from './VehicleBrief';
import SectionContainer from './shared';

const CalculatorContainer = styled.div`
    margin-top: 16px;
`;

export type FinancingProps = {
    applicationScenario: ApplicationScenarioConfig;
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    calculatorContext: CalculatorContext<GenericCalculatorValues>;
    dealerId: string;
    state: State;
    showCarPriceModal: boolean;
    setShowCarPriceModal: (v: boolean) => void;
};

const Financing = ({
    applicationScenario,
    calculatorContext,
    endpoint,
    dealerId,
    state,
    showCarPriceModal,
    setShowCarPriceModal,
}: FinancingProps) => {
    const [calculatorExpand, setCalculatorExpand] = useState(
        state.configuration.withFinancing || endpoint.applicationModule.showFinanceCalculator
    );
    const { t } = useTranslation(['calculators', 'carDetails']);

    const {
        getFieldContext,
        latestErrorMessage,
        setLatestErrorMessage,
        change,
        values: calculatorValues,
    } = calculatorContext;

    useEffect(() => {
        change('isFinancingEnabled', state.configuration.withFinancing);
    }, [change, state.configuration.withFinancing]);

    const toggleCalculator = useCallback(() => {
        const newCalculatorExpand = !calculatorExpand;

        if (change) {
            change('isFinancingEnabled', newCalculatorExpand);
        }
        setCalculatorExpand(newCalculatorExpand);
    }, [setCalculatorExpand, change, calculatorExpand]);

    const { Checkbox, ErrorMessageBox, WarningMessageBox } = useThemeComponents();

    const { availableFinanceProducts, selectedBank } = calculatorContext.getFieldContext<BankFieldContext>('bank');

    // Intentionally froze selected bank, so it won't rerender on bank change
    const initialSelectedBank = useRef(selectedBank);

    const marketTypeValue = useDefaultMarketTypeValue(dealerId, initialSelectedBank.current);

    const { formatAmountWithCurrency, amountDecimals } = useCompanyFormats();

    const showFromValue = useMemo(
        () => !!endpoint.applicationModule.showFromValueOnVehicleDetails,
        [endpoint.applicationModule]
    );

    const vehicleFieldContext = getFieldContext<VehicleFieldContext>('vehicle');
    const { selectedVehicle } = vehicleFieldContext;

    const [totalPrice, carPrice] = useMemo(() => {
        if (!selectedVehicle) {
            return [];
        }

        const variant = getLocalVariant(selectedVehicle);

        return [
            getDefaultTotalPrice(variant.vehiclePrice, endpoint.applicationModule.marketType, marketTypeValue),
            variant.vehiclePrice,
        ];
    }, [selectedVehicle, endpoint.applicationModule, marketTypeValue]);

    const data: LowestMonthlyInstalmentPayload = useMemo(
        () => ({
            variantSuiteId: selectedVehicle?.versioning.suiteId,
            totalPrice,
            carPrice,
            bankModuleId: endpoint.applicationModule.bankModuleId,
            applicationModuleId: endpoint.applicationModule.id,
            dealerId,
            assignedOnly: true,
        }),
        [selectedVehicle, totalPrice, carPrice, endpoint.applicationModule, dealerId]
    );

    const { data: lowestMonthlyInstalmentData } = useGetLowestMonthlyInstalmentQuery({
        variables: {
            data,
        },
        skip: !showFromValue,
    });

    const financingTitle = useMemo(
        () =>
            applicationScenario.withFinancing
                ? t('calculators:fields.withFinancing.label')
                : t('calculators:fields.financingCalculator.label'),
        [applicationScenario.withFinancing, t]
    );

    const closeErrorMessageBox = useCallback(() => {
        setLatestErrorMessage(latestErrorMessage?.fieldName, null);
    }, [latestErrorMessage?.fieldName, setLatestErrorMessage]);

    const dynamicMarketTypeValue = useDefaultMarketTypeValue(dealerId, selectedBank);
    const dynamicPlaceholderValues = usePlaceholderValues({
        module: endpoint.applicationModule,
        marketTypeValue: dynamicMarketTypeValue,
    });

    const showNoFPWarning =
        availableFinanceProducts.length <= 0 &&
        endpoint.applicationModule.showFinanceCalculator &&
        !applicationScenario.isFinancingOptional;

    return (
        <>
            {showNoFPWarning && (
                <SectionContainer isCheckboxChecked>
                    <WarningMessageBox
                        description={t(`calculators:notification.noFinanceProducts.description`)}
                        message={t(`calculators:notification.noFinanceProducts.title`)}
                    />
                </SectionContainer>
            )}

            {availableFinanceProducts.length > 0 && (
                <SectionContainer
                    hidden={!(applicationScenario.withFinancing || endpoint.applicationModule.showFinanceCalculator)}
                    isCheckboxChecked={
                        !endpoint.applicationModule.tradeIn &&
                        !(state.configuration.withInsurance || endpoint.applicationModule.showInsuranceCalculator)
                            ? true
                            : state.configuration.withFinancing
                    }
                >
                    <CheckboxContainer
                        className="financing-checkbox-container"
                        hasTooltip={false}
                        hideCheckbox={!applicationScenario.isFinancingOptional}
                        hasPrice
                    >
                        {applicationScenario.isFinancingOptional ? (
                            <CheckboxField
                                customComponent={Checkbox}
                                name="configuration.withFinancing"
                                onClick={toggleCalculator}
                            >
                                {financingTitle}
                            </CheckboxField>
                        ) : (
                            <span>{financingTitle}</span>
                        )}
                        <span>
                            {t('calculators:monthlyPaymentInfo', {
                                value: formatAmountWithCurrency(
                                    roundUpWithPrecision(
                                        lowestMonthlyInstalmentData?.lowestMonthlyInstalment || 0,
                                        amountDecimals
                                    )
                                ),
                            })}
                        </span>
                    </CheckboxContainer>

                    <CalculatorContainer hidden={!calculatorExpand}>
                        {!isNil(calculatorContext.values.market) && (
                            <GenericGridCalculator calculatorContext={calculatorContext}>
                                <CarPriceFieldModal
                                    fieldKey="carPrice"
                                    isDiscountEnabled={endpoint.applicationModule.flexibleDiscount.isEnabled}
                                    onCancel={() => setShowCarPriceModal(false)}
                                    onOk={() => setShowCarPriceModal(false)}
                                    open={showCarPriceModal}
                                    size={0}
                                />
                            </GenericGridCalculator>
                        )}

                        <FinancingDisclaimer
                            applicationModule={endpoint.applicationModule}
                            dealerId={dealerId}
                            disclaimerParameters={dynamicPlaceholderValues}
                            financeProductId={calculatorValues.financeProduct}
                            totalPrice={totalPrice}
                            vehicleId={data.variantSuiteId}
                        />
                        {latestErrorMessage?.message && (
                            <ErrorMessageBox
                                description={latestErrorMessage.message}
                                onClose={closeErrorMessageBox}
                                closable
                            />
                        )}
                    </CalculatorContainer>
                </SectionContainer>
            )}

            <SectionContainer
                hidden={applicationScenario.withFinancing || endpoint.applicationModule.showFinanceCalculator}
                isCheckboxChecked
            >
                <VehiclePrice>{formatAmountWithCurrency(calculatorValues.carPriceAfterDiscount)}</VehiclePrice>
            </SectionContainer>
        </>
    );
};

export default Financing;
