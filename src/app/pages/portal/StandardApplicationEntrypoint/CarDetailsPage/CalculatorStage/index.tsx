/* eslint-disable max-len */
import { MailOutlined } from '@ant-design/icons';
import { Button as AntdButton } from 'antd';
import dayjs from 'dayjs';
import { isEmpty, isEqual, isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { PromoCodeDataFragment } from '../../../../../api/fragments/PromoCodeData';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { VehicleCalculatorSpecsFragment } from '../../../../../api/fragments/VehicleCalculatorSpecs';
import { FinanceProductSortingField, InsuranceProductSortingField, SortingOrder } from '../../../../../api/types';
import { GenericCalculatorProvider } from '../../../../../calculator/CalculatorProvider';
import { VehicleFieldContext } from '../../../../../calculator/computing/defaultFields/vehicleField';
import { defaultInsuranceAge } from '../../../../../calculator/computing/insuranceFields/dateOfBirthField';
import { CalculatorContext } from '../../../../../calculator/computing/types';
import type { ConfigurationValues } from '../../../../../calculator/form/types';
import { GenericCalculatorValues } from '../../../../../calculator/types';
import PortalLoadingElement from '../../../../../components/PortalLoadingElement';
import { useMultipleDealerIds } from '../../../../../components/contexts/DealerContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getMappedCustomerFieldValues } from '../../../../../utilities/kycPresets';
import useDefaultMarketTypeValue from '../../../../../utilities/useDefaultMarketTypeValue';
import useInitialValue from '../../../../../utilities/useInitialValue';
import useMarketSpecificCalculatorValues from '../../../../../utilities/useMarketSpecificCalculatorValues';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
} from '../../../../admin/ModuleDetailsPage/modules/implementations/shared';
import useLocalFinanceProducts from '../../../../shared/CIPage/useLocalFinanceProducts';
import useLocalInsuranceProducts from '../../../../shared/CIPage/useLocalInsuranceProduct';
import getIsDealerOptionsVisible, {
    getIncludeDealerOptionsForFinancing,
} from '../../../../shared/getIsDealerOptionsVisible';
import getIsFlexibleDiscountEnabled from '../../../../shared/getIsFlexibleDiscountEnabled';
import { ApplicationScenarioConfig } from '../../../FinderApplicationPublicAccessEntrypoint/types';
import { HeaderButtonContainer } from '../../CarListingPage/CarListingPageInner';
import type { StandardApplicationState } from '../../Journey/shared';
import { useStandardApplicationContext } from '../../StandardApplicationContext';
import { FooterButtonContainer } from '../../styledComponents';
import { usePersistStandardJourneyValues } from '../usePersistStandardJourneyValues';
import CalculatorForm from './CalculatorForm';
import CalculatorPage from './CalculatorPage';
import CalculatorFooter from './CalculatorPage/CalculatorFooter';
import ChangeVehicleTrigger from './CalculatorPage/ChangeVehicleTrigger';
import SelectVehicleModal from './CalculatorPage/SelectVehicleModal';
import { getLocalVariant } from './CalculatorPage/VehicleBrief';
import CompareAction from './CompareAction';
import { useShareModal } from './ShareModal';
import type { ActionsProps, State } from './shared';

export type CalculatorStageProps = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    dealerIds: string[];
    application: StandardApplicationState | null;
    variantId: string;
    variants: VehicleCalculatorSpecsFragment[];
};

export const LinkButton = styled(AntdButton)`
    &.ant-btn {
        font-size: 16px;
        padding-left: 0;
        padding-right: 0;
    }
`;

type SetConfigurationAction = { type: 'setConfiguration'; configuration: ConfigurationValues };

type Action = SetConfigurationAction;

export const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setConfiguration':
            return { ...state, configuration: action.configuration };

        default:
            return state;
    }
};

const CalculatorStage = ({ endpoint, dealerIds, application, variantId, variants }: CalculatorStageProps) => {
    const { t } = useTranslation('carDetails');

    const { lead } = useStandardApplicationContext();

    const navigate = useNavigate();
    const { layout } = useRouter();

    const shareModal = useShareModal();
    const [promoCode, setPromoCode] = useState<PromoCodeDataFragment>(null);
    const { Button, StandardLayout, BackButton } = useThemeComponents();
    const { state: stateLocation } = useLocation();
    const { setDealerOptions, setVisible, dealers } = useMultipleDealerIds();

    const [isValid, setIsValid] = useState<boolean>(false);

    const { applicationModule } = endpoint;
    const translated = useTranslatedString();

    const { persistedValue: persistedJourneyValues, transformPersistedCalculatorValues } =
        usePersistStandardJourneyValues();

    const applicationScenario = useMemo((): ApplicationScenarioConfig => {
        const withFinancing = hasFinancingScenario(applicationModule.scenarios);
        const isFinancingOptional = withFinancing ? applicationModule.isFinancingOptional : false;
        const withInsurance = hasInsuranceScenario(applicationModule.scenarios);
        const isInsuranceOptional = withInsurance ? applicationModule.isInsuranceOptional : false;
        const testDrive = false;

        return {
            withFinancing,
            isFinancingOptional,
            withInsurance,
            isInsuranceOptional,
            testDrive,
        };
    }, [applicationModule]);

    const [state, dispatch] = useReducer(reducer, {
        configuration: !isEmpty(persistedJourneyValues?.calculatorValues?.configuration)
            ? persistedJourneyValues.calculatorValues.configuration
            : {
                  withFinancing: applicationScenario.withFinancing,
                  tradeIn: false,
                  testDrive: false,
                  withInsurance: applicationScenario.withInsurance,
              },
    });

    const actions: ActionsProps = useMemo(
        () => ({
            setConfiguration: (configuration: ConfigurationValues) =>
                dispatch({ type: 'setConfiguration', configuration }),
        }),
        [dispatch]
    );
    const canNotNextForApplyNew = useMemo(
        () =>
            application &&
            !(state.configuration.withFinancing || state.configuration.withInsurance || state.configuration.testDrive),
        [state.configuration, application]
    );

    // load dependencies

    const { loading: loadingFinanceProducts, financeProducts } = useLocalFinanceProducts({
        module: endpoint.applicationModule,
        sort: { field: FinanceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds,
    });

    const { loading: loadingInsuranceProducts, insuranceProducts } = useLocalInsuranceProducts({
        module: endpoint.applicationModule,
        sort: { field: InsuranceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds,
    });

    const previousEndpointDealer = useRef(null);

    useEffect(() => {
        if (
            endpoint.__typename === 'StandardApplicationPublicAccessEntrypoint' &&
            endpoint.applicationModule.dealerVehicles.length > 0
        ) {
            // to hide up the options in dealer dropdown
            const endpointDealer = endpoint.applicationModule.dealerVehicles.filter(
                dealer =>
                    !dealer.vehicleSuiteIds.includes(stateLocation?.vehicleSuiteId) ||
                    !dealerIds.includes(dealer.dealerId)
            );
            if (
                endpointDealer.length &&
                !isEqual(
                    previousEndpointDealer.current,
                    endpointDealer.map(dealer => dealer.dealerId)
                )
            ) {
                previousEndpointDealer.current = endpointDealer.map(dealer => dealer.dealerId);
                setVisible(
                    endpointDealer.map(dealer => dealer.dealerId),
                    false
                );
            }
        }
    }, [endpoint.__typename, setDealerOptions, translated]);

    useEffect(() => {
        const dealerOption = dealers
            .filter(
                dealer =>
                    dealerIds.includes(dealer.id) &&
                    dealer.visible &&
                    endpoint.__typename === 'StandardApplicationPublicAccessEntrypoint' &&
                    endpoint.dealerIds.some(endpointDealerContext => endpointDealerContext.includes(dealer.id))
            )
            .map(({ visible, id }) => {
                if (visible && endpoint.__typename === 'StandardApplicationPublicAccessEntrypoint') {
                    const dealerDetail = endpoint.applicationModule.dealerVehicles.find(
                        dealer =>
                            dealer.dealerId === id && dealer.vehicleSuiteIds.includes(stateLocation?.vehicleSuiteId)
                    )?.dealer;

                    if (isNil(dealerDetail)) {
                        return null;
                    }

                    return {
                        value: dealerDetail.id,
                        label: translated(dealerDetail.legalName),
                    };
                }

                return null;
            })
            .filter(Boolean);
        setDealerOptions(dealerOption);
    }, [endpoint.__typename, setDealerOptions, translated]);

    const marketTypeValue = useDefaultMarketTypeValue(dealerIds.length > 1 ? null : dealerIds[0]);

    const marketSpecificCalculatorValues = useMarketSpecificCalculatorValues(
        applicationModule.marketType,
        marketTypeValue
    );

    const customerDataFromLead = useMemo(() => {
        if (!lead?.customer?.fields.length) {
            return null;
        }

        return getMappedCustomerFieldValues(lead.customer.fields);
    }, [lead]);

    const defaultDateOfBirthFromLead = useMemo(
        () => (customerDataFromLead?.Birthday ? dayjs(customerDataFromLead.Birthday.value) : null),
        [customerDataFromLead]
    );

    const defaultDateOfBirth = useMemo(() => dayjs().subtract(defaultInsuranceAge, 'year'), []);

    const insuranceCalculatorValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (!applicationModule.showInsuranceCalculator) {
            return {};
        }

        return {
            isInsuranceEnabled: hasInsuranceScenario(applicationModule.scenarios),
            dateOfBirth: defaultDateOfBirthFromLead || defaultDateOfBirth,
            noClaimDiscount: 50,
            // For standard application, date of registration default to today
            dateOfRegistration: dayjs(),
            yearsOfDriving: 8,
            insurancePremium: undefined,
        };
    }, [
        applicationModule.scenarios,
        applicationModule.showInsuranceCalculator,
        defaultDateOfBirth,
        defaultDateOfBirthFromLead,
    ]);

    const calculatorsValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (stateLocation?.calculatorValues) {
            return { ...stateLocation.calculatorValues, ...insuranceCalculatorValues };
        }

        if (persistedJourneyValues?.calculatorValues?.calculator) {
            return transformPersistedCalculatorValues(persistedJourneyValues);
        }

        return {
            ...marketSpecificCalculatorValues,
            ...insuranceCalculatorValues,
            vehicle: variantId,
            isFinancingEnabled: applicationScenario.withFinancing,
        };
    }, [
        stateLocation?.calculatorValues,
        persistedJourneyValues,
        marketSpecificCalculatorValues,
        insuranceCalculatorValues,
        variantId,
        applicationScenario.withFinancing,
        transformPersistedCalculatorValues,
    ]);

    const initialCalculatorValues = useInitialValue<Partial<GenericCalculatorValues>>(calculatorsValues);

    const bankDisplayPreference = useMemo(() => applicationModule.bankDisplayPreference, [applicationModule]);

    const [isSubmitting, setIsSubmitting] = useState(false);

    const [selectVehicleModalVisible, setSelectVehicleModalVisible] = useState<boolean>(false);

    const getSelectedVehicleName = useCallback(
        (calculatorContext: CalculatorContext<GenericCalculatorValues>) => {
            const vehicleFieldContext = calculatorContext.getFieldContext<VehicleFieldContext>('vehicle');
            const { name: selectedVehicleName } = getLocalVariant(vehicleFieldContext.selectedVehicle);

            return translated(selectedVehicleName);
        },
        [translated]
    );

    const [ableToGoBack, allowCompareAndShare] = useMemo(() => {
        if (!lead && !application) {
            return [true, true];
        }

        return [false, false];
    }, [application, lead]);

    if (loadingFinanceProducts || loadingInsuranceProducts) {
        return <PortalLoadingElement />;
    }

    const promoCodeViewable = !!applicationModule.promoCodeModule;

    return (
        <GenericCalculatorProvider
            bankDisplayPreference={bankDisplayPreference}
            companyId={applicationModule.company.id}
            dealerId={dealerIds.length > 1 ? null : dealerIds[0]}
            financeProducts={financeProducts}
            hasFinancingCalculator={endpoint.applicationModule.showFinanceCalculator}
            includeDealerOptionsForFinancing={getIncludeDealerOptionsForFinancing(applicationModule)}
            initialValues={initialCalculatorValues}
            insuranceProducts={insuranceProducts}
            insurerDisplayPreference={applicationModule.insurerDisplayPreference}
            isDealerOptionsVisible={getIsDealerOptionsVisible(applicationModule)}
            isFinancingOptional={applicationScenario.isFinancingOptional}
            isFlexibleDiscountEnabled={getIsFlexibleDiscountEnabled(applicationModule)}
            isPublicAccess={endpoint.__typename === 'StandardApplicationPublicAccessEntrypoint'}
            marketType={applicationModule.marketType}
            promoCode={promoCode}
            promoCodeViewable={promoCodeViewable}
            vehicles={variants}
        >
            {calculatorContext => (
                <StandardLayout
                    backIcon={ableToGoBack ? <BackButton type="link">{t('carDetails:actions.back')}</BackButton> : null}
                    extra={
                        <HeaderButtonContainer>
                            {layout?.__typename === 'PorscheV3Layout' && (
                                <ChangeVehicleTrigger
                                    allowChangeVehicle={!application}
                                    setModalVisible={setSelectVehicleModalVisible}
                                    variant="secondary"
                                />
                            )}
                            {allowCompareAndShare && (
                                <>
                                    <CompareAction />
                                    <Button
                                        disabled={!isValid}
                                        icon={<MailOutlined />}
                                        onClick={shareModal.open}
                                        porscheFallbackIcon="email"
                                        type={layout?.__typename === 'PorscheV3Layout' ? 'secondary' : 'link'}
                                    >
                                        {t('carDetails:actions.share')}
                                    </Button>
                                </>
                            )}
                        </HeaderButtonContainer>
                    }
                    footer={[
                        endpoint.__typename === 'StandardApplicationEntrypoint' && (
                            <FooterButtonContainer>
                                <Button
                                    key="submit"
                                    disabled={!isValid || isSubmitting || canNotNextForApplyNew}
                                    form="calculatorForm"
                                    htmlType="submit"
                                    porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
                                    type="primary"
                                >
                                    {t('carDetails:actions.next')}
                                </Button>
                            </FooterButtonContainer>
                        ),
                    ].filter(Boolean)}
                    onBack={() => navigate('..')}
                    title={layout?.__typename === 'PorscheV3Layout' && getSelectedVehicleName(calculatorContext)}
                    hasFooterBar
                >
                    <BasicProLayoutContainer>
                        <CalculatorForm
                            actions={actions}
                            application={application}
                            calculatorContext={calculatorContext}
                            dealerId={dealerIds.length > 1 ? null : dealerIds[0]}
                            endpoint={endpoint}
                            setIsSubmitting={setIsSubmitting}
                            setIsValid={setIsValid}
                            setPromoCode={setPromoCode}
                            state={state}
                        >
                            <CalculatorPage
                                application={application}
                                applicationScenario={applicationScenario}
                                calculatorContext={calculatorContext}
                                dealerId={dealerIds.length > 1 ? null : dealerIds[0]}
                                endpoint={endpoint}
                                promoCode={promoCode}
                                setPromoCode={setPromoCode}
                                setSelectVehicleModalVisible={setSelectVehicleModalVisible}
                                state={state}
                            />
                        </CalculatorForm>
                        {shareModal.render(
                            [calculatorContext],
                            endpoint,
                            state.configuration.withFinancing,
                            state.configuration.withInsurance,
                            state.configuration.tradeIn,
                            state.configuration.testDrive,
                            dealerIds.length > 1 ? null : dealerIds[0],
                            promoCode
                        )}
                        <SelectVehicleModal
                            calculatorContext={calculatorContext}
                            modalVisible={selectVehicleModalVisible}
                            setModalVisible={setSelectVehicleModalVisible}
                        />
                    </BasicProLayoutContainer>
                    {endpoint.__typename === 'StandardApplicationEntrypoint' && <CalculatorFooter />}
                </StandardLayout>
            )}
        </GenericCalculatorProvider>
    );
};

export default CalculatorStage;
