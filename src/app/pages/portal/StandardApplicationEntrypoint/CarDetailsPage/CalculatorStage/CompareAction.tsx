/* eslint-disable max-len */
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import usePublic from '../../../../../utilities/usePublic';
import CompareIcon from '../../../../../../../public/compare.svg';

const CompareAction = () => {
    const { t } = useTranslation('carList');
    const { Button } = useThemeComponents();
    const navigate = useNavigate();
    const compareIcon = usePublic('compare.svg');
    const { variantId } = useParams<{ variantId: string }>();
    const { layout } = useRouter();

    const compare = useCallback(() => {
        navigate('../comparison', { state: { vehicleIds: [variantId] } });
    }, [navigate, variantId]);

    if (layout?.__typename === 'PorscheV3Layout') {
        return (
            <Button onClick={compare} porscheFallbackIconSource="compare" type="secondary">
                {t('carList:compareButton')}
            </Button>
        );
    }

    return (
        <Button
            icon={<CompareIcon fill="var(--ant-primary-color)" />}
            onClick={compare}
            porscheFallbackIconSource={compareIcon}
            type="link"
        >
            {t('carList:compareButton')}
        </Button>
    );
};

export default CompareAction;
