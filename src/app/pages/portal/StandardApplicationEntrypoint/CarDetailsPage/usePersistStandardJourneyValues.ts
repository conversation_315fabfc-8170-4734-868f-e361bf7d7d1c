import dayjs from 'dayjs';
import { isEmpty, isEqual, omit } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { usePersistentData, usePersistentDataListener } from '../../../../utilities/usePersistData';
import type { CalculatorFormValues } from './CalculatorStage/shared';
import type { StandardJourneyTemporaryValue } from './shared';

export const STANDARD_JOURNEY_TEMPORARY_KEY = 'standardJourneyTemporaryKey';

export const usePersistStandardJourneyValues = () => {
    const { sessionTimeout } = useCompany();
    const { setItem, deleteItem } = usePersistentData();

    const expireAt = useMemo(() => sessionTimeout * 60, [sessionTimeout]);

    const persistedValue = usePersistentDataListener<StandardJourneyTemporaryValue>(STANDARD_JOURNEY_TEMPORARY_KEY);

    const save = useCallback(
        (newValue?: StandardJourneyTemporaryValue) => {
            const updated: StandardJourneyTemporaryValue = {
                ...newValue,
                draftCreated: true,
            };

            if (!isEqual(omit('expireAt', persistedValue), omit('expireAt', updated))) {
                setItem(STANDARD_JOURNEY_TEMPORARY_KEY, updated, expireAt, '');
            }
        },
        [expireAt, persistedValue, setItem]
    );

    const remove = useCallback(() => {
        deleteItem(STANDARD_JOURNEY_TEMPORARY_KEY);
    }, [deleteItem]);

    /**
     * transform calculator values stored in session storage to be used in calculator form
     */
    const transformPersistedCalculatorValues = useCallback(
        (payload: StandardJourneyTemporaryValue): CalculatorFormValues['calculator'] => {
            if (isEmpty(payload?.calculatorValues?.calculator)) {
                return null;
            }

            const { calculator } = payload.calculatorValues;
            const { dateOfBirth, dateOfRegistration } = calculator;

            return {
                ...calculator,
                dateOfBirth: dateOfBirth ? dayjs(dateOfBirth) : null,
                dateOfRegistration: dateOfRegistration ? dayjs(dateOfRegistration) : null,
            };
        },
        []
    );

    return { save, remove, transformPersistedCalculatorValues, persistedValue };
};
