import { ApolloError, useApolloClient } from '@apollo/client';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { PromoCodeDataFragment } from '../../../../api/fragments/PromoCodeData';
import {
    UpdateStandardApplicationDraftDocument,
    UpdateStandardApplicationDraftMutation,
    UpdateStandardApplicationDraftMutationVariables,
} from '../../../../api/mutations/updateStandardApplicationDraft';
import { CalculatorContext } from '../../../../calculator/computing';
import { PromoCodeValueContext } from '../../../../calculator/computing/defaultFields/promoCodeValue';
import getFinancingFromCalculatorValues from '../../../../calculator/getFinancingFromCalculatorValues';
import getInsuranceValueFromCalculator from '../../../../calculator/getInsuranceValueFromCalculator';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import useDebounce from '../../../../utilities/useDebounce';
import useHandleError from '../../../../utilities/useHandleError';
import type { CalculatorFormValues } from './CalculatorStage/shared';
import type { StandardJourneyTemporaryValue } from './shared';
import { usePersistStandardJourneyValues } from './usePersistStandardJourneyValues';

const useUpdateDraft = (
    calculatorContext: CalculatorContext<any>,
    showPromoCodeErrorModal: (title: string) => void,
    setPromoCode: (value: PromoCodeDataFragment) => void,
    temporaryValue?: StandardJourneyTemporaryValue
) => {
    const { t } = useTranslation(['carDetails']);
    const { notification } = useThemeComponents();
    const navigate = useNavigate();
    const apolloClient = useApolloClient();

    // create a reference which will always be up to date
    const contextReference = useRef(calculatorContext);
    contextReference.current = calculatorContext;
    const { currentLanguageId } = useLanguage();

    const { save: persistStandardJourneyValue } = usePersistStandardJourneyValues();

    return useDebounce(
        useHandleError<CalculatorFormValues>(
            async values => {
                notification.loading({
                    content: t('carDetails:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                const { promoCode } = contextReference.current.getFieldContext<PromoCodeValueContext>('promoCodeInput');

                try {
                    const { data } = await apolloClient
                        .mutate<
                            UpdateStandardApplicationDraftMutation,
                            UpdateStandardApplicationDraftMutationVariables
                        >({
                            mutation: UpdateStandardApplicationDraftDocument,
                            variables: {
                                token: temporaryValue?.token,
                                customer: { newLocalCustomer: { fields: [] } },
                                financing: getFinancingFromCalculatorValues(values.calculator, promoCode),
                                insurancing: values.configuration.withInsurance
                                    ? getInsuranceValueFromCalculator(values.calculator)
                                    : null,
                                configuration: values.configuration,
                                tradeInVehicle: [],
                                promoCodeId: promoCode?.id,
                                languageId: currentLanguageId,
                            },
                        })
                        .finally(() => {
                            notification.destroy('primary');
                        });

                    if (data) {
                        persistStandardJourneyValue({
                            ...temporaryValue,
                            calculatorValues: values,
                            token: data.result.token,
                        });
                    }

                    // go to the journey
                    navigate('../apply', { state: { token: data.result.token } });
                } catch (error) {
                    notification.destroy('primary');

                    if (error instanceof ApolloError) {
                        const apolloErrors = getApolloErrors(error);

                        const { setError } = contextReference.current;

                        if (apolloErrors) {
                            Object.entries(apolloErrors).forEach(([key, value]) => {
                                setError(key, value);
                            });
                        }

                        const promoCodeError: { title: string; description: string } = error.graphQLErrors.find(
                            ({ extensions }) => extensions.code === 'BAD_USER_INPUT' && extensions.$promoCodeError
                        )?.extensions?.$promoCodeError as { title: string; description: string };

                        if (promoCodeError) {
                            showPromoCodeErrorModal(promoCodeError.title);
                            setError('promoCodeInput', promoCodeError.description);
                            setPromoCode(null);

                            return;
                        }
                    }

                    throw error;
                }
            },
            [
                notification,
                t,
                apolloClient,
                currentLanguageId,
                navigate,
                persistStandardJourneyValue,
                temporaryValue,
                showPromoCodeErrorModal,
                setPromoCode,
            ]
        )
    );
};

export default useUpdateDraft;
