/* eslint-disable max-len */
import { ApolloError, useApolloClient } from '@apollo/client';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import {
    ApplyNewStandardApplicationMutation,
    ApplyNewStandardApplicationMutationVariables,
    ApplyNewStandardApplicationDocument,
} from '../../../../../api/mutations/applyNewStandardApplication';
import { CalculatorContext } from '../../../../../calculator/computing';
import getFinancingFromCalculatorValues from '../../../../../calculator/getFinancingFromCalculatorValues';
import getInsuranceValueFromCalculator from '../../../../../calculator/getInsuranceValueFromCalculator';
import { useThemeComponents } from '../../../../../themes/hooks';
import getApolloErrors from '../../../../../utilities/getApolloErrors';
import useDebounce from '../../../../../utilities/useDebounce';
import useHandleError from '../../../../../utilities/useHandleError';
import type { StandardApplicationState } from '../../Journey/shared';
import type { CalculatorFormValues } from '../CalculatorStage/shared';

const useApplyNewAction = (
    calculatorContext: CalculatorContext<any>,
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment,
    application: StandardApplicationState
) => {
    const { t } = useTranslation(['carDetails']);
    const { notification } = useThemeComponents();
    const navigate = useNavigate();
    const apolloClient = useApolloClient();

    // create a reference which will always be up to date
    const contextReference = useRef(calculatorContext);
    contextReference.current = calculatorContext;

    return useDebounce(
        useHandleError<CalculatorFormValues>(
            async values => {
                notification.loading({
                    content: t('carDetails:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                try {
                    const { data } = await apolloClient
                        .mutate<ApplyNewStandardApplicationMutation, ApplyNewStandardApplicationMutationVariables>({
                            mutation: ApplyNewStandardApplicationDocument,
                            variables: {
                                applicationId: application.id,
                                moduleId: endpoint.applicationModule.id,
                                financing: getFinancingFromCalculatorValues(values.calculator),
                                insurancing: values.configuration.withInsurance
                                    ? getInsuranceValueFromCalculator(values.calculator)
                                    : null,
                                configuration: values.configuration,
                            },
                        })
                        .finally(() => {
                            notification.destroy('primary');
                        });

                    // go to the journey
                    navigate('../apply', { state: { token: data.result.token } });
                } catch (error) {
                    notification.destroy('primary');

                    if (error instanceof ApolloError) {
                        const apolloErrors = getApolloErrors(error);

                        const { setError } = contextReference.current;

                        if (apolloErrors) {
                            Object.entries(apolloErrors).forEach(([key, value]) => {
                                setError(key, value);
                            });
                        }
                    }

                    throw error;
                }
            },
            [notification, t, apolloClient, application.id, endpoint.applicationModule.id, navigate]
        )
    );
};

export default useApplyNewAction;
