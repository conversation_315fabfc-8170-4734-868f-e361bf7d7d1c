import { Col, Row, Space } from 'antd';
import { Formik, FormikHelpers, useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { usePrefetchAgreementsForEventQuery } from '../../../../api/queries/prefetchAgreementsForEvent';
import { usePrefetchKycFieldsForEventQuery } from '../../../../api/queries/prefetchKYCFieldsForEvent';
import {
    AssetCondition,
    CompanyTheme,
    CustomerKind,
    LocalCustomerManagementModule,
    PorscheIdData,
} from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import MandatoryPorscheIDLogin from '../../../../components/PorscheID/MandatoryPorscheIDLogin';
import getKYCDataFromPorscheId from '../../../../components/PorscheID/getKYCDataFromPorscheId';
import { getPorscheIDConfigByEvent } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { SelectedCapValues, ViewState } from '../../../../components/cap/searchCustomersAndLeads/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import Form from '../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useHeaderContext } from '../../../../layouts/HeaderContextManager';
import { useRuntimeConfig } from '../../../../runtimeConfig';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues, prepareKYCFieldPayload } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import useValidator from '../../../../utilities/useValidator';
import { getApplicantAgreements } from '../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import {
    getAgreementValues,
    mergeAgreementValues,
} from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { SearchCapCustomerContextManager } from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/ContextManager';
import getKYCDataFromCap, {
    getCurrentVehicleFromCap,
} from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/getKYCDataFromCap';
// eslint-disable-next-line max-len
import useCapSearchCustomerAction from '../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/useCapSearchCustomerAction';
import CustomerDetails from '../../../shared/JourneyPage/CustomerDetails';
import ResetKYCButtonPorsche from '../../../shared/JourneyPage/CustomerDetails/ResetKYCButtonPorsche';
import JourneySectionWrapper from '../../../shared/JourneyPage/JourneySectionWrapper';
import JourneySteps from '../../../shared/JourneyPage/JourneySteps';
import DealerInfo from '../../ConfiguratorApplicationEntrypoint/DealerInfo';
import { JourneyStage } from '../../FinderApplicationPublicAccessEntrypoint/types';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import { fullWidthColSpan, SectionDivider } from '../../StandardApplicationEntrypoint/KYCPage/shared';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import { Backdrop, StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import { useEventJourneyKycAndAgreementContext } from '../Entrypoint/EventJourneyKycAndAgreement';
import { useEventJourneySetupContext } from '../Entrypoint/EventJourneySetup';
import type { EventApplicationState } from '../Journey/shared';
import useSubmitDraft from '../useSubmitDraft';
import Banner from './Banner';
import ConsentsAndDeclarations from './ConsentsAndDeclarations';
import OcrAndMyinfo from './CustomerDetails/OcrAndMyInfo';
import CustomizedFieldsSection from './CustomizedFieldsSection';
import LiveChat from './LiveChat';
import VehicleInterest from './VehicleInterest';
import { ApplicantFormValues } from './shared';
import useEventApplicantValidator from './useEventApplicantValidator';
import useSideBar from './useSideBar';

const leftColSpan = { xl: 8, lg: 12, md: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24 };

// ensure inputs in a form with two inputs and a button to have their content graciously visible
const StyledCol = styled(Col)`
    @media (min-width: 993px) and (max-width: 1200px) {
        display: block;
        flex: 0 0 100%;
        max-width: 100%;
    }
`;

declare global {
    interface Window {
        ttq;
    }
}

export type Agreements = ApplicationAgreementDataFragment[];

export type InnerFormProps = {
    state?: State<EventApplicationState>;
    dispatch?: Dispatch<Action<EventApplicationState>>;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
} & Partial<UploadDocumentProp>;

const InnerForm = ({ state, dispatch, uploadDocument, removeDocument, kycExtraSettings }: InnerFormProps) => {
    const navigate = useNavigate();
    const router = useRouter();
    const [isPorscheIDFetchLoading, setIsPorscheIDFetchLoading] = useState<boolean>(false);

    const runtime = useRuntimeConfig();
    const { t } = useTranslation([
        'eventApplicantForm',

        // Load it first, so that when there is test drive
        // It won't trigger portal loading
        'appointmentPage',
    ]);
    const translatedString = useTranslatedString();

    const {
        event,
        eventModule,
        endpoint,
        liveChatSetting,
        showResetKYCButton,
        variants,
        showLiveChat,
        getDraftJourneyStages,
    } = useEventJourneySetupContext();
    const {
        kycState,
        setKycState,
        agreementState,
        setAgreementState,
        customerKind,
        hasGuarantorPreset,
        isCorporateCustomer,
        setIsCorporate,
        setPrefill,
    } = useEventJourneyKycAndAgreementContext();

    const submitDraft = useSubmitDraft(event.id, endpoint, {
        isPrivateAccess: event.privateAccess,
        userIds: event.userIds,
    });

    const { setHeaderVariant } = useHeaderContext();
    const { theme, BackButton, StandardLayout } = useThemeComponents();

    const {
        initialValues,
        values,
        isSubmitting,
        setFieldTouched,
        resetForm,
        handleSubmit,
        setFieldValue,
        validateForm,
        submitForm,
        setValues,
    } = useFormikContext<ApplicantFormValues>();

    const [withMyInfo, setWithMyInfo] = useState(false);

    // Only capture the first loading for both kyc and agreement
    // This to prevent flicker on the front side
    const [isKycLoaded, setKycLoaded] = useState(false);
    const [isAgreementLoaded, setAgreementLoaded] = useState(false);

    const [capModalHasError, setCapModalHasError] = useState(false);

    useEffect(() => {
        setHeaderVariant(event.hasCustomiseBanner && event.banner ? 'transparent' : 'absolute');

        return () => setHeaderVariant('absolute');
    }, [event.banner, event.hasCustomiseBanner, setHeaderVariant]);

    // Fetch KYC
    usePrefetchKycFieldsForEventQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            urlSlug: event.urlSlug,
            configuration: {
                assetCondition: AssetCondition.New,
                testDrive: values.configuration.testDrive,
                tradeIn: values.configuration.tradeIn,
                visitAppointment: values.configuration.visitAppointment,
            },
            dealerId: values.dealerId,
            eventModuleId: eventModule.id,
        },
        // Skip when kyc step is for guarantor
        skip: customerKind === CustomerKind.Guarantor,
        onCompleted: data => {
            const applicantKyc = getApplicantKyc(data.applicantKYC ?? []);
            const corporateKyc = getApplicantKyc(data.corporateKYC ?? []);
            const guarantorKyc = getApplicantKyc(data.guarantorKYC ?? []);
            const activeKyc = isCorporateCustomer ? corporateKyc : applicantKyc;

            const newCustomerFields = getInitialValues(prepareKYCFieldPayload(values.customer.fields, true), activeKyc);

            // Set outer state, so it align with validator
            setKycState({
                applicant: applicantKyc,
                corporate: corporateKyc,
                active: activeKyc,
                guarantor: guarantorKyc,
            });

            // Set formik value
            if (!isKycLoaded) {
                setFieldValue('customer.fields', newCustomerFields);
            }

            // Make sure it's shown loading portal once
            setKycLoaded(oldLoaded => (!oldLoaded ? true : oldLoaded));
        },
    });

    // Fetch Agreements
    usePrefetchAgreementsForEventQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            urlSlug: event.urlSlug,
            configuration: {
                assetCondition: AssetCondition.New,
                testDrive: values.configuration.testDrive,
                tradeIn: values.configuration.tradeIn,
                visitAppointment: values.configuration.visitAppointment,
            },
            dealerId: values.dealerId,
            eventModuleId: eventModule.id,
        },

        // Skip when kyc step is for guarantor
        skip: customerKind === CustomerKind.Guarantor,
        onCompleted: data => {
            const activeAgreements: ApplicationAgreementDataFragment[] = isCorporateCustomer
                ? getApplicantAgreements(data.corporateAgreements)
                : getApplicantAgreements(data.applicantAgreements);

            // Get remaining agreements that align with active one
            const newAgreementValues = getAgreementValues(mergeAgreementValues(activeAgreements, values.agreements));

            // Set outer state, so it align with validator
            setAgreementState({
                values: newAgreementValues,
                data: activeAgreements,
            });

            // Set formik value
            if (!isAgreementLoaded) {
                setFieldValue('agreements', newAgreementValues);
            }

            // Make sure it's shown loading portal once
            setAgreementLoaded(oldLoaded => (!oldLoaded ? true : oldLoaded));
        },
    });

    // Touch customer fields on fields change
    useEffect(() => {
        setFieldTouched('customer.fields', true);
    }, [kycState.active, setFieldTouched]);

    // Touch agreements fields on fields change
    useEffect(() => {
        setFieldTouched('agreements', true);
    }, [agreementState.data, setFieldTouched]);

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycState.active) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycState.active, resetForm, values.agreements]);

    const backIcon = state ? null : <BackButton type="link"> {t('eventApplicantForm:actions.back')}</BackButton>;

    const sectionDivider = useMemo(
        () =>
            event.showDealership ? (
                <Col span={24} style={{ padding: '0 24px' }}>
                    <SectionDivider />
                </Col>
            ) : null,
        [event.showDealership]
    );

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();

        const hasTitTok = !isNil(runtime.router.tikTok?.id);

        if (hasTitTok) {
            window.ttq.track('SubmitForm');
        }
    }, [submitForm, validateForm]);

    const withTradeIn = useMemo(() => values?.configuration.tradeIn, [values?.configuration.tradeIn]);

    const onCapValuesChanged = useCallback(
        (capValues: SelectedCapValues) => {
            switch (capValues.selectedValue) {
                case ViewState.BusinessPartner: {
                    const { selectedBusinessPartner } = capValues;

                    if (selectedBusinessPartner.currentVehicle && values.tradeInVehicle?.length) {
                        setFieldValue(
                            'tradeInVehicle.0',
                            getCurrentVehicleFromCap(selectedBusinessPartner, values.tradeInVehicle)
                        );
                    }

                    setFieldValue(
                        'customer.fields',
                        getKYCDataFromCap(selectedBusinessPartner, kycState.applicant, values.customer)
                    );
                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                        salesPersonId: selectedBusinessPartner?.responsibleSalesPersonId,
                    });

                    break;
                }

                default: {
                    const { selectedBusinessPartner, selectedLead } = capValues;

                    setFieldValue('capValues', {
                        businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                        businessPartnerId: selectedBusinessPartner?.businessPartnerId,
                        salesPersonId: selectedBusinessPartner?.responsibleSalesPersonId,
                        leadGuid: selectedLead?.leadGuid,
                        leadId: selectedLead?.leadId,
                    });
                }
            }
        },
        [setFieldValue, kycState.applicant, values.customer, values.tradeInVehicle]
    );

    const onCapSearchErrorConfirmed = useCallback(() => {
        setCapModalHasError(true);
    }, []);

    const user = useAccount(true);
    const showCommentsToBank = user && state?.application?.bank?.showCommentsField;

    const capModuleId = event.module.__typename === 'EventApplicationModule' ? event.module.capModuleId : null;
    const isCapEnabled =
        event.module.__typename === 'EventApplicationModule' ? !!event.module.capModuleId && event.isCapEnabled : null;

    const { showSearchCapCustomerButton, immediateOpenCapCustomerSearch } = useCapSearchCustomerAction({
        kycHasCompleted: state?.application?.draftFlow?.isApplicantKYCCompleted,
        requireLogin: event.privateAccess,
        isCapEnabled,
        isOptional: event.isSearchCapCustomerOptional,
        hasLeadGuid: !!values.capValues?.leadGuid,
        capModalHasError,
    });

    const { porscheIdIntegrationEnabled, isPorscheIdLoginMandatory } = useMemo(
        () => getPorscheIDConfigByEvent(event),
        [event]
    );

    const currentStage = useMemo(() => {
        if (state?.stage) {
            return state.stage;
        }

        return isPorscheIdLoginMandatory ? JourneyStage.PorscheIdLoginRegister : JourneyStage.ApplicantKYC;
    }, [isPorscheIdLoginMandatory, state?.stage]);

    const onPorscheIDCustomerFetched = useCallback(
        (porscheIdData: PorscheIdData) => {
            getKYCDataFromPorscheId(porscheIdData, setValues, withTradeIn);

            if (dispatch && state?.stage === JourneyStage.PorscheIdLoginRegister) {
                dispatch({
                    type: 'goTo',
                    stage: JourneyStage.ApplicantKYC,
                });
            }
        },

        [dispatch, setValues, state, withTradeIn]
    );

    const submitDraftWithPorscheId = useCallback(async () => {
        const result = await submitDraft(values, CustomerKind.Local, true);

        return result?.application?.id;
    }, [submitDraft, values]);

    useEffect(() => {
        if (
            isPorscheIdLoginMandatory &&
            values.customerCiamId &&
            state?.stage === JourneyStage.PorscheIdLoginRegister
        ) {
            dispatch({
                type: 'goTo',
                stage: JourneyStage.ApplicantKYC,
            });
        }
    }, [dispatch, isPorscheIdLoginMandatory, state?.stage, values.customerCiamId]);

    const { hasSidebarContent } = useSideBar(event, endpoint, values?.dealerId, {
        ...values.configuration,
        withMyInfo,
    });

    // Only show loading for the first one
    if (!isKycLoaded || !isAgreementLoaded) {
        return <PortalLoadingElement />;
    }

    return (
        <SearchCapCustomerContextManager
            applicationModuleId={event.module.id}
            applicationRequireLogin={event.privateAccess}
            capModuleId={capModuleId}
            dealerId={values.dealerId}
            eventId={event.id}
            kycHasCompleted={state?.application?.draftFlow?.isApplicantKYCCompleted}
            lead={state?.application.lead}
            onCapModalErrorConfirmed={onCapSearchErrorConfirmed}
            onCapValuesChanged={onCapValuesChanged}
        >
            <StandardLayout
                {...(!event?.hasCustomiseBanner && {
                    backIcon,
                    onBack: event.privateAccess ? () => navigate('..') : null,
                    title: translatedString(event.name),
                })}
                hasFooterBar
            >
                {event?.hasCustomiseBanner && event?.banner && <Banner banner={event.banner} jumpTo="formSection" />}
                <BasicProLayoutContainer id="formSection">
                    <JourneySteps
                        currentStage={currentStage}
                        stages={state?.stages || getDraftJourneyStages(values.dealerId)}
                    />
                    <FormAutoTouch />
                    <Form
                        data-cy="leadGenFormId"
                        id="applicantForm"
                        name="applicantForm"
                        onSubmitCapture={handleSubmit}
                    >
                        <Row gutter={[24, 68]}>
                            {hasSidebarContent && (
                                <StyledCol {...leftColSpan} style={{ padding: '0 12px' }}>
                                    <Row gutter={[50, 24]}>
                                        <VehicleInterest
                                            customerKind={customerKind}
                                            event={event}
                                            variants={variants}
                                        />
                                        {event.showDealership && (
                                            <Col span={24}>
                                                <DealerInfo
                                                    dealer={event.dealers.find(dealer => dealer.id === values.dealerId)}
                                                />
                                            </Col>
                                        )}

                                        <OcrAndMyinfo
                                            dealerId={values.dealerId}
                                            endpoint={endpoint}
                                            event={event}
                                            kycPresets={kycState.active}
                                            setWithMyInfo={setWithMyInfo}
                                            state={state}
                                            topDivider={sectionDivider}
                                            withMyInfo={withMyInfo}
                                        />
                                    </Row>
                                </StyledCol>
                            )}

                            <StyledCol
                                {...(hasSidebarContent ? rightColSpan : { span: 24 })}
                                style={{ padding: '0 12px' }}
                            >
                                <JourneySectionWrapper
                                    applicationType="EventApplication"
                                    isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                                    stage={currentStage}
                                    stages={state?.stages || getDraftJourneyStages(values.dealerId)}
                                    withCapSearchButton={showSearchCapCustomerButton}
                                >
                                    <Space direction="vertical" size={20} style={{ width: '100%' }}>
                                        {currentStage === JourneyStage.PorscheIdLoginRegister && (
                                            <MandatoryPorscheIDLogin
                                                applicationId={state?.application?.id}
                                                endpointId={endpoint.id}
                                                onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                                                routerId={router.id}
                                                setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                                                submitDraft={submitDraftWithPorscheId}
                                                isEvent
                                            />
                                        )}
                                        {currentStage === JourneyStage.ApplicantKYC && (
                                            <>
                                                <CustomerDetails
                                                    applicationId={state?.application?.id}
                                                    colSpan={hasSidebarContent ? undefined : fullWidthColSpan}
                                                    customerKind={customerKind}
                                                    endpointId={endpoint.id}
                                                    gutter={24}
                                                    hasGuarantorPreset={hasGuarantorPreset}
                                                    hasUploadDocuments={false}
                                                    hasVSOUpload={false}
                                                    immediateOpenCapCustomerSearch={immediateOpenCapCustomerSearch}
                                                    isApplyingFromDetails={false}
                                                    isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                                                    kycExtraSettings={kycExtraSettings}
                                                    kycPresets={kycState.active}
                                                    onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                                                    porscheIdIntegrationEnabled={porscheIdIntegrationEnabled}
                                                    removeDocument={removeDocument}
                                                    resetFormHandler={resetFormHandler}
                                                    routerId={router.id}
                                                    setIsCorporate={setIsCorporate}
                                                    setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                                                    setPrefill={setPrefill}
                                                    showCommentsToInsurer={false}
                                                    showRemarks={showCommentsToBank}
                                                    showResetButton={showResetKYCButton && !withMyInfo}
                                                    showTabs={kycState.corporate?.length > 0}
                                                    showTitle={false}
                                                    submitDraftWithPorscheId={submitDraftWithPorscheId}
                                                    uploadDocument={uploadDocument}
                                                    withFinancing={false}
                                                    enableMobileVerification
                                                    isEvent
                                                />
                                                {values.customizedFields.length > 0 && <CustomizedFieldsSection />}
                                                <ConsentsAndDeclarations
                                                    applicationAgreements={agreementState.data}
                                                    hideDivider
                                                />
                                            </>
                                        )}
                                    </Space>
                                </JourneySectionWrapper>
                            </StyledCol>
                        </Row>
                    </Form>
                    {showLiveChat && <LiveChat chatSetting={liveChatSetting} variants={variants} />}
                </BasicProLayoutContainer>
                <StyledJourneyToolbar>
                    {showResetKYCButton &&
                        !withMyInfo &&
                        (theme === CompanyTheme.Porsche || theme === CompanyTheme.PorscheV3) && (
                            <ResetKYCButtonPorsche onConfirm={resetFormHandler} />
                        )}
                    <NextButton
                        data-cy="leadGenFormButton"
                        disabled={isSubmitting || (isPorscheIdLoginMandatory && !values.customerCiamId)}
                        form="applicantForm"
                        onSubmit={onSubmit}
                    />
                </StyledJourneyToolbar>
                {isPorscheIDFetchLoading && <Backdrop />}
            </StandardLayout>
        </SearchCapCustomerContextManager>
    );
};

export type ApplicantFormProps = {
    initialValues: ApplicantFormValues;
    onSubmit: (values: ApplicantFormValues, helpers: FormikHelpers<ApplicantFormValues>) => void;
    state?: State<EventApplicationState>;
    dispatch?: Dispatch<Action<EventApplicationState>>;
} & Partial<UploadDocumentProp>;

const ApplicantForm = ({ onSubmit, initialValues, ...rest }: ApplicantFormProps) => {
    const { agreementState, kycState, prefill } = useEventJourneyKycAndAgreementContext();
    const { event } = useEventJourneySetupContext();

    const kycExtraSettings = useMemo(() => event.kycExtraSettings, [event.kycExtraSettings]);
    const applicantValidations = useEventApplicantValidator(agreementState.data, kycState.active, kycExtraSettings);

    const validate = useValidator(applicantValidations, { prefill });

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            <InnerForm kycExtraSettings={kycExtraSettings} {...rest} />
        </Formik>
    );
};

export default ApplicantForm;
