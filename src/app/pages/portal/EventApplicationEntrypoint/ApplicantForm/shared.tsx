import styled from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import {
    CapValuesInput,
    EventApplicationConfigurationPayload,
    EventCustomizedFieldInput,
    TradeInVehiclePayload,
} from '../../../../api/types';
import BasicProPageWithHeader from '../../../../layouts/BasicProLayout/BasicProPageWithHeader';
import { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type { AppointmentValues } from '../../StandardApplicationEntrypoint/AppointmentPage/appointmentValues';

type CustomerFormValues = { fields: KYCPresetFormFields };

export type Agreements = ApplicationAgreementDataFragment[];

export type ApplicantFormValues = {
    agreements: AgreementValues;
    customer: CustomerFormValues;
    configuration: EventApplicationConfigurationPayload;
    vehicleInterest: {
        model: string;
        subModel: string;
        variant: string;
    };
    tradeInVehicle: TradeInVehiclePayload[];
    isCorporateCustomer: boolean;
    hasGuarantor: boolean;
    dealerId: string;
    prefill: boolean;
    remarks: string;
    customizedFields: EventCustomizedFieldInput[];
    appointment?: AppointmentValues;
    visitAppointment?: AppointmentValues;

    capValues?: CapValuesInput;
    customerCiamId?: string;
};

export const Container = styled.div`
    display: flex;
    flex-direction: column;

    & .ant-form-item-label > label {
        font-size: 16px;
    }
`;

export const Title = styled.h4<{ size?: 'default' | 'large' }>`
    font-size: 20px;
    margin-bottom: ${({ size = 'default' }) => (size === 'default' ? '18px' : '24px')};
    font-weight: 900;
`;

export const StyledBasicProPageWithHeader = styled(BasicProPageWithHeader)`
    &.ant-pro-page-container {
        background-color: #fff;
        padding-bottom: 120px;
    }

    & .ant-page-header-heading {
        justify-content: center;
        position: relative;
    }

    & .ant-page-header-back {
        position: absolute;
        left: 0;
    }

    & .ant-page-header-heading-extra {
        position: absolute;
        right: 0;
    }
`;
