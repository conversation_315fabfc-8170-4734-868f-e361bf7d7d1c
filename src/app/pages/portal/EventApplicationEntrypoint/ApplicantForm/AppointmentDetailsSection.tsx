import { Col, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type {
    AppointmentModuleSpecsFragment,
    ConfiguratorApplicationEntrypointContextDataFragment,
    ConfiguratorApplicationModuleDebugJourneyFragment,
    EventApplicationEntrypointContextDataFragment,
    EventApplicationModuleDebugJourneyFragment,
    FinderApplicationEntrypointContextDataFragment,
    FinderApplicationPrivateModuleDebugJourneyFragment,
    FinderApplicationPublicAccessEntrypointContextDataFragment,
    FinderApplicationPublicModuleDebugJourneyFragment,
    JourneyEventDataFragment,
    LaunchpadApplicationModuleDebugJourneyFragment,
    StandardApplicationEntrypointContextDataFragment,
    StandardApplicationModuleDebugJourneyFragment,
    VisitAppointmentModuleSpecsFragment,
} from '../../../../api/fragments';
import { useRetrieveBlockedAppointmentTimeSlotQuery } from '../../../../api/queries/retrieveBlockedAppointmentTimeSlot';
import { ApplicationScenario } from '../../../../api/types';
import AppointmentDatepicker from '../../StandardApplicationEntrypoint/AppointmentPage/AppointmentDatepicker';
import {
    AllowedApplicationForAppointment,
    ApplicationModuleFragment,
} from '../../StandardApplicationEntrypoint/AppointmentPage/shared';
import type { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { ApplicantFormValues, Container, Title } from './shared';

const colSpan = { span: 24 };

const Description = styled(Typography.Text)`
    // adding a negative margin because the sibling form field has a fixed margin
    margin-top: -14px;
    display: block;
    &.ant-typography {
        font-size: 16px;
    }
`;

type FeatureModule =
    | StandardApplicationModuleDebugJourneyFragment
    | ConfiguratorApplicationModuleDebugJourneyFragment
    | EventApplicationModuleDebugJourneyFragment
    | FinderApplicationPrivateModuleDebugJourneyFragment
    | FinderApplicationPublicModuleDebugJourneyFragment
    | LaunchpadApplicationModuleDebugJourneyFragment;

type EntrypointFeatureModule =
    | StandardApplicationEntrypointContextDataFragment
    | ConfiguratorApplicationEntrypointContextDataFragment
    | EventApplicationEntrypointContextDataFragment
    | FinderApplicationEntrypointContextDataFragment
    | FinderApplicationPublicAccessEntrypointContextDataFragment
    | LaunchpadApplicationModuleDebugJourneyFragment;

export const retrieveAppointmentModuleFromEntrypoint = (
    entrypoint: EntrypointFeatureModule
): AppointmentModules & { hasShowroomVisit: boolean; hasTestDrive: boolean } => {
    switch (entrypoint.__typename) {
        case 'ConfiguratorApplicationEntrypoint': {
            const module = entrypoint.configuratorApplicationModule;

            return {
                hasShowroomVisit: module.scenarios.includes(ApplicationScenario.VisitAppointment),
                hasTestDrive: module.scenarios.includes(ApplicationScenario.Appointment),
                appointmentModule:
                    module.appointmentModule?.__typename === 'AppointmentModule' &&
                    (module.appointmentModule as AppointmentModuleSpecsFragment),
                visitAppointmentModule:
                    module.visitAppointmentModule?.__typename === 'VisitAppointmentModule' &&
                    module.visitAppointmentModule,
            };
        }
        case 'FinderApplicationPublicAccessEntrypoint':
        case 'FinderApplicationEntrypoint': {
            const module = entrypoint.finderApplicationModules[0];

            return {
                hasShowroomVisit: module.scenarios.includes(ApplicationScenario.VisitAppointment),
                hasTestDrive: module.scenarios.includes(ApplicationScenario.Appointment),
                appointmentModule:
                    module.appointmentModule?.__typename === 'AppointmentModule' &&
                    (module.appointmentModule as AppointmentModuleSpecsFragment),
                visitAppointmentModule:
                    module.visitAppointmentModule?.__typename === 'VisitAppointmentModule' &&
                    (module.visitAppointmentModule as VisitAppointmentModuleSpecsFragment),
            };
        }
        case 'StandardApplicationEntrypoint': {
            const module = entrypoint.applicationModule;

            return {
                hasShowroomVisit: module.scenarios.includes(ApplicationScenario.VisitAppointment),
                hasTestDrive: module.scenarios.includes(ApplicationScenario.Appointment),
                appointmentModule:
                    module.appointmentModule?.__typename === 'AppointmentModule' &&
                    (module.appointmentModule as AppointmentModuleSpecsFragment),
                visitAppointmentModule:
                    module.visitAppointmentModule?.__typename === 'VisitAppointmentModule' &&
                    (module.visitAppointmentModule as VisitAppointmentModuleSpecsFragment),
            };
        }

        default:
            return null;
    }
};

type AppointmentModules = {
    appointmentModule: AppointmentModuleSpecsFragment;
    visitAppointmentModule: VisitAppointmentModuleSpecsFragment;
};

export const retrieveAppointmentScenarios = (module: FeatureModule, event?: JourneyEventDataFragment) => {
    switch (module.__typename) {
        case 'FinderApplicationPrivateModule':
        case 'FinderApplicationPublicModule':
        case 'ConfiguratorModule':
        case 'StandardApplicationModule': {
            return {
                hasShowroomVisit:
                    module.scenarios.includes(ApplicationScenario.VisitAppointment) &&
                    module.visitAppointmentModule &&
                    module.visitAppointmentModule.__typename === 'VisitAppointmentModule',
                hasTestDrive:
                    module.scenarios.includes(ApplicationScenario.Appointment) &&
                    module.appointmentModule &&
                    module.appointmentModule.__typename === 'AppointmentModule',
            };
        }

        case 'EventApplicationModule': {
            if (isNil(event)) {
                return {
                    hasShowroomVisit: false,
                    hasTestDrive: false,
                };
            }

            return {
                hasShowroomVisit:
                    event.scenarios.includes(ApplicationScenario.VisitAppointment) &&
                    module.visitAppointmentModule &&
                    module.visitAppointmentModule.__typename === 'VisitAppointmentModule' &&
                    event.displayVisitAppointmentDatepicker,
                hasTestDrive:
                    event.scenarios.includes(ApplicationScenario.Appointment) &&
                    module.appointmentModule &&
                    module.appointmentModule.__typename === 'AppointmentModule' &&
                    event.displayAppointmentDatepicker,
            };
        }

        default:
            return {
                hasShowroomVisit: false,
                hasTestDrive: false,
            };
    }
};

export const retrieveAppointmentModule = (module: FeatureModule): AppointmentModules => {
    switch (module.__typename) {
        case 'FinderApplicationPrivateModule':
        case 'FinderApplicationPublicModule':
        case 'ConfiguratorModule':
        case 'StandardApplicationModule':
        case 'EventApplicationModule':
        case 'LaunchPadModule': {
            return {
                appointmentModule:
                    module.appointmentModule?.__typename === 'AppointmentModule' &&
                    (module.appointmentModule as AppointmentModuleSpecsFragment),
                visitAppointmentModule:
                    module.visitAppointmentModule?.__typename === 'VisitAppointmentModule' &&
                    (module.visitAppointmentModule as VisitAppointmentModuleSpecsFragment),
            };
        }

        default:
            return {
                appointmentModule: null,
                visitAppointmentModule: null,
            };
    }
};

type AppointmentDetailsSectionProps = {
    applicationModule: State<AllowedApplicationForAppointment>['application']['module'];
    showSkipValidation?: boolean;
    application?: AllowedApplicationForAppointment;
    event?: JourneyEventDataFragment;
    appointmentType: AppointmentType;
};

export enum AppointmentType {
    VisitAppointment = 'visitAppointment',
    Appointment = 'appointment',
}
const AppointmentDetailsSection = ({
    applicationModule,
    showSkipValidation,
    application,
    event,
    appointmentType,
}: AppointmentDetailsSectionProps) => {
    const { t } = useTranslation('eventApplicantForm');

    const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
        applicationModule as ApplicationModuleFragment
    );

    const { hasShowroomVisit, hasTestDrive } = retrieveAppointmentScenarios(
        applicationModule as ApplicationModuleFragment,
        event
    );

    const { values } = useFormikContext<ApplicantFormValues>();

    /**
     * Test Drive query
     */
    const { data: testDriveData } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: appointmentModule?.id,
        },
        skip: !appointmentModule?.id || application?.draftFlow?.isAppointmentCompleted,
    });

    const bookedAppointmentTimeSlots = useMemo(
        () => testDriveData?.retrieveBlockedAppointmentTimeSlot || [],
        [testDriveData?.retrieveBlockedAppointmentTimeSlot]
    );

    /**
     * Showroom Visit query
     */
    const { data: showroomVisitData } = useRetrieveBlockedAppointmentTimeSlotQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: visitAppointmentModule?.id,
        },
        skip: !appointmentModule?.id || application?.draftFlow?.isAppointmentCompleted,
    });

    const bookedVisitAppointmentTimeSlots = useMemo(
        () => showroomVisitData?.retrieveBlockedAppointmentTimeSlot || [],
        [showroomVisitData?.retrieveBlockedAppointmentTimeSlot]
    );

    return (
        <Container>
            {hasTestDrive && appointmentType === AppointmentType.Appointment && values.configuration.testDrive && (
                <Row gutter={[24, 12]}>
                    <Col {...colSpan}>
                        <AppointmentDatepicker
                            appointmentModule={appointmentModule}
                            bookedAppointmentTimeSlots={bookedAppointmentTimeSlots}
                            event={event}
                            helperText={
                                <Description>{t('eventApplicantForm:appointmentDetails.helperText')}</Description>
                            }
                            prefix="appointment"
                            showSkipValidation={showSkipValidation}
                            title={<Title size="large">{t('eventApplicantForm:appointmentDetails.title')}</Title>}
                            hideBookingInformation
                            required
                        />
                    </Col>
                </Row>
            )}
            {hasShowroomVisit &&
                appointmentType === AppointmentType.VisitAppointment &&
                values.configuration.visitAppointment && (
                    <Row gutter={[24, 12]}>
                        <Col {...colSpan}>
                            <AppointmentDatepicker
                                appointmentModule={visitAppointmentModule}
                                bookedAppointmentTimeSlots={bookedVisitAppointmentTimeSlots}
                                event={event}
                                helperText={
                                    <Description>
                                        {t('eventApplicantForm:visitAppointmentDetails.helperText')}
                                    </Description>
                                }
                                prefix="visitAppointment"
                                showSkipValidation={showSkipValidation}
                                title={
                                    <Title size="large">{t('eventApplicantForm:visitAppointmentDetails.title')}</Title>
                                }
                                hideBookingInformation
                                required
                            />
                        </Col>
                    </Row>
                )}
        </Container>
    );
};

export default AppointmentDetailsSection;
