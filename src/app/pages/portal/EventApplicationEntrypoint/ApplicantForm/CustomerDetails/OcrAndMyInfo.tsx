/* eslint-disable max-len */
import { ApolloClient, NormalizedCacheObject, useApolloClient } from '@apollo/client';
import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { EventApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/EventApplicationEntrypointContextData';
import { JourneyEventDataFragment } from '../../../../../api/fragments/JourneyEventData';
import { KycFieldSpecsFragment } from '../../../../../api/fragments/KYCFieldSpecs';
import {
    GetMyInfoAuthorizeUrlDocument,
    GetMyInfoAuthorizeUrlQuery,
    GetMyInfoAuthorizeUrlQueryVariables,
} from '../../../../../api/queries/getMyInfoAuthorizeUrl';
import { CustomerKind, LayoutType } from '../../../../../api/types';
import MyInfo from '../../../../../components/MyInfo';
import { useRouter } from '../../../../../components/contexts/shared';
import Ocr, { useOcrDetectedHandler } from '../../../../../components/ocr';
import { useThemeComponents } from '../../../../../themes/hooks';
import useDealershipSettingId from '../../../../../utilities/useDealershipSettingId';
import { State } from '../../../StandardApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../Journey/shared';
import useSubmitDraft from '../../useSubmitDraft';
import { ApplicantFormValues } from '../shared';
import useUpdateEventApplication from '../useUpdateEventApplication';
import { setMyInfoTemporaryValues } from './utils';

const Container = styled(Col)`
    ${props =>
        props.theme.layoutType === LayoutType.PorscheV3 &&
        css`
            display: flex;
            flex-direction: column;
            gap: 24px;
        `}
`;

type OcrAndMyinfoProps = {
    kycPresets: KycFieldSpecsFragment[];
    event: JourneyEventDataFragment;
    endpoint: EventApplicationEntrypointContextDataFragment;
    state?: State<EventApplicationState>;
    withMyInfo: boolean;
    setWithMyInfo: (value: boolean) => void;
    dealerId: string;
    topDivider?: React.ReactNode;
};

const OcrAndMyinfo = ({
    kycPresets,
    event,
    endpoint,
    state,
    withMyInfo,
    setWithMyInfo,
    dealerId,
    topDivider = null,
}: OcrAndMyinfoProps) => {
    const { t } = useTranslation('eventApplicantForm');
    const { notification } = useThemeComponents();

    const client = useApolloClient() as ApolloClient<NormalizedCacheObject>;
    const router = useRouter();
    const submitDraft = useSubmitDraft(event.id, endpoint, {
        withMyinfo: true,
        isPrivateAccess: event.privateAccess,
        userIds: event.userIds,
    });
    const updateEvent = useUpdateEventApplication(endpoint);
    const { values, initialValues } = useFormikContext<ApplicantFormValues>();
    const dealershipSettingId = useDealershipSettingId(dealerId);

    const myInfoEnabled = !!dealershipSettingId(event.myInfoSetting);
    const showMyInfoContent = !withMyInfo && myInfoEnabled;
    const showOcr = !withMyInfo && endpoint.eventApplicationModule.isOcrEnabled;

    const myInfoOnClick = useCallback(async () => {
        let applicationId: string;

        if (!state?.token) {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const result = await submitDraft(values, CustomerKind.Local);

            applicationId = result.application.id;
        } else {
            const result = await updateEvent(state.token, values, CustomerKind.Local);

            applicationId = result.application.id;
        }

        // Set appointment values inside local storage, that will be retrieved later
        setMyInfoTemporaryValues(applicationId, {
            appointment: values.appointment,
        });

        const { data } = await client.query<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>({
            query: GetMyInfoAuthorizeUrlDocument,
            variables: {
                applicationId,
                routerId: router.id,
                endpointId: endpoint.id,
                customerKind: CustomerKind.Local,
                withTradeIn: values.configuration.tradeIn,
                withTestDrive: values.configuration.testDrive,
            },
            fetchPolicy: 'no-cache',
        });

        notification.destroy('primary');

        if (data?.authorizeUrl) {
            globalThis.location.href = data.authorizeUrl;
        }
    }, [client, endpoint.id, router.id, state?.token, submitDraft, t, updateEvent, values, notification]);

    const onOcrDetected = useOcrDetectedHandler(kycPresets);

    const tradeInVehicle = useMemo(
        () => ({
            withTradeIn: initialValues.configuration.tradeIn,
            withTestDrive: initialValues.configuration.testDrive,
            name: 'tradeInVehicle',
        }),
        [initialValues]
    );

    if (!showOcr && !showMyInfoContent) {
        return null;
    }

    return (
        <>
            {topDivider}
            <Container span={24}>
                {showMyInfoContent && (
                    <MyInfo
                        applicationId={state?.application?.id}
                        customerFieldPrefix="customer.fields"
                        customerKind={CustomerKind.Local}
                        onClick={myInfoOnClick}
                        setWithMyInfo={setWithMyInfo}
                        tradeInVehicle={tradeInVehicle}
                    />
                )}
                {showOcr && <Ocr onDetected={onOcrDetected} />}
            </Container>
        </>
    );
};

export default OcrAndMyinfo;
