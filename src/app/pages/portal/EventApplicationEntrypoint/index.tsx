import { Outlet, Route, Routes } from 'react-router-dom';
// eslint-disable-next-line max-len
import { EventApplicationEntrypointContextDataFragment } from '../../../api/fragments/EventApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import PorscheNotFound from '../../../components/results/PorscheNotFound';
import { useThemeComponents } from '../../../themes/hooks';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import EventDetailsPage from '../EventDetailsPage';
import EventListPage from '../EventListPage';
import SessionRevokedPage from '../SessionRevokedPage';
import CustomerValidationPage from '../StandardApplicationEntrypoint/CustomerValidationPage';
import TestDriveJourney from '../TestDriveEntrypoint/Journey';
import Entrypoint from './Entrypoint';
import Journey from './Journey';
import ThankYouPage from './ThankYou';
import usePathScriptObserver from './usePathScriptObserver';

export type EventApplicationEntrypointProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
};

const EventApplicationEntrypoint = ({ endpoint }: EventApplicationEntrypointProps) => {
    const { StandardLayout } = useThemeComponents();
    usePathScriptObserver(endpoint.pathScripts, endpoint.pathname);

    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key="authorize" element={<CustomerValidationPage layout={StandardLayout} />} path="authorize" />
                <Route key="list" element={<EventListPage endpoint={endpoint} />} path="/" />
                <Route key="details" element={<EventDetailsPage endpoint={endpoint} />} path="details/:eventSlug" />
                <Route key="eventSlugRoot" element={<Outlet />} path=":eventSlug">
                    <Route key="event" element={<Entrypoint endpoint={endpoint} />} index />
                    <Route key="testDrive" element={<TestDriveJourney endpoint={endpoint} />} path="testDrive" />
                </Route>
                <Route key="apply" element={<Journey endpoint={endpoint} />} path="apply" />
                <Route key="sessionRevoked" element={<SessionRevokedPage />} path="sessionRevoked" />
                <Route key="thankyou" element={<ThankYouPage />} path="thankyou" />
                <Route key="404" element={<PorscheNotFound />} path="*" />
            </Routes>
        </>
    );
};

export default EventApplicationEntrypoint;
