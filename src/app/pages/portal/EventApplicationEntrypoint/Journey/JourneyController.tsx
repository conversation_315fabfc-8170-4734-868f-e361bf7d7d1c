import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments';
import { ApplicationKind, ApplicationSigningPurpose } from '../../../../api/types';
import ApplicationSessionModal from '../../../../components/ApplicationSessionExpireModal';
import UserSessionExpireModal from '../../../../components/UserSessionExpireModal';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useThemeComponents } from '../../../../themes/hooks';
import useApplicationConcurrencyRedirect from '../../../../utilities/useApplicationConcurrencyRedirect';
import RedirectAndReplace from '../../../shared/RedirectAndReplace';
import useEndSession from '../../../shared/useEndSession';
import useExtendSession from '../../../shared/useExtendSession';
import AdyenPaymentPage from '../../StandardApplicationEntrypoint/AdyenPaymentPage';
import FiservPaymentPage from '../../StandardApplicationEntrypoint/FiservPaymentPage';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import {
    NamiralRedirectPage,
    NamirialPage,
    NamirialRejectPage,
    NamirialTimeoutPage,
} from '../../StandardApplicationEntrypoint/NamirialPage';
import OTPPage from '../../StandardApplicationEntrypoint/OTPPage';
import PayGatePaymentPage from '../../StandardApplicationEntrypoint/PayGatePaymentPage';
import PorschePaymentPage from '../../StandardApplicationEntrypoint/PorschePaymentPage';
import TtbPaymentPage from '../../StandardApplicationEntrypoint/TtbPaymentPage';
import EventApplication from '../EventApplication';
import GuarantorKYC from '../GuarantorKYC';
import InitializeStage from './InitializeStage';
import JourneyLayout from './JourneyLayout';
import reducer from './reducer';
import type { EventApplicationState } from './shared';

export type JourneyControllerProps = {
    initialToken: string;
    initialApplication: EventApplicationState;
    initialStage: JourneyStage;
    endpoint: EventApplicationEntrypointContextDataFragment;
    journeyStages: JourneyStage[];
};

const JourneyController = ({
    initialToken,
    initialApplication,
    initialStage,
    endpoint,
    journeyStages,
}: JourneyControllerProps) => {
    const initialState = useRef({
        token: initialToken,
        stage: initialStage,
        application: initialApplication,
        stages: journeyStages,
    });
    const navigate = useNavigate();
    const [state, dispatch] = useReducer(reducer, initialState.current);
    const locationState = useLocation().state as {
        token?: string;
        myInfoAuthorizationCode?: string;
        linkId?: string;
        porscheIdAuthorizationCode?: string;
    };
    const { myInfoAuthorizationCode, linkId, porscheIdAuthorizationCode } = locationState;

    useApplicationConcurrencyRedirect(initialToken);

    const { StandardLayout } = useThemeComponents();

    const extendSession = useExtendSession(dispatch);
    const endSession = useEndSession();

    useEffect(() => {
        // ensure we locally persist the latest token as well
        navigate('.', {
            state: { myInfoAuthorizationCode, token: state.token, linkId, porscheIdAuthorizationCode },
            replace: true,
        });
    }, [state.token, navigate, myInfoAuthorizationCode, linkId, porscheIdAuthorizationCode]);

    const journeyContent = useMemo(() => {
        switch (state.stage) {
            case JourneyStage.Initialize:
                return <InitializeStage dispatch={dispatch} state={state} />;

            // For my info case, this start here:
            case JourneyStage.PorscheIdLoginRegister:
            case JourneyStage.ApplicantKYC:
                return <EventApplication dispatch={dispatch} endpoint={endpoint} state={state} />;

            case JourneyStage.GuarantorKYC:
                return <GuarantorKYC dispatch={dispatch} endpoint={endpoint} state={state} />;

            case JourneyStage.Deposit: {
                switch (state.application.deposit?.__typename) {
                    case 'ApplicationAdyenDeposit':
                        return (
                            <AdyenPaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationPorscheDeposit':
                        return (
                            <PorschePaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationFiservDeposit':
                        return (
                            <FiservPaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationPayGateDeposit':
                        return (
                            <PayGatePaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    case 'ApplicationTtbDeposit':
                        return (
                            <TtbPaymentPage
                                CustomLayout={StandardLayout}
                                dispatch={dispatch}
                                shouldIncludeLayout={false}
                                state={state}
                            />
                        );

                    default:
                        return <NotFoundResult />;
                }
            }

            case JourneyStage.Otp:
                return (
                    <OTPPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Finance}
                        shouldIncludeLayout={false}
                        state={state}
                    />
                );

            case JourneyStage.Namirial:
            case JourneyStage.GuarantorNamirial:
                return <NamirialPage purpose={ApplicationSigningPurpose.Finance} state={state} />;

            case JourneyStage.NamirialRedirect:
            case JourneyStage.GuarantorNamirialRedirect:
                return (
                    <NamiralRedirectPage
                        dispatch={dispatch}
                        purpose={ApplicationSigningPurpose.Finance}
                        state={state}
                    />
                );

            case JourneyStage.NamirialReject:
            case JourneyStage.GuarantorNamirialReject:
                return <NamirialRejectPage />;

            case JourneyStage.NamirialTimeout:
            case JourneyStage.GuarantorNamirialTimeout:
                return <NamirialTimeoutPage />;

            case JourneyStage.Unknown:
            default:
                return <NotFoundResult />;
        }
    }, [StandardLayout, endpoint, state]);

    const handleExtendSession = useCallback(async () => {
        await extendSession(state.token, ApplicationKind.Event);
    }, [extendSession, state]);
    const handleEndSession = useCallback(() => endSession(state.token, ApplicationKind.Event), [endSession, state]);

    if (state.application.draftFlow.isReceived) {
        return <RedirectAndReplace path={{ pathname: '../thankyou' }} state={state} />;
    }

    return (
        <>
            {!state.application.event.privateAccess && (
                <ApplicationSessionModal
                    endSession={handleEndSession}
                    expireAt={state.application.expireAt}
                    extendSession={handleExtendSession}
                />
            )}
            {state.application.event.privateAccess && <UserSessionExpireModal />}
            <JourneyLayout dispatch={dispatch} state={state}>
                {journeyContent}
            </JourneyLayout>
        </>
    );
};

export default JourneyController;
