import { Dispatch, useEffect } from 'react';
import { getPorscheIDConfigByEvent } from '../../../../components/PorscheID/getPorscheIDConfig';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { Action, State, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from './shared';

export const getStageFromApplication = (
    application: State<EventApplicationState>['application'],
    initialized = true
) => {
    const { draftFlow, deposit, signing, module } = application;
    if (module.__typename !== 'EventApplicationModule') {
        throw new Error('ModuleType not supported ');
    }

    const { isPorscheIdLoginMandatory } = getPorscheIDConfigByEvent(application.event);

    if (!initialized && application.withCustomerDevice) {
        // todo render calculator
    }

    if (!draftFlow.isApplicantKYCCompleted && !draftFlow.isPorscheIdLoginCompleted && isPorscheIdLoginMandatory) {
        return JourneyStage.PorscheIdLoginRegister;
    }

    if (!draftFlow.isApplicantKYCCompleted) {
        return JourneyStage.ApplicantKYC;
    }

    if (!draftFlow.areGlobalAgreementsCompleted) {
        return JourneyStage.Unknown;
    }

    if (draftFlow.hasGuarantor && !draftFlow.isGuarantorCompleted) {
        return JourneyStage.GuarantorKYC;
    }

    if (deposit && !draftFlow.isDepositCompleted && !draftFlow.isDepositSkipped) {
        return JourneyStage.Deposit;
    }

    if (signing && !draftFlow.isSigningCompleted) {
        switch (signing.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.Namirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.Otp;

            default:
                return JourneyStage.Unknown;
        }
    }

    return JourneyStage.Unknown;
};

export type InitializeStageProps = {
    state: State<EventApplicationState>;
    dispatch: Dispatch<Action<EventApplicationState>>;
};

const InitializeStage = ({ state, dispatch }: InitializeStageProps) => {
    const { application } = state;

    useEffect(() => {
        dispatch({ type: 'goTo', stage: getStageFromApplication(application) });
    }, [application, dispatch]);

    return <PortalLoadingElement />;
};

export default InitializeStage;
