import { useLocation } from 'react-router-dom';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments';
import { Maybe } from '../../../../api/types';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import JourneyBootstrap from './JourneyBootstrap';

export type JourneyLocationState = {
    token: string;
    stage?: JourneyStage;
};

export type JourneyProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
};

const Journey = ({ endpoint }: JourneyProps) => {
    const state = useLocation().state as Maybe<JourneyLocationState>;

    if (!state) {
        return <NotFoundResult />;
    }

    const { token, stage = JourneyStage.Initialize } = state;

    if (!token) {
        // that shouldn't happen as well
        return <NotFoundResult />;
    }

    return <JourneyBootstrap endpoint={endpoint} initialStage={stage} initialToken={token} />;
};

export default Journey;
