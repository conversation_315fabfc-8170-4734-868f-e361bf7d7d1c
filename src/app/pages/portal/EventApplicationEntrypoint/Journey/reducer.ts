import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getStageFromApplication } from './InitializeStage';
import type { EventApplicationState } from './shared';

const reducer = (
    state: State<EventApplicationState>,
    action: Action<EventApplicationState>
): State<EventApplicationState> => {
    switch (action.type) {
        case 'refresh':
            return { ...state, token: action.token, application: action.application };

        case 'next':
            return {
                ...state,
                stage: getStageFromApplication(action.application),
                token: action.token,
                application: action.application,
                stages: getApplicationJourneyStages(action.application),
            };

        case 'goTo':
            return { ...state, stage: action.stage };

        default:
            return state;
    }
};

export default reducer;
