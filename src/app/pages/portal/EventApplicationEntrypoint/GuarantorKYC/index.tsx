/* eslint-disable max-len */
import { Dispatch, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments';
import { CustomerKind, EventCustomizedFieldInput } from '../../../../api/types';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useHandleError from '../../../../utilities/useHandleError';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import ApplicantForm from '../ApplicantForm';
import { ApplicantFormValues } from '../ApplicantForm/shared';
import { EventJourneyKycAndAgreementProvider } from '../Entrypoint/EventJourneyKycAndAgreement';
import { EventJourneySetupProvider } from '../Entrypoint/EventJourneySetup';
import type { EventApplicationState } from '../Journey/shared';

export type GuarantorKYCProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
    state: State<EventApplicationState>;
    dispatch: Dispatch<Action<EventApplicationState>>;
};

const GuarantorInner = ({ state, dispatch }: Omit<GuarantorKYCProps, 'endpoint'>) => {
    const { t } = useTranslation('eventApplicantForm');
    const { notification } = useThemeComponents();

    const { token, application } = state;
    const { guarantorAgreements, guarantorKYC } = application;

    const agreementsKYC = guarantorAgreements.map(agreement => ({
        ...agreement,
        isAgreed: false,
    }));
    const agreements = useAgreementsValues(agreementsKYC);

    const guarantorKyc = useMemo(() => getApplicantKyc(guarantorKYC), [guarantorKYC]);

    const initialValues: ApplicantFormValues = useMemo(() => {
        const { vehicle } = application;

        const getVehicleValue = () => {
            if (!vehicle) {
                return null;
            }

            if (vehicle.__typename !== 'LocalVariant') {
                throw new Error('Invalid Variant');
            }

            return vehicle;
        };

        const vehicleValue = getVehicleValue();

        const initialFields = application.guarantor ? application.guarantor.fields : [];

        return {
            agreements,
            customer: guarantorKyc ? { fields: getInitialValues(initialFields, guarantorKyc) } : null,
            configuration: application.configuration,
            vehicleInterest: {
                model: vehicleValue?.model.parentModel ? vehicleValue.model.parentModelId : vehicleValue?.model.id,
                subModel: vehicleValue?.model.parentModel ? vehicleValue.model.id : null,
                variant: vehicleValue?.id,
            },
            isCorporateCustomer: false,
            tradeInVehicle: application.tradeInVehicle,
            hasGuarantor: false,
            dealerId: application.dealerId,
            prefill: false,
            customizedFields: application.customizedFields as EventCustomizedFieldInput[],
            remarks: application?.remarks ?? '',
        };
    }, [agreements, guarantorKyc, application]);

    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();

    const uploadOcrFiles = useUploadOcrFiles();
    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const onSubmit = useHandleError(
        async (values: ApplicantFormValues) => {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const submitAgreementResult = await submitAgreements(
                token,
                values.agreements,
                CustomerKind.Guarantor,
                false
            );

            const submitCustomerDetailsResult = await submitCustomerDetails({
                token: submitAgreementResult.token,
                fields: values.customer.fields,
                customerKind: CustomerKind.Guarantor,
                sameCorrespondenceAddress: values.prefill,
            });

            await uploadOcrFiles(submitCustomerDetailsResult.token);

            notification.destroy('primary');

            if (submitCustomerDetailsResult.__typename !== 'ApplicationJourney') {
                throw new Error('unexpected journey context');
            }

            const { application: newApplication } = submitCustomerDetailsResult;

            if (newApplication.__typename !== 'EventApplication') {
                throw new Error('unexpected type');
            }

            dispatch({
                type: 'next',
                token: submitCustomerDetailsResult.token,
                application: newApplication,
            });
        },
        [dispatch, notification, submitAgreements, submitCustomerDetails, t, token, uploadOcrFiles]
    );

    return (
        <EventJourneyKycAndAgreementProvider
            customerKind={CustomerKind.Guarantor}
            initialActiveAgreements={agreementsKYC}
            initialActiveKycFields={guarantorKyc}
        >
            <ApplicantForm
                dispatch={dispatch}
                initialValues={initialValues}
                onSubmit={onSubmit}
                removeDocument={removeDocument}
                state={state}
                uploadDocument={uploadDocument}
            />
        </EventJourneyKycAndAgreementProvider>
    );
};

const GuarantorKYC = ({ endpoint, state, dispatch }: GuarantorKYCProps) => (
    <OcrFilesManager>
        <EventJourneySetupProvider application={state.application} endpoint={endpoint} event={state.application.event}>
            <GuarantorInner dispatch={dispatch} state={state} />
        </EventJourneySetupProvider>
    </OcrFilesManager>
);

export default GuarantorKYC;
