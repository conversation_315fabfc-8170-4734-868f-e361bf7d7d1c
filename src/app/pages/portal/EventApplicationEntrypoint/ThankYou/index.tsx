import { Col, Row, Space } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { StartTestDriveMutation } from '../../../../api';
import { ApplicationStage, Maybe } from '../../../../api/types';
import UserSessionExpireModal from '../../../../components/UserSessionExpireModal';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getApplicationIdentifier } from '../../../../utilities/application';
import useTranslatedString from '../../../../utilities/useTranslatedString';
// eslint-disable-next-line max-len
import useTestDriveStartModal from '../../../shared/ApplicationDetailsPage/generic/TestDriveModal/TestDriveStartedModal';
import FullJourneySteps from '../../../shared/JourneyPage/FullJourneySteps';
import BookingDeposit from '../../../shared/ThankYou/BookingDeposit';
import DealerInfo from '../../../shared/ThankYou/DealerInfo';
import SubmittedContent from '../../../shared/ThankYou/SubmittedContent';
import VehicleDetails from '../../../shared/ThankYou/VehicleDetails';
import { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../Journey/shared';

const colSpan = { lg: 8, md: 12, sm: 24, xs: 24 };

const ResponsiveContainer = styled(Space)`
    width: 100%;
    padding: 0 16px;

    @media (min-width: 576px) {
        padding: 0 32px;
    }

    @media (min-width: 768px) {
        padding: 0 64px;
    }

    @media (min-width: 992px) {
        padding: 0 128px;
    }

    @media (min-width: 1200px) {
        padding: 0 388.5px;
    }
`;

type ContentsTranslation = {
    lead: {
        title: string;
        description: string;
    };
    reservation: {
        title: string;
        description: string;
    };
    emailDescription: string;
    reference: string;
};

export type ThankYouPageProps = {
    state: State<EventApplicationState>;
};

const getEventApplication = (application: EventApplicationState) => {
    if (application.__typename === 'EventApplication') {
        return application;
    }

    throw new Error('Invalid event application');
};

const ThankYouPageContent = ({ state }: ThankYouPageProps) => {
    const { t } = useTranslation('eventThankYou');
    const translatedString = useTranslatedString();
    const navigate = useNavigate();
    const { application } = state;
    const { module } = application;

    const { Button, StandardLayout } = useThemeComponents();

    const eventApplication = getEventApplication(application);

    const hasDeposit = !!application.deposit;

    if (module.__typename !== 'EventApplicationModule') {
        return <NotFoundResult />;
    }
    const onFinishModal = useCallback((data: StartTestDriveMutation) => {
        window.location.href = data?.result?.redirectionLink;
    }, []);
    const [startTestDrive, renderTestDriveModal] = useTestDriveStartModal(
        {
            __typename: 'EventApplication',
            id: application.id,
            vehicle: application.vehicle?.__typename === 'LocalVariant' && application.vehicle,
            appointmentStage: application.appointmentStage,
            draftFlow: application.draftFlow,
            moduleId: module.id,
            dealer: application.dealer,
            vehicleId: application.vehicle?.__typename === 'LocalVariant' && application.vehicle.id,
        },
        ApplicationStage.Appointment,
        onFinishModal
    );

    const hasTestDriveProcessForPrivateAccess = useMemo(
        () =>
            application.event.privateAccess &&
            !isNil(application.appointmentStage) &&
            application.configuration.testDrive &&
            application.appointmentStage.appointmentModule.__typename === 'AppointmentModule' &&
            application.appointmentStage.appointmentModule.hasTestDriveProcess &&
            application.appointmentStage.appointmentModule.hasTestDriveSigning,
        [application.appointmentStage, application.configuration.testDrive, application.event.privateAccess]
    );

    const onDone = useCallback(() => {
        if (hasTestDriveProcessForPrivateAccess) {
            startTestDrive();
        } else {
            navigate(`../${eventApplication.event.urlSlug}`);
        }
    }, [hasTestDriveProcessForPrivateAccess, startTestDrive, navigate, eventApplication.event.urlSlug]);

    const make = useMemo(() => {
        if (application.__typename === 'EventApplication' && application.vehicle?.__typename === 'LocalVariant') {
            return translatedString(
                application.vehicle.model.parentModel
                    ? application.vehicle.model.parentModel.make.name
                    : application.vehicle.model.make.name
            );
        }

        return '';
    }, [application, translatedString]);

    const depositAmount = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            const { dealerId, event } = application;

            const { defaultValue, overrides } = event.depositAmount;
            if (event.depositAmount.overrides.length) {
                const dealerSpecific = overrides.find(override => override.dealerId === dealerId);

                const value = !dealerSpecific ? defaultValue : dealerSpecific.value;

                return value;
            }

            return defaultValue;
        }

        return application.deposit.amount;
    }, [application]);

    const identifier = useMemo(
        () =>
            getApplicationIdentifier(application, [
                ApplicationStage.Financing,
                ApplicationStage.Reservation,
                ApplicationStage.Lead,
                ApplicationStage.Appointment,
                ApplicationStage.VisitAppointment,
            ]) ?? application.lead?.identifier,
        [application]
    );

    const contents: ContentsTranslation = t<
        string,
        { returnObjects: true; make: string; reference: string },
        ContentsTranslation
    >('eventThankYou:contents', {
        returnObjects: true,
        make,
        reference: identifier,
    });

    const contentProps = useMemo(
        () => ({
            title: hasDeposit ? contents.reservation?.title : contents.lead?.title,
            description: hasDeposit ? contents.reservation?.description : contents.lead?.description,
            subDescription: contents.emailDescription,
            reference: contents.reference,
        }),
        [contents, hasDeposit]
    );

    if (!application.vehicle) {
        return (
            <div data-cy="eventThankyouPage">
                <StandardLayout title={t('eventThankYou:title')}>
                    <BasicProLayoutContainer>
                        <ResponsiveContainer align="center" direction="vertical" size={30}>
                            <FullJourneySteps appendBottomSpacing={false} stages={state?.stages} />
                            <Row gutter={[0, 32]} style={{ width: '100%' }}>
                                <Col span={24}>
                                    <SubmittedContent {...contentProps} textAlign="center" />
                                </Col>
                                <Col span={24}>
                                    <Space direction="vertical" size={24} style={{ width: '100%' }}>
                                        {application?.dealer && application.event.showDealership && (
                                            <DealerInfo dealer={application?.dealer} textAlign="center" />
                                        )}
                                        <Button
                                            key="done"
                                            data-cy="eventThankPageButton"
                                            htmlType="button"
                                            onClick={onDone}
                                            type="primary"
                                            block
                                        >
                                            {t('eventThankYou:actions.done')}
                                        </Button>
                                    </Space>
                                </Col>
                            </Row>
                        </ResponsiveContainer>
                        {state.application?.event?.privateAccess && <UserSessionExpireModal />}
                    </BasicProLayoutContainer>
                </StandardLayout>
            </div>
        );
    }

    return (
        <div data-cy="eventThankyouPage">
            <StandardLayout title={t('eventThankYou:title')}>
                <BasicProLayoutContainer>
                    <FullJourneySteps stages={state?.stages} />
                    <Row gutter={[24, 24]} justify="center" style={{ marginBottom: '24px' }}>
                        <Col {...colSpan}>
                            <Space direction="vertical" size={24} style={{ width: '100%' }}>
                                <VehicleDetails state={state} />
                                {hasDeposit && <BookingDeposit depositAmount={depositAmount} />}
                            </Space>
                        </Col>
                        <Col {...colSpan}>
                            <Space direction="vertical" size={32} style={{ width: '100%' }}>
                                <SubmittedContent {...contentProps} />
                                <Space direction="vertical" size={24} style={{ width: '100%' }}>
                                    {application?.dealer && application.event.showDealership && (
                                        <DealerInfo dealer={application?.dealer} />
                                    )}
                                    <Button
                                        key="done"
                                        data-cy="eventThankPageButton"
                                        htmlType="button"
                                        onClick={onDone}
                                        type="primary"
                                        block
                                    >
                                        {!hasTestDriveProcessForPrivateAccess
                                            ? t('eventThankYou:actions.done')
                                            : t('eventThankYou:actions.startTestDrive')}
                                    </Button>
                                </Space>
                            </Space>
                        </Col>
                    </Row>
                    {renderTestDriveModal()}
                    {state.application?.event?.privateAccess && <UserSessionExpireModal />}
                </BasicProLayoutContainer>
            </StandardLayout>
        </div>
    );
};

const ThankYouPage = () => {
    const state = useLocation().state as Maybe<State<EventApplicationState>>;

    if (!state) {
        return <NotFoundResult />;
    }

    return <ThankYouPageContent state={state} />;
};

export default ThankYouPage;
