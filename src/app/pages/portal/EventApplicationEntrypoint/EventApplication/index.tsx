/* eslint-disable max-len */
import { Dispatch, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
    EventApplicationEntrypointContextDataFragment,
    EventApplicationModuleDebugJourneyFragment,
} from '../../../../api/fragments';
import { EventCustomizedFieldInput } from '../../../../api/types';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useHandleError from '../../../../utilities/useHandleError';
import useAgreementSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementSubmission';
import { getAgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import useAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useAppointmentSubmission';
import useVisitAppointmentSubmission from '../../StandardApplicationEntrypoint/AppointmentPage/useVisitAppointmentSubmission';
import useCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useCustomerDetailsSubmission';
import useUpdateApplicationFields from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useUpdateApplicationFields';
import { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getApplicantKyc } from '../../StandardApplicationEntrypoint/KYCPage/getKyc';
import ApplicantForm from '../ApplicantForm';
import { retrieveAppointmentModule, retrieveAppointmentScenarios } from '../ApplicantForm/AppointmentDetailsSection';
import { getMyInfoTemporaryValues, removeMyInfoTemporaryValues } from '../ApplicantForm/CustomerDetails/utils';
import { ApplicantFormValues } from '../ApplicantForm/shared';
import useUpdateEventApplication from '../ApplicantForm/useUpdateEventApplication';
import {
    EventJourneyKycAndAgreementProvider,
    useEventJourneyKycAndAgreementContext,
} from '../Entrypoint/EventJourneyKycAndAgreement';
import { EventJourneySetupProvider, useEventJourneySetupContext } from '../Entrypoint/EventJourneySetup';
import type { EventApplicationState } from '../Journey/shared';

export type EventApplicationProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
    state: State<EventApplicationState>;
    dispatch: Dispatch<Action<EventApplicationState>>;
};

/**
 * This page use mostly the same logic with the Entrypoint
 * But, it used for redirection from MyInfo, which is the KYC page, which is the initial page like Entrypoint
 */
const Inner = ({ state, dispatch }: Omit<EventApplicationProps, 'endpoint'>) => {
    const { t } = useTranslation(['eventApplicantForm', 'common']);
    const { notification } = useThemeComponents();

    const { token, application } = state;

    const { eventModule, endpoint } = useEventJourneySetupContext();
    const { customerKind, prefill } = useEventJourneyKycAndAgreementContext();

    const updateEvent = useUpdateEventApplication(endpoint);
    const submitAgreements = useAgreementSubmission();
    const submitCustomerDetails = useCustomerDetailsSubmission();
    const updateApplicationFields = useUpdateApplicationFields();

    const uploadOcrFiles = useUploadOcrFiles();
    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    /**
     * existing `submitAppointment` is test drive appointment
     * ::
     * submitVisitAppointment is only serve for `Showroom Visit`
     */
    const submitAppointment = useAppointmentSubmission();
    const submitVisitAppointment = useVisitAppointmentSubmission();

    const onSubmit = useHandleError(
        async ({ appointment, visitAppointment, ...values }: ApplicantFormValues) => {
            notification.loading({
                content: t('eventApplicantForm:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const updateEventResult = await updateEvent(token, values, customerKind);
            const submitAgreementResult = await submitAgreements(
                updateEventResult.token,
                values.agreements,
                customerKind,
                values.hasGuarantor
            );

            const showRemarks = application?.bank?.showCommentsField;

            if (showRemarks) {
                // we wait to update remarks first
                await updateApplicationFields(submitAgreementResult.token, values.remarks);
                // then we later call the KYC
                // as calling submit customer will immediately call the next step and will have not the remarks
            }
            const submitCustomerDetailsResult = await submitCustomerDetails({
                token: submitAgreementResult.token,
                fields: values.customer.fields,
                customerKind,
                sameCorrespondenceAddress: prefill,
                customerCiamId: values.customerCiamId,
            });
            await uploadOcrFiles(submitCustomerDetailsResult.token);

            const { appointmentModule, visitAppointmentModule } = retrieveAppointmentModule(
                application.event.module.__typename === 'EventApplicationModule' &&
                    (application.event.module as EventApplicationModuleDebugJourneyFragment)
            );

            const { hasShowroomVisit, hasTestDrive } = retrieveAppointmentScenarios(
                application.event.module.__typename === 'EventApplicationModule' &&
                    (application.event.module as EventApplicationModuleDebugJourneyFragment),
                application.event
            );

            let temporarilyJourneyResult = submitCustomerDetailsResult;

            // submit for appointment test drive
            if (hasTestDrive && values.configuration.testDrive) {
                const result = await submitAppointment(
                    temporarilyJourneyResult.token,
                    appointment,
                    appointmentModule,
                    application?.event?.displayAppointmentDatepicker,
                    eventModule.company.timeZone
                );
                temporarilyJourneyResult = result;
            }

            // submit for appointment showroom visit
            if (hasShowroomVisit && values.configuration.visitAppointment) {
                const result = await submitVisitAppointment(
                    temporarilyJourneyResult.token,
                    visitAppointment,
                    visitAppointmentModule,
                    true,
                    eventModule.company.timeZone
                );
                temporarilyJourneyResult = result;
            }

            notification.destroy('primary');

            if (temporarilyJourneyResult.__typename === 'GiftVoucherJourneyContext') {
                throw new Error('unsupported journey context');
            }
            const { application: newApplication } = temporarilyJourneyResult;

            if (newApplication.__typename !== 'EventApplication') {
                throw new Error('unexpected type');
            }

            // Remove temporary values
            removeMyInfoTemporaryValues(application.id);

            dispatch({
                type: 'next',
                token: temporarilyJourneyResult.token,
                application: newApplication,
            });
        },
        [
            notification,
            t,
            updateEvent,
            token,
            customerKind,
            submitAgreements,
            application?.bank?.showCommentsField,
            application.event,
            application.id,
            submitCustomerDetails,
            prefill,
            uploadOcrFiles,
            dispatch,
            updateApplicationFields,
            submitAppointment,
            eventModule.company.timeZone,
            submitVisitAppointment,
        ]
    );

    const initialValues: ApplicantFormValues = useMemo(() => {
        const { vehicle } = application;

        const getVehicleValue = () => {
            if (!vehicle) {
                return null;
            }

            if (vehicle.__typename !== 'LocalVariant') {
                throw new Error('Invalid Variant');
            }

            return vehicle;
        };

        const vehicleValue = getVehicleValue();

        const myInfoTemporaryValues = getMyInfoTemporaryValues(application.id);

        const applicantKYC = getApplicantKyc(application.applicantKYC);
        const initialCustomerFields = application.applicant ? application.applicant.fields : [];

        return {
            customer: applicantKYC ? { fields: getInitialValues(initialCustomerFields, applicantKYC) } : null,
            agreements: getAgreementValues(application.applicantAgreements),
            configuration: {
                ...application.configuration,
                visitAppointment: false,
            },
            vehicleInterest: {
                model: vehicleValue?.model.parentModel ? vehicleValue.model.parentModelId : vehicleValue?.model.id,
                subModel: vehicleValue?.submodelId,
                variant: vehicleValue?.id,
            },
            isCorporateCustomer: false,
            tradeInVehicle: application.tradeInVehicle,
            hasGuarantor: false,
            dealerId: application.dealerId,
            prefill: false,
            customizedFields: application.customizedFields as EventCustomizedFieldInput[],
            appointment: {
                date: myInfoTemporaryValues?.appointment?.date,
                time: myInfoTemporaryValues?.appointment?.time,
                useCurrentDateTime: myInfoTemporaryValues?.appointment?.useCurrentDateTime ?? false,
            },
            visitAppointment: {
                date: myInfoTemporaryValues?.visitAppointment?.date,
                time: myInfoTemporaryValues?.visitAppointment?.time,
                useCurrentDateTime: myInfoTemporaryValues?.visitAppointment?.useCurrentDateTime ?? false,
            },

            remarks: application.remarks ?? '',
            customerCiamId:
                application.applicant.__typename === 'LocalCustomer' ? application.applicant.customerCiamId : null,
        };
    }, [application]);

    return (
        <ApplicantForm
            dispatch={dispatch}
            initialValues={initialValues}
            onSubmit={onSubmit}
            removeDocument={removeDocument}
            state={state}
            uploadDocument={uploadDocument}
        />
    );
};

const EventApplication = ({ endpoint, state, dispatch }: EventApplicationProps) => (
    <OcrFilesManager>
        <EventJourneySetupProvider application={state.application} endpoint={endpoint} event={state.application.event}>
            <EventJourneyKycAndAgreementProvider>
                <Inner dispatch={dispatch} state={state} />
            </EventJourneyKycAndAgreementProvider>
        </EventJourneySetupProvider>
    </OcrFilesManager>
);

export default EventApplication;
