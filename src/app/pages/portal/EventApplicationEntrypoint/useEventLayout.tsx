import { isNil } from 'lodash/fp';
import { Dispatch, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { getPorscheIDConfigByApplicationJourney } from '../../../components/PorscheID/getPorscheIDConfig';
import { Action, State, JourneyStage } from '../StandardApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from './Journey/shared';

const useEventLayout = (
    dispatch: Dispatch<Action<EventApplicationState>>,
    state: State<EventApplicationState>
): {
    title: string;
    onBack: () => void;
    shouldShowBackIcon?: boolean;
    /**
     * This is used to determine whether to wrap the content page with JourneyLayout. default is true.
     */
    useJourneyLayout?: boolean;
} => {
    const { t } = useTranslation([
        'customerDetails',
        'journey',
        'paymentDetails',
        'confirmRemoteFlow',
        'otpPage',
        'appointmentPage',
    ]);
    const navigate = useNavigate();

    const { application, stage: journeyStage } = state;
    const { draftFlow } = application;

    const shouldShowBackIcon = !application.withCustomerDevice;
    const isApplyingFromDetails =
        !isNil(draftFlow.isApplyingForFinanceCompleted) || !isNil(draftFlow.isApplyingForInsuranceCompleted);

    const useJourneyLayout = true;

    const { isPorscheIdLoginMandatory } = getPorscheIDConfigByApplicationJourney(application);

    const applicantKYCBackAction = useMemo(
        () => (isPorscheIdLoginMandatory ? () => navigate('..') : () => navigate(-1)),
        [isPorscheIdLoginMandatory, navigate]
    );

    switch (journeyStage) {
        case JourneyStage.PorscheIdLoginRegister:
            return {
                title: t('customerDetails:title'),
                onBack: () => {},
                shouldShowBackIcon: false,
                useJourneyLayout: false,
            };

        case JourneyStage.ApplicantKYC:
            return {
                title: t('customerDetails:title'),
                onBack: shouldShowBackIcon ? () => applicantKYCBackAction() : () => {},
                shouldShowBackIcon: shouldShowBackIcon && !isApplyingFromDetails,
                useJourneyLayout: false,
            };

        case JourneyStage.GuarantorKYC:
            return {
                title: t('customerDetails:title'),
                onBack: shouldShowBackIcon ? () => navigate(-1) : () => {},
                shouldShowBackIcon,
                useJourneyLayout,
            };

        case JourneyStage.Deposit:
            return {
                title: t('paymentDetails:title'),
                onBack: () => dispatch({ type: 'goTo', stage: JourneyStage.ApplicantKYC }),
                shouldShowBackIcon,
                useJourneyLayout,
            };

        case JourneyStage.Appointment:
            return {
                title: t('appointmentPage:title'),
                onBack: () => navigate('..'),
                shouldShowBackIcon,
                useJourneyLayout,
            };

        case JourneyStage.ConfirmEmailSend:
            return {
                title: t('confirmRemoteFlow:title'),
                onBack: () => {},
                shouldShowBackIcon,
                useJourneyLayout: false,
            };

        case JourneyStage.Otp:
            return {
                title: t('otpPage:title'),
                onBack: () => navigate('..'),
                shouldShowBackIcon,
                useJourneyLayout,
            };

        default:
            return {
                title: '',
                onBack: () => {},
                shouldShowBackIcon,
                useJourneyLayout,
            };
    }
};

export default useEventLayout;
