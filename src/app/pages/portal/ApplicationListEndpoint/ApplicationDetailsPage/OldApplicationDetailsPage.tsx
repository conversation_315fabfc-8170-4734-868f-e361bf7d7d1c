/* eslint-disable max-len */
import { isEmpty } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router';
import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { ApplicationListEndpointContextDataFragment } from '../../../../api/fragments/ApplicationListEndpointContextData';
import { useGetApplicationQuery } from '../../../../api/queries/getApplication';
import { useGetReferenceApplicationListEndpointsQuery } from '../../../../api/queries/getReferenceApplicationListEndpoints';
import { ApplicationStage, LeadStageOption } from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { ApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import { getApplicationIdentifier } from '../../../../utilities/application';
import GenericApplicationDetailsPage from '../../../shared/ApplicationDetailsPage/generic';
import StandardApplicationDetailsPage from '../../../shared/ApplicationDetailsPage/standard';
import {
    eventPortalPath,
    ReferenceApplicationProvider,
    ReferencePathPrefixes,
} from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import ApplicationDetailWrapper from '../../../shared/CIPage/ApplicationDetailWrapper';
import MobilityBookingDetailsPage from '../../../shared/MobilityBookingDetailsPage';
import NotFoundPage from '../../NotFoundPage';
import useBackNavigation from './useBackNavigation';

export type ApplicationDetailsPageProps = {
    endpoint: ApplicationListEndpointContextDataFragment;
};

export const getBankModuleId = (application: ApplicationDataFragment) => {
    switch (application.__typename) {
        case 'ConfiguratorApplication':
        case 'EventApplication':
        case 'StandardApplication':
        case 'FinderApplication':
            return application.bank?.moduleId;

        default:
            return null;
    }
};

export const getApplicationPageElement = (
    application: ApplicationDataFragment,
    stage: ApplicationStage,
    endpointId?: string
) => {
    switch (application.__typename) {
        case 'StandardApplication':
        case 'ConfiguratorApplication':
        case 'FinderApplication': {
            if (
                [
                    ApplicationStage.Lead,
                    ApplicationStage.Reservation,
                    ApplicationStage.Appointment,
                    ApplicationStage.Insurance,
                ].includes(stage)
            ) {
                return (
                    <GenericApplicationDetailsPage application={application} endpointId={endpointId} stage={stage} />
                );
            }

            return <StandardApplicationDetailsPage application={application} stage={stage} />;
        }

        case 'LaunchpadApplication':
        case 'EventApplication':
            // Generic should be used for non-finance one
            return <GenericApplicationDetailsPage application={application} endpointId={endpointId} stage={stage} />;

        case 'MobilityApplication':
            return <MobilityBookingDetailsPage application={application} />;

        default:
            return <NotFoundPage />;
    }
};

type PortalApplicationDetailsInnerProps = {
    application: ApplicationDataFragment;
    endpoint: ApplicationListEndpointContextDataFragment;
};

const PortalApplicationDetailsInner = ({ application, endpoint }: PortalApplicationDetailsInnerProps) => {
    const router = useRouter(true);

    const { data, loading } = useGetReferenceApplicationListEndpointsQuery({
        variables: {
            filter: {
                applicationModuleId: application.moduleId,
                stage: endpoint.applicationStage,
                routerId: router.id,
            },
        },
    });

    const getEventEndpoint = useMemo(() => {
        const prefix = router.endpoints.find(
            item =>
                item.__typename === 'EventApplicationEntrypoint' &&
                application.__typename === 'EventApplication' &&
                item.eventApplicationModule?.id === application.moduleId
        )?.pathname;

        return eventPortalPath(prefix);
    }, [router.endpoints, application]);

    const leadEndpoint = useMemo(
        () =>
            router?.endpoints?.find(
                item =>
                    item.__typename === 'LeadListEndpoint' &&
                    [LeadStageOption.Lead, LeadStageOption.LeadAndContact].includes(item.leadStage)
            )?.pathname,
        [router.endpoints]
    );

    const contactEndpoint = useMemo(
        () =>
            router?.endpoints?.find(
                item =>
                    item.__typename === 'LeadListEndpoint' &&
                    [LeadStageOption.Contact, LeadStageOption.LeadAndContact].includes(item.leadStage)
            )?.pathname,
        [router.endpoints]
    );

    const referencePaths = useMemo((): ReferencePathPrefixes => {
        if (!data?.endpoints) {
            return {};
        }

        const stages = data.endpoints.reduce((acc, endpoint) => {
            if (!acc[endpoint.applicationStage]) {
                return {
                    ...acc,
                    [endpoint.applicationStage]: [endpoint.pathname],
                };
            }

            return acc;
        }, {});

        return {
            stages: {
                ...stages,
                [ApplicationStage.Lead]: leadEndpoint,
                Contact: contactEndpoint,
            },
            references: {
                getEventDetail: getEventEndpoint,
            },
        } as ReferencePathPrefixes;
    }, [contactEndpoint, data?.endpoints, getEventEndpoint, leadEndpoint]);

    if (loading) {
        return <PortalLoadingElement />;
    }

    return (
        <ReferenceApplicationProvider referencePaths={referencePaths}>
            {getApplicationPageElement(application, endpoint.applicationStage, endpoint.id)}
        </ReferenceApplicationProvider>
    );
};

const OldApplicationDetailsPage = ({ endpoint }: ApplicationDetailsPageProps) => {
    const { PageWithHeader } = useThemeComponents();
    const { id } = useParams<{ id: string }>();

    const { data, loading, refetch } = useGetApplicationQuery({ variables: { id }, fetchPolicy: 'cache-and-network' });
    const application = data?.application;

    const navigate = useNavigate();
    const { t } = useTranslation('applicationDetails');

    const extra = useMemo(() => ({ application, refetch, forCI: true }), [application, refetch]);

    const backNavigation = useBackNavigation({ endpoint, application });

    if (loading) {
        return <PortalLoadingElement />;
    }

    // third checker is to user attempt visiting application from different endpoint type
    if (
        isEmpty(application) ||
        application.__typename === 'MobilityApplication' ||
        !application.stages.includes(endpoint.applicationStage)
    ) {
        return <NotFoundPage />;
    }

    const getTitle = () => {
        switch (endpoint.applicationStage) {
            case ApplicationStage.Reservation:
                return t('applicationDetails:titles.reservation', { id: application.reservationStage?.identifier });

            case ApplicationStage.Appointment:
                return t('applicationDetails:titles.appointment', { id: application.appointmentStage?.identifier });

            case ApplicationStage.VisitAppointment:
                return t('applicationDetails:titles.visitAppointment', {
                    id: application.visitAppointmentStage?.identifier,
                });

            case ApplicationStage.Insurance:
                return t('applicationDetails:titles.insurance', { id: application.insuranceStage?.identifier });

            default: {
                const identifier = getApplicationIdentifier(application, endpoint.applicationStage);

                return t('applicationDetails:titles.application', { id: identifier });
            }
        }
    };

    return (
        <ApplicationDetailsExtraContext.Provider value={extra}>
            <ApplicationDetailWrapper>
                <PageWithHeader onBack={() => navigate(backNavigation)} title={getTitle()}>
                    <PortalApplicationDetailsInner application={application} endpoint={endpoint} />
                </PageWithHeader>
            </ApplicationDetailWrapper>
        </ApplicationDetailsExtraContext.Provider>
    );
};

export default OldApplicationDetailsPage;
