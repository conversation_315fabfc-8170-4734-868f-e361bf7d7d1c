import { useMemo } from 'react';
import { useLocation } from 'react-router';
import { type ApplicationListEndpointContextDataFragment } from '../../../../api/fragments/ApplicationListEndpointContextData';
import { type LaunchPadApplicationEntrypointContextDataFragment } from '../../../../api/fragments/LaunchPadApplicationEntrypointContextData';
import { type GetApplicationQuery } from '../../../../api/queries/getApplication';
import { ApplicationStage, LeadStageOption } from '../../../../api/types';

import { useRouter } from '../../../../components/contexts/shared';

const useBackNavigation = ({
    endpoint,
    application,
}: {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment | ApplicationListEndpointContextDataFragment;
    application: GetApplicationQuery['application'];
}) => {
    const router = useRouter(true);
    const location = useLocation();

    return useMemo(() => {
        if (location?.state?.previousEndpoint) {
            return location.state.previousEndpoint;
        }

        // if it's not appointment details page, use browser back
        if (
            endpoint.__typename === 'ApplicationListEndpoint' &&
            endpoint.applicationStage !== ApplicationStage.Appointment
        ) {
            return -1;
        }

        if (application?.stages.includes(ApplicationStage.Appointment)) {
            // build navigation for appointment
            const appointmentEndpoint = router?.endpoints?.find(
                item =>
                    item.__typename === 'ApplicationListEndpoint' &&
                    item.applicationStage === ApplicationStage.Appointment
            )?.pathname;

            if (appointmentEndpoint) {
                return appointmentEndpoint;
            }
        }

        if (application?.stages.includes(ApplicationStage.VisitAppointment)) {
            // build navigation for showroom visit
            const visitAppointmentEndpoint = router?.endpoints?.find(
                item =>
                    item.__typename === 'ApplicationListEndpoint' &&
                    item.applicationStage === ApplicationStage.VisitAppointment
            )?.pathname;

            if (visitAppointmentEndpoint) {
                return visitAppointmentEndpoint;
            }
        }

        if (application?.lead.__typename === 'LaunchpadLead') {
            const launchpadEndpoint = router?.endpoints?.find(
                item => item.__typename === 'LaunchPadApplicationEntrypoint'
            );

            if (launchpadEndpoint) {
                return `${launchpadEndpoint.pathname}/leads/${application?.lead.versioning.suiteId}`;
            }
        }

        const leadEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'LeadListEndpoint' &&
                [
                    application?.lead.isLead ? LeadStageOption.Lead : LeadStageOption.Contact,
                    LeadStageOption.LeadAndContact,
                ].includes(item.leadStage)
        )?.pathname;

        if (leadEndpoint) {
            return leadEndpoint;
        }

        return -1;
    }, [application, endpoint, location?.state?.previousEndpoint, router?.endpoints]);
};

export default useBackNavigation;
