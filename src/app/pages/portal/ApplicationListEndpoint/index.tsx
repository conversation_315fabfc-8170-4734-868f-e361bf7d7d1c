import { Route, Routes } from 'react-router-dom';
import { ApplicationListEndpointContextDataFragment } from '../../../api/fragments/ApplicationListEndpointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import withPrivateAccess from '../../../utilities/withPrivateAccess';
import MetaData from '../../shared/MetaData';
import NotFoundPage from '../NotFoundPage';
import ApplicationDetailsPage from './ApplicationDetailsPage';
import ApplicationListPage from './ApplicationListPage';

export type ApplicationListEndpointProps = {
    endpoint: ApplicationListEndpointContextDataFragment;
};

const ApplicationListEndpoint = ({ endpoint }: ApplicationListEndpointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.title}`} />
            <Routes>
                <Route
                    key={`application-${endpoint.id}`}
                    element={<ApplicationListPage endpoint={endpoint} />}
                    path=""
                />
                <Route
                    key={`applicationDetails-${endpoint.id}`}
                    element={<ApplicationDetailsPage endpoint={endpoint} />}
                    path=":id"
                />
                <Route key="404" element={<NotFoundPage />} path="*" />
            </Routes>
        </>
    );
};

export default withPrivateAccess(ApplicationListEndpoint);
