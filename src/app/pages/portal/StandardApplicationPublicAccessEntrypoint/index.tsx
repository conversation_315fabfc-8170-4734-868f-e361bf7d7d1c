import { useEffect } from 'react';
import { Route, Routes } from 'react-router-dom';
// eslint-disable-next-line max-len
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { useDealerContext } from '../../../components/contexts/DealerContextManager';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import { StandardApplicationProvider } from '../StandardApplicationEntrypoint/StandardApplicationContext';
import CarDetailPage from './CarDetailsPage';
import CarListingPage from './CarListingPage';
import ComparisonPage from './ComparisonPage';

export type StandardApplicationPublicAccessEntrypointProps = {
    endpoint: StandardApplicationPublicAccessEntrypointContextDataFragment;
};

const StandardApplicationPublicAccessEntrypoint = ({ endpoint }: StandardApplicationPublicAccessEntrypointProps) => {
    const { setDealerOptions, setDisplayDropdown } = useDealerContext();
    const translated = useTranslatedString();
    const router = useRouter();

    useEffect(() => {
        if (endpoint.dealers.length > 0) {
            const dealerOption = endpoint.dealers.map(dealer => ({
                value: dealer.id,
                label: translated(dealer.legalName),
            }));
            setDisplayDropdown(true);
        }
    }, [endpoint.dealers, setDealerOptions, setDisplayDropdown, translated]);

    return (
        <StandardApplicationProvider>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key="list" element={<CarListingPage endpoint={endpoint} />} path="" />
                <Route key="carDetails" element={<CarDetailPage endpoint={endpoint} />} path="details/:variantId" />
                <Route key="comparison" element={<ComparisonPage endpoint={endpoint} />} path="comparison" />
            </Routes>
        </StandardApplicationProvider>
    );
};

export default StandardApplicationPublicAccessEntrypoint;
