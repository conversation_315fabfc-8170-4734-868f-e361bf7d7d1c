import { useLocation } from 'react-router-dom';
import { Maybe } from '../../../../api';
import {
    EventApplicationEntrypointContextDataFragment,
    FinderApplicationEntrypointContextDataFragment,
    FinderApplicationPublicAccessEntrypointContextDataFragment,
    LaunchPadApplicationEntrypointContextDataFragment,
} from '../../../../api/fragments';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import JourneyBootstrap from './JourneyBootstrap';

export type JourneyLocationState = {
    token: string;
    stage?: JourneyStage;
};

export type JourneyProps = {
    endpoint:
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment
        | LaunchPadApplicationEntrypointContextDataFragment;
};

const Journey = ({ endpoint }: JourneyProps) => {
    const state = useLocation().state as Maybe<JourneyLocationState>;

    if (!state) {
        return <NotFoundResult />;
    }

    const { token, stage = JourneyStage.Initialize } = state;

    if (!token) {
        // that shouldn't happen as well
        return <NotFoundResult />;
    }

    return <JourneyBootstrap endpoint={endpoint} initialStage={stage} initialToken={token} />;
};

export default Journey;
