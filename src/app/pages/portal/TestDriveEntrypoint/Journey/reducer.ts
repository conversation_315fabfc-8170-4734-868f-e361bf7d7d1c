import { getApplicationJourneyStages } from '../../../shared/JourneyPage/mapJourneySteps';
import type { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { getStageFromApplication } from './initializeStage';
import type { TestDriveJourneyState } from './shared';

const reducer = (
    state: State<TestDriveJourneyState>,
    action: Action<TestDriveJourneyState>
): State<TestDriveJourneyState> => {
    switch (action.type) {
        case 'refresh':
            return { ...state, token: action.token, application: action.application };

        case 'next':
            return {
                ...state,
                stage: getStageFromApplication(action.application),
                token: action.token,
                application: action.application,
                stages: getApplicationJourneyStages(action.application),
            };

        case 'goTo':
            return { ...state, stage: action.stage };

        default:
            return state;
    }
};

export default reducer;
