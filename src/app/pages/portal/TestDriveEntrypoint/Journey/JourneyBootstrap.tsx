import { useApolloClient } from '@apollo/client';
import { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router';
import {
    EventApplicationEntrypointContextDataFragment,
    FinderApplicationEntrypointContextDataFragment,
    FinderApplicationPublicAccessEntrypointContextDataFragment,
    LaunchPadApplicationEntrypointContextDataFragment,
} from '../../../../api/fragments';
import {
    GetApplicationJourneyDocument,
    GetApplicationJourneyQuery,
    GetApplicationJourneyQueryVariables,
} from '../../../../api/queries';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import UserSessionExpireModal from '../../../../components/UserSessionExpireModal';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import NotFoundResult from '../../../../components/results/NotFoundResult';
import { useThemeComponents } from '../../../../themes/hooks';
import getApolloErrors from '../../../../utilities/getApolloErrors';
import { useParseJWTPayload } from '../../../../utilities/parseJWTPayload';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import JourneyController from './JourneyController';

export type JourneyBootstrapProps = {
    endpoint:
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment
        | LaunchPadApplicationEntrypointContextDataFragment;
    initialToken: string;
    initialStage: JourneyStage;
};

type TokenPayload = {
    applicationModuleId: { $oid: string };
    origin: 'test-drive' | 'remote-applicant';
};

const isValidModuleId = (
    endpoint:
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment
        | LaunchPadApplicationEntrypointContextDataFragment,
    applicationModuleId: string
) => {
    if (
        endpoint.__typename === 'FinderApplicationEntrypoint' ||
        endpoint.__typename === 'FinderApplicationPublicAccessEntrypoint'
    ) {
        return endpoint.finderApplicationModules.some(module => module.id === applicationModuleId);
    }

    if (endpoint.__typename === 'EventApplicationEntrypoint') {
        return endpoint.eventApplicationModule.id === applicationModuleId;
    }

    if (endpoint.__typename === 'LaunchPadApplicationEntrypoint') {
        return endpoint.launchPadApplicationModule.id === applicationModuleId;
    }

    return false;
};

const JourneyBootstrap = ({ initialToken, initialStage, endpoint }: JourneyBootstrapProps) => {
    const apolloClient = useApolloClient();

    const { applicationModuleId } = useParseJWTPayload<TokenPayload>(initialToken);
    const [state, setInitialState] = useState<GetApplicationJourneyQuery['result'] | null>(null);

    const [error, setError] = useState<Error | null>(null);

    const user = useAccount(true);
    const location = useLocation();

    const { notification } = useThemeComponents();

    const isInitialized = !!state;
    const isValid = isValidModuleId(endpoint, applicationModuleId.$oid);
    useEffect(() => {
        if (isInitialized) {
            // already initialized, we skip it
            return;
        }

        apolloClient
            .query<GetApplicationJourneyQuery, GetApplicationJourneyQueryVariables>({
                query: GetApplicationJourneyDocument,
                fetchPolicy: 'no-cache',
                variables: { token: initialToken, refreshToken: true },
            })
            .then(result => setInitialState(result.data.result))
            .catch(error => {
                const apolloErrors = getApolloErrors(error);

                if (apolloErrors !== null) {
                    const { $root: rootError } = apolloErrors;

                    if (rootError) {
                        notification.error(rootError);
                    }
                } else {
                    console.error(error);
                }

                setError(error);
            });
    }, [isInitialized, isValid, apolloClient, initialToken, notification]);
    if (!isValid || !!error) {
        // things are not as expected
        return <NotFoundResult />;
    }

    if (!state) {
        return <PortalLoadingElement />;
    }

    const { application } = state;

    if (
        application.__typename !== 'EventApplication' &&
        application.__typename !== 'FinderApplication' &&
        application.__typename !== 'ConfiguratorApplication' &&
        application.__typename !== 'StandardApplication' &&
        application.__typename !== 'LaunchpadApplication'
    ) {
        // things are not as expected (invalid app type)
        return <NotFoundResult />;
    }

    const isPrivateAccess = application.__typename === 'EventApplication' && application.event.privateAccess;
    if (isPrivateAccess && !user) {
        return <Navigate state={{ nextPage: location.pathname }} to={{ pathname: '/auth/signIn' }} />;
    }

    return (
        <>
            {isPrivateAccess && <UserSessionExpireModal />}
            <JourneyController
                endpoint={endpoint}
                initialApplication={application}
                initialStage={initialStage}
                initialToken={state.token}
            />
        </>
    );
};

export default JourneyBootstrap;
