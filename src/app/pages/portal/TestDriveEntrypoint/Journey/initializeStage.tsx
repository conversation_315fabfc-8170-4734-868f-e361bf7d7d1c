import { Dispatch, useEffect } from 'react';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { State, Action, JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { TestDriveJourneyState } from './shared';

export const getStageFromApplication = (
    application: State<TestDriveJourneyState>['application'],
    initialized = true
) => {
    const { draftFlow, testDriveSigning, module } = application;
    if (
        module.__typename !== 'EventApplicationModule' &&
        module.__typename !== 'FinderApplicationPrivateModule' &&
        module.__typename !== 'FinderApplicationPublicModule' &&
        module.__typename !== 'LaunchPadModule'
    ) {
        throw new Error('ModuleType not supported ');
    }

    if (
        application.__typename !== 'LaunchpadApplication' &&
        draftFlow.hasTestDriveKYCandConsents &&
        (!draftFlow.areTestDriveKYCCompleted || !draftFlow.areTestDriveAgreementsCompleted)
    ) {
        return JourneyStage.TestDriveKYC;
    }

    if (testDriveSigning && !draftFlow.isTestDriveProcessSigningCompleted) {
        switch (testDriveSigning.__typename) {
            case 'ApplicationNamirialSigning':
                return JourneyStage.TestDriveNamirial;

            case 'ApplicationOTPSigning':
                return JourneyStage.TestDriveOtp;

            default:
                return JourneyStage.Unknown;
        }
    }

    return JourneyStage.Unknown;
};

export type InitializeStageProps = {
    state: State<TestDriveJourneyState>;
    dispatch: Dispatch<Action<TestDriveJourneyState>>;
};

const InitializeStage = ({ state, dispatch }: InitializeStageProps) => {
    const { application } = state;

    useEffect(() => {
        dispatch({ type: 'goTo', stage: getStageFromApplication(application) });
    }, [application, dispatch]);

    return <PortalLoadingElement />;
};

export default InitializeStage;
