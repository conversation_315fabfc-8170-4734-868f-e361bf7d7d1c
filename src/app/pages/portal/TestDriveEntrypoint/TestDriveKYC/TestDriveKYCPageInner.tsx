import { Col, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
import { CustomerKind, LocalCustomerManagementModule } from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import Form from '../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../utilities/kycPresets/shared';
import CustomerDetails from '../../../shared/JourneyPage/CustomerDetails';
import ProceedWithCustomerButton from '../../../shared/ProceedWithCustomerButton';
import { Agreements } from '../../EventApplicationEntrypoint/ApplicantForm';
import ConsentsAndDeclarations from '../../EventApplicationEntrypoint/ApplicantForm/ConsentsAndDeclarations';
// eslint-disable-next-line max-len
import useProceedWithCustomerDeviceButton from '../../StandardApplicationEntrypoint/KYCPage/useProceedWithCustomerDeviceButton';
import { NextButton } from '../../StandardApplicationEntrypoint/shared/JourneyButton';
import { Backdrop, StyledJourneyToolbar } from '../../StandardApplicationEntrypoint/styledComponents';
import OcrAndMyinfo from '../Shared/OcrAndMyinfo';
import VehicleInterest from '../Shared/VehicleOfInterest';
import type { TestDriveKYCJourneyValues, TestDriveKYCPageProps } from './shared';

const leftColSpan = { xl: 8, lg: 12, md: 24, xs: 24 };
const rightColSpan = { xl: 16, lg: 12, md: 24, xs: 24 };

type TestDriveKYCPageInnerProps = TestDriveKYCPageProps & {
    setPrefill: Dispatch<SetStateAction<boolean>>;
    prefill: boolean;
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    kycAgreements: Agreements;
    hasCorporatePreset: boolean;
    token: string;
} & UploadDocumentProp;

const TestDriveKYCPageInner = ({
    state,
    endpoint,
    dispatch,
    setPrefill,
    kycPresets,
    kycExtraSettings,
    kycAgreements,
    hasCorporatePreset,
    token,
    uploadDocument,
    removeDocument,
}: TestDriveKYCPageInnerProps) => {
    const { application } = state;

    const { t } = useTranslation('customerDetails');
    const { values, handleSubmit, resetForm, initialValues, isSubmitting, validateForm, submitForm } =
        useFormikContext<TestDriveKYCJourneyValues>();
    const { StandardLayout } = useThemeComponents();
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const { testDriveKYC, guarantorKYC } = useMemo(
        () => ({
            testDriveKYC: application.testDriveKYC,
            guarantorKYC: application.__typename !== 'LaunchpadApplication' ? application.guarantorKYC : [],
        }),
        [application]
    );

    const { proceedWithCustomerButton, proceedWithCustomer } = useProceedWithCustomerDeviceButton(
        testDriveKYC,
        state,
        dispatch
    );

    const [withMyInfo, setWithMyInfo] = useState(false);

    const onSubmit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [submitForm, validateForm]);

    const title = useMemo(
        () => (
            <Row style={{ width: '100%' }}>
                <Typography>{t('customerDetails:title')}</Typography>
            </Row>
        ),
        [t]
    );

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycPresets) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycPresets, resetForm, values.agreements]);

    if (application.__typename === 'LaunchpadApplication') {
        return null;
    }

    return (
        <>
            <FormAutoTouch />
            <StandardLayout
                extra={
                    proceedWithCustomerButton && (
                        <ProceedWithCustomerButton onClick={() => proceedWithCustomer(values)} />
                    )
                }
                onBack={null}
                title={title}
                hasFooterBar
            >
                <BasicProLayoutContainer>
                    <Form id="testDriveKYC" name="testDriveKYC" onSubmitCapture={handleSubmit}>
                        <Row gutter={[24, 50]}>
                            <Col {...leftColSpan}>
                                <Row gutter={[50, 50]}>
                                    <VehicleInterest application={application} />
                                    <OcrAndMyinfo
                                        application={application}
                                        endpoint={endpoint}
                                        kycPresets={kycPresets}
                                        onLoading={setIsLoading}
                                        setWithMyInfo={setWithMyInfo}
                                        state={state}
                                        withMyInfo={withMyInfo}
                                    />
                                </Row>
                            </Col>

                            <Col {...rightColSpan}>
                                <Row gutter={[16, 16]}>
                                    <Col span={24}>
                                        <CustomerDetails
                                            customerKind={CustomerKind.Local}
                                            hasGuarantorPreset={guarantorKYC.length > 0}
                                            hasUploadDocuments={application.bank?.hasUploadDocuments}
                                            hasVSOUpload={false}
                                            isApplyingFromDetails={false}
                                            kycExtraSettings={kycExtraSettings}
                                            kycPresets={kycPresets}
                                            removeDocument={removeDocument}
                                            resetFormHandler={resetFormHandler}
                                            setIsCorporate={null}
                                            setPrefill={setPrefill}
                                            showCommentsToInsurer={false}
                                            showRemarks={false}
                                            showResetButton={false}
                                            showTabs={hasCorporatePreset}
                                            uploadDocument={uploadDocument}
                                            withFinancing={false}
                                        />
                                    </Col>
                                    <Col span={24}>
                                        <ConsentsAndDeclarations
                                            applicationAgreements={kycAgreements}
                                            hideDivider={false}
                                        />
                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    </Form>
                </BasicProLayoutContainer>
                {isLoading && <Backdrop />}

                <StyledJourneyToolbar>
                    <NextButton key="nextButton" disabled={isSubmitting} onSubmit={onSubmit} />
                </StyledJourneyToolbar>
            </StandardLayout>
        </>
    );
};

export default TestDriveKYCPageInner;
