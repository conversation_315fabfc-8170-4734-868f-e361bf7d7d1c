/* eslint-disable max-len */
import { ApolloError } from '@apollo/client';
import { Formik } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationAgreementDataFragment, KycFieldSpecsFragment } from '../../../../api/fragments';
import { ApplicationAgreement, LocalCustomerFieldKey } from '../../../../api/types';
import { useUploadOcrFiles } from '../../../../components/ocr';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues, KYCPresetFormFields } from '../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import useAgreementsValidator from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import useTestDriveAgreementsSubmission from '../../../shared/CIPage/ConsentAndDeclarations/useTestDriveAgreementsSubmission';
import useKYCData from '../../../shared/JourneyPage/CustomerDetails/useKYCData';
import useDeleteApplicationDocument from '../../../shared/useDeleteApplicationDocument';
import useUploadApplicationDocument, { UploadDocumentKind } from '../../../shared/useUploadApplicationDocument';
import useTestDriveCustomerDetailsSubmission from '../../StandardApplicationEntrypoint/CustomerDetailsPage/useTestDriveCustomerDetailsSubmission';
import type { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { StyleContainer } from '../../StandardApplicationEntrypoint/styledComponents';
import type { TestDriveJourneyState } from '../Journey/shared';
import { getApplicationModuleFromEndpoint } from '../Shared/OcrAndMyinfo';
import TestDriveKYCPageInner from './TestDriveKYCPageInner';
import type { TestDriveKYCJourneyValues, TestDriveKYCPageProps } from './shared';

export type CustomKYCFormFields = Pick<
    KYCPresetFormFields,
    | LocalCustomerFieldKey.FirstName
    | LocalCustomerFieldKey.LastName
    | LocalCustomerFieldKey.FullName
    | LocalCustomerFieldKey.Email
    | LocalCustomerFieldKey.IdentityNumber
>;

export type Agreements = Omit<ApplicationAgreement, 'displayName' | 'orderNumber'>[];

export type TestDriveKYCPageFormProps = {
    state: State<TestDriveJourneyState>;
    setSaveDraft: (value: boolean) => void;
};

const TestDriveKYCPageForm = ({ state, endpoint, dispatch }: TestDriveKYCPageProps) => {
    const { application, token } = state;

    const { t } = useTranslation('customerDetails');
    const { notification } = useThemeComponents();

    const [saveDraft, setSaveDraft] = useState(false);
    const [prefill, setPrefill] = useState(false);

    const [agreementAndKyc] = useState<{
        testDriveKYC: KycFieldSpecsFragment[];
        testDriveAgreements: ApplicationAgreementDataFragment[];
    }>({
        testDriveAgreements: application.testDriveAgreements,
        testDriveKYC: application.testDriveKYC,
    });

    const { applicant, applicantAgreements } = application;

    const module = getApplicationModuleFromEndpoint(endpoint, application);

    const submitAgreements = useTestDriveAgreementsSubmission();
    const submitCustomerDetails = useTestDriveCustomerDetailsSubmission();

    const applicantKYCFields = useMemo(() => (applicant ? applicant.fields : []), [applicant]);

    const { kycAgreements, kycFields } = useKYCData(
        false,
        agreementAndKyc.testDriveKYC,
        [],
        agreementAndKyc.testDriveAgreements,
        []
    );

    const kycExtraSettings = useMemo(() => {
        switch (application?.__typename) {
            case 'ConfiguratorApplication': {
                if (application.module?.__typename === 'ConfiguratorModule') {
                    return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                        ? application.module.customerModule.extraSettings
                        : null;
                }

                return null;
            }
            case 'EventApplication': {
                if (application.module?.__typename === 'EventApplicationModule') {
                    return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                        ? application.module.customerModule.extraSettings
                        : null;
                }

                return null;
            }
            case 'FinderApplication': {
                if (
                    application.module?.__typename === 'FinderApplicationPrivateModule' ||
                    application.module?.__typename === 'FinderApplicationPublicModule'
                ) {
                    return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                        ? application.module.customerModule.extraSettings
                        : null;
                }

                return null;
            }

            case 'StandardApplication': {
                if (application.module?.__typename === 'StandardApplicationModule') {
                    return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                        ? application.module.customerModule.extraSettings
                        : null;
                }

                return null;
            }

            case 'LaunchpadApplication': {
                if (application.module?.__typename === 'LaunchPadModule') {
                    return application.module.customerModule?.__typename === 'LocalCustomerManagementModule'
                        ? application.module.customerModule.extraSettings
                        : null;
                }

                return null;
            }

            default:
                return null;
        }
    }, [application]);

    const agreementsKYC = kycAgreements.map(agreement => ({
        ...agreement,
        isAgreed:
            applicantAgreements.find(applicantAgreement => applicantAgreement.id === agreement.id)?.isAgreed || false,
    }));
    const agreementsValidator = useAgreementsValidator(agreementsKYC, 'agreements');
    const agreements = useAgreementsValues(agreementsKYC);

    const applicants = useMemo(() => {
        const documents =
            application.__typename === 'EventApplication' || application.__typename === 'FinderApplication'
                ? application.documents
                : null;

        const fields = {
            ...getInitialValues(applicantKYCFields, kycFields, documents),
        };

        return { fields };
    }, [applicantKYCFields, application, kycFields]);

    const applicantsValidator = useKYCFormValidator({
        field: kycFields,
        extraSettings: kycExtraSettings,
        moduleCountryCode: module.company.countryCode,
        prefix: 'customer.fields',
        saveDraft,
    });

    const validations = useMemo(
        () => validators.compose(applicantsValidator, agreementsValidator),
        [agreementsValidator, applicantsValidator]
    );

    const validate = useValidator(validations, { prefill });

    const initialValues: TestDriveKYCJourneyValues = useMemo(
        () => ({
            agreements,
            customer: applicants,
            tradeInVehicle: application.tradeInVehicle,
            remarks: application?.remarks ?? '',
            vsoUpload: [],
            prefill: false,
        }),
        [agreements, applicants, application?.remarks, application.tradeInVehicle]
    );

    const uploadOcrFiles = useUploadOcrFiles();

    const uploadDocument = useUploadApplicationDocument(token, UploadDocumentKind.ApplicationAndLead);
    const removeDocument = useDeleteApplicationDocument(UploadDocumentKind.ApplicationAndLead, token);

    const onSubmit = useHandleError(
        async (values: TestDriveKYCJourneyValues) => {
            try {
                notification.loading({
                    content: t('customerDetails:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                const submitTestDriveAgreementKYC = await submitAgreements(state.token, values.agreements);

                const submitTestDriveKYC = await submitCustomerDetails(
                    submitTestDriveAgreementKYC.token,
                    values.customer.fields,
                    values.prefill
                );

                await uploadOcrFiles(submitTestDriveKYC.token);

                notification.destroy('primary');

                // go to the journey
                if (
                    submitTestDriveKYC.application.__typename !== 'StandardApplication' &&
                    submitTestDriveKYC.application.__typename !== 'EventApplication' &&
                    submitTestDriveKYC.application.__typename !== 'ConfiguratorApplication' &&
                    submitTestDriveKYC.application.__typename !== 'FinderApplication'
                ) {
                    throw new Error('unexpected type');
                }

                if (!saveDraft) {
                    dispatch({
                        type: 'next',
                        token: submitTestDriveKYC.token,
                        application: submitTestDriveKYC.application,
                    });
                } else {
                    notification.success({
                        content: t('customerDetails:messages.draftSaved'),
                        key: 'secondary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            } finally {
                notification.destroy('primary');
            }
        },

        [notification, t, state.token, submitAgreements, submitCustomerDetails, saveDraft, uploadOcrFiles, dispatch]
    );

    return (
        <StyleContainer>
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} validateOnMount>
                <TestDriveKYCPageInner
                    dispatch={dispatch}
                    endpoint={endpoint}
                    hasCorporatePreset={false}
                    kycAgreements={agreementsKYC}
                    kycExtraSettings={kycExtraSettings}
                    kycPresets={kycFields}
                    prefill={prefill}
                    removeDocument={removeDocument}
                    setPrefill={setPrefill}
                    state={state}
                    token={token}
                    uploadDocument={uploadDocument}
                />
            </Formik>
        </StyleContainer>
    );
};

const TestDriveKYCPage = (props: TestDriveKYCPageProps) => (
    <OcrFilesManager>
        <TestDriveKYCPageForm {...props} />
    </OcrFilesManager>
);

export default TestDriveKYCPage;
