/* eslint-disable max-len */
import { Col, Row } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import type { ConfiguratorApplicationState } from '../../ConfiguratorApplicationEntrypoint/Journey/shared';
import GenericVehicleInterest from '../../ConfiguratorApplicationEntrypoint/ThankYou/VehicleInfo';
import calculateTotalPrice from '../../ConfiguratorApplicationEntrypoint/helper';
import type { EventApplicationState } from '../../EventApplicationEntrypoint/Journey/shared';
import VehicleInterest from '../../FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInterest';
import type { FinderApplicationState } from '../../FinderApplicationPublicAccessEntrypoint/shared';
import type { StandardApplicationState } from '../../StandardApplicationEntrypoint/Journey/shared';

const colSpan = { span: 24 };

type VehicleOfInterestProps = {
    application:
        | ConfiguratorApplicationState
        | EventApplicationState
        | FinderApplicationState
        | StandardApplicationState;
};

const Title = styled.h4`
    font-size: 20px;
    margin-bottom: 1rem;
    font-weight: 900;
`;

const VehicleOfInterest = ({ application }: VehicleOfInterestProps) => {
    const { t } = useTranslation('customerDetails');

    const translate = useTranslatedString();

    const VehicleInfo = useMemo(() => {
        switch (application.__typename) {
            case 'FinderApplication': {
                return <VehicleInterest application={application} />;
            }

            case 'ConfiguratorApplication': {
                const { vehicle } = application;
                switch (vehicle.__typename) {
                    case 'LocalVariant': {
                        const vehicleData = {
                            make: translate(
                                vehicle.model.parentModel
                                    ? vehicle.model.parentModel.make.name
                                    : vehicle.model.make.name
                            ),
                            variant: translate(vehicle.name),
                            defaultVariantName: vehicle.name.defaultValue,
                            defaultModelName: vehicle.model.parentModel
                                ? vehicle.model.parentModel.name.defaultValue
                                : vehicle.model.name.defaultValue,
                            totalPrice: calculateTotalPrice(application),
                            filename: application.vehicleImage?.filename,
                            source: application.vehicleImage?.url,
                            carPrice: vehicle.vehiclePrice,
                        };

                        return <GenericVehicleInterest {...vehicleData} />;
                    }

                    default:
                        throw new Error('not supported');
                }
            }

            case 'EventApplication':
            case 'StandardApplication': {
                const { vehicle } = application;
                switch (vehicle.__typename) {
                    case 'LocalVariant': {
                        const vehicleData = {
                            make: translate(vehicle.model.make.name),
                            variant: translate(vehicle.name),
                            totalPrice: null,
                            filename: vehicle?.images?.[0]?.filename,
                            source: vehicle?.images?.[0]?.url,
                        };

                        return <GenericVehicleInterest {...vehicleData} />;
                    }

                    default:
                        throw new Error('not supported');
                }
            }

            default:
                throw new Error('ApplicationKind not supported');
        }
    }, [application, translate]);

    return (
        <Col span={24}>
            <Title>{t('customerDetails:sectionTitles.vehicleOfInterest')}</Title>

            <Row gutter={[24, 24]}>
                <Col {...colSpan}>{VehicleInfo}</Col>
            </Row>
        </Col>
    );
};

export default VehicleOfInterest;
