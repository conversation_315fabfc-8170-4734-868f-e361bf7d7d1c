import { ApolloClient, NormalizedCacheObject, useApolloClient } from '@apollo/client';
import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import {
    ConfiguratorApplicationEntrypointContextDataFragment,
    EventApplicationEntrypointContextDataFragment,
    FinderApplicationEntrypointContextDataFragment,
    FinderApplicationPublicAccessEntrypointContextDataFragment,
    LaunchPadApplicationEntrypointContextDataFragment,
} from '../../../../api/fragments';
import { KycFieldSpecsFragment } from '../../../../api/fragments/KYCFieldSpecs';
// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import {
    GetMyInfoAuthorizeUrlDocument,
    GetMyInfoAuthorizeUrlQuery,
    GetMyInfoAuthorizeUrlQueryVariables,
} from '../../../../api/queries/getMyInfoAuthorizeUrl';
import { CustomerKind } from '../../../../api/types';
import MyInfo from '../../../../components/MyInfo';
import { useRouter } from '../../../../components/contexts/shared';
import Ocr, { useOcrDetectedHandler } from '../../../../components/ocr';
import useDealershipSettingId from '../../../../utilities/useDealershipSettingId';
import type { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { TestDriveJourneyState } from '../Journey/shared';
import type { TestDriveKYCJourneyValues } from '../TestDriveKYC/shared';

type OcrAndMyinfoProps = {
    state: State<TestDriveJourneyState>;
    application: TestDriveJourneyState;
    kycPresets: KycFieldSpecsFragment[];
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | ConfiguratorApplicationEntrypointContextDataFragment
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
    onLoading: (isLoading: boolean) => void;
    withMyInfo: boolean;
    setWithMyInfo: (value: boolean) => void;
};

export const getApplicationModuleFromEndpoint = (
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | ConfiguratorApplicationEntrypointContextDataFragment
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment
        | LaunchPadApplicationEntrypointContextDataFragment,
    application: TestDriveJourneyState
) => {
    switch (endpoint.__typename) {
        case 'ConfiguratorApplicationEntrypoint':
            return endpoint.configuratorApplicationModule;

        case 'FinderApplicationPublicAccessEntrypoint': {
            return endpoint.finderApplicationModules.find(
                module =>
                    application.__typename === 'FinderApplication' &&
                    application.module.__typename === 'FinderApplicationPublicModule' &&
                    application.module.id === module.id
            );
        }
        case 'FinderApplicationEntrypoint': {
            return endpoint.finderApplicationModules.find(
                module =>
                    application.__typename === 'FinderApplication' &&
                    application.module.__typename === 'FinderApplicationPrivateModule' &&
                    application.module.id === module.id
            );
        }

        case 'EventApplicationEntrypoint':
            return endpoint.eventApplicationModule;

        case 'StandardApplicationEntrypoint':
            return endpoint.applicationModule;

        case 'LaunchPadApplicationEntrypoint':
            return endpoint.launchPadApplicationModule;

        default:
            throw new Error('Application Module not found');
    }
};

const retrievemyInfoEnabled = (
    application: TestDriveJourneyState,
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | ConfiguratorApplicationEntrypointContextDataFragment
        | EventApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment
) => {
    switch (application.__typename) {
        case 'EventApplication': {
            const dealerSettingId = useDealershipSettingId(application.dealerId);

            return dealerSettingId(application.event.myInfoSetting);
        }

        case 'ConfiguratorApplication': {
            return (
                endpoint.__typename === 'ConfiguratorApplicationEntrypoint' &&
                endpoint.configuratorApplicationModule.myInfoSettingId
            );
        }

        case 'FinderApplication': {
            const module = getApplicationModuleFromEndpoint(endpoint, application);

            return (
                module.__typename === 'FinderApplicationPrivateModule' ||
                (module.__typename === 'FinderApplicationPublicModule' && module.myInfoSettingId)
            );
        }

        case 'StandardApplication': {
            return (
                endpoint.__typename === 'StandardApplicationEntrypoint' && endpoint.applicationModule.myInfoSettingId
            );
        }

        default:
            throw new Error('ApplicationKind not supported');
    }
};

const OcrAndMyinfo = ({
    state,
    application,
    endpoint,
    kycPresets,
    onLoading,
    withMyInfo,
    setWithMyInfo,
}: OcrAndMyinfoProps) => {
    const client = useApolloClient() as ApolloClient<NormalizedCacheObject>;
    const router = useRouter();
    const { values } = useFormikContext<TestDriveKYCJourneyValues>();

    const module = getApplicationModuleFromEndpoint(endpoint, application);
    const myInfoEnabled = retrievemyInfoEnabled(application, endpoint);
    const isOcrEnabled = module.__typename !== 'LaunchPadModule' && module.isOcrEnabled;
    const showMyInfoContent = !withMyInfo && myInfoEnabled;
    const showOcr = !withMyInfo && isOcrEnabled;

    const myInfoOnClick = useCallback(async () => {
        const { data } = await client.query<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>({
            query: GetMyInfoAuthorizeUrlDocument,
            variables: {
                applicationId: application.id,
                routerId: router.id,
                endpointId: endpoint.id,
                customerKind: CustomerKind.Local,
                withTradeIn: application.configuration.tradeIn,
                withTestDrive: application.configuration.testDrive,
            },
            fetchPolicy: 'no-cache',
        });

        if (data?.authorizeUrl) {
            globalThis.location.href = data.authorizeUrl;
        }
    }, [
        client,
        application.id,
        application.configuration.tradeIn,
        application.configuration.testDrive,
        router.id,
        endpoint.id,
    ]);

    const onOcrDetected = useOcrDetectedHandler(kycPresets);

    const tradeInVehicle = useMemo(
        () => ({
            withTradeIn: application.configuration.tradeIn,
            withTestDrive: application.configuration.testDrive,
            name: 'tradeInVehicle',
        }),
        [values]
    );

    if (!showOcr && !showMyInfoContent) {
        return null;
    }

    return (
        <Col span={24}>
            {showMyInfoContent && (
                <MyInfo
                    applicationId={application.id}
                    customerFieldPrefix="customer.fields"
                    customerKind={CustomerKind.Local}
                    onClick={myInfoOnClick}
                    onLoading={onLoading}
                    setWithMyInfo={setWithMyInfo}
                    tradeInVehicle={tradeInVehicle}
                />
            )}
            {showOcr && <Ocr onDetected={onOcrDetected} />}
        </Col>
    );
};

export default OcrAndMyinfo;
