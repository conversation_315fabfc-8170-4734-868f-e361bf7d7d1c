/**
 * Stream export event applications excel for large exports
 *
 * This is used in event details > applications list tab for large exports
 */

import { nanoid } from 'nanoid';
import { LeadStageOption, ApplicationStage, PeriodPayload } from '../../api/types';
import { generateApplicationFilename } from './shared';
import { CapExcelPurpose, ExportFormat } from './types';

export type ExportEventsType = {
    eventId?: string;
    dealerIds: string[];
    company?: { countryCode?: string; displayName?: string };
    token: string | null;
    identifier?: string;
    eventDisplayName?: string;
};

type ExportEventApplicationType = ExportEventsType & {
    applicationIds: string[];
    stage: ApplicationStage;
};

type ExportEventLeadType = ExportEventsType & {
    leadIds: string[];
    stage: LeadStageOption;
    format: ExportFormat;
    period?: PeriodPayload;
};

export const streamExportEventsLead = async ({
    eventId,
    leadIds,
    dealerIds,
    company,
    token,
    identifier,
    eventDisplayName,
    format,
    stage,
    period,
}: ExportEventLeadType) => {
    const nonce = nanoid();

    const headersObj = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };

    const body: Pick<ExportEventLeadType, 'dealerIds' | 'format' | 'stage' | 'leadIds' | 'company' | 'period'> & {
        nonce: string;
    } = { nonce, dealerIds, format, stage, leadIds, company, period };

    const runExport = async (capPurpose?: CapExcelPurpose[], filePurpose?: string[]) => {
        const endpoint = `/api/export/eventLeads/stream/${eventId}`;

        const generatedFilenames: string[] = [];

        if (Array.isArray(filePurpose) && filePurpose.length > 0) {
            filePurpose.forEach(purpose => {
                generatedFilenames.push(
                    generateApplicationFilename([company?.displayName, eventDisplayName, stage, purpose])
                );
            });
        } else if (format === 'reporting') {
            generatedFilenames.push(generateApplicationFilename([eventDisplayName, stage, 'Report']));
        } else {
            generatedFilenames.push(generateApplicationFilename([company?.displayName, identifier, stage]));
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: headersObj,
            body: JSON.stringify({
                ...body,
                ...(capPurpose && { capPurpose }),
                filename: generatedFilenames,
            }),
        });

        return response;
    };

    const response =
        format === 'cap'
            ? await runExport(['BP_UPLOAD', 'BP_LEAD_UPLOAD'], ['BP', 'BP_LEAD'])
            : await runExport([], []);

    return response;
};

export const streamExportEventsApplication = async ({
    eventId,
    applicationIds,
    dealerIds,
    company,
    token,
    identifier,
    eventDisplayName,
    stage,
}: ExportEventApplicationType) => {
    const nonce = nanoid();

    const headersObj = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };

    const body: Pick<ExportEventApplicationType, 'dealerIds' | 'stage' | 'applicationIds' | 'company'> & {
        nonce: string;
    } = { nonce, dealerIds, stage, applicationIds, company };

    const runExport = async () => {
        const endpoint = `/api/export/eventApplications/stream/${eventId}`;

        const generatedFilenames: string[] = [];

        // Generate filename for event applications export
        generatedFilenames.push(
            generateApplicationFilename([company?.displayName, eventDisplayName || identifier, stage])
        );

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: headersObj,
            body: JSON.stringify({
                ...body,
                filename: generatedFilenames,
            }),
        });

        return response;
    };

    return runExport();
};
