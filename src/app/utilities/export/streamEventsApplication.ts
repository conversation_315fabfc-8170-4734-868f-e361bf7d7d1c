/**
 * Stream export event applications excel for large exports
 *
 * This is used in event details > applications list tab for large exports
 */

import { nanoid } from 'nanoid';
import { ApplicationStage } from '../../api/types';
import { generateApplicationFilename } from './shared';

export type ExportEventsType = {
    eventId?: string;
    dealerIds: string[];
    company?: { countryCode?: string; displayName?: string };
    token: string | null;
    identifier?: string;
    eventDisplayName?: string;
};

type ExportEventApplicationType = ExportEventsType & {
    applicationIds: string[];
    stage: ApplicationStage;
};

export const streamExportEventsApplication = async ({
    eventId,
    applicationIds,
    dealerIds,
    company,
    token,
    identifier,
    eventDisplayName,
    stage,
}: ExportEventApplicationType) => {
    const nonce = nanoid();

    const headersObj = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };

    const body: Pick<ExportEventApplicationType, 'dealerIds' | 'stage' | 'applicationIds' | 'company'> & {
        nonce: string;
    } = { nonce, dealerIds, stage, applicationIds, company };

    const runExport = async () => {
        const endpoint = `/api/export/eventApplications/stream/${eventId}`;

        const generatedFilenames: string[] = [];

        // Generate filename for event applications export
        generatedFilenames.push(
            generateApplicationFilename([company?.displayName, eventDisplayName || identifier, stage])
        );

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: headersObj,
            body: JSON.stringify({
                ...body,
                filename: generatedFilenames,
            }),
        });

        return response;
    };

    return runExport();
};

export default streamExportEventsApplication;
