import { InfoCircleOutlined } from '@ant-design/icons';
import { Col, ColProps, Row, RowProps } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { get, isEmpty, isNil, upperCase } from 'lodash/fp';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { allowedExtensions } from '../../../server/utils/extensions';
import { ApplicationDocumentDataFragment } from '../../api/fragments/ApplicationDocumentData';
import { ApplicationInsurancingDataFragment } from '../../api/fragments/ApplicationInsurancingData';
import { KycFieldSpecsFragment } from '../../api/fragments/KYCFieldSpecs';
import {
    ApplicationDocumentKind,
    CustomerKind,
    DrivingLicensePayload,
    DrivingLicenseType,
    LocalCustomerField,
    LocalCustomerFieldKey,
    LocalCustomerFieldSettings,
    LocalCustomerFieldSource,
    LocalCustomerManagementModule,
    PhonePayload,
    ReferenceDetailPayload,
    SalaryTransferredBankPayload,
    StringDescriptionPayload,
    UaeIdentityPayload,
} from '../../api/types';
import getInsuranceCalculatorValueFromApplication from '../../calculator/getInsuranceCalculatorValueFromApplication';
import { useCompany } from '../../components/contexts/CompanyContextManager';
import { defaultFilterOption } from '../../components/fields/SelectField';
import useDialCodeByCountry from '../../pages/admin/CompanyDetailsPage/useDialCodeByCountry';
import { ApplicantFormValues } from '../../pages/portal/ConfiguratorApplicationEntrypoint/ApplicantKYCPage/types';
import { useThemeComponents } from '../../themes/hooks';
import useCapHobbyOptions from '../useCapHobbyOptions';
import useSystemOptions, { CitizenshipType } from '../useSystemOptions';
import { TooltipRenderer } from '../useTooltip';
import DisabledFieldKey from './DisabledFieldKey/DisabledFieldKey';
import DrivingLicenses from './DrivingLicenses';
import JobTitleField from './JobTitleField';
import JobTitleThField from './JobTitleThField';
import ReferenceDetailFieldset from './ReferenceDetailFieldset';
import RelationshipWithApplicantField from './RelationshipWithApplicantField';
import ResidentialStatusVwfsField from './ResidentialStatusVwfsField';
import SalaryTransferredBankFieldset from './SalaryTransferredBankFieldset';
import UAEDrivingLicenses from './UAEDrivingLicenses';
import UAEIdentityFieldset from './UAEIdentityFieldset';
import WatchFieldSource from './WatchFieldSource';
import {
    getKycFieldLabel,
    type KycPresetFieldProps,
    type KYCPresetFormValueFields,
    type UploadDocumentProp,
} from './shared';
import useInsuranceKyc from './useInsuranceKyc';

export type KYCPresetFormNumberFields = {
    [key in
        | LocalCustomerFieldKey.NoClaimDiscount
        | LocalCustomerFieldKey.TimeOfAddress
        | LocalCustomerFieldKey.TimeOfEmployment
        | LocalCustomerFieldKey.CorporateRegisteredCapital
        | LocalCustomerFieldKey.CorporateNumberOfEmployee
        | LocalCustomerFieldKey.CorporateAnnualRevenue
        | LocalCustomerFieldKey.MonthlyIncome
        | LocalCustomerFieldKey.OtherIncome]: KYCPresetFormValueFields<number>;
};

export type KYCPresetFormStringFields = {
    [key in
        | LocalCustomerFieldKey.Title
        | LocalCustomerFieldKey.NonBinaryTitle
        | LocalCustomerFieldKey.Salutation
        | LocalCustomerFieldKey.SalutationBmw
        | LocalCustomerFieldKey.LastNameFront
        | LocalCustomerFieldKey.FirstName
        | LocalCustomerFieldKey.LastName
        | LocalCustomerFieldKey.LastNameJapan
        | LocalCustomerFieldKey.FirstNameJapan
        | LocalCustomerFieldKey.FullName
        | LocalCustomerFieldKey.Email
        | LocalCustomerFieldKey.IdentityNumber
        | LocalCustomerFieldKey.Nationality
        | LocalCustomerFieldKey.Country
        | LocalCustomerFieldKey.PostalCode
        | LocalCustomerFieldKey.Passport
        | LocalCustomerFieldKey.Address
        | LocalCustomerFieldKey.UnitNumber
        | LocalCustomerFieldKey.Citizenship
        | LocalCustomerFieldKey.Race
        | LocalCustomerFieldKey.Gender
        | LocalCustomerFieldKey.NonBinaryGender
        | LocalCustomerFieldKey.MaritalStatus
        | LocalCustomerFieldKey.ResidentialStatus
        | LocalCustomerFieldKey.ResidentialStatusVwfs
        | LocalCustomerFieldKey.Region
        | LocalCustomerFieldKey.City
        | LocalCustomerFieldKey.Road
        | LocalCustomerFieldKey.District
        | LocalCustomerFieldKey.CorrespondenceAddress
        | LocalCustomerFieldKey.CorrespondenceCity
        | LocalCustomerFieldKey.CorrespondenceDistrict
        | LocalCustomerFieldKey.JobTitle
        | LocalCustomerFieldKey.JobTitleTh
        | LocalCustomerFieldKey.Occupation
        | LocalCustomerFieldKey.EmploymentStatus
        | LocalCustomerFieldKey.CompanyAddress
        | LocalCustomerFieldKey.CompanyCity
        | LocalCustomerFieldKey.CompanyName
        | LocalCustomerFieldKey.CompanyPhoneticName
        | LocalCustomerFieldKey.CompanyDistrict
        | LocalCustomerFieldKey.RelationshipWithApplicant
        | LocalCustomerFieldKey.CorporateName
        | LocalCustomerFieldKey.CorporateIdentityNumber
        | LocalCustomerFieldKey.CorporateIndustryCategory
        | LocalCustomerFieldKey.CompanyPhoneExtension
        | LocalCustomerFieldKey.AddressType
        | LocalCustomerFieldKey.Emirate
        | LocalCustomerFieldKey.Education
        | LocalCustomerFieldKey.IncomeType
        | LocalCustomerFieldKey.DeliveryAddress
        | LocalCustomerFieldKey.ResidenceType
        | LocalCustomerFieldKey.BusinessTitle]: KYCPresetFormValueFields<string>;
};

export type KYCPresetFormStringDescriptionFields = {
    [key in
        | LocalCustomerFieldKey.JobTitle
        | LocalCustomerFieldKey.JobTitleTh
        | LocalCustomerFieldKey.ResidentialStatusVwfs
        | LocalCustomerFieldKey.RelationshipWithApplicant]: KYCPresetFormValueFields<StringDescriptionPayload>;
};

export type KYCPresetFormDateFields = {
    [key in
        | LocalCustomerFieldKey.Birthday
        | LocalCustomerFieldKey.DriverLicensePassDate
        | LocalCustomerFieldKey.CorporateRegistrationDate
        | LocalCustomerFieldKey.DateOfJoining
        | LocalCustomerFieldKey.PreferredFirstPaymentDate]: KYCPresetFormValueFields<Date | string>;
};

export type KYCPresetFormFileFields = {
    [key in
        | LocalCustomerFieldKey.UploadDrivingLicense
        | LocalCustomerFieldKey.UploadIdentity
        | LocalCustomerFieldKey.UploadPassport
        | LocalCustomerFieldKey.UploadOtherDocument]: KYCPresetFormValueFields<DrivingLicensePayload['uploadDLCopy']>;
};

export type KYCPresetFormFields = Partial<
    KYCPresetFormStringFields &
        KYCPresetFormNumberFields &
        KYCPresetFormDateFields &
        KYCPresetFormStringDescriptionFields &
        KYCPresetFormFileFields & {
            // miscellaneous
            [LocalCustomerFieldKey.Phone]: KYCPresetFormValueFields<PhonePayload>;
        } & {
            [LocalCustomerFieldKey.DrivingLicense]: KYCPresetFormValueFields<DrivingLicensePayload[]>;
        } & {
            [LocalCustomerFieldKey.DrivingLicenseTh]: KYCPresetFormValueFields<DrivingLicensePayload[]>;
        } & {
            [LocalCustomerFieldKey.DrivingLicenseMy]: KYCPresetFormValueFields<DrivingLicensePayload[]>;
        } & {
            [LocalCustomerFieldKey.UaeDrivingLicense]: KYCPresetFormValueFields<DrivingLicensePayload[]>;
        } & {
            [LocalCustomerFieldKey.CorporatePhone]: KYCPresetFormValueFields<PhonePayload>;
        } & {
            [LocalCustomerFieldKey.Telephone]: KYCPresetFormValueFields<PhonePayload>;
        } & {
            [LocalCustomerFieldKey.ReferenceDetailSet]: KYCPresetFormValueFields<ReferenceDetailPayload>;
        } & {
            [LocalCustomerFieldKey.SalaryTransferredBankSet]: KYCPresetFormValueFields<SalaryTransferredBankPayload>;
        } & {
            [LocalCustomerFieldKey.UaeIdentitySet]: KYCPresetFormValueFields<UaeIdentityPayload>;
        } & {
            [LocalCustomerFieldKey.CompanyPhone]: KYCPresetFormValueFields<PhonePayload>;
        } & {
            [LocalCustomerFieldKey.Hobby]: KYCPresetFormValueFields<string[]>;
        }
>;

export const LocalGuarantorFieldKeys = [
    LocalCustomerFieldKey.FirstName,
    LocalCustomerFieldKey.LastName,
    LocalCustomerFieldKey.FullName,
    LocalCustomerFieldKey.Email,
];

export const fieldsToRemoveWhitespace = [
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
    LocalCustomerFieldKey.CurrentVehicleVin,
];

type KYCInitialValueType = {
    source: LocalCustomerFieldSource;
    value: any;
};

const getInitialValueFromField = (
    initialFields: LocalCustomerField[],
    kycPresetField: KycFieldSpecsFragment,
    documents?: ApplicationDocumentDataFragment[]
): KYCInitialValueType => {
    const field = initialFields.find(initialField => initialField.key === kycPresetField.key);

    if (!field) {
        if (kycPresetField.key === LocalCustomerFieldKey.UaeDrivingLicense) {
            return {
                source: LocalCustomerFieldSource.UserInput,
                value: [{ type: DrivingLicenseType.Uae, isUAEResident: true }],
            };
        }

        if (kycPresetField.key === LocalCustomerFieldKey.DrivingLicense) {
            return { source: LocalCustomerFieldSource.UserInput, value: [{ type: undefined }] };
        }

        if (kycPresetField.key === LocalCustomerFieldKey.DrivingLicenseTh) {
            return { source: LocalCustomerFieldSource.UserInput, value: [{ type: undefined }] };
        }

        if (kycPresetField.key === LocalCustomerFieldKey.DrivingLicenseMy) {
            return { source: LocalCustomerFieldSource.UserInput, value: [{ type: undefined }] };
        }

        return undefined;
    }

    switch (field.__typename) {
        case 'LocalCustomerDateField':
            return { source: field.source, value: field.dateValue ? new Date(field.dateValue) : undefined };

        case 'LocalCustomerStringField':
            return { source: field.source, value: field.stringValue };

        case 'LocalCustomerArrayStringField':
            return { source: field.source, value: field.stringValues };

        case 'LocalCustomerPhoneField':
            return { source: field.source, value: field.phoneValue };

        case 'LocalCustomerDrivingLicenseField': {
            const drivingLicenses = field?.drivingLicenseValue ?? [];

            return { source: field.source, value: drivingLicenses };
        }

        case 'LocalCustomerNumberField': {
            return { source: field.source, value: field.numberValue };
        }

        case 'LocalCustomerStringDescriptionField':
            return { source: field.source, value: field.stringDescriptionValue };

        case 'LocalCustomerUploadsField': {
            if (!isNil(documents)) {
                const filteredDocuments = documents.filter(document => {
                    switch (field.key) {
                        case LocalCustomerFieldKey.UploadDrivingLicense:
                            return document.kind === ApplicationDocumentKind.KycLicenseUpload;

                        case LocalCustomerFieldKey.UploadIdentity:
                            return (
                                document.kind === ApplicationDocumentKind.IdentityCardBack ||
                                document.kind === ApplicationDocumentKind.IdentityCardFront ||
                                document.kind === ApplicationDocumentKind.KycIdentityUpload
                            );

                        case LocalCustomerFieldKey.UploadOtherDocument:
                            return document.kind === ApplicationDocumentKind.KycOtherDocumentUpload;

                        case LocalCustomerFieldKey.UploadPassport:
                            return document.kind === ApplicationDocumentKind.KycPassportUpload;

                        default:
                            return null;
                    }
                });

                return { source: field.source, value: filteredDocuments || field.uploads };
            }

            return { source: field.source, value: field.uploads };
        }

        case 'LocalCustomerReferenceDetailSetField':
            return { source: field.source, value: field.referenceDetailValue };

        case 'LocalCustomerSalaryTransferredBankSetField':
            return { source: field.source, value: field.salaryTransferredBankValue };

        case 'LocalCustomerUAEIdentitySetField':
            return { source: field.source, value: field.uaeIdentityValue };

        default:
            return undefined;
    }
};

export const getInitialValues = (
    initialFields: LocalCustomerField[],
    fields: KycFieldSpecsFragment[],
    documents?: ApplicationDocumentDataFragment[]
) =>
    fields.reduce<KYCPresetFormFields>((acc, field) => {
        switch (field.key) {
            case LocalCustomerFieldKey.Title:
            case LocalCustomerFieldKey.NonBinaryTitle:
            case LocalCustomerFieldKey.Salutation:
            case LocalCustomerFieldKey.SalutationBmw:
            case LocalCustomerFieldKey.LastNameFront:
            case LocalCustomerFieldKey.FirstName:
            case LocalCustomerFieldKey.LastName:
            case LocalCustomerFieldKey.LastNameJapan:
            case LocalCustomerFieldKey.FirstNameJapan:
            case LocalCustomerFieldKey.FullName:
            case LocalCustomerFieldKey.Email:
            case LocalCustomerFieldKey.IdentityNumber:
            case LocalCustomerFieldKey.Nationality:
            case LocalCustomerFieldKey.Birthday:
            case LocalCustomerFieldKey.Phone:
            case LocalCustomerFieldKey.DrivingLicense:
            case LocalCustomerFieldKey.DrivingLicenseTh:
            case LocalCustomerFieldKey.DrivingLicenseMy:
            case LocalCustomerFieldKey.Country:
            case LocalCustomerFieldKey.PostalCode:
            case LocalCustomerFieldKey.Address:
            case LocalCustomerFieldKey.UnitNumber:
            case LocalCustomerFieldKey.Passport:
            case LocalCustomerFieldKey.Race:
            case LocalCustomerFieldKey.Gender:
            case LocalCustomerFieldKey.NonBinaryGender:
            case LocalCustomerFieldKey.MaritalStatus:
            case LocalCustomerFieldKey.ResidentialStatus:
            case LocalCustomerFieldKey.Citizenship:
            case LocalCustomerFieldKey.NoClaimDiscount:
            case LocalCustomerFieldKey.DriverLicensePassDate:
            case LocalCustomerFieldKey.Road:
            case LocalCustomerFieldKey.District:
            case LocalCustomerFieldKey.Telephone:
            case LocalCustomerFieldKey.Region:
            case LocalCustomerFieldKey.City:
            case LocalCustomerFieldKey.TimeOfAddress:
            case LocalCustomerFieldKey.CorrespondenceAddress:
            case LocalCustomerFieldKey.CorrespondenceCity:
            case LocalCustomerFieldKey.CorrespondenceDistrict:
            case LocalCustomerFieldKey.JobTitle:
            case LocalCustomerFieldKey.JobTitleTh:
            case LocalCustomerFieldKey.Occupation:
            case LocalCustomerFieldKey.EmploymentStatus:
            case LocalCustomerFieldKey.TimeOfEmployment:
            case LocalCustomerFieldKey.CompanyAddress:
            case LocalCustomerFieldKey.CompanyCity:
            case LocalCustomerFieldKey.CompanyName:
            case LocalCustomerFieldKey.CompanyPhoneticName:
            case LocalCustomerFieldKey.CompanyDistrict:
            case LocalCustomerFieldKey.CompanyPhone:
            case LocalCustomerFieldKey.CompanyPhoneExtension:
            case LocalCustomerFieldKey.RelationshipWithApplicant:
            case LocalCustomerFieldKey.MonthlyIncome:
            case LocalCustomerFieldKey.OtherIncome:
            case LocalCustomerFieldKey.CorporateName:
            case LocalCustomerFieldKey.CorporateIdentityNumber:
            case LocalCustomerFieldKey.CorporateIndustryCategory:
            case LocalCustomerFieldKey.CorporateRegisteredCapital:
            case LocalCustomerFieldKey.CorporateNumberOfEmployee:
            case LocalCustomerFieldKey.CorporateAnnualRevenue:
            case LocalCustomerFieldKey.CorporateRegistrationDate:
            case LocalCustomerFieldKey.CorporatePhone:
            case LocalCustomerFieldKey.ResidentialStatusVwfs:
            case LocalCustomerFieldKey.UaeDrivingLicense:
            case LocalCustomerFieldKey.UploadDrivingLicense:
            case LocalCustomerFieldKey.UploadIdentity:
            case LocalCustomerFieldKey.UploadPassport:
            case LocalCustomerFieldKey.UploadOtherDocument:
            case LocalCustomerFieldKey.Education:
            case LocalCustomerFieldKey.AddressType:
            case LocalCustomerFieldKey.DateOfJoining:
            case LocalCustomerFieldKey.Emirate:
            case LocalCustomerFieldKey.IncomeType:
            case LocalCustomerFieldKey.PreferredFirstPaymentDate:
            case LocalCustomerFieldKey.ReferenceDetailSet:
            case LocalCustomerFieldKey.ResidenceType:
            case LocalCustomerFieldKey.SalaryTransferredBankSet:
            case LocalCustomerFieldKey.DeliveryAddress:
            case LocalCustomerFieldKey.UaeIdentitySet:
            case LocalCustomerFieldKey.PurchaseIntention:
            case LocalCustomerFieldKey.Comments:
            case LocalCustomerFieldKey.BusinessTitle:
            case LocalCustomerFieldKey.Hobby: {
                return {
                    ...acc,
                    [field.key]: getInitialValueFromField(initialFields, field, documents),
                };
            }

            case LocalCustomerFieldKey.CurrentVehicleSource:
            case LocalCustomerFieldKey.CurrentVehicleOwnership:
            case LocalCustomerFieldKey.CurrentVehicleMake:
            case LocalCustomerFieldKey.CurrentVehicleModel:
            case LocalCustomerFieldKey.CurrentVehicleEquipmentLine:
            case LocalCustomerFieldKey.CurrentVehicleModelYear:
            case LocalCustomerFieldKey.CurrentVehicleMileage:
            case LocalCustomerFieldKey.CurrentVehiclePurchaseYear:
            case LocalCustomerFieldKey.CurrentVehicleEngineType:
            case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
            case LocalCustomerFieldKey.CurrentVehicleContractEnd:
            case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement:
            case LocalCustomerFieldKey.CurrentVehicleVin:
                return { ...acc };

            default:
                throw new Error(`KYC preset initial value not implemented: ${field.key}`);
        }
    }, {});

export const getKycInitialValuesFromInsurance = (
    insurancing?: ApplicationInsurancingDataFragment,
    kyc?: KycFieldSpecsFragment[]
) => {
    if (!insurancing || isNil(kyc) || !kyc?.length) {
        return {};
    }

    const insuranceValues = getInsuranceCalculatorValueFromApplication(insurancing);

    const result: Partial<{
        [key in LocalCustomerFieldKey]: KYCInitialValueType;
    }> = {};

    if (insuranceValues.dateOfBirth && !isNil(kyc.find(item => item.key === LocalCustomerFieldKey.Birthday))) {
        result[LocalCustomerFieldKey.Birthday] = {
            source: LocalCustomerFieldSource.UserInput,
            value: insuranceValues.dateOfBirth.toDate(),
        };
    }

    if (
        insuranceValues.noClaimDiscount &&
        !isNil(kyc.find(item => item.key === LocalCustomerFieldKey.NoClaimDiscount))
    ) {
        result[LocalCustomerFieldKey.NoClaimDiscount] = {
            source: LocalCustomerFieldSource.UserInput,
            value: insuranceValues.noClaimDiscount,
        };
    }

    return result;
};

export const isWhitespaceRemovableField = (fieldKey: LocalCustomerFieldKey) =>
    fieldsToRemoveWhitespace.includes(fieldKey);

export const prepareKYCFieldPayload = <T extends LocalCustomerFieldSettings = LocalCustomerFieldSettings>(
    values: KYCPresetFormFields,
    includeTypeName?: boolean
): T[] =>
    Object.keys(values).reduce((acc, key: LocalCustomerFieldKey): T[] => {
        switch (key) {
            case LocalCustomerFieldKey.Title:
            case LocalCustomerFieldKey.NonBinaryTitle:
            case LocalCustomerFieldKey.Salutation:
            case LocalCustomerFieldKey.SalutationBmw:
            case LocalCustomerFieldKey.LastNameFront:
            case LocalCustomerFieldKey.FirstName:
            case LocalCustomerFieldKey.LastName:
            case LocalCustomerFieldKey.LastNameJapan:
            case LocalCustomerFieldKey.FirstNameJapan:
            case LocalCustomerFieldKey.FullName:
            case LocalCustomerFieldKey.Email:
            case LocalCustomerFieldKey.IdentityNumber:
            case LocalCustomerFieldKey.Nationality:
            case LocalCustomerFieldKey.Country:
            case LocalCustomerFieldKey.PostalCode:
            case LocalCustomerFieldKey.Address:
            case LocalCustomerFieldKey.UnitNumber:
            case LocalCustomerFieldKey.Passport:
            case LocalCustomerFieldKey.Race:
            case LocalCustomerFieldKey.Gender:
            case LocalCustomerFieldKey.NonBinaryGender:
            case LocalCustomerFieldKey.MaritalStatus:
            case LocalCustomerFieldKey.ResidentialStatus:
            case LocalCustomerFieldKey.Road:
            case LocalCustomerFieldKey.District:
            case LocalCustomerFieldKey.Region:
            case LocalCustomerFieldKey.City:
            case LocalCustomerFieldKey.Occupation:
            case LocalCustomerFieldKey.EmploymentStatus:
            case LocalCustomerFieldKey.CompanyAddress:
            case LocalCustomerFieldKey.CompanyCity:
            case LocalCustomerFieldKey.CompanyName:
            case LocalCustomerFieldKey.CompanyPhoneticName:
            case LocalCustomerFieldKey.CompanyDistrict:
            case LocalCustomerFieldKey.CompanyPhoneExtension:
            case LocalCustomerFieldKey.CorporateName:
            case LocalCustomerFieldKey.CorporateIndustryCategory:
            case LocalCustomerFieldKey.CorporateIdentityNumber:
            case LocalCustomerFieldKey.Citizenship:
            case LocalCustomerFieldKey.AddressType:
            case LocalCustomerFieldKey.Education:
            case LocalCustomerFieldKey.Emirate:
            case LocalCustomerFieldKey.IncomeType:
            case LocalCustomerFieldKey.DeliveryAddress:
            case LocalCustomerFieldKey.ResidenceType:
            case LocalCustomerFieldKey.PurchaseIntention:
            case LocalCustomerFieldKey.Comments:
            case LocalCustomerFieldKey.BusinessTitle: {
                const field = values[key];

                if (isEmpty(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        stringValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerStringField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.Hobby: {
                const field = values[key];

                if (isEmpty(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        stringValues: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerArrayStringField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.CorrespondenceAddress:
            case LocalCustomerFieldKey.CorrespondenceCity:
            case LocalCustomerFieldKey.CorrespondenceDistrict: {
                const field = values[key];
                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        stringValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerStringField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.TimeOfEmployment:
            case LocalCustomerFieldKey.TimeOfAddress:
            case LocalCustomerFieldKey.MonthlyIncome:
            case LocalCustomerFieldKey.OtherIncome:
            case LocalCustomerFieldKey.CorporateRegisteredCapital:
            case LocalCustomerFieldKey.CorporateNumberOfEmployee:
            case LocalCustomerFieldKey.CorporateAnnualRevenue:
            case LocalCustomerFieldKey.CurrentVehicleModelYear: {
                const field = values[key];

                // field is empty
                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        numberValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerNumberField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.CorporateRegistrationDate:
            case LocalCustomerFieldKey.Birthday:
            case LocalCustomerFieldKey.DriverLicensePassDate:
            case LocalCustomerFieldKey.DateOfJoining:
            case LocalCustomerFieldKey.PreferredFirstPaymentDate:
            case LocalCustomerFieldKey.CurrentVehicleContractEnd:
            case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement: {
                const field = values[key];
                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        dateValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerDateField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.CorporatePhone:
            case LocalCustomerFieldKey.Telephone:
            case LocalCustomerFieldKey.CompanyPhone:
            case LocalCustomerFieldKey.Phone: {
                const field = values[key];
                if (isNil(field?.value?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        phoneValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerPhoneField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.UaeDrivingLicense:
            case LocalCustomerFieldKey.DrivingLicense:
            case LocalCustomerFieldKey.DrivingLicenseTh:
            case LocalCustomerFieldKey.DrivingLicenseMy: {
                const field = values[key];
                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        drivingLicenseValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerDrivingLicenseField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.NoClaimDiscount: {
                const field = values[key];

                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        numberValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerNumberField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.ResidentialStatusVwfs:
            case LocalCustomerFieldKey.JobTitle:
            case LocalCustomerFieldKey.JobTitleTh:
            case LocalCustomerFieldKey.RelationshipWithApplicant: {
                const field = values[key];

                if (isNil(field?.value?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        stringDescriptionValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerStringDescriptionField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.UploadIdentity:
            case LocalCustomerFieldKey.UploadDrivingLicense:
            case LocalCustomerFieldKey.UploadOtherDocument:
            case LocalCustomerFieldKey.UploadPassport: {
                const field = values[key];

                if (isNil(field?.source)) {
                    return acc;
                }

                // Intentionally set it to empty array
                // as uploads handled from the outside
                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        fileValues: [],
                        ...(includeTypeName && { __typename: 'LocalCustomerUploadsField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.ReferenceDetailSet: {
                const field = values[key];

                if (isNil(field?.value)) {
                    return acc;
                }

                // Need to check the contact number value, since the prefix is prefilled
                const { contactNumber, ...rest } = field.value;

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        referenceDetailValue: {
                            ...rest,
                            contactNumber: !isNil(contactNumber?.value) ? contactNumber : undefined,
                        },
                        ...(includeTypeName && { __typename: 'LocalCustomerReferenceDetailSetField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.SalaryTransferredBankSet: {
                const field = values[key];

                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        salaryTransferredBankValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerSalaryTransferredBankSetField' }),
                    },
                ];
            }

            case LocalCustomerFieldKey.UaeIdentitySet: {
                const field = values[key];

                if (isNil(field?.value)) {
                    return acc;
                }

                return [
                    ...acc,
                    {
                        key,
                        source: field.source,
                        uaeIdentityValue: field.value,
                        ...(includeTypeName && { __typename: 'LocalCustomerUAEIdentitySetField' }),
                    },
                ];
            }

            default:
                throw new Error(`Prepare payload customer field key not implemented: ${key}`);
        }
    }, []);

export const prepareKYCFieldValues = (fields: LocalCustomerField[]) =>
    fields.map(field => {
        switch (field.__typename) {
            case 'LocalCustomerStringField': {
                return { key: field.key, source: field.source, value: field.stringValue };
            }

            case 'LocalCustomerNumberField': {
                return { key: field.key, source: field.source, value: field.numberValue };
            }

            case 'LocalCustomerDateField': {
                return { key: field.key, source: field.source, value: new Date(field.dateValue) };
            }

            case 'LocalCustomerPhoneField': {
                return { key: field.key, source: field.source, value: field.phoneValue };
            }

            case 'LocalCustomerReferenceDetailSetField': {
                return { key: field.key, source: field.source, value: field.referenceDetailValue };
            }

            case 'LocalCustomerSalaryTransferredBankSetField': {
                return { key: field.key, source: field.source, value: field.salaryTransferredBankValue };
            }

            case 'LocalCustomerUAEIdentitySetField': {
                return { key: field.key, source: field.source, value: field.uaeIdentityValue };
            }

            default:
                throw new Error(`Preparation failed. field with type ${field.__typename} not implemented`);
        }
    });

export const getMappedCustomerFieldValues = (fields: LocalCustomerField[]) =>
    fields.reduce<KYCPresetFormFields>((acc, field) => {
        switch (field.__typename) {
            case 'LocalCustomerStringField': {
                return { ...acc, [field.key]: { source: field.source, value: field.stringValue } };
            }

            case 'LocalCustomerNumberField': {
                return { ...acc, [field.key]: { source: field.source, value: field.numberValue } };
            }

            case 'LocalCustomerDateField': {
                return { ...acc, [field.key]: { source: field.source, value: new Date(field.dateValue) } };
            }

            case 'LocalCustomerPhoneField': {
                return { ...acc, [field.key]: { source: field.source, value: field.phoneValue } };
            }

            case 'LocalCustomerReferenceDetailSetField': {
                return { ...acc, [field.key]: { source: field.source, value: field.referenceDetailValue } };
            }

            case 'LocalCustomerSalaryTransferredBankSetField': {
                return { ...acc, [field.key]: { source: field.source, value: field.salaryTransferredBankValue } };
            }

            case 'LocalCustomerUAEIdentitySetField': {
                return { ...acc, [field.key]: { source: field.source, value: field.uaeIdentityValue } };
            }

            default:
                return { ...acc };
        }
    }, {});

const enabledMyinfoField = [
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.MaritalStatus,
];

export const phoneNumberKycFields = [
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CorporatePhone,
];

export const KycPresetField = ({
    extraSettings,
    field,
    colSpan,
    prefix,
    createdAt,
    customerKind,
    bankDisable = false,
    markMyinfo = true,
    isDisabled = false,
    fromJourney = true,
    uploadDocument,
    removeDocument,
    customerType,
    enableMobileVerification,
}: KycPresetFieldProps & UploadDocumentProp & { extraSettings: LocalCustomerManagementModule['extraSettings'] }) => {
    const { t } = useTranslation(['customerDetails', 'regions', 'common']);
    const { values, setFieldValue } = useFormikContext<ApplicantFormValues>();
    // change dropdown to company theme

    const genericCustomer = useMemo(() => values[customerType], [customerType, values]);

    const {
        titleOptions,
        nonBinaryTitleOptions,
        salutationOptions,
        salutationBMWOptions,
        citizenshipOptions,
        employmentStatus,
        addressTypeOptions,
        residenceTypeOptions,
        incomeTypeOptions,
        educationOptions,
        emirateOptions,
        purchaseIntentionOptions,
        noClaimDiscountOptions,
    } = useSystemOptions();

    const { FormFields } = useThemeComponents();

    const prefixName = prefix ? `${prefix}.${field.key}` : field.key;
    const company = useCompany(true);
    const dialCodeByCountry = useDialCodeByCountry();
    const { capHobbyOptions } = useCapHobbyOptions(company?.countryCode);

    const selectedCountry = useMemo(() => values?.customer?.fields?.Country, [values]);

    // load insurance kyc if it's activated
    // Since this loaded for all applications, optional values was used
    const insuranceKyc = useInsuranceKyc(values?.configuration?.withInsurance, { createdAt });

    const uploadDocumentKind = useMemo(() => {
        switch (field.key) {
            case LocalCustomerFieldKey.UploadDrivingLicense:
                return ApplicationDocumentKind.KycLicenseUpload;

            case LocalCustomerFieldKey.UploadIdentity:
                return ApplicationDocumentKind.KycIdentityUpload;

            case LocalCustomerFieldKey.UploadPassport:
                return ApplicationDocumentKind.KycPassportUpload;

            case LocalCustomerFieldKey.UploadOtherDocument:
                return ApplicationDocumentKind.KycOtherDocumentUpload;

            // the rest no need to upload and hence no ApplicationDocumentKind associated with them.
            default:
                return undefined;
        }
    }, [field.key]);

    const upload = useCallback(
        (file: File) => uploadDocument(uploadDocumentKind, file),
        [uploadDocument, uploadDocumentKind]
    );

    const remove = useCallback(file => removeDocument(file.id), [removeDocument]);

    useEffect(() => {
        if (![...phoneNumberKycFields, LocalCustomerFieldKey.ReferenceDetailSet].includes(field.key)) {
            return;
        }

        const countryCodeOrName = company?.countryCode;
        const dialCode = dialCodeByCountry(countryCodeOrName);

        if (!dialCode) {
            return;
        }

        // For phone
        if (phoneNumberKycFields.includes(field.key) && !values?.customer?.fields?.[field.key]?.value?.prefix) {
            setFieldValue(`${prefixName}.value.prefix`, dialCode);
        }

        // For reference detail set: contact number
        if (
            field.key === LocalCustomerFieldKey.ReferenceDetailSet &&
            !values?.customer?.fields?.ReferenceDetailSet?.value?.contactNumber?.prefix
        ) {
            setFieldValue(`${prefixName}.value.contactNumber.prefix`, dialCode);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [field.key, dialCodeByCountry, setFieldValue, prefixName, company, selectedCountry]);

    const disabled = useMemo(
        () =>
            isDisabled ||
            (!enabledMyinfoField.includes(field.key) &&
                get(`${prefixName}.source`, values) === LocalCustomerFieldSource.MyInfo) ||
            (get(`${prefixName}.source`, values) === LocalCustomerFieldSource.UserInput && bankDisable) ||
            customerKind === CustomerKind.Guarantor ||
            (!fromJourney &&
                (field.key === LocalCustomerFieldKey.Title || field.key === LocalCustomerFieldKey.NonBinaryTitle)),
        [bankDisable, customerKind, field.key, fromJourney, isDisabled, prefixName, values]
    );

    const { fromMyinfo, label } = useMemo(() => {
        const fromMyinfo = get(`${prefixName}.source`, values) === LocalCustomerFieldSource.MyInfo;

        return {
            fromMyinfo,
            label: getKycFieldLabel(fromMyinfo && markMyinfo, t(`customerDetails:fields.${field.key}.label`)),
        };
    }, [prefixName, markMyinfo, field, values, t]);

    const creationDate = useMemo(() => {
        const date = get('customer.fields.CreationDate.value', values);

        return dayjs(date).format('DD MMM YYYY');
    }, [values]);

    const birthdayField = useMemo(() => {
        if (genericCustomer?.fields?.UAEDrivingLicense) {
            return (
                <Col key={field.key} {...colSpan}>
                    <FormFields.DatePickerField
                        {...t('customerDetails:fields.drivingLicense.uaeResident.birthday', {
                            returnObjects: true,
                        })}
                        key={LocalCustomerFieldKey.Birthday}
                        defaultPickerValue={dayjs()}
                        disabled={disabled}
                        disabledDate={date => date.isAfter(dayjs())}
                        name={`${prefixName}.value`}
                        picker="date"
                        required={field.isRequired}
                        tooltip={{
                            title: t(`customerDetails:fields.drivingLicense.uaeResident.birthday.tooltip`),
                            icon: <InfoCircleOutlined style={{ color: '#000' }} />,
                        }}
                        {...insuranceKyc.birthdayProps}
                    />
                </Col>
            );
        }

        return (
            <Col key={field.key} {...colSpan}>
                <FormFields.BirthdayField
                    key={field.key}
                    disabled={disabled}
                    format={t('common:formats.datePicker')}
                    {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                    label={label}
                    name={`${prefixName}.value`}
                    required={field.isRequired}
                    tooltip={
                        extraSettings?.minimumAge > 0
                            ? {
                                  title: t(`customerDetails:fields.Birthday.tooltip`, {
                                      minAge: extraSettings.minimumAge,
                                  }),
                                  icon: <InfoCircleOutlined style={{ color: '#000' }} />,
                              }
                            : null
                    }
                />
            </Col>
        );
    }, [FormFields, colSpan, disabled, extraSettings, field, genericCustomer, insuranceKyc, label, prefixName, t]);

    if (disabled) {
        return (
            <DisabledFieldKey
                key={field.key}
                colSpan={colSpan}
                country={upperCase(company?.countryCode)}
                field={field}
                fromMyinfo={fromMyinfo}
                label={label}
                prefixName={prefixName}
                t={t}
            />
        );
    }

    const render = () => {
        switch (field.key) {
            case LocalCustomerFieldKey.Title:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={titleOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.NonBinaryTitle:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={nonBinaryTitleOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.IdentityNumber: {
                if (genericCustomer?.fields.Citizenship?.value === CitizenshipType.Others) {
                    return null;
                }

                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.IdentityNumberField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabled={disabled}
                            format={value => value.toUpperCase()}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.Salutation:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={salutationOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.SalutationBmw:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={salutationBMWOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.JobTitle:
                return (
                    <JobTitleField
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        label={label}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                    />
                );

            case LocalCustomerFieldKey.JobTitleTh:
                return (
                    <JobTitleThField
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        label={label}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                    />
                );

            case LocalCustomerFieldKey.EmploymentStatus:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={employmentStatus}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Citizenship:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={citizenshipOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.RelationshipWithApplicant:
                return (
                    <RelationshipWithApplicantField
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        label={label}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                    />
                );

            case LocalCustomerFieldKey.ResidentialStatusVwfs:
                return (
                    <ResidentialStatusVwfsField
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        label={label}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                    />
                );

            case LocalCustomerFieldKey.FullName:
            case LocalCustomerFieldKey.Email:
            case LocalCustomerFieldKey.PostalCode:
            case LocalCustomerFieldKey.Occupation:
            case LocalCustomerFieldKey.CompanyAddress:
            case LocalCustomerFieldKey.CompanyCity:
            case LocalCustomerFieldKey.CompanyDistrict:
            case LocalCustomerFieldKey.CorporateName:
            case LocalCustomerFieldKey.CorporateIdentityNumber:
            case LocalCustomerFieldKey.CorporateIndustryCategory:
            case LocalCustomerFieldKey.CompanyPhoneExtension:
            case LocalCustomerFieldKey.Comments:
            case LocalCustomerFieldKey.BusinessTitle:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.InputField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.LastNameFront:
            case LocalCustomerFieldKey.FirstName:
            case LocalCustomerFieldKey.LastName:
            case LocalCustomerFieldKey.LastNameJapan:
            case LocalCustomerFieldKey.FirstNameJapan:
            case LocalCustomerFieldKey.District:
            case LocalCustomerFieldKey.City:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.InputField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            maxLength={40}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Road:
            case LocalCustomerFieldKey.CompanyName:
            case LocalCustomerFieldKey.CompanyPhoneticName:
            case LocalCustomerFieldKey.Address:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.InputField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            maxLength={60}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.UnitNumber:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.InputField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            maxLength={10}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.DeliveryAddress:
                return (
                    <Col key={field.key} {...colSpan}>
                        <TooltipRenderer translationKey="deliveryAddress">
                            {tooltip => (
                                <FormFields.InputField
                                    key={field.key}
                                    disabled={disabled}
                                    label={label}
                                    name={`${prefixName}.value`}
                                    required={field.isRequired}
                                    tooltip={tooltip}
                                />
                            )}
                        </TooltipRenderer>
                    </Col>
                );

            case LocalCustomerFieldKey.CorrespondenceAddress:
            case LocalCustomerFieldKey.CorrespondenceCity:
            case LocalCustomerFieldKey.CorrespondenceDistrict:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.InputField
                            key={field.key}
                            disabled={values.prefill || disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.CorporateRegisteredCapital:
            case LocalCustomerFieldKey.CorporateAnnualRevenue:
            case LocalCustomerFieldKey.CorporateNumberOfEmployee:
            case LocalCustomerFieldKey.TimeOfEmployment:
            case LocalCustomerFieldKey.TimeOfAddress:
            case LocalCustomerFieldKey.MonthlyIncome:
            case LocalCustomerFieldKey.OtherIncome:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.InputNumberField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Passport: {
                if (genericCustomer?.fields.Citizenship?.value === CitizenshipType.Others) {
                    return (
                        <Col key={field.key} {...colSpan}>
                            <FormFields.InputField
                                key={field.key}
                                {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                                disabled={disabled}
                                name={`${prefixName}.value`}
                                required={field.isRequired}
                            />
                        </Col>
                    );
                }

                return null;
            }

            case LocalCustomerFieldKey.Birthday: {
                return birthdayField;
            }

            case LocalCustomerFieldKey.CorporateRegistrationDate: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.DatePickerField
                            key={field.key}
                            disabled={disabled}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            label={label}
                            name={`${prefixName}.value`}
                            picker="date"
                            required={field.isRequired}
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.CreationDate: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.DisplayField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            value={creationDate}
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.Nationality:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.NationalityField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Country:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.CountryField
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Region:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.RegionField
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            selectedCountry={selectedCountry}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Phone:
                if (extraSettings?.mobileVerification && enableMobileVerification) {
                    const verifyLabel = fromJourney
                        ? t('localCustomerModuleDetails:mobileVerification.verifyMobileNo')
                        : label;

                    return (
                        <Col key={field.key} {...colSpan}>
                            <FormFields.PhoneVerificationField
                                key={field.key}
                                companyId={company?.id}
                                disabled={disabled}
                                label={verifyLabel}
                                name={`${prefixName}.value`}
                                removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                                required={field.isRequired}
                            />
                        </Col>
                    );
                }

                // If mobile verification is disabled, use the regular PhoneAndPrefixField
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.PhoneAndPrefixField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.Telephone:
            case LocalCustomerFieldKey.CompanyPhone:
            case LocalCustomerFieldKey.CorporatePhone:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.PhoneAndPrefixField
                            key={field.key}
                            disabled={disabled}
                            label={label}
                            name={`${prefixName}.value`}
                            removeWhiteSpace={isWhitespaceRemovableField(field.key)}
                            required={field.isRequired}
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.Gender:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.GenderField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabled={disabled}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.NonBinaryGender:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.GenderField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabled={disabled}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            hasNonBinaryOptions
                            showSearch
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.MaritalStatus:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.MaritalStatusField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabled={disabled}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.ResidentialStatus:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.ResidentialStatusField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabled={disabled}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.Race:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.RaceField
                            key={field.key}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabled={disabled}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            case LocalCustomerFieldKey.DrivingLicense:
                return (
                    <DrivingLicenses
                        key={field.key}
                        colSpan={colSpan}
                        disabled={disabled}
                        fromJourney={fromJourney}
                        fromMyinfo={fromMyinfo}
                        isRequired={field.isRequired}
                        markMyinfo={markMyinfo}
                        prefixName={`${prefixName}.value`}
                    />
                );

            case LocalCustomerFieldKey.DrivingLicenseTh:
                return (
                    <DrivingLicenses
                        key={field.key}
                        colSpan={colSpan}
                        disabled={disabled}
                        fromMyinfo={fromMyinfo}
                        isRequired={field.isRequired}
                        markMyinfo={markMyinfo}
                        prefixName={`${prefixName}.value`}
                        showClass={false}
                    />
                );

            case LocalCustomerFieldKey.DrivingLicenseMy:
                return (
                    <DrivingLicenses
                        key={field.key}
                        colSpan={colSpan}
                        country="MY"
                        disabled={disabled}
                        fromMyinfo={fromMyinfo}
                        isRequired={field.isRequired}
                        markMyinfo={markMyinfo}
                        prefixName={`${prefixName}.value`}
                    />
                );

            case LocalCustomerFieldKey.UaeDrivingLicense:
                return (
                    <UAEDrivingLicenses
                        key={field.key}
                        colSpan={colSpan}
                        createdAt={createdAt}
                        disabled={disabled}
                        isDisplayUpload={fromJourney}
                        isRequired={field.isRequired}
                        prefixName={`${prefixName}.value`}
                        removeDocument={removeDocument}
                        uploadDocument={uploadDocument}
                    />
                );

            case LocalCustomerFieldKey.NoClaimDiscount:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={noClaimDiscountOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.DriverLicensePassDate:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.DatePickerField
                            key={field.key}
                            disabled={disabled}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            label={label}
                            name={`${prefixName}.value`}
                            picker="date"
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.DateOfJoining:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.DatePickerField
                            key={field.key}
                            disabled={disabled}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabledDate={date => dayjs(date).isAfter(dayjs(), 'day')}
                            label={label}
                            name={`${prefixName}.value`}
                            picker="date"
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.PreferredFirstPaymentDate:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.DatePickerField
                            key={field.key}
                            disabled={disabled}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            disabledDate={date =>
                                dayjs(date).isAfter(dayjs().add(45, 'days'), 'day') ||
                                dayjs(date).isBefore(dayjs(), 'day')
                            }
                            label={label}
                            name={`${prefixName}.value`}
                            picker="date"
                            required={field.isRequired}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.UploadDrivingLicense:
            case LocalCustomerFieldKey.UploadIdentity:
            case LocalCustomerFieldKey.UploadOtherDocument:
            case LocalCustomerFieldKey.UploadPassport:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.MultipleDraggerField
                            key={field.key}
                            disabled={disabled}
                            {...t(`customerDetails:fields.${field.key}`, { returnObjects: true })}
                            customRemove={removeDocument ? remove : null}
                            customUpload={uploadDocument ? upload : null}
                            extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                            label={label}
                            name={`${prefixName}.value`}
                            required={field.isRequired}
                            sizeLimitInMiB={20}
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.AddressType: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={addressTypeOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.IncomeType: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={incomeTypeOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.Education: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={educationOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.Emirate: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={emirateOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.ResidenceType: {
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={residenceTypeOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );
            }

            case LocalCustomerFieldKey.ReferenceDetailSet: {
                return (
                    <ReferenceDetailFieldset
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        fromMyinfo={fromMyinfo}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                    />
                );
            }

            case LocalCustomerFieldKey.SalaryTransferredBankSet: {
                return (
                    <SalaryTransferredBankFieldset
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        fromMyinfo={fromMyinfo}
                        hideUploadField={!fromJourney}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                        removeDocument={removeDocument}
                        uploadDocument={uploadDocument}
                    />
                );
            }

            case LocalCustomerFieldKey.UaeIdentitySet: {
                return (
                    <UAEIdentityFieldset
                        colSpan={colSpan}
                        disabled={disabled}
                        field={field}
                        fromMyinfo={fromMyinfo}
                        hideUploadField={!fromJourney}
                        markMyinfo={markMyinfo}
                        prefixName={prefixName}
                        removeDocument={removeDocument}
                        uploadDocument={uploadDocument}
                    />
                );
            }

            case LocalCustomerFieldKey.CurrentVehicleSource:
            case LocalCustomerFieldKey.CurrentVehicleOwnership:
            case LocalCustomerFieldKey.CurrentVehicleMake:
            case LocalCustomerFieldKey.CurrentVehicleModel:
            case LocalCustomerFieldKey.CurrentVehicleEquipmentLine:
            case LocalCustomerFieldKey.CurrentVehicleModelYear:
            case LocalCustomerFieldKey.CurrentVehicleMileage:
            case LocalCustomerFieldKey.CurrentVehiclePurchaseYear:
            case LocalCustomerFieldKey.CurrentVehicleEngineType:
            case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
            case LocalCustomerFieldKey.CurrentVehicleContractEnd:
            case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement:
            case LocalCustomerFieldKey.CurrentVehicleVin: {
                return null;
            }

            case LocalCustomerFieldKey.PurchaseIntention:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            name={`${prefixName}.value`}
                            options={purchaseIntentionOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            case LocalCustomerFieldKey.Hobby:
                return (
                    <Col key={field.key} {...colSpan}>
                        <FormFields.SelectField
                            key={field.key}
                            disabled={disabled}
                            filterOption={defaultFilterOption}
                            label={label}
                            listHeight={250}
                            mode="multiple"
                            name={`${prefixName}.value`}
                            options={capHobbyOptions}
                            required={field.isRequired}
                            showSearch
                        />
                    </Col>
                );

            default:
                throw new Error(`Failed to render KYC preset field: ${field.key}`);
        }
    };

    return <WatchFieldSource name={prefixName}>{render()}</WatchFieldSource>;
};

export type KycPresetFieldsRendererProps = {
    fields: KycFieldSpecsFragment[];
    colSpan: ColProps;
    prefix?: string;
    bankDisable?: boolean;
    createdAt?: string | Date;
    markMyinfo?: boolean;
    customerKind?: CustomerKind;
    isFieldDisabled?: (field: KycFieldSpecsFragment) => boolean;
    gutter: RowProps['gutter'];
    customerType: string;
    enableMobileVerification?: boolean;
} & UploadDocumentProp;

export const KycPresetFieldsRenderer = ({
    fields,
    isFieldDisabled: isFieldDisabledProps,
    gutter,
    uploadDocument,
    removeDocument,
    ...props
}: KycPresetFieldsRendererProps & { extraSettings: LocalCustomerManagementModule['extraSettings'] }) => {
    const [outerFields, uploadFields] = useMemo(() => {
        const uploadFieldKeys = [
            LocalCustomerFieldKey.UploadIdentity,
            LocalCustomerFieldKey.UploadPassport,
            LocalCustomerFieldKey.UploadDrivingLicense,
            LocalCustomerFieldKey.UploadOtherDocument,
        ];

        const shouldSeparateField = fields.filter(field => uploadFieldKeys.includes(field.key))?.length > 1;

        return [
            shouldSeparateField ? fields.filter(field => !uploadFieldKeys.includes(field.key)) : fields,
            shouldSeparateField ? fields.filter(field => uploadFieldKeys.includes(field.key)) : [],
        ];
    }, [fields]);

    const isFieldDisabled = useCallback(
        (field: KycFieldSpecsFragment): boolean => isFieldDisabledProps?.(field) ?? false,
        [isFieldDisabledProps]
    );

    return (
        <>
            <Col span={24}>
                <Row gutter={gutter}>
                    {outerFields.map(field => (
                        <KycPresetField
                            key={field.key}
                            {...props}
                            field={field}
                            isDisabled={isFieldDisabled(field)}
                            removeDocument={removeDocument}
                            uploadDocument={uploadDocument}
                            fromJourney
                        />
                    ))}
                </Row>
            </Col>
            {uploadFields.length > 0 && (
                <Col span={24}>
                    <Row gutter={gutter}>
                        {uploadFields.map(field => (
                            <KycPresetField
                                key={field.key}
                                {...props}
                                field={field}
                                isDisabled={isFieldDisabled(field)}
                                removeDocument={removeDocument}
                                uploadDocument={uploadDocument}
                                fromJourney
                            />
                        ))}
                    </Row>
                </Col>
            )}
        </>
    );
};
