import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { ApplicationStage as FrontendStage } from '../../../app/utilities/getApplicationFileName';
import {
    ApplicationKind,
    ApplicationStatus,
    Company,
    EventApplication,
    EventApplicationModule,
    PasswordConfiguration,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createCompanySMTPTransports,
    sendApplicationExportReady,
    sendApplicationExportPassword,
    sendApplicationExportFail,
} from '../../emails';
import { getBEApplicationStage } from '../../export/exportApplications';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import { getPassword } from '../../export/utils';
import createLoaders, { Loaders } from '../../loaders';
import { createPermissionController, ApplicationPolicyAction, PermissionController } from '../../permissions';
import createI18nInstance from '../../utils/createI18nInstance';
import getExcelApplicationRows from '../../utils/excel/applications';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

const BATCH_SIZE = 500;

// Convert string stage to frontend enum for getBEApplicationStage
const convertStringToFrontendStage = (stage: string): FrontendStage | null => {
    switch (stage) {
        case 'Financing':
            return FrontendStage.Financing;
        case 'Lead':
            return FrontendStage.Lead;
        case 'Reservation':
            return FrontendStage.Reservation;
        case 'Mobility':
            return FrontendStage.Mobility;
        case 'Appointment':
            return FrontendStage.Appointment;
        case 'Insurance':
            return FrontendStage.Insurance;
        case 'VisitAppointment':
            return FrontendStage.VisitAppointment;
        case 'TradeIn':
            return FrontendStage.TradeIn;
        default:
            return null;
    }
};

export type ProcessEventApplicationExportMessage = {
    userId: ObjectId;
    eventId: string;
    applicationIds: string[];
    dealerIds: string[];
    stage: string;
    nonce?: string;
    languageId?: string;
    filename?: string[];
};

type EventExportContext = {
    userId: ObjectId;
    eventId: ObjectId;
    event: any;
    eventModule: EventApplicationModule;
    company: Company;
    user: any;
    loaders: Loaders;
    permissions: PermissionController;
    applicationPermission: any;
    dealerIdsInRequest: ObjectId[];
    stage: any;
    collections: any;
    i18n: any;
};

type ExportFilePath = {
    filePath: string;
    password?: string;
};

// Initialize export context
const initializeEventApplicationExportContext = async (
    message: ProcessEventApplicationExportMessage
): Promise<EventExportContext> => {
    const { userId, eventId: inputEventId, dealerIds: queryDealerIds, stage: inputStage, languageId } = message;

    const { collections } = await getDatabaseContext();
    const eventId = new ObjectId(inputEventId);

    // Get event and validate
    const event = await collections.events.findOne({ _id: eventId });
    if (!event) {
        throw new Error('Event not found');
    }

    // Get event module
    const eventModule = (await collections.modules.findOne({
        _id: event.moduleId,
    })) as EventApplicationModule;

    if (!eventModule) {
        throw new Error('Event module not found');
    }

    // Get user and company
    const user = await collections.users.findOne({ _id: userId });
    if (!user) {
        throw new Error('User not found');
    }

    // Get company from event module
    const company = await collections.companies.findOne({ _id: eventModule.companyId });
    if (!company) {
        throw new Error('Company not found');
    }

    // Create loaders and permissions
    const loaders = createLoaders();
    const permissions = await createPermissionController(user);
    const applicationPermission = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View);

    // Get dealer IDs
    const dealerIdsInRequest = queryDealerIds?.map(dealerId => new ObjectId(dealerId)) ?? [];

    // Validate stage
    const frontendStage = convertStringToFrontendStage(inputStage);
    if (!frontendStage) {
        throw new Error('Invalid stage');
    }
    const stage = getBEApplicationStage(frontendStage);

    // Create i18n instance
    const i18n = await createI18nInstance(languageId || null);

    return {
        userId,
        eventId,
        event,
        eventModule,
        company,
        user,
        loaders,
        permissions,
        applicationPermission,
        dealerIdsInRequest,
        stage,
        collections,
        i18n,
    };
};

// Create batch iterator for applications
const createEventApplicationBatchIterator = function* (cursor: any, batchSize: number) {
    let batch: EventApplication[] = [];
    let hasNext = true;

    while (hasNext) {
        try {
            const doc = cursor.next();
            if (doc) {
                batch.push(doc);
                if (batch.length >= batchSize) {
                    yield batch;
                    batch = [];
                }
            } else {
                hasNext = false;
                if (batch.length > 0) {
                    yield batch;
                }
            }
        } catch (error) {
            hasNext = false;
            if (batch.length > 0) {
                yield batch;
            }
        }
    }
};

// Generate workbook with streaming
const generateEventApplicationWorkbookStreaming = async (
    cursor: any,
    context: EventExportContext,
    languageId?: string
) => {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const worksheet = workbook.sheet(0).name('Export Data');

    let headerAdded = false;
    let currentRow = 1;
    let totalProcessed = 0;

    const batchIterator = createEventApplicationBatchIterator(cursor, BATCH_SIZE);

    for (const batch of batchIterator) {
        if (batch.length === 0) {
            continue;
        }

        try {
            const rows = await processEventApplications(
                context.loaders,
                batch,
                [context.eventModule],
                context.company,
                context.stage,
                languageId
            );

            if (rows.length === 0) {
                continue;
            }

            // Add header if not added yet
            if (!headerAdded && rows.length > 0) {
                const headerRow = Object.keys(rows[0]);
                headerRow.forEach((header, index) => {
                    worksheet.cell(currentRow, index + 1).value(header);
                });
                setHeaderColumnsWidth(worksheet, headerRow);
                currentRow++;
                headerAdded = true;
            }

            // Add data rows
            rows.forEach(row => {
                const values = Object.values(row);
                values.forEach((value, index) => {
                    worksheet.cell(currentRow, index + 1).value(value);
                });
                currentRow++;
                totalProcessed++;
            });
        } catch (error) {
            console.error('Error processing batch:', error);
            throw new Error(`Error processing batch: ${error.message}`);
        }
    }

    console.log(`Processed ${totalProcessed} applications for event ${context.eventId}`);

    return workbook;
};

// Process event applications function
const processEventApplications = async (
    loaders: Loaders,
    applications: EventApplication[],
    modules: EventApplicationModule[],
    company: Company,
    stage: any,
    languageId?: string
) => {
    // No applications to process
    if (applications.length === 0) {
        return [];
    }

    try {
        return getExcelApplicationRows(
            applications,
            modules,
            {
                format: 'system',
                stage,
                currencyCode: company.currency,
                timeZone: company.timeZone,
                routerFirstLanguage: languageId || null,
            },
            loaders
        );
    } catch (error) {
        throw new Error(`Error processing event applications batch: ${error.message}`);
    }
};

// Generate export files
const generateEventApplicationExportFiles = async (
    context: EventExportContext,
    message: ProcessEventApplicationExportMessage
): Promise<ExportFilePath[]> => {
    const { collections, applicationPermission, eventId, dealerIdsInRequest } = context;
    const { applicationIds: applicationStringIds, filename: inputFilename, nonce: inputNonce } = message;

    const applicationIds = applicationStringIds.filter(id => ObjectId.isValid(id)).map(id => new ObjectId(id));

    let cursor: any;
    const filePaths: ExportFilePath[] = [];

    try {
        // Create aggregation cursor for applications
        cursor = collections.applications.find({
            $and: [
                applicationPermission,
                {
                    eventId,
                    isDraft: false,
                    status: { $ne: ApplicationStatus.Drafted },
                    dealerId: { $in: dealerIdsInRequest },
                    kind: ApplicationKind.Event,
                    _id: { $in: applicationIds },
                },
            ],
        });

        // Generate workbook
        const workbook = await generateEventApplicationWorkbookStreaming(cursor, context, message.languageId);

        // Determine password protection
        const passwordProtect = context.company.passwordConfiguration !== PasswordConfiguration.Off;
        const password = passwordProtect ? await getPassword(inputNonce || nanoid()) : null;

        // Generate filename
        const filename = inputFilename?.[0] || `event_applications_${dayjs().format('DD_MM_YYYY')}.xlsx`;
        const tempDir = os.tmpdir();
        const filePath = path.join(tempDir, `${nanoid()}_${filename}`);

        // Save workbook
        const buffer = await workbook.outputAsync({ ...(passwordProtect && { password }) });
        await fs.promises.writeFile(filePath, buffer);

        filePaths.push({
            filePath,
            password: password || undefined,
        });

        return filePaths;
    } catch (error) {
        console.error('Error generating event application export files:', error);
        throw error;
    } finally {
        if (cursor) {
            await cursor.close();
        }
    }
};

export const processEventApplicationExport = async (
    message: ProcessEventApplicationExportMessage,
    _job: Job<Document>
) => {
    let filePaths: ExportFilePath[] = [];

    try {
        // Initialize export context
        const context = await initializeEventApplicationExportContext(message);

        // Generate export files
        filePaths = await generateEventApplicationExportFiles(context, message);

        // Send emails
        const exportPassword = filePaths[0]?.password;
        const applicationType = 'Events';

        await sendEventApplicationExportEmails(context, filePaths, applicationType, exportPassword);
    } catch (error) {
        console.error('Unexpected error in processEventApplicationExport:', error);

        // Send failure notification
        await sendEventApplicationFailureNotification(message, error);

        throw error;
    } finally {
        // Clean up temporary files
        if (filePaths.length > 0) {
            for (const { filePath } of filePaths) {
                try {
                    await fs.promises.unlink(filePath);
                } catch (unlinkError) {
                    console.error('Error cleaning up file:', unlinkError);
                }
            }
        }
    }
};

// Send export emails
const sendEventApplicationExportEmails = async (
    context: EventExportContext,
    filePaths: ExportFilePath[],
    applicationType: string,
    exportPassword?: string
) => {
    const { company, user, i18n } = context;

    try {
        const emailContext = await getCompanyEmailContext(company);
        const transports = await createCompanySMTPTransports(company);
        const { t } = i18n;

        // Send export ready email
        await sendApplicationExportReady(
            {
                i18n,
                subject: t('emails:applicationExportReady.subject', {
                    companyName: company.displayName,
                    applicationType,
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                    applicationType,
                },
                to: { name: user.displayName, address: user.email },
                attachments: filePaths.map(fp => ({
                    filename: path.basename(fp.filePath),
                    path: fp.filePath,
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                })),
            },
            transports,
            emailContext.sender
        );

        // Send password email if needed
        if (exportPassword) {
            await sendApplicationExportPassword(
                {
                    i18n,
                    subject: t('emails:applicationExportPassword.subject', {
                        companyName: company.displayName,
                        applicationType,
                    }),
                    data: {
                        user,
                        password: exportPassword,
                        emailContext,
                        applicationType,
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transports,
                emailContext.sender
            );
        }
    } catch (error) {
        console.error('Error sending event application export emails:', error);
        throw error;
    }
};

// Send failure notification
const sendEventApplicationFailureNotification = async (message: ProcessEventApplicationExportMessage, _error: any) => {
    try {
        const { collections } = await getDatabaseContext();
        const user = await collections.users.findOne({ _id: message.userId });

        if (!user) {
            console.error('User not found for sending failure notification');

            return;
        }

        // Get event and company from event module
        const eventId = new ObjectId(message.eventId);
        const event = await collections.events.findOne({ _id: eventId });
        if (!event) {
            console.error('Event not found for sending failure notification');

            return;
        }

        const eventModule = await collections.modules.findOne({ _id: event.moduleId });
        if (!eventModule) {
            console.error('Event module not found for sending failure notification');

            return;
        }

        const company = await collections.companies.findOne({ _id: eventModule.companyId });
        if (!company) {
            console.error('Company not found for sending failure notification');

            return;
        }

        const { i18n } = await createI18nInstance(message.languageId || null);
        await i18n.loadNamespaces(['emails', 'common']);
        const { t } = i18n;

        const emailContext = await getCompanyEmailContext(company);
        const transports = await createCompanySMTPTransports(company);

        await sendApplicationExportFail(
            {
                i18n,
                subject: t('emails:applicationExportFail.subject', {
                    companyName: company.displayName,
                    applicationType: 'Events',
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                    applicationType: 'Events',
                },
                to: { name: user.displayName, address: user.email },
            },
            transports,
            emailContext.sender
        );
    } catch (emailError) {
        console.error('Error sending failure notification:', emailError);
    }
};
