import { Document } from 'bson';
import { Job } from 'bull';
import { Que<PERSON>Hand<PERSON> } from './QueueHandler';
import * as implementations from './implementations';

export type QueueMessage =
    | ({ type: 'resetPasswordNotification' } & implementations.ResetPasswordNotificationMessage)
    | ({ type: 'submitApplicationToBank' } & implementations.SubmitApplicationToBankMessage)
    | ({ type: 'workerBeat' } & implementations.WorkerBeatMessage)
    | ({ type: 'onUserAuthentication' } & implementations.OnUserAuthenticationMessage)
    | ({ type: 'generatePreviewOnCompanyAsset' } & implementations.GeneratePreviewOnCompanyAssetMessage)
    | ({ type: 'generatePreviewOnUserAsset' } & implementations.GeneratePreviewOnUserAssetMessage)
    | ({ type: 'generatePreviewOnBankAsset' } & implementations.GeneratePreviewOnBankAssetMessage)
    | ({ type: 'generatePreviewOnVariantAsset' } & implementations.GeneratePreviewOnVariantAssetMessage)
    | ({ type: 'upsertPermissions' } & implementations.UpsertPermissionsMessage)
    | ({ type: 'deleteUnusedPermissions' } & implementations.DeleteUnusedPermissionsMessage)
    | ({ type: 'sendUserActivationLink' } & implementations.SendUserActivationLinkMessage)
    | ({ type: 'onApplicationSubmitted' } & implementations.OnApplicationSubmittedMessage)
    | ({ type: 'generatePreviewOnApplicationDocument' } & implementations.GeneratePreviewOnApplicationDocumentMessage)
    | ({ type: 'generatePreviewOnLeadDocument' } & implementations.GeneratePreviewOnLeadDocumentMessage)
    | ({ type: 'generatePreviewOnGiftVoucherDocument' } & implementations.GeneratePreviewOnGiftVoucherDocumentMessage)
    | ({ type: 'onApplicationApproved' } & implementations.OnApplicationApprovedMessage)
    | ({ type: 'onInsuranceApplicationApproved' } & implementations.OnInsuranceApplicationApprovedMessage)
    | ({ type: 'onInsuranceApplicationDeclined' } & implementations.OnInsuranceApplicationDeclinedMessage)
    | ({ type: 'onInsuranceApplicationCancelled' } & implementations.OnInsuranceApplicationCancelledMessage)
    | ({ type: 'onApplicationCancelled' } & ({
          type: 'onApplicationCancelled';
      } & implementations.OnApplicationCancelledMessage))
    | ({ type: 'onApplicationDeclined' } & implementations.OnApplicationDeclinedMessage)
    | ({ type: 'onApplicationCompleted' } & implementations.OnApplicationCompletedMessage)
    | ({ type: 'onBankReviewInProgress' } & implementations.OnBankReviewInProgressMessage)
    | ({ type: 'onShareApplication' } & implementations.OnShareApplicationMessage)
    | ({ type: 'onComparisonShareApplication' } & implementations.OnComparisonShareApplicationMessage)
    | ({ type: 'sendOTPNotification' } & implementations.SendOTPNotificationMessage)
    | ({ type: 'onSigningInitialized' } & implementations.OnSigningInitializedMessage)
    | ({ type: 'onTestDriveSigningInitialized' } & implementations.OnTestDriveSigningInitializedMessage)
    | ({ type: 'onSigningInsuranceInitialized' } & implementations.OnSigningInsuranceInitializedMessage)
    | ({ type: 'generatePreviewOnDealerSocialMediaAsset' } & implementations.GeneratePreviewOnDealerSocialMediaAsset)
    | ({ type: 'generatePreviewOnEdmSocialMediaAsset' } & implementations.GeneratePreviewOnEdmSocialMediaAsset)
    | ({
          type: 'generatePreviewOnVariantConfiguratorPackageSectionImageAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorPackageSectionImageAsset)
    | ({
          type: 'generatePreviewOnVariantConfiguratorPackageAdditionalDetailsAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorPackageAdditionalDetailsAsset)
    | ({
          type: 'generatePreviewOnVariantConfiguratorOptionsAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorOptionsAsset)
    | ({
          type: 'generatePreviewOnVariantConfiguratorOptionSettingAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorOptionSettingAsset)
    | ({
          type: 'generatePreviewOnVariantConfiguratorMatrixAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorMatrixAsset)
    | ({ type: 'generatePreviewOnModuleAsset' } & implementations.GeneratePreviewOnModuleAssetMessage)
    | ({
          type: 'generatePreviewOnConfiguratorModuleAsset';
      } & implementations.GeneratePreviewOnConfiguratorModuleAssetMessage)
    | ({
          type: 'generatePreviewOnEventApplicationModuleAsset';
      } & implementations.GeneratePreviewOnEventApplicationModuleAssetMessage)
    | ({
          type: 'generatePreviewOnEventLevelAsset';
      } & implementations.GeneratePreviewOnEventLevelAssetMessage)
    | ({
          type: 'generatePreviewOnGiftVoucherModuleAsset';
      } & implementations.GeneratePreviewOnGiftVoucherModuleAssetMessage)
    | ({
          type: 'generatePreviewOnFinderApplicationModuleAsset';
      } & implementations.GeneratePreviewOnFinderApplicationModuleAssetMessage)
    | ({
          type: 'saveOrderConfiguratorApplication';
      } & implementations.ConfiguratorApplicationExternalLink)
    | ({
          type: 'verifyEmailUpdateNotification';
      } & implementations.VerifyEmailUpdateNotificationMessage)
    | ({
          type: 'generatePreviewOnVariantConfiguratorColorSettingAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorColorSettingAsset)
    | ({
          type: 'generatePreviewOnVariantConfiguratorTrimSettingAsset';
      } & implementations.GeneratePreviewOnVariantConfiguratorTrimSettingAsset)
    | ({
          type: 'syncConfiguratorInventory';
      } & implementations.SyncConfiguratorInventoryMessage)
    | ({
          type: 'sendApplicationSubmissionMail';
      } & implementations.SendApplicationSubmissionMail)
    | ({
          type: 'sendProceedWithCustomerEmail';
      } & implementations.SendProceedWithCustomerEmail)
    | ({
          type: 'sendExportApplicationPasswordEmail';
      } & implementations.SendExportApplicationPasswordEmail)
    | ({
          type: 'processApplicationExport';
      } & implementations.ProcessApplicationExportMessage)
    | ({
          type: 'processCustomerExport';
      } & implementations.ProcessCustomerExportMessage)
    | ({
          type: 'processLeadExport';
      } & implementations.ProcessLeadExportMessage)
    | ({
          type: 'processEventLeadExport';
      } & implementations.ProcessEventLeadExportMessage)
    | ({
          type: 'processEventApplicationExport';
      } & implementations.ProcessEventApplicationExportMessage)
    | ({
          type: 'onRequestReleaseLetter';
      } & implementations.onRequestReleaseLetterMessage)
    | ({
          type: 'onRequestDisbursement';
      } & implementations.onRequestDisbursementMessage)
    | ({
          type: 'generatePreviewOnBannerAsset';
      } & implementations.GeneratePreviewOnBannerAssetMessage)
    | ({ type: 'generatePreviewOnWebpageAsset' } & implementations.GeneratePreviewOnWebpageImageMessage)
    | ({ type: 'onPendingDisbursement' } & implementations.OnPendingDisbursementMessage)
    | ({ type: 'onPendingInfoFromCustomer' } & implementations.OnPendingInfoFromCustomerMessage)
    | ({ type: 'onApplicationExpired' } & implementations.OnApplicationExpiredMessage)
    | ({
          type: 'generatePreviewOnStockAsset';
      } & implementations.GeneratePreviewOnStockAssetMessage)
    | ({ type: 'generatePreviewOnMobilityModuleAsset' } & implementations.GeneratePreviewOnMobilityModuleAssetMessage)
    | ({
          type: 'generatePreviewOnAppointmentModuleAsset';
      } & implementations.GeneratePreviewOnAppointmentModuleAssetMessage)
    | ({
          type: 'generatePreviewOnVisitAppointmentModuleAsset';
      } & implementations.GeneratePreviewOnVisitAppointmentModuleAssetMessage)
    | ({ type: 'sendMobilityEmail' } & implementations.SendMobilityEmailHandlerMessage)
    | ({ type: 'sendOperatorLocationUpdate' } & implementations.SendOperatorLocationUpdateMessage)
    | ({ type: 'onExternalDocumentUploaded' } & implementations.OnExternalDocumentUploadedMessage)
    | ({ type: 'syncFinderVehicleService' } & implementations.SyncFinderVehicleServiceMessage)
    | ({ type: 'syncPorscheMasterDataService' } & implementations.SyncPorscheMasterDataServiceMessage)
    | ({ type: 'submitApplicationToInsurer' } & implementations.SubmitApplicationToInsurerMessage)
    | ({ type: 'sendFinderReminder' } & implementations.SendFinderReminderHandlerMessage)
    | ({
          type: 'generatePreviewOnWebsiteSocialMediaAsset';
      } & implementations.GeneratePreviewOnWebsiteSocialMediaAsset)
    | ({ type: 'purgeApplicationData' } & implementations.PurgeApplicationDataMessage)
    | ({ type: 'sendAssigneeReassignmentEmail' } & implementations.SendAssigneeReassignmentEmailMessage)
    | ({ type: 'sendAppointmentEndedTestDrive' } & implementations.SendAppointmentEndedTestDriveHandlerMessage)
    | ({ type: 'sendAppointmentSubmitConfirmation' } & implementations.SendAppointmentSubmitConfirmationHandlerMessage)
    | ({ type: 'sendAppointmentBookingAmendment' } & implementations.SendAppointmentBookingAmendmentHandlerMessage)
    | ({ type: 'sendAppointmentEndTestDriveReminder' } & implementations.SendEndTestDriveReminderHandlerMessage)
    | ({
          type: 'sendAppointmentBookingConfirmation';
      } & implementations.SendAppointmentBookingConfirmationHandlerMessage)
    | ({
          type: 'sendVisitAppointmentSubmitConfirmation';
      } & implementations.SendVisitAppointmentSubmitConfirmationHandlerMessage)
    | ({
          type: 'sendVisitAppointmentBookingAmendment';
      } & implementations.SendVisitAppointmentBookingAmendmentHandlerMessage)
    | ({
          type: 'sendVisitAppointmentBookingConfirmation';
      } & implementations.SendVisitAppointmentBookingConfirmationHandlerMessage)
    | ({
          type: 'sendVisitAppointmentBookingComplete';
      } & implementations.SendVisitAppointmentBookingCompleteHandlerMessage)
    | ({ type: 'sendAppointmentFinderReserved' } & implementations.SendAppointmentFinderReservedHandlerMessage)
    | ({ type: 'sendGiftVoucherEmail' } & implementations.SendGiftVoucherHandlerMessage)
    | ({ type: 'updateCustomerManagementModuleOrder' } & implementations.UpdateCustomerManagementModuleOrderMessage)
    | ({ type: 'onApplicationAssigneeUpdated' } & implementations.OnApplicationAssigneeUpdatedMessage)
    | ({ type: 'sendEmptyVariantImageReminderEmail' } & implementations.SendEmptyVariantImageReminderMessage)
    | ({ type: 'sendPorscheMasterDataAutoSyncUpdateEmail' } & implementations.SendPorscheMasterDataAutoSyncMessage)
    | ({ type: 'sendSalespersonSubmissionToBankFailed' } & implementations.SendSalespersonSubmissionToBankFailedEmail)
    | ({
          type: 'sendSalespersonSubmissionToInsurerFailed';
      } & implementations.SendSalespersonSubmissionToInsurerFailedEmail)
    | ({ type: 'onCapSubmitted' } & implementations.OnCapSubmittedMessage)
    | ({ type: 'onLeadAssigneeUpdated' } & implementations.OnLeadAssigneeUpdatedMessage)
    | ({ type: 'generateTradeInPending' } & implementations.GenerateTradeInPendingMessage)
    | ({
          type: 'sendTradeInVehicleChangedToSalesManager';
      } & implementations.SendTradeInVehicleChangedToSalesManagerMessage)
    | ({ type: 'sendTradeInQuotedEmailToAssignee' } & implementations.SendTradeInQuotedEmailToAssigneeMessage)
    | ({ type: 'updateTestDriveActivityStatus' } & implementations.UpdateTestDriveActivityStatusMessage)
    | ({
          type: 'updateVisitAppointmentBookingActivityStatus';
      } & implementations.UpdateVisitAppointmentBookingActivityStatusMessage)
    | ({
          type: 'onSendSalesOfferEmail';
      } & implementations.OnSendSalesOfferEmailMessage)
    | ({
          type: 'onGenerateSalesOfferPdf';
      } & implementations.OnGenerateSalesOfferPdfMessage);
const mainQueueHandler = (message: QueueMessage, job: Job<Document>) => {
    switch (message.type) {
        case 'resetPasswordNotification':
            return implementations.resetPasswordNotificationHandler(message, job);

        case 'submitApplicationToBank':
            return implementations.submitApplicationToBank(message, job);

        case 'workerBeat':
            return implementations.workerBeatHandler(message, job);

        case 'onUserAuthentication':
            return implementations.onUserAuthenticationHandler(message, job);

        case 'generatePreviewOnCompanyAsset':
            return implementations.generatePreviewOnCompanyAsset(message, job);

        case 'generatePreviewOnUserAsset':
            return implementations.generatePreviewOnUserAsset(message, job);

        case 'generatePreviewOnBankAsset':
            return implementations.generatePreviewOnBankAsset(message, job);

        case 'upsertPermissions':
            return implementations.upsertPermissionsHandler(message, job);

        case 'deleteUnusedPermissions':
            return implementations.deleteUnusedPermissionsHandler(message, job);

        case 'generatePreviewOnVariantAsset':
            return implementations.generatePreviewOnVariantAsset(message, job);

        case 'sendUserActivationLink':
            return implementations.sendUserActivationLink(message, job);

        case 'onApplicationSubmitted':
            return implementations.onApplicationSubmittedHandler(message, job);

        case 'generatePreviewOnApplicationDocument':
            return implementations.generatePreviewOnApplicationDocument(message, job);

        case 'generatePreviewOnLeadDocument':
            return implementations.generatePreviewOnLeadDocument(message, job);

        case 'generatePreviewOnGiftVoucherDocument':
            return implementations.generatePreviewOnGiftVoucherDocument(message, job);

        case 'onApplicationApproved':
            return implementations.onApplicationApproved(message, job);

        case 'onInsuranceApplicationApproved':
            return implementations.onInsuranceApplicationApproved(message, job);

        case 'onInsuranceApplicationDeclined':
            return implementations.onInsuranceApplicationDeclined(message, job);

        case 'onInsuranceApplicationCancelled':
            return implementations.onInsuranceApplicationCancelled(message, job);

        case 'onApplicationCancelled':
            return implementations.onApplicationCancelled(message, job);

        case 'onApplicationDeclined':
            return implementations.onApplicationDeclined(message, job);

        case 'onApplicationCompleted':
            return implementations.onApplicationCompleted(message, job);

        case 'onBankReviewInProgress':
            return implementations.onBankReviewInProgress(message, job);

        case 'onShareApplication':
            return implementations.onShareApplicationHandler(message, job);

        case 'onComparisonShareApplication':
            return implementations.onComparisonShareApplicationHandler(message, job);

        case 'generatePreviewOnDealerSocialMediaAsset':
            return implementations.generatePreviewOnDealerSocialMediaAsset(message, job);

        case 'generatePreviewOnEdmSocialMediaAsset':
            return implementations.generatePreviewOnEdmSocialMediaAsset(message, job);

        case 'sendOTPNotification':
            return implementations.sendOTPNotificationHandler(message, job);

        case 'onSigningInitialized':
            return implementations.onSigningInitializedHandler(message, job);

        case 'onSigningInsuranceInitialized':
            return implementations.onSigningInsuranceInitialized(message, job);

        case 'generatePreviewOnVariantConfiguratorColorSettingAsset':
            return implementations.generatePreviewOnVariantConfiguratorColorSettingAsset(message, job);

        case 'generatePreviewOnVariantConfiguratorTrimSettingAsset':
            return implementations.generatePreviewOnVariantConfiguratorTrimSettingAsset(message, job);

        case 'generatePreviewOnVariantConfiguratorPackageSectionImageAsset':
            return implementations.generatePreviewOnVariantConfiguratorPackageSectionImageAsset(message, job);

        case 'generatePreviewOnVariantConfiguratorPackageAdditionalDetailsAsset':
            return implementations.generatePreviewOnVariantConfiguratorPackageAdditionalDetailsAsset(message, job);

        case 'generatePreviewOnVariantConfiguratorOptionsAsset':
            return implementations.generatePreviewOnVariantConfiguratorOptionsAsset(message, job);

        case 'generatePreviewOnVariantConfiguratorOptionSettingAsset':
            return implementations.generatePreviewOnVariantConfiguratorOptionSettingAsset(message, job);

        case 'generatePreviewOnVariantConfiguratorMatrixAsset':
            return implementations.generatePreviewOnVariantConfiguratorMatrixAsset(message, job);

        case 'generatePreviewOnModuleAsset':
            return implementations.generatePreviewOnModuleAsset(message, job);

        case 'generatePreviewOnConfiguratorModuleAsset':
            return implementations.generatePreviewOnConfiguratorModuleAsset(message, job);

        case 'generatePreviewOnEventApplicationModuleAsset':
            return implementations.generatePreviewOnEventApplicationModuleAsset(message, job);

        case 'generatePreviewOnEventLevelAsset':
            return implementations.generatePreviewOnEventLevelAsset(message, job);

        case 'generatePreviewOnFinderApplicationModuleAsset':
            return implementations.generatePreviewOnFinderApplicationModuleAsset(message, job);

        case 'generatePreviewOnGiftVoucherModuleAsset':
            return implementations.generatePreviewOnGiftVoucherModuleAsset(message, job);

        case 'saveOrderConfiguratorApplication':
            return implementations.saveOrderConfiguratorApplication(message, job);

        case 'verifyEmailUpdateNotification':
            return implementations.verifyEmailUpdateNotificationHandler(message, job);

        case 'sendApplicationSubmissionMail':
            return implementations.sendApplicationSubmissionMail(message, job);

        case 'syncConfiguratorInventory':
            return implementations.syncConfiguratorInventory(message, job);

        case 'sendProceedWithCustomerEmail':
            return implementations.sendProceedWithCustomerEmail(message, job);

        case 'sendExportApplicationPasswordEmail':
            return implementations.sendExportApplicationPasswordEmail(message, job);

        case 'processApplicationExport':
            return implementations.processApplicationExport(message, job);

        case 'processCustomerExport':
            return implementations.processCustomerExport(message, job);

        case 'processLeadExport':
            return implementations.processLeadExport(message, job);

        case 'processEventLeadExport':
            return implementations.processEventLeadExport(message, job);

        case 'processEventApplicationExport':
            return implementations.processEventApplicationExport(message, job);

        case 'onRequestReleaseLetter':
            return implementations.onRequestReleaseLetterHandler(message, job);

        case 'onRequestDisbursement':
            return implementations.onRequestDisbursementHandler(message, job);

        case 'generatePreviewOnBannerAsset':
            return implementations.generatePreviewOnBannerAsset(message, job);

        case 'generatePreviewOnWebpageAsset':
            return implementations.generatePreviewOneWebpageImage(message, job);

        case 'onPendingDisbursement':
            return implementations.onPendingDisbursement(message, job);

        case 'onPendingInfoFromCustomer':
            return implementations.onPendingInfoFromCustomer(message, job);

        case 'onApplicationExpired':
            return implementations.onApplicationExpired(message, job);

        case 'generatePreviewOnMobilityModuleAsset':
            return implementations.generatePreviewOnMobilityModuleAsset(message, job);

        case 'generatePreviewOnStockAsset':
            return implementations.generatePreviewOnStockAsset(message, job);

        case 'sendMobilityEmail':
            return implementations.sendMobilityEmailHandler(message, job);

        case 'sendOperatorLocationUpdate':
            return implementations.sendOperatorLocationUpdate(message, job);

        case 'onExternalDocumentUploaded':
            return implementations.onExternalDocumentUploaded(message, job);

        case 'syncFinderVehicleService':
            return implementations.syncFinderVehicleService(message, job);

        case 'syncPorscheMasterDataService':
            return implementations.syncPorscheMasterDataService(message, job);

        case 'submitApplicationToInsurer':
            return implementations.submitApplicationToInsurer(message, job);

        case 'sendFinderReminder':
            return implementations.sendFinderReminderHandler(message, job);

        case 'generatePreviewOnWebsiteSocialMediaAsset':
            return implementations.generatePreviewOnWebsiteSocialMediaAsset(message, job);

        case 'generatePreviewOnAppointmentModuleAsset':
            return implementations.generatePreviewOnAppointmentModuleAsset(message, job);

        case 'generatePreviewOnVisitAppointmentModuleAsset':
            return implementations.generatePreviewOnVisitAppointmentModuleAsset(message, job);

        case 'purgeApplicationData':
            return implementations.purgeApplicationData(message, job);

        case 'sendAssigneeReassignmentEmail':
            return implementations.sendAssigneeReassignmentEmailHandler(message, job);

        case 'onTestDriveSigningInitialized':
            return implementations.onTestDriveSigningInitializedHandler(message, job);

        case 'sendAppointmentEndedTestDrive':
            return implementations.sendAppointmentEndedTestDriveHandler(message, job);

        case 'sendAppointmentSubmitConfirmation':
            return implementations.sendAppointmentSubmitConfirmationHandler(message, job);

        case 'sendAppointmentBookingAmendment':
            return implementations.sendAppointmentBookingAmendmentHandler(message, job);

        case 'sendAppointmentBookingConfirmation':
            return implementations.sendAppointmentBookingConfirmationHandler(message, job);

        case 'sendAppointmentFinderReserved':
            return implementations.sendAppointmentFinderReservedHandler(message, job);

        case 'sendAppointmentEndTestDriveReminder':
            return implementations.sendAppointmentEndTestDriveReminderHandler(message, job);

        case 'sendVisitAppointmentSubmitConfirmation':
            return implementations.sendVisitAppointmentSubmitConfirmationHandler(message, job);

        case 'sendVisitAppointmentBookingAmendment':
            return implementations.sendVisitAppointmentBookingAmendmentHandler(message, job);

        case 'sendVisitAppointmentBookingConfirmation':
            return implementations.sendVisitAppointmentBookingConfirmationHandler(message, job);

        case 'sendVisitAppointmentBookingComplete':
            return implementations.sendVisitAppointmentBookingCompleteHandler(message, job);

        case 'sendGiftVoucherEmail':
            return implementations.sendGiftVoucherEmailHandler(message, job);

        case 'updateCustomerManagementModuleOrder':
            return implementations.updateCustomerManagementModuleOrder(message, job);

        case 'onApplicationAssigneeUpdated':
            return implementations.onApplicationAssigneeUpdated(message, job);

        case 'onLeadAssigneeUpdated':
            return implementations.onLeadAssigneeUpdated(message, job);

        case 'sendEmptyVariantImageReminderEmail':
            return implementations.sendEmptyVariantImageReminder(message, job);

        case 'sendSalespersonSubmissionToBankFailed':
            return implementations.sendSalespersonSubmissionToBankFailedEmailed(message, job);

        case 'sendSalespersonSubmissionToInsurerFailed':
            return implementations.sendSalespersonSubmissionToInsurerFailedEmailed(message, job);

        case 'sendPorscheMasterDataAutoSyncUpdateEmail':
            return implementations.sendPorscheMasterDataAutoSyncUpdate(message, job);

        case 'onCapSubmitted':
            return implementations.onCapSubmittedHandler(message, job);

        case 'generateTradeInPending':
            return implementations.generateTradeInPendingHandler(message, job);

        case 'sendTradeInVehicleChangedToSalesManager':
            return implementations.SendTradeInVehicleChangedToSalesManagerHandler(message, job);

        case 'sendTradeInQuotedEmailToAssignee':
            return implementations.SendTradeInQuotedEmailToAssigneeHandler(message, job);

        case 'updateTestDriveActivityStatus':
            return implementations.updateTestDriveActivityStatus(message, job);

        case 'updateVisitAppointmentBookingActivityStatus':
            return implementations.updateVisitAppointmentBookingActivityStatus(message, job);

        case 'onGenerateSalesOfferPdf':
            return implementations.onGenerateSalesOfferPdf(message, job);

        case 'onSendSalesOfferEmail':
            return implementations.onSendSalesOfferEmail(message, job);

        default:
            // @ts-ignore
            throw new Error(`Message of type "${message.type}" is unknown to the worker`);
    }
};

export const mainQueue = new QueueHandler('main', mainQueueHandler, {
    getLabel: message => `main.${message.type}`,
});
