import { Router } from 'express';
import exportApplicationDocuments from '../export/exportApplicationDocuments';
import exportApplications from '../export/exportApplications';
import exportConfiguratorApplications from '../export/exportConfiguratorApplications';
import exportConfiguratorLeads from '../export/exportConfiguratorLeads';
import exportConfiguratorVariantPrices from '../export/exportConfiguratorVariantPrices';
import exportCustomerRecords from '../export/exportCustomerRecords';
import exportEventApplications from '../export/exportEventApplications';
import exportEventLeads from '../export/exportEventLeads';
import exportFinderApplications from '../export/exportFinderApplications';
import exportFinderLeads from '../export/exportFinderLeads';
import exportGiftVoucherRecords from '../export/exportGiftVoucherRecords';
import exportInventories from '../export/exportInventories';
import exportLanguagePack from '../export/exportLanguagePack';
import exportLeads from '../export/exportLeads';
import exportLtaTemplate from '../export/exportLtaTemplate';
import exportTradeIns from '../export/exportTradeIns';
import exportVehicles from '../export/exportVehicles';
import exportWebsiteLocation from '../export/exportWebsiteLocation';
import exportFinanceProduct from '../export/financeProduct';
import exportMarketingDashboard from '../export/marketingDashboard/exportMarketingDashboard';
import streamExportApplications from '../export/streamExportApplications';
import streamExportCustomerRecords from '../export/streamExportCustomerRecords';
import streamExportEventApplications from '../export/streamExportEventApplications';
import streamExportEventLeads from '../export/streamExportEventLeads';
import streamExportLeads from '../export/streamExportLeads';
import captureRequestHandlerErrors from '../utils/captureRequestHandlerErrors';

const createExportRouter = () => {
    const router = Router();

    router.post('/eventApplications/stream/:eventId', captureRequestHandlerErrors(streamExportEventApplications));
    router.post('/eventApplications/:eventId', captureRequestHandlerErrors(exportEventApplications));
    router.post('/eventLeads/stream/:eventId', captureRequestHandlerErrors(streamExportEventLeads));
    router.post('/eventLeads/:eventId', captureRequestHandlerErrors(exportEventLeads));
    router.post(
        '/configuratorApplications/:configuratorId',
        captureRequestHandlerErrors(exportConfiguratorApplications)
    );
    router.post('/configuratorLeads/:configuratorId', captureRequestHandlerErrors(exportConfiguratorLeads));
    router.post('/finderApplications/', captureRequestHandlerErrors(exportFinderApplications));
    router.post('/finderLeads/', captureRequestHandlerErrors(exportFinderLeads));
    router.post('/applications', captureRequestHandlerErrors(exportApplications));
    router.post('/applications/stream', captureRequestHandlerErrors(streamExportApplications));

    router.post('/leads', captureRequestHandlerErrors(exportLeads));
    router.post('/leads/stream', captureRequestHandlerErrors(streamExportLeads));
    router.get('/vehicles', captureRequestHandlerErrors(exportVehicles));
    router.get('/lta/template', captureRequestHandlerErrors(exportLtaTemplate));

    router.get('/inventories/:dealerId', captureRequestHandlerErrors(exportInventories));
    router.get('/application/documents/:applicationId', captureRequestHandlerErrors(exportApplicationDocuments));
    router.get(
        '/configuratorVariantPrices/:modelConfiguratorId',
        captureRequestHandlerErrors(exportConfiguratorVariantPrices)
    );

    router.get('/languagePack/:languagePackId', captureRequestHandlerErrors(exportLanguagePack));
    router.post('/customerRecords', captureRequestHandlerErrors(exportCustomerRecords));
    router.post('/customerRecords/stream', captureRequestHandlerErrors(streamExportCustomerRecords));
    router.post('/giftVoucherRecords', captureRequestHandlerErrors(exportGiftVoucherRecords));

    router.post('/tradeIns', captureRequestHandlerErrors(exportTradeIns));
    router.post('/fpTable', captureRequestHandlerErrors(exportFinanceProduct));
    router.get('/websiteLocation', captureRequestHandlerErrors(exportWebsiteLocation));

    router.post('/marketingDashboard', captureRequestHandlerErrors(exportMarketingDashboard));

    return router;
};

export default createExportRouter;
