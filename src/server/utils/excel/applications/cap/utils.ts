import dayjs from 'dayjs';
import { keyBy } from 'lodash/fp';
import { ApplicationJourney, ApplicationKind, EventApplication } from '../../../../database/documents/Applications';
import { ConsentsAndDeclarations, DataField } from '../../../../database/documents/ConsentsAndDeclarations';
import { Event } from '../../../../database/documents/Event';
import { LocalModel, Vehicle, VehicleKind } from '../../../../database/documents/Vehicle';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers';
import countryCodes from '../../../../datasets/countryCode';
import regionCodes from '../../../../datasets/regionCode';
import { consentAgreedByAgreeingDataProcessingCountries } from '../../../../integrations/cap/utils/getCountryCode';
import { getLeadMediumCode, getLeadOriginCode } from '../../../../integrations/cap/utils/getLeadDetailFromApplication';
import { getInterestByMatchingName } from '../../../../integrations/cap/utils/getVehicleDetails';
import { Loaders } from '../../../../loaders';
import { retrieveUTMParameterQueryParameter } from '../../../../schema/resolvers/mutations/events/updateEvent';
import ensureManyFromLoaders from '../../../ensureManyFromLoaders';
import { uniqueObjectIds } from '../../../fp';
import {
    customerByIdFromApplications,
    dealerByIdFromApplications,
    journeyByApplicationSuiteIdFromApplications,
    userByIdFromApplications,
    vehicleByIdFromApplications,
} from '../system/utils';
import { AgreementSupportedModule, CapSupportedApplication, CapSupportedModule, FormatCapPurpose } from './types';

const consentListFromModules = async (modules: AgreementSupportedModule[]) => {
    const { collections } = await getDatabaseContext();

    return collections.consentsAndDeclarations
        .find({ moduleId: { $in: modules.map(module => module.agreementsModuleId) } })
        .toArray();
};

const eventByIdFromApplications = async (applications: CapSupportedApplication[], loaders: Loaders) => {
    const eventIds = uniqueObjectIds(
        applications
            .filter(application => application.kind === ApplicationKind.Event)
            .map((application: EventApplication) => application.eventId)
            .filter(Boolean)
    );

    const events = await loaders.eventById.loadMany(eventIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), events);
};

const leadByIdFromApplications = async (applications: CapSupportedApplication[], loaders: Loaders) => {
    const leadIds = uniqueObjectIds(applications.map(application => application.leadId).filter(Boolean));

    if (!leadIds.length) {
        return {};
    }

    const leads = await loaders.leadById.loadMany(leadIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), leads);
};

export const getCapSupportingData = async (
    applications: CapSupportedApplication[],
    modules: CapSupportedModule[],
    loaders: Loaders
) => {
    const [
        { customerById, customerByLatestSuiteId },
        assigneeById,
        dealerById,
        vehicleById,
        journey,
        consentList,
        eventById,
        leadById,
    ] = await Promise.all([
        customerByIdFromApplications(applications, loaders),
        userByIdFromApplications(applications, loaders),
        dealerByIdFromApplications(applications, loaders),
        vehicleByIdFromApplications(applications, loaders),
        journeyByApplicationSuiteIdFromApplications(applications, loaders),
        consentListFromModules(modules),
        eventByIdFromApplications(applications, loaders),
        leadByIdFromApplications(applications, loaders),
    ]);

    return {
        customerById,
        customerByLatestSuiteId,
        assigneeById,
        dealerById,
        vehicleById,
        journeyByApplicationSuiteId: journey.journeyByApplicationSuiteId,
        consentList,
        eventById,
        leadById,
    };
};

export const getCapSexCode = (titleOrGender: string) => {
    switch (titleOrGender) {
        case 'MR':
        case 'MALE':
            return '2';

        case 'MS':
        case 'FEMALE':
            return '1';

        case 'MX':
        case 'NOTSPECIFIED':
            return '';

        default:
            return '';
    }
};

export const getCapPrimaryInterest = (model?: LocalModel, vehicle?: Vehicle): string => {
    if (!vehicle) {
        return '';
    }

    if (vehicle._kind === VehicleKind.FinderVehicle) {
        const vehicleName = vehicle.listing?.vehicle?.name?.localize;

        return vehicleName ? getInterestByMatchingName(vehicleName) : '';
    }

    if (vehicle._kind === VehicleKind.LocalVariant) {
        const vehicleName = vehicle.name?.defaultValue;

        return vehicleName ? getInterestByMatchingName(vehicleName) : '';
    }

    return '';
};

export const getCapConsentStatus = (
    consentsAndDeclarations: ConsentsAndDeclarations[],
    applicationJourney: ApplicationJourney,
    purpose: FormatCapPurpose,
    countryCode: string
) => {
    const { applicantAgreements } = applicationJourney || {};
    const { agreements } = applicantAgreements || {};

    const generateString = (agreed: boolean) => (agreed ? 'X' : '');

    const isDataProcessing = agreements?.some(agreement => {
        const { consentId } = agreement;
        const consentDocument = consentsAndDeclarations.find(consent => consent._id.equals(consentId));

        return consentDocument.dataField === DataField.DataProcessing;
    });

    if (isDataProcessing && consentAgreedByAgreeingDataProcessingCountries.includes(countryCode)) {
        return [
            generateString(true),
            generateString(purpose === FormatCapPurpose.BP),
            generateString(purpose === FormatCapPurpose.BP),
            generateString(purpose === FormatCapPurpose.BP),
            generateString(purpose === FormatCapPurpose.BP),
        ];
    }

    const marketingConsent = agreements?.find(agreement => agreement.platformsAgreed);

    if (marketingConsent) {
        const isEmailAgreed = marketingConsent.platformsAgreed.email;
        const isFaxAgreed = marketingConsent.platformsAgreed.fax;
        const isPhoneAgreed = marketingConsent.platformsAgreed.phone;
        const isMailAgreed = marketingConsent.platformsAgreed.mail;
        // const isSmsAgreed = marketingConsent.platformsAgreed.sms;

        return [
            generateString(isDataProcessing),
            generateString(isMailAgreed),
            generateString(isPhoneAgreed),
            generateString(isEmailAgreed),
            generateString(isFaxAgreed),
        ];
    }

    return [generateString(isDataProcessing), '', '', '', ''];
};

export const getCapPurchaseMonthAndYear = (specified: Date, countryCode: string) => {
    const date = dayjs(specified).add(countryCode === 'JP' ? 3 : 1, 'months');

    return [dayjs(date).format('MM'), dayjs(date).format('YYYY')];
};

export const getCountryCode = (country: string) => countryCodes.find(countryCode => countryCode.name === country)?.code;

export const getCountryCodeWithFallback = (country: string, companyCountryCode?: string): string =>
    getCountryCode(country) ?? companyCountryCode?.toUpperCase();

export const getRegionCode = (region: string) => regionCodes.find(regionCode => regionCode.city === region)?.region;

export const getVehicleVariantID = (vehicle: Vehicle) => {
    if (!vehicle) {
        return '';
    }

    switch (vehicle._kind) {
        case VehicleKind.LocalVariant:
            return vehicle.identifier;

        case VehicleKind.FinderVehicle:
            return vehicle.listing.vehicle.orderTypeCode;

        default:
            return '';
    }
};

export const getVehicleCondition = (vehicle: Vehicle) => {
    if (!vehicle) {
        return { INTEREST_NV: '', INTEREST_CPO: '', VIN: '', INV_ID: '' };
    }

    switch (vehicle._kind) {
        case VehicleKind.FinderVehicle: {
            const vehicleInfo = {
                VIN: vehicle.listing.vehicle.vin,
                INV_ID: vehicle.listing.id,
            };

            if (vehicle.listing.vehicle.condition.value === 'preowned') {
                return { ...vehicleInfo, INTEREST_NV: '', INTEREST_CPO: 'X' };
            }

            return { ...vehicleInfo, INTEREST_NV: 'X', INTEREST_CPO: '' };
        }

        default:
            return { INTEREST_NV: 'X', INTEREST_CPO: '', VIN: '', INV_ID: '' };
    }
};

export const getCapValuesFromEvent = (campaignValues: EventApplication['campaignValues'], eventDetails: Event) => {
    const { utmParametersSettings, enableDynamicUtmTracking } = eventDetails;
    const { defaultValue, overrides = [] } = utmParametersSettings;

    if (campaignValues) {
        return {
            campaignId: campaignValues.capCampaignId,
            medium: campaignValues.capLeadOrigin ? getLeadMediumCode(campaignValues.capLeadOrigin) : '',
            leadSource: campaignValues.capLeadSource ? getLeadOriginCode(campaignValues.capLeadSource) : '',
        };
    }

    if (enableDynamicUtmTracking) {
        const target = overrides.find(setting => {
            const parameters = retrieveUTMParameterQueryParameter(setting);

            return (
                parameters?.utm_medium === campaignValues?.utmMedium &&
                parameters?.utm_campaign === campaignValues?.utmCampaign &&
                parameters?.utm_source === campaignValues?.utmSource
            );
        });

        if (target) {
            return {
                campaignId: target.capCampaignId || '',
                medium: target ? getLeadMediumCode(target.capLeadOrigin) : '',
                leadSource: target ? getLeadOriginCode(target.capLeadSource) : '',
            };
        }
    }

    return {
        campaignId: defaultValue?.capCampaignId || '',
        medium: getLeadMediumCode(defaultValue.capLeadOrigin) || '',
        leadSource: getLeadOriginCode(defaultValue.capLeadSource) || '',
    };
};

export const getCompanyName2 = (customerFields: LocalCustomerAggregatedFields) =>
    (customerFields.occupation || '') +
    (customerFields.companyPhoneticName
        ? `${customerFields.occupation ? ', ' : ''}${customerFields.companyPhoneticName}`
        : '');
