import dayjs from 'dayjs';
import { isBoolean, isEmpty, isNaN, isString } from 'lodash/fp';
import { ApplicationKind, CapAvailableApplicationType } from '../../../database/documents/Applications';
import { CapMetadataKind } from '../../../database/documents/CapMetadata';
import { CurrentVehicleEquipmentLine, CurrentVehicleSource } from '../../../database/documents/Customer';
import { Lead } from '../../../database/documents/Lead';
import { EngineType, FinderVehicle, LocalVariant, VehicleKind } from '../../../database/documents/Vehicle';
import getDatabaseContext from '../../../database/getDatabaseContext';
import createLoaders from '../../../loaders';
import { dateFromCapDateFormat, formatCapDate } from './capDateFormat';
import { CapCustomerCurrentVehicle, LeadSubmissionVehicleDetails } from './types';

const vehicleSourceCode = {
    [CurrentVehicleSource.SoldByDealer]: '01',
    [CurrentVehicleSource.VehicleInHouseHold]: '02',
    [CurrentVehicleSource.PreviousOwnedVehicle]: '03',
    [CurrentVehicleSource.PurchaseAlternative]: '04',
    [CurrentVehicleSource.TradeInByDealer]: '05',
};

export const getVehicleSourceCode = (vehicleSource: CurrentVehicleSource) => vehicleSourceCode[vehicleSource] || '';
export const getVehicleSourceByCapCode = (capCode: string): CurrentVehicleSource => {
    const vehicleSource = Object.keys(vehicleSourceCode).find(
        key => vehicleSourceCode[key] === capCode
    ) as CurrentVehicleSource;

    return vehicleSource ?? null;
};

const getVehicleOwnership = (ownership: string) => {
    if (isString(ownership)) {
        return ownership === '2';
    }

    return null;
};

export const getVehicleDetailsFromApplication = async (
    application: CapAvailableApplicationType
): Promise<LeadSubmissionVehicleDetails> => {
    const loaders = createLoaders();

    switch (application.kind) {
        case ApplicationKind.Configurator:
        case ApplicationKind.Standard:
        case ApplicationKind.Event: {
            const vehicleDetails = (await loaders.vehicleById.load(application.vehicleId)) as LocalVariant;

            return {
                modelId: getCapModelId(vehicleDetails),
                modelTypeId: vehicleDetails.identifier,
                condition: 'new',
            };
        }

        case ApplicationKind.Finder: {
            const vehicleDetails = (await loaders.vehicleById.load(application.vehicleId)) as FinderVehicle;

            return {
                modelId: getCapModelId(vehicleDetails),
                modelTypeId: vehicleDetails.listing.id,
                condition: vehicleDetails.listing.vehicle.condition.value !== 'new' ? 'used' : 'new',
            };
        }

        default:
            throw Error('Application is not supported');
    }
};

export const getVehicleDetailsFromLead = async (lead: Lead): Promise<LeadSubmissionVehicleDetails> => {
    const loaders = createLoaders();

    const vehicleDetails = await loaders.vehicleById.load(lead.vehicleId);

    if (vehicleDetails._kind === VehicleKind.LocalVariant) {
        return {
            modelId: getCapModelId(vehicleDetails),
            modelTypeId: vehicleDetails.identifier,
            condition: 'new',
        };
    }

    if (vehicleDetails._kind === VehicleKind.FinderVehicle) {
        return {
            modelId: getCapModelId(vehicleDetails),
            modelTypeId: vehicleDetails.listing.id,
            condition: vehicleDetails.listing.vehicle.condition.value !== 'new' ? 'used' : 'new',
        };
    }

    throw Error('Vehicle is not supported');
};

export const primaryInterestMapping = [
    { model: 'boxter', modelId: 'id_ao1_primint' },
    { model: 'spyder', modelId: 'id_ao1_primint' },
    { model: 'cayman', modelId: 'id_ao2_primint' },
    { model: '911', modelId: 'id_ao3_primint' },
    { model: '992', modelId: 'id_ao3_primint' },
    { model: 'cayenne', modelId: 'id_ao4_primint' },
    { model: 'panamera', modelId: 'id_ao5_primint' },
    { model: 'macan', modelId: 'id_ao8_primint' },
    { model: 'taycan', modelId: 'id_ao9_primint' },
];

export const getInterestByMatchingName = (vehicleName: string) => {
    const primaryInterest = primaryInterestMapping.find(primaryInterest =>
        vehicleName.toLowerCase().includes(primaryInterest.model)
    );

    return primaryInterest?.modelId || vehicleName;
};

export const getCapModelId = (vehicleDetails: LocalVariant | FinderVehicle) => {
    switch (vehicleDetails._kind) {
        case 'finderVehicle': {
            const vehicleName = vehicleDetails.listing?.vehicle?.name?.localize;

            return vehicleName ? getInterestByMatchingName(vehicleName) : '';
        }

        case 'localVariant': {
            const vehicleName = vehicleDetails.name?.defaultValue;

            return vehicleName ? getInterestByMatchingName(vehicleName) : '';
        }

        default:
            return '';
    }
};

const getCapVehicleMake = async (make: string) => {
    const { collections } = await getDatabaseContext();
    const otherVehicleMake = '0099'; // Other car make key of C@P metadata

    if (isEmpty(make)) {
        return otherVehicleMake;
    }
    const vehicleMake = await collections.capMetadata.findOne({
        kind: CapMetadataKind.VehicleMake,
        value: { $regex: make, $options: 'i' },
    });

    return vehicleMake ? vehicleMake.key : otherVehicleMake;
};

const getCapVehicleModel = async (model: string, make?: string) => {
    const { collections } = await getDatabaseContext();

    const otherVehicleModelKey = '99999'; // 99999 is other car model key of C@P metadata

    if (isEmpty(model)) {
        return otherVehicleModelKey;
    }

    const vehicleMakeKey = await getCapVehicleMake(make);

    const vehicleModel = await collections.capMetadata.findOne({
        kind: CapMetadataKind.VehicleMake,
        value: { $regex: model, $options: 'i' },
        makeKey: vehicleMakeKey,
    });

    if (!vehicleModel) {
        const otherVehicleModel = await collections.capMetadata.findOne({
            kind: CapMetadataKind.VehicleModel,
            value: { $regex: 'other', $options: 'i' },
            makeKey: vehicleMakeKey,
        });

        return otherVehicleModel?.key || otherVehicleModelKey;
    }

    return vehicleModel.key;
};

export const getCapVehicle = async (kind: CapMetadataKind, value: string, make?: string) => {
    switch (kind) {
        case CapMetadataKind.VehicleMake: {
            return getCapVehicleMake(value);
        }

        case CapMetadataKind.VehicleModel: {
            return getCapVehicleModel(value, make);
        }

        default:
            throw new Error('C@P metadata kind is not available');
    }
};

export const getCapVehicleNameByKey = async (kind: CapMetadataKind, value: string) => {
    const { collections } = await getDatabaseContext();

    if (kind !== CapMetadataKind.VehicleMake && kind !== CapMetadataKind.VehicleModel) {
        throw new Error('C@P metadata kind is not available');
    }

    const vehicleData = await collections.capMetadata.findOne({
        kind,
        key: value,
    });

    return vehicleData?.value ? vehicleData.value : value;
};

const vehicleEngineTypeCode = {
    [EngineType.Petrol]: 'PE',
    [EngineType.Diesel]: 'DI',
    [EngineType.Hybrid]: 'HY',
    [EngineType.Electric]: 'EL',
};

export const getVehicleEngineTypeCode = (vehicleEngineType: EngineType) =>
    vehicleEngineTypeCode[vehicleEngineType] || '';
export const getVehicleEngineTypeByCapCode = (capCode: string): EngineType => {
    const vehicleEngineType = Object.keys(vehicleEngineTypeCode).find(
        key => vehicleEngineTypeCode[key] === capCode
    ) as EngineType;

    return vehicleEngineType ?? null;
};

const vehicleEquipmentLineCode = {
    [CurrentVehicleEquipmentLine.Basic]: 'BA',
    [CurrentVehicleEquipmentLine.FullyEquipped]: 'FU',
    [CurrentVehicleEquipmentLine.Other]: '99',
    [CurrentVehicleEquipmentLine.Premium]: 'PR',
    [CurrentVehicleEquipmentLine.Sport]: 'SP',
};

export const getVehicleEquipmentLineCode = (vehicleEquipmentLine: CurrentVehicleEquipmentLine) =>
    vehicleEquipmentLineCode[vehicleEquipmentLine] || vehicleEquipmentLineCode[CurrentVehicleEquipmentLine.Basic];
export const getVehicleEquipmentLineByCapCode = (capCode: string): CurrentVehicleEquipmentLine => {
    const vehicleEquipmentLine = Object.keys(vehicleEquipmentLineCode).find(
        key => vehicleEquipmentLineCode[key] === capCode
    ) as CurrentVehicleEquipmentLine;

    return vehicleEquipmentLine ?? null;
};

export const getDefaultVehicleOwnership = (ownership: boolean) => {
    if (isBoolean(ownership)) {
        return ownership ? '2' : '1';
    }

    return '2';
};

export const getVehicleReplacementDate = (vehicleContractEnd: Date, potentialReplacement: Date) => {
    if (potentialReplacement) {
        return formatCapDate(dayjs(potentialReplacement));
    }

    return vehicleContractEnd ? formatCapDate(dayjs(vehicleContractEnd)) : undefined;
};

const canBeParsedToInt = (value: string) => !isNaN(parseInt(value, 10));

export const getCustomerCurrentVehicleFromCap = async (vehicleData: CapCustomerCurrentVehicle) => ({
    vehicleSource: getVehicleSourceByCapCode(vehicleData.CompetitorSourceId),
    ownership: getVehicleOwnership(vehicleData.isCustomerOwner),
    makeId: vehicleData.brandCode,
    make: await getCapVehicleNameByKey(CapMetadataKind.VehicleMake, vehicleData.brandCode),
    modelId: vehicleData.modelCode,
    model: await getCapVehicleNameByKey(CapMetadataKind.VehicleModel, vehicleData.modelCode),
    modelYear:
        !isEmpty(vehicleData.modelCode) && canBeParsedToInt(vehicleData.modelYear)
            ? parseInt(vehicleData.modelYear, 10)
            : undefined,
    equipmentLine: getVehicleEquipmentLineByCapCode(vehicleData.equipmentLineCode),
    engineType: getVehicleEngineTypeByCapCode(vehicleData.engineTypeCode),
    purchaseYear: !isEmpty(vehicleData.purchaseDate)
        ? parseInt(dateFromCapDateFormat(vehicleData.purchaseDate).format('YYYY'), 10)
        : undefined,
    vehicleContractEnd: !isEmpty(vehicleData.vehicleReplacementDate)
        ? dateFromCapDateFormat(vehicleData.vehicleReplacementDate).toDate()
        : undefined,
    potentialReplacement: !isEmpty(vehicleData.vehicleReplacementDate)
        ? dateFromCapDateFormat(vehicleData.vehicleReplacementDate).toDate()
        : undefined,
});
