import * as Sentry from '@sentry/node';
import { RequestHandler } from 'express';
import { isEmpty } from 'lodash';
import { uniqWith } from 'lodash/fp';
import { ObjectId, ReturnDocument } from 'mongodb';
import { Params } from 'react-router';
import urljoin from 'url-join';
import config from '../../../core/config';
import {
    ApplicationKind,
    AuditTrailKind,
    AuthorKind,
    Company,
    CustomerKind,
    EndpointType,
    getKYCPresetsForCustomerModule,
    LaunchpadLead,
    LaunchPadModule,
    LeadCreatedByPorscheRetainAuditTrail,
    LeadStatus,
    LeadUpdatedByPorscheRetainAuditTrail,
    LocalCustomer,
    LocalCustomerField,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    LocalCustomerManagementModule,
    PorscheRetainInfo,
    PorscheRetainModule,
    sortLocalCustomerFields,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getLeadIdentifier } from '../../../database/helpers/leads';
import {
    getAdvancedVersioningBySystemForCreation,
    getAdvancedVersioningForCreation,
    getAuthorFromAuthoring,
    getSimpleVersioningForUpdate,
} from '../../../utils/versioning';
import { decryptThenVerify, signThenEncrypt } from '../shared';
import { RetainNotificationPayload, SuccessResponse } from './types';
import { genFailedResponse, validatePayload } from './validations';

const withBasicScope = (request: Record<string, unknown>) => (callback: (scope: Sentry.Scope) => void) => {
    Sentry.withScope(scope => {
        scope.clearBreadcrumbs();
        scope.setTag('integration', 'Retain');
        scope.setContext('request', request);
        callback(scope);
    });
};

const getCustomerFields = (company: Company, payload: RetainNotificationPayload): LocalCustomerField[] => [
    {
        key: LocalCustomerFieldKey.Title,
        value: payload.customer.title,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.FirstName,
        deterministicString: payload.customer.firstName,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.LastName,
        deterministicString: payload.customer.lastName,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.Email,
        deterministicString: payload.customer.email,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.Phone,
        randomizedPhone: {
            prefix: company?.phone?.prefix || 0,
            value: payload.customer.mobile,
        },
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.Region,
        deterministicString: payload.customer.region,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.City,
        deterministicString: payload.customer.city,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.PostalCode,
        deterministicString: payload.customer.postCode,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.Country,
        value: payload.customer.country,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.Address,
        deterministicString: payload.customer.address,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
    {
        key: LocalCustomerFieldKey.UnitNumber,
        deterministicString: payload.customer.unitNumber,
        source: LocalCustomerFieldSource.PorscheRetain,
    },
];

const getRetainInfo = (payload: RetainNotificationPayload): PorscheRetainInfo => ({
    leadId: payload._id,
    status: payload.status,
    score: payload.score,
    externalIdentifier: payload.externalIdentifier,
    tradeIn: payload.tradeIn,
    isEligible: payload.isEligible,

    vehicleDetails: {
        modelType: payload.modelType,
        modelLine: payload.modelLine,
        vin: payload.vin,
    },

    financingDetails: {
        maturityDate: new Date(payload.maturityDate),
        rv: payload.rv,
        customerRate: payload.customerRate,
        firstPayment: payload.firstPayment,
        firstPaymentDate: new Date(payload.firstPaymentDate),
        remainingLoan: payload.remainingLoan,
        financingStatus: payload.financingStatus,
        contractTerm: payload.contractTerm,
        termRealized: payload.termRealized,
        termRemaining: payload.termRemaining,
        netAmountFinanced: payload.netAmountFinanced,
        deposit: payload.deposit,
        purchasePrice: payload.purchasePrice,
        latestImportDate: new Date(payload.latestImportDate),
    },
});

type Body = string;

const retainNotification: RequestHandler<Params, unknown, Body> = async (req, res, next) => {
    try {
        // Get secrets from module settings
        const { collections } = await getDatabaseContext();
        const moduleId = new ObjectId(req.params.id);

        const settings = await collections.settings.findOne<{
            secrets: { notificationUrl?: string; privateKey?: string; publicCertRetain?: string };
        }>({
            moduleId,
        });

        const secrets = { publicKey: settings?.secrets?.publicCertRetain, privateKey: settings?.secrets?.privateKey };

        if (!secrets.privateKey || !secrets.publicKey) {
            withBasicScope({ url: req.url, params: req.params })(() => {
                Sentry.captureMessage('Retain integration settings are not set up', 'error');
            });

            res.status(500).send(genFailedResponse('general', 'Retain integration settings are not set up'));

            return;
        }

        const data = req.body;

        if (isEmpty(req.body)) {
            withBasicScope({ url: req.url, params: req.params })(() => {
                Sentry.captureMessage('invalid request, body is empty', 'warning');
            });

            const failedResponse = genFailedResponse('general', 'Empty body');
            res.status(400).send(await signThenEncrypt(secrets, JSON.stringify(failedResponse)));

            return;
        }

        const decryptedData = await decryptThenVerify(secrets, data);

        const payload: RetainNotificationPayload = JSON.parse(decryptedData);

        // Validate the payload, If any invalid, return failure response
        const failedResponse = validatePayload(payload);
        if (failedResponse) {
            res.status(400).send(await signThenEncrypt(secrets, JSON.stringify(failedResponse)));

            return;
        }

        // get retain module
        const retainModule = (await collections.modules.findOne({ _id: moduleId })) as PorscheRetainModule;

        // get launchpad module
        const linkedLaunchPadModule = (await collections.modules.findOne({
            _id: retainModule.launchPadModuleId,
        })) as LaunchPadModule;

        const { companyId } = linkedLaunchPadModule;
        const company = await collections.companies.findOne({ _id: companyId });

        // get router and endpoint
        const router = await collections.routers.findOne({
            companyId,
            'endpoints._type': EndpointType.LaunchPadApplicationEntrypoint,
            'endpoints.launchPadApplicationModuleId': linkedLaunchPadModule?._id,
        });

        const endpoint = router?.endpoints.find(
            ep =>
                ep._type === EndpointType.LaunchPadApplicationEntrypoint &&
                ep.launchPadApplicationModuleId.equals(linkedLaunchPadModule?._id)
        );

        const parts = [`${config.protocol}://${router?.hostname}`];
        if (router?.pathname && router?.pathname !== '/') {
            parts.push(router.pathname);
        }
        if (endpoint?.pathname && endpoint?.pathname !== '/') {
            parts.push(endpoint.pathname);
        }
        parts.push('leads');

        const leadDetailUrl = urljoin(parts);

        // get lead base on status
        const status = [LeadStatus.SubmittedToCap, LeadStatus.SubmittedWithError];
        const lead = await collections.leads.findOne({
            status: { $in: status },
            kind: ApplicationKind.Launchpad,
            'capValues.businessPartnerId': payload.customer.id,
            '_versioning.isLatest': true,
            isDraft: false,
            isLead: true,
        });

        if (lead) {
            // update lead
            await collections.leads.findOneAndUpdate(
                {
                    _id: lead._id,
                },
                {
                    $set: {
                        retainInfo: getRetainInfo(payload),

                        date: new Date(),
                        ...getSimpleVersioningForUpdate({ kind: AuthorKind.PorscheRetain, date: new Date() }),
                    },
                },
                { returnDocument: ReturnDocument.AFTER }
            );

            const auditTrail: LeadUpdatedByPorscheRetainAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.LeadUpdatedByPorscheRetain,
                leadId: lead._id,
                leadSuiteId: lead._versioning.suiteId,
                author: getAuthorFromAuthoring({ kind: AuthorKind.PorscheRetain, date: new Date() }),
            };

            await collections.auditTrails.insertOne(auditTrail);

            // get assignee and genrate url to return Retain
            const assignee = await collections.users.findOne({
                _id: lead.assigneeId,
            });

            const url = urljoin(leadDetailUrl, lead._versioning.suiteId.toString());

            const response: SuccessResponse = {
                id: lead._id.toString(),
                assignee: {
                    id: assignee?._id.toString(),
                    name: assignee?.displayName,
                    email: assignee?.email,
                },
                url,
                status: lead.status,
            };

            res.status(200).send(await signThenEncrypt(secrets, JSON.stringify(response)));

            return;
        }

        // handle Lead not found, create new lead
        // get company userGroups
        const userGroups = await collections.userGroups.find({ companyId }).toArray();
        const userIds = userGroups.map(group => group.userIds).flat();

        // get assignee from company userGroups
        const assignee = await collections.users.findOne({
            _id: { $in: userIds },
            email: payload.assigneeEmail,
        });

        if (!assignee) {
            withBasicScope({ url: req.url, params: req.params })(() => {
                Sentry.captureMessage('invalid request, assignee does not exist', 'warning');
            });

            const failedResponse = genFailedResponse('assigneeNotFound', 'Assignee does not exist');
            res.status(400).send(await signThenEncrypt(secrets, JSON.stringify(failedResponse)));

            return;
        }

        // get the customer & vehicle modules
        const customerModule = (await collections.modules.findOne({
            _id: linkedLaunchPadModule.customerModuleId,
        })) as LocalCustomerManagementModule;

        const kycPresets = getKYCPresetsForCustomerModule(customerModule, CustomerKind.Local);

        const customerFields = getCustomerFields(company, payload);

        // create new customer
        const newCustomerId = new ObjectId();
        const customer: LocalCustomer = {
            _id: newCustomerId,
            _kind: CustomerKind.Local,
            moduleId: customerModule._id,
            kycPresetIds: uniqWith(
                (a, b) => a.equals(b),
                kycPresets.map(kyc => kyc._id)
            ),
            fields: sortLocalCustomerFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                kycPresets,
                customerFields
            ),
            isDeleted: false,
            _versioning: getAdvancedVersioningBySystemForCreation(),
        };

        // insert the customer in the database
        await collections.customers.insertOne(customer);

        // get dealer integration
        const { dealerGuid } = payload.customer.responsibleSalesPerson;

        const dealerIntegration = await collections.dealers.findOne({
            companyId,
            'integrationDetails.additionalParameter': {
                $elemMatch: {
                    purpose: 'C@P Dealer Guid',
                    value: dealerGuid,
                },
            },
        });

        let dealerId = dealerIntegration?._id;

        if (!dealerIntegration) {
            // get dealers from userGroups
            const userGroupDealers = userGroups
                .filter(g => g.userIds.some(u => u.equals(assignee._id)))
                .map(u => u.dealerIds)
                .flat();

            [dealerId] = userGroupDealers;
        }

        const leadIdentifier = await getLeadIdentifier(company);

        const versioning = getAdvancedVersioningForCreation(
            { kind: AuthorKind.PorscheRetain, date: new Date() },
            new ObjectId()
        );

        const newContact: LaunchpadLead = {
            _id: new ObjectId(),
            kind: ApplicationKind.Launchpad,
            identifier: leadIdentifier,
            isLead: false,
            customerId: newCustomerId,
            assigneeId: assignee._id,
            dealerId,
            moduleId: linkedLaunchPadModule._id,
            status: LeadStatus.PendingQualify,
            isDraft: false,
            documents: [],
            endpointId: endpoint?._id,
            routerId: router?._id,
            capValues: {
                businessPartnerGuid: payload.customer.guid,
                businessPartnerId: payload.customer.id,
                dealerGuid: payload.customer.responsibleSalesPerson.dealerGuid,
                salesPersonId: payload.customer.responsibleSalesPerson.id,
                salespersonGuid: payload.customer.responsibleSalesPerson.guid,
            },
            tradeInVehicle: [],

            retainInfo: getRetainInfo(payload),

            _versioning: versioning,
        };

        await collections.leads.insertOne(newContact);

        //  audit log
        const leadSubmissionAuditTrail: LeadCreatedByPorscheRetainAuditTrail = {
            _id: new ObjectId(),
            _kind: AuditTrailKind.LeadCreatedByPorscheRetain,
            _date: new Date(),
            leadId: newContact._id,
            leadSuiteId: newContact._versioning.suiteId,
            author: getAuthorFromAuthoring({ kind: AuthorKind.PorscheRetain, date: new Date() }),
        };

        await collections.auditTrails.insertOne(leadSubmissionAuditTrail);

        // generate url to return Retain
        const url = urljoin(leadDetailUrl, newContact._versioning.suiteId.toString());

        const response: SuccessResponse = {
            id: newContact._id.toString(),
            assignee: {
                id: assignee?._id.toString(),
                name: assignee?.displayName,
                email: assignee?.email,
            },
            url,
            status: newContact.status,
        };

        res.status(200).send(await signThenEncrypt(secrets, JSON.stringify(response)));
    } catch (error) {
        withBasicScope({ url: req.url, params: req.params })(() => {
            Sentry.captureException(error);
        });

        res.status(500).send(genFailedResponse('general', 'An unexpected error occurred. Please try again later.'));
    }
};

export default retainNotification;
