import { ApplicationDocumentKind } from '../Applications';

export enum CapSubmissionKind {
    BusinessPartner = 'businessPartner',
    CustomerCompetitorVehicle = 'customerCompetitorVehicle',
    Consent = 'consent',
    Lead = 'lead',
    Activity = 'activity',
    TestDrive = 'testDrive',
}

export type CapValues = {
    businessPartnerGuid?: string;
    businessPartnerId?: string;
    competitorVehicleGuids?: string[];
    leadGuid?: string;
    leadId?: string;
    activityGuid?: string;
    testDriveActivityGuid?: string;
    testDriveId?: string;
    showroomVisitActivityGuid?: string;
    showroomVisitId?: string;
    consentGuids?: string[];
    campaignGuid?: string;
    dealerGuid?: string;
    salesPersonId?: string;
    salespersonGuid?: string;
    submissionSucceeded?: CapSubmissionKind[];
};

export const acceptedDocumentKindForLead = [
    ApplicationDocumentKind.CustomerIdentity,
    ApplicationDocumentKind.OtherAttachment,
    ApplicationDocumentKind.KYCIdentityUpload,
    ApplicationDocumentKind.KYCLicenseUpload,
    ApplicationDocumentKind.KYCPassportUpload,
    ApplicationDocumentKind.KYCOtherDocumentUpload,
];

export enum IntentType {
    ScheduledAppointment = 'ScheduledAppointment',
    WalkIn = 'WalkIn',
}

export enum PurposeOfVisit {
    NewVehicleEnquiryPurchase = 'NewVehicleEnquiryPurchase',
    PreOwnedVehicleEnquiryPurchase = 'PreOwnedVehicleEnquiryPurchase',
    VehicleConfigurationConsultation = 'VehicleConfigurationConsultation',
    InterestInTestDrive = 'InterestInTestDrive',
    VehicleCollectionDelivery = 'VehicleCollectionDelivery',
    BrandExperienceEventAttendance = 'BrandExperienceEventAttendance',
    AccessoriesMerchandiseShopping = 'AccessoriesMerchandiseShopping',
    TradeInEvaluation = 'TradeInEvaluation',
}

export type VehicleDetails = {
    modelType?: string;
    modelLine?: string;
    vin?: string;
};

export type FinancingDetails = {
    maturityDate?: Date;
    rv?: number;
    customerRate?: number;
    firstPayment?: number;
    firstPaymentDate?: Date;
    remainingLoan?: number;
    financingStatus?: string;
    contractTerm?: number;
    termRealized?: number;
    termRemaining?: number;
    netAmountFinanced?: number;
    deposit?: number;
    purchasePrice?: number;
    latestImportDate?: Date;
};

export type PorscheRetainInfo = {
    leadId?: string;
    status?: string;
    score?: number;
    externalIdentifier?: string;
    tradeIn?: number;
    isEligible?: boolean;

    vehicleDetails?: VehicleDetails;

    financingDetails?: FinancingDetails;
};

export enum LeadStatus {
    Drafted = 'drafted',
    Shared = 'shared',
    PendingQualify = 'pendingQualify',
    SubmittedToCap = 'submittedToCap',
    Unqualified = 'unqualified',
    SubmittedWithError = 'submittedWithError',
    SubmissionFailed = 'submissionFailed',
    Lost = 'lost',
    Completed = 'completed',
    SubmittingToCap = 'submittingToCap',
}
