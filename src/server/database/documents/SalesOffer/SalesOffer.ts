import { ObjectId } from 'mongodb';
import { SimpleVersioning } from '../Versioning';
import { SalesOfferDocumentKind } from './enums';
import {
    DepositSalesOffer,
    FinanceSalesOffer,
    InsuranceSalesOffer,
    MainDetailsSalesOffer,
    SalesOfferDocument,
    TradeInSalesOffer,
    VehicleSalesOffer,
    VSASalesOffer,
} from './typings';

export type SalesOffer = {
    _id: ObjectId;
    moduleId: ObjectId;
    mainDetails: MainDetailsSalesOffer;
    vehicle: VehicleSalesOffer;
    tradeIn: TradeInSalesOffer;

    finance: FinanceSalesOffer;
    insurance: InsuranceSalesOffer;
    deposit: DepositSalesOffer;
    vsa: VSASalesOffer;
    vsaSerialNumber: string;

    // record latest feature application suite id
    // when feature is switch off, we should reset it to null
    latestReservationApplicationSuiteId?: ObjectId | null;
    latestFinancingApplicationSuiteId?: ObjectId | null;
    latestInsuranceApplicationSuiteId?: ObjectId | null;

    // launchpad suite ID
    leadSuiteId: ObjectId;
    // this is the feature code for the feature
    porscheCode?: string;

    otherDocuments: SalesOfferDocument<SalesOfferDocumentKind.Others>[];

    _versioning: SimpleVersioning;
};

export default SalesOffer;
