import { uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import createLoaders from '../../loaders';
import {
    Application,
    ApplicationKind,
    ConditionType,
    Event,
    GiftVoucher,
    KYCPreset,
    KycFieldPurpose,
    Lead,
    LocalCustomerFieldKey,
    LocalCustomerManagementModuleKycField,
    Module,
    ModuleType,
} from '../documents';
import {
    ConditionContext,
    buildConditionContextFromApplication,
    buildConditionContextFromGiftVoucher,
    buildConditionContextFromLead,
    isMatchingConditions,
} from './conditions';

// Make sure it is in the same order as in the frontend
// src/app/pages/admin/UpdateKYCPresetPage/KYCFieldDefinitions.ts
// Per AN-2479, definitions will become 2, for JP and non-JP
// Later on AN-2416 or AN-2417, will become only one, with order saved in db instead of here
const baseOrderedKycFields: LocalCustomerFieldKey[] = [
    LocalCustomerFieldKey.Address,
    LocalCustomerFieldKey.UnitNumber,
    LocalCustomerFieldKey.Road,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.District,
    LocalCustomerFieldKey.City,
    LocalCustomerFieldKey.Region,
    LocalCustomerFieldKey.Country,
];

const jpOrderedKycFields: LocalCustomerFieldKey[] = [
    LocalCustomerFieldKey.Country,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.Region,
    LocalCustomerFieldKey.City,
    LocalCustomerFieldKey.District,
    LocalCustomerFieldKey.Address,
    LocalCustomerFieldKey.Road,
    LocalCustomerFieldKey.UnitNumber,
];

export const getOrderedKycFields = (input: LocalCustomerFieldKey[]) => [
    LocalCustomerFieldKey.Title,
    LocalCustomerFieldKey.NonBinaryTitle,
    LocalCustomerFieldKey.Salutation,
    LocalCustomerFieldKey.SalutationBmw,
    LocalCustomerFieldKey.LastNameFront,
    LocalCustomerFieldKey.FirstName,
    LocalCustomerFieldKey.LastName,
    LocalCustomerFieldKey.LastNameJapan,
    LocalCustomerFieldKey.FirstNameJapan,
    LocalCustomerFieldKey.FullName,
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Birthday,
    LocalCustomerFieldKey.Nationality,
    LocalCustomerFieldKey.Citizenship,
    LocalCustomerFieldKey.Race,
    LocalCustomerFieldKey.Gender,
    LocalCustomerFieldKey.NonBinaryGender,
    LocalCustomerFieldKey.MaritalStatus,
    LocalCustomerFieldKey.Education,
    LocalCustomerFieldKey.ResidentialStatus,
    LocalCustomerFieldKey.ResidentialStatusVWFS,
    LocalCustomerFieldKey.ResidenceType,
    // Passport is added dynamically during KYC
    // Make sure it comes alongside with IdentityNumber
    LocalCustomerFieldKey.Passport,
    LocalCustomerFieldKey.IdentityNumber,

    ...input,

    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.TimeOfAddress,
    LocalCustomerFieldKey.AddressType,

    LocalCustomerFieldKey.CorrespondenceAddress,
    LocalCustomerFieldKey.CorrespondenceCity,
    LocalCustomerFieldKey.CorrespondenceDistrict,
    LocalCustomerFieldKey.DeliveryAddress,

    LocalCustomerFieldKey.Emirate,
    LocalCustomerFieldKey.JobTitle,
    LocalCustomerFieldKey.JobTitleTh,
    LocalCustomerFieldKey.Occupation,
    LocalCustomerFieldKey.EmploymentStatus,
    LocalCustomerFieldKey.TimeOfEmployment,
    LocalCustomerFieldKey.CompanyName,
    LocalCustomerFieldKey.CompanyPhoneticName,
    LocalCustomerFieldKey.CompanyCity,
    LocalCustomerFieldKey.CompanyDistrict,
    LocalCustomerFieldKey.CompanyAddress,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.RelationshipWithApplicant,
    LocalCustomerFieldKey.IncomeType,
    LocalCustomerFieldKey.MonthlyIncome,
    LocalCustomerFieldKey.OtherIncome,
    LocalCustomerFieldKey.DateOfJoining,
    LocalCustomerFieldKey.PreferredFirstPaymentDate,
    LocalCustomerFieldKey.SalaryTransferredBankSet,
    LocalCustomerFieldKey.DrivingLicense,
    LocalCustomerFieldKey.DrivingLicenseTh,
    LocalCustomerFieldKey.DrivingLicenseMy,
    LocalCustomerFieldKey.UAEDrivingLicense,
    LocalCustomerFieldKey.UAEIdentitySet,
    LocalCustomerFieldKey.ReferenceDetailSet,
    LocalCustomerFieldKey.CorporateName,
    LocalCustomerFieldKey.CorporateIdentityNumber,
    LocalCustomerFieldKey.CorporateRegistrationDate,
    LocalCustomerFieldKey.CorporateIndustryCategory,
    LocalCustomerFieldKey.CorporateRegisteredCapital,
    LocalCustomerFieldKey.CorporateAnnualRevenue,
    LocalCustomerFieldKey.CorporateNumberOfEmployee,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.NoClaimDiscount,
    LocalCustomerFieldKey.DriverLicensePassDate,
    LocalCustomerFieldKey.UploadIdentity,
    LocalCustomerFieldKey.UploadPassport,
    LocalCustomerFieldKey.UploadDrivingLicense,
    LocalCustomerFieldKey.UploadOtherDocument,
    LocalCustomerFieldKey.PurchaseIntention,
    LocalCustomerFieldKey.Comments,
    LocalCustomerFieldKey.BusinessTitle,
];

export const getOrderedKycFieldsFromCountry = (countryCode: string) => {
    if (countryCode === 'JP') {
        return getOrderedKycFields(jpOrderedKycFields);
    }

    return getOrderedKycFields(baseOrderedKycFields);
};

const getKycPresetsFromEntries = (kycPresets: KYCPreset[], customerKind?: ConditionContext['customerKind']) => {
    const kycpreset = kycPresets.filter(kyc => kyc.isActive);
    const citizenship = kycpreset.some(kyc =>
        kyc.fields.find(field => field.key === LocalCustomerFieldKey.Citizenship)
    );
    const identityNumber = kycpreset.some(kyc =>
        kyc.fields.find(field => field.key === LocalCustomerFieldKey.IdentityNumber)
    );

    return kycpreset.map(kyc => {
        if (citizenship) {
            if (!identityNumber) {
                const citizenshipIndex = kyc.fields.findIndex(field => field.key === LocalCustomerFieldKey.Citizenship);

                kyc.fields.splice(
                    citizenshipIndex + 1,
                    0,
                    // push identity number

                    {
                        isRequired: false,
                        key: LocalCustomerFieldKey.IdentityNumber,
                        purpose: [KycFieldPurpose.KYC],
                    },
                    // push passport
                    {
                        isRequired: false,
                        key: LocalCustomerFieldKey.Passport,
                        purpose: [KycFieldPurpose.KYC],
                    }
                );
            } else {
                // push passport
                const identityNumberIndex = kyc.fields.findIndex(
                    field => field.key === LocalCustomerFieldKey.IdentityNumber
                );

                kyc.fields.splice(identityNumberIndex + 1, 0, {
                    isRequired: false,
                    key: LocalCustomerFieldKey.Passport,
                    purpose: [KycFieldPurpose.KYC],
                });
            }
        }

        return kyc;
    });
};

export const getKYCPresetsForCustomerModule = (
    module: Module,
    customerKind?: ConditionContext['customerKind']
): KYCPreset[] => {
    switch (module._type) {
        case ModuleType.LocalCustomerManagement: {
            return getKycPresetsFromEntries(module.kycPresets, customerKind);
        }

        default:
            return [];
    }
};

export const getCustomerModuleFromApplicationModule = async (applicationModule: Module, loaders = createLoaders()) => {
    switch (applicationModule._type) {
        case ModuleType.StandardApplicationModule:
        case ModuleType.ConfiguratorModule:
        case ModuleType.MobilityModule:
        case ModuleType.FinderApplicationPublicModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.EventApplicationModule:
        case ModuleType.LaunchPadModule: {
            const customerModule = await loaders.customerModuleById.load(applicationModule.customerModuleId);
            if (!customerModule || customerModule._type !== ModuleType.LocalCustomerManagement) {
                // should never happen
                throw new Error('Customer module not found when retrieving it from application module');
            }

            return customerModule;
        }

        default:
            return null;
    }
};

export const getCustomerModuleFromApplication = async (application: Application, loaders = createLoaders()) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Mobility:
        case ApplicationKind.Finder:
        case ApplicationKind.Event:
        case ApplicationKind.Launchpad: {
            const applicationModule = await loaders.moduleById.load(application.moduleId);
            if (!applicationModule) {
                // should never happen
                throw new Error('Application module not found when retrieving it from application');
            }

            return getCustomerModuleFromApplicationModule(applicationModule);
        }

        default: {
            return null;
        }
    }
};

export const getCustomerModuleFromLead = async (lead: Lead, loaders = createLoaders()) => {
    const applicationModule = await loaders.moduleById.load(lead.moduleId);
    if (!applicationModule) {
        // should never happen
        throw new Error('Application module not found when retrieving it from lead');
    }

    return getCustomerModuleFromApplicationModule(applicationModule);
};

export const getKYCPresetsForCustomerModuleId = async (
    moduleId: ObjectId,
    customerKind?: ConditionContext['customerKind'],
    loaders = createLoaders()
): Promise<KYCPreset[]> => {
    if (!moduleId) {
        return [];
    }

    // get the customer module
    const customerModule = await loaders.moduleById.load(moduleId);

    return getKYCPresetsForCustomerModule(customerModule, customerKind);
};

export const getKYCPresetsForEvent = async (event: Event, customerKind: ConditionContext['customerKind']) => {
    const eventKycPresets = event.kycPresets.flatMap<KYCPreset>(kycPreset => {
        const baseFields: KYCPreset['fields'] = [];
        const tradeInFields: KYCPreset['fields'] = [];
        const testDriveFields: KYCPreset['fields'] = [];

        kycPreset.fields.forEach(field => {
            if (
                event.isAllowTradeIn &&
                [
                    LocalCustomerFieldKey.CurrentVehicleSource,
                    LocalCustomerFieldKey.CurrentVehicleOwnership,
                    LocalCustomerFieldKey.CurrentVehicleMake,
                    LocalCustomerFieldKey.CurrentVehicleModel,
                    LocalCustomerFieldKey.CurrentVehicleEquipmentLine,
                    LocalCustomerFieldKey.CurrentVehicleModelYear,
                    LocalCustomerFieldKey.CurrentVehiclePurchaseYear,
                    LocalCustomerFieldKey.CurrentVehicleEngineType,
                    LocalCustomerFieldKey.CurrentVehicleMileage,
                    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
                    LocalCustomerFieldKey.CurrentVehicleContractEnd,
                    LocalCustomerFieldKey.CurrentVehiclePotentialReplacement,
                    LocalCustomerFieldKey.CurrentVehicleVin,
                ].includes(field.key)
            ) {
                tradeInFields.push(field);
            } else if (
                event.isAllowTestDrive &&
                [
                    LocalCustomerFieldKey.DrivingLicense,
                    LocalCustomerFieldKey.DrivingLicenseMy,
                    LocalCustomerFieldKey.DrivingLicenseTh,
                    LocalCustomerFieldKey.UAEDrivingLicense,
                    LocalCustomerFieldKey.UploadDrivingLicense,
                ].includes(field.key)
            ) {
                testDriveFields.push(field);
            } else {
                baseFields.push(field);
            }
        });

        return [
            // Filtered out fields
            { ...kycPreset, fields: baseFields },

            // Below is handler for tradeIn and testDrive fields
            // _id and displayname actually was not really matter
            // because the output of this query is the merged fields
            {
                _id: new ObjectId(),
                displayName: 'Trade In Fields',
                fields: tradeInFields,
                isActive: true,
                conditions: [{ type: ConditionType.IsApplicant }, { type: ConditionType.IsTradeIn }],
            },
            {
                _id: new ObjectId(),
                displayName: 'Test Drive Fields',
                fields: testDriveFields,
                isActive: true,
                conditions: [{ type: ConditionType.IsApplicant }, { type: ConditionType.IsTestDrive }],
            },
        ];
    });

    return getKycPresetsFromEntries(eventKycPresets, customerKind);
};

export const getTestDriveKYCPresetsForApplication = async (application: Application) => {
    const kycPresets =
        application.kind === ApplicationKind.Event
            ? await getKYCPresetsForApplication(application, 'local')
            : (await getKYCPresetsForApplication(application, 'local')).filter(preset =>
                  preset.conditions.some(condition =>
                      condition.type === ConditionType.Or
                          ? condition.children.some(child => child.type === ConditionType.IsTestDriveProcess)
                          : condition.type === ConditionType.IsTestDriveProcess
                  )
              );

    return kycPresets;
};

export const getTestDriveKYCForApplication = async (application: Application, loaders = createLoaders()) => {
    const kycPresets = await getTestDriveKYCPresetsForApplication(application);

    const customerModule = await getCustomerModuleFromApplication(application, loaders);

    return getKYCFieldsFromPresets(
        customerModule.kycFields.sort((a, b) => a.order - b.order),
        kycPresets
    );
};

export const getKYCPresetsForApplication = async (
    application: Application,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
) => {
    // get the application module
    const applicationModule = await loaders.moduleById.load(application.moduleId);

    if (!applicationModule) {
        // should never happen
        throw new Error('Application module not found');
    }

    const initialPresets = await (async (): Promise<KYCPreset[]> => {
        switch (applicationModule._type) {
            case ModuleType.StandardApplicationModule:
            case ModuleType.ConfiguratorModule:
            case ModuleType.MobilityModule:
            case ModuleType.FinderApplicationPublicModule:
            case ModuleType.FinderApplicationPrivateModule:
            case ModuleType.LaunchPadModule:
                return getKYCPresetsForCustomerModuleId(applicationModule.customerModuleId, customerKind, loaders);

            case ModuleType.EventApplicationModule: {
                if (application.kind !== ApplicationKind.Event) {
                    // should never happen
                    throw new Error('Application kind does not match module type');
                }
                const event = await loaders.eventById.load(application.eventId);

                if (!event) {
                    // should never happen
                    throw new Error('Event not found');
                }

                return getKYCPresetsForEvent(event, customerKind);
            }

            default:
                // either not supported or not yet implemented
                return [];
        }
    })();

    // build the context
    const conditionContext = await buildConditionContextFromApplication(application, customerKind, loaders);

    return initialPresets.filter(preset => isMatchingConditions(preset.conditions || [], conditionContext));
};

export const getKYCPresetsForLead = async (lead: Lead, loaders = createLoaders()) => {
    // get the application module
    const applicationModule = await loaders.moduleById.load(lead.moduleId);

    if (!applicationModule) {
        // should never happen
        throw new Error('Application module not found');
    }

    const initialPresets = await (async (): Promise<KYCPreset[]> => {
        switch (applicationModule._type) {
            case ModuleType.StandardApplicationModule:
            case ModuleType.ConfiguratorModule:
            case ModuleType.MobilityModule:
            case ModuleType.FinderApplicationPublicModule:
            case ModuleType.FinderApplicationPrivateModule:
            case ModuleType.LaunchPadModule:
                return getKYCPresetsForCustomerModuleId(applicationModule.customerModuleId, 'local', loaders);

            case ModuleType.EventApplicationModule: {
                if (lead.kind !== ApplicationKind.Event) {
                    // should never happen
                    throw new Error('Lead kind does not match module type');
                }

                const event = await loaders.eventById.load(lead.eventId);

                if (!event) {
                    // should never happen
                    throw new Error('Event not found');
                }

                return getKYCPresetsForEvent(event, 'local');
            }

            default:
                // either not supported or not yet implemented
                return [];
        }
    })();

    // build the context
    const conditionContext = buildConditionContextFromLead(lead);

    return initialPresets.filter(preset => isMatchingConditions(preset.conditions || [], conditionContext));
};

export const getKYCPresetsForGiftVoucher = async (
    giftVoucher: GiftVoucher,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
) => {
    // get the application module
    const giftVoucherModule = await loaders.moduleById.load(giftVoucher.moduleId);

    if (!giftVoucherModule || giftVoucherModule._type !== ModuleType.GiftVoucherModule) {
        // should never happen
        throw new Error('Gift Voucher module not found');
    }

    const initialPresets = await getKYCPresetsForCustomerModuleId(
        giftVoucherModule.customerModuleId,
        customerKind,
        loaders
    );

    // build the context
    const conditionContext = await buildConditionContextFromGiftVoucher(giftVoucher, customerKind);

    return initialPresets.filter(preset => isMatchingConditions(preset.conditions || [], conditionContext));
};

export const getKYCFieldsFromPresets = (
    kycFieldSettings: LocalCustomerManagementModuleKycField[],
    presets: KYCPreset[],
    placement?: KycFieldPurpose
) => {
    const requiredFieldsMap: Record<string, boolean> = {};

    let allFields = presets.flatMap(preset => preset.fields);

    if (placement) {
        allFields = allFields.filter(item => item.purpose.includes(placement));
    }

    // Mark fields as required if ANY preset has them as required
    allFields.forEach(field => {
        if (field.isRequired) {
            requiredFieldsMap[field.key] = true;
        }
    });

    // Create a unique list of fields, keeping the last occurrence of each field key
    // but ensuring we apply our composite logic for isRequired
    const uniqueFields = uniqWith((a, b) => a.key === b.key, allFields).map(field => ({
        ...field,
        // Use our composite logic: if ANY preset required this field, it should be required
        isRequired: requiredFieldsMap[field.key] || field.isRequired,
    }));

    const settings = kycFieldSettings.reduce(
        (acc, setting) => {
            acc[setting.field] = setting.order;

            return acc;
        },
        {} as { [key in LocalCustomerFieldKey]: number }
    );

    return uniqueFields.sort((a, b) => settings[a.key] - settings[b.key]);
};

export const getKycFieldsFromApplication = async (
    application: Application,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
) => {
    const kycPresets = await getKYCPresetsForApplication(application, customerKind, loaders);
    const customerModule = await getCustomerModuleFromApplication(application, loaders);

    return getKYCFieldsFromPresets(
        customerModule.kycFields.sort((a, b) => a.order - b.order),
        kycPresets
    );
};

export const getKycFieldsFromLead = async (lead: Lead, loaders = createLoaders()) => {
    const kycPresets = await getKYCPresetsForLead(lead, loaders);
    const customerModule = await getCustomerModuleFromLead(lead, loaders);

    return getKYCFieldsFromPresets(
        customerModule.kycFields.sort((a, b) => a.order - b.order),
        kycPresets,
        KycFieldPurpose.KYC
    );
};

export const getKYCPresetsForApplicationModule = async (
    applicationModule: Extract<
        Module,
        {
            _type:
                | ModuleType.StandardApplicationModule
                | ModuleType.EventApplicationModule
                | ModuleType.ConfiguratorModule
                | ModuleType.MobilityModule
                | ModuleType.FinderApplicationPublicModule
                | ModuleType.FinderApplicationPrivateModule;
        }
    >,
    loaders = createLoaders()
): Promise<KYCPreset[]> => {
    const initialPresets = await getKYCPresetsForCustomerModuleId(applicationModule.customerModuleId, null, loaders);

    const conditionContext: ConditionContext = {
        applicationModuleId: applicationModule._id,
        customerKind: 'local',
    };

    return initialPresets.filter(preset => isMatchingConditions(preset.conditions || [], conditionContext));
};
