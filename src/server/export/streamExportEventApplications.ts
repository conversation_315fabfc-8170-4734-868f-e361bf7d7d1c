import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { isArray, isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { ApplicationStage as FrontendStage } from '../../app/utilities/getApplicationFileName';
import { RequestLocals } from '../core/express';
import { ApplicationKind, ApplicationStatus, EventApplicationModule } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../permissions';
import { mainQueue } from '../queues';
import { getSessionDataFromRequest } from '../schema/session';
import { uniqueObjectIds } from '../utils/fp';
import { getBEApplicationStage } from './exportApplications';

// Convert string stage to frontend enum for getBEApplicationStage
const convertStringToFrontendStage = (stage: string): FrontendStage | null => {
    switch (stage) {
        case 'Financing':
            return FrontendStage.Financing;
        case 'Lead':
            return FrontendStage.Lead;
        case 'Reservation':
            return FrontendStage.Reservation;
        case 'Mobility':
            return FrontendStage.Mobility;
        case 'Appointment':
            return FrontendStage.Appointment;
        case 'Insurance':
            return FrontendStage.Insurance;
        case 'VisitAppointment':
            return FrontendStage.VisitAppointment;
        case 'TradeIn':
            return FrontendStage.TradeIn;
        default:
            return null;
    }
};

type Param = {
    eventId: string;
};

type ExportRequestBody = {
    dealerIds: string[];
    applicationIds: string[];
    stage: string;
    languageId?: string;
    nonce?: string;
    filename?: string[];
    company?: { countryCode?: string; displayName?: string };
};

type QueryParam = Record<string, never>;

/**
 * Queue a background job to export event applications and send the result via email
 */
const streamExportEventApplications: RequestHandler<
    Param,
    unknown,
    ExportRequestBody,
    QueryParam,
    RequestLocals
> = async (req, res, next) => {
    try {
        const { getPermissionController } = res.locals.context;
        const permissions = await getPermissionController();

        const { eventId: inputEventId } = req.params;

        if (!ObjectId.isValid(inputEventId)) {
            res.status(400).send('Bad request');

            return;
        }

        const {
            dealerIds: queryDealerIds,
            applicationIds: applicationStringIds,
            stage: inputStage,
            languageId,
            filename,
        } = req.body;

        // Validate application ids
        if (!isArray(applicationStringIds) || !applicationStringIds?.length) {
            res.status(400).send('Bad request');

            return;
        }

        const applicationIds = uniqueObjectIds(
            (applicationStringIds ?? []).filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
        );

        // If it's still empty, return 404
        if (applicationIds.length === 0) {
            res.status(404).send('Not found');

            return;
        }

        const frontendStage = convertStringToFrontendStage(inputStage);
        if (!frontendStage) {
            res.status(400).send('Bad request');

            return;
        }
        getBEApplicationStage(frontendStage); // Validate stage

        const { collections } = await getDatabaseContext();

        const eventId = new ObjectId(inputEventId);
        const event = await collections.events.findOne({ _id: eventId });
        if (!event) {
            res.status(404).send('Not found');

            return;
        }

        const userToken = req.headers.Authorization as string;
        const session = await getSessionDataFromRequest(req, userToken);
        const { userId } = session;

        // Get dealer IDs from request
        const dealerIdsInRequest = queryDealerIds?.map(dealerId => new ObjectId(dealerId)) ?? [];

        // Get application permissions
        const applicationPermission = await permissions.applications.getFilterQueryForAction(
            ApplicationPolicyAction.View
        );

        // Get event module
        const eventModule = (await collections.modules.findOne({
            _id: event.moduleId,
        })) as EventApplicationModule;

        if (!eventModule) {
            res.status(404).send('Event module not found');

            return;
        }

        // Check if there are any applications to export
        const totalApplications = await collections.applications.countDocuments({
            $and: [
                applicationPermission,
                {
                    eventId,
                    isDraft: false,
                    status: { $ne: ApplicationStatus.Drafted },
                    dealerId: { $in: dealerIdsInRequest },
                    kind: ApplicationKind.Event,
                    _id: { $in: applicationIds },
                },
            ],
        });

        if (totalApplications === 0) {
            res.status(204).end();

            return;
        }

        // Queue the job with validated data
        await mainQueue.add({
            type: 'processEventApplicationExport',
            userId,
            eventId: inputEventId,
            applicationIds: applicationStringIds,
            dealerIds: queryDealerIds,
            stage: inputStage,
            nonce: req.body.nonce,
            languageId,
            filename,
        });

        res.status(200).send('OK');
    } catch (error) {
        console.error('Error in streamExportEventApplications:', error);

        if (!res.headersSent) {
            res.status(500).send(`Error queuing export job: ${error.message || 'Unknown error'}`);
        } else {
            next(error);
        }
    }
};

export default streamExportEventApplications;
