import dayjs from 'dayjs';
import { RequestHandler } from 'express';
import { isEmpty, isNil, isString, uniqBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { PeriodPayload } from '../../app/api/types';
import { RequestLocals } from '../core/express';
import { ApplicationModule, ModuleType, PasswordConfiguration, getLocalCustomerAggregatedFields } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { isApplicationModule } from '../database/helpers/modules';
import { mainQueue } from '../queues/mainQueue';
import {
    getLatestApplication,
    lookupApplicationModule,
    lookupApplications,
    lookupDealer,
    lookupLatestCustomer,
} from '../schema/resolvers/queries/customers/listCustomersDeprecate';
import { getSessionDataFromRequest } from '../schema/session';
import createI18nInstance from '../utils/createI18nInstance';
import { getFormattedDate } from '../utils/date';
import getSystemConsentSetting from '../utils/excel/applications/system/setting/consent';
import getSystemKycSetting from '../utils/excel/applications/system/setting/kyc';
import { SystemMainSettingKey } from '../utils/excel/applications/system/setting/main';
import {
    consentDataByApplicationVersionIdFromApplicationJourneys,
    journeyByApplicationSuiteIdFromApplications,
} from '../utils/excel/applications/system/utils';
import { uniqueObjectIds } from '../utils/fp';
import { setHeaderColumnsWidth } from './exportApplications/shared';
import { getPassword } from './utils';

type ExportCustomersRequestBody = {
    moduleIds: string[];
    dealerIds: string[];
    period: PeriodPayload;
    nonce?: string;
    languageId?: string;
};

const getApplicationReferencesMax = (customers: any) => {
    let max = 0;
    customers?.forEach(customer => {
        customer?.support?.referenceApplications?.forEach(application => {
            const references = application.stages
                .map(listStage => {
                    if (!isNil(application?.[`${listStage}Stage`])) {
                        return application?.[`${listStage}Stage`]?.identifier;
                    }

                    return null;
                })
                .filter(Boolean);
            if (references?.length > max) {
                max = references?.length;
            }
        });
    });

    return max;
};

const getApplicationReferences = (customers: any) => {
    const max = getApplicationReferencesMax(customers);

    return Array.from({ length: max }).map((_, i) => ({
        key: `${SystemMainSettingKey.Reference}${i + 1}`,
        header: `Application Reference ${i + 1}`,
        getCellValue: ({ support }, { _ }, { key }) => {
            const filter = support.referenceApplications?.flatMap(app => {
                const references = app?.stages
                    .map(listStage => {
                        if (!isNil(app?.[`${listStage}Stage`])) {
                            return app?.[`${listStage}Stage`]?.identifier;
                        }

                        return null;
                    })
                    .filter(Boolean);

                return references;
            });

            return filter[+key.slice(-1) - 1];
        },
    }));
};

const getCreatedAtColumn = () => ({
    key: SystemMainSettingKey.CreatedAt,
    header: 'Date Created',
    getCellValue: ({ _id, _versioning }, { t, timeZonesMap }) => {
        const timeZone = timeZonesMap[_id.toHexString()];

        return getFormattedDate(t, _versioning.createdAt, timeZone);
    },
});

const generateExportExcel = async (applicationModules: ApplicationModule[], customers: any, support: any) => {
    const { t } = support;
    const [, allConsentColumns] = await getSystemConsentSetting(
        t,
        applicationModules
            .map(module => module._type !== ModuleType.SalesOfferModule && module?.agreementsModuleId)
            .filter(Boolean)
    );

    const allKycColumns = uniqBy(
        'key',
        (
            await Promise.all(
                applicationModules.map(
                    module => module._type !== ModuleType.SalesOfferModule && getSystemKycSetting(module, t)
                )
            )
        )
            .filter(Boolean)
            .flatMap(([, allKycColumns]) => allKycColumns)
    );

    const referenceColumns = getApplicationReferences(customers);

    const createdAtColumn = getCreatedAtColumn();

    const settings = [createdAtColumn, ...allKycColumns, ...referenceColumns, ...allConsentColumns];
    const header = [[...settings.filter(Boolean).map(setting => setting.header)]];

    const body = customers
        .map(item =>
            !isEmpty(item)
                ? settings.filter(Boolean).flatMap(setting => setting.getCellValue(item, support, { key: setting.key }))
                : null
        )
        .filter(Boolean);

    const rows = [...header, ...body];
    const sheetName = 'Default Worksheet';

    const workbook = await XlsxPopulate.fromBlankAsync();
    const worksheet = workbook.sheet(0).name(sheetName);
    worksheet.cell('A1').value(rows);
    setHeaderColumnsWidth(worksheet, rows[0]);

    return workbook;
};

const exportCustomerRecords: RequestHandler<unknown, unknown, ExportCustomersRequestBody, {}, RequestLocals> = async (
    req,
    res,
    next
) => {
    const { moduleIds: inputModuleIds, dealerIds: inputDealerIds, period, nonce: inputNonce, languageId } = req.body;

    const { loaders } = res.locals.context;

    // Validate input module and input dealers
    const moduleIds = uniqueObjectIds(
        (inputModuleIds ?? []).filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
    );

    const dealerIds = uniqueObjectIds(
        (inputDealerIds ?? []).filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
    );

    if (isEmpty(moduleIds) || isEmpty(dealerIds) || !period) {
        res.status(400).send('Bad request');

        return;
    }

    const applicationModules = (
        await Promise.all(
            moduleIds.map(moduleId =>
                loaders.moduleById.load(moduleId).then(module => (isApplicationModule(module) ? module : null))
            )
        )
    ).filter(Boolean);

    if (isEmpty(applicationModules)) {
        res.status(404).send('Not found');

        return;
    }

    const { collections } = await getDatabaseContext();

    const companies = await collections.companies
        .find({ _id: { $in: uniqueObjectIds(applicationModules.map(module => module.companyId)) } })
        .toArray();

    // modules passed below are constrained to the same company
    const company = companies?.[0];
    if (!company) {
        res.status(404).send('Not found');

        return;
    }

    const start: Date = period.start ? dayjs(period.start).startOf('day').toDate() : null;
    const end: Date = period.end ? dayjs(period.end).endOf('day').toDate() : null;

    const pipeline = [
        // non deleted customer
        {
            $match: {
                isDeleted: false,
            },
        },
        ...lookupApplications,
        ...getLatestApplication(start, end),
        ...lookupLatestCustomer,
        ...lookupApplicationModule,
        ...lookupDealer,
        // Todo: Add Customer view permission check
        {
            $match: {
                'latestApplication.moduleId': { $in: applicationModules.map(app => app._id) },
                'latestApplication.dealerId': { $in: dealerIds },
            },
        },
        { $sort: { '_versioning.createdAt': -1 } },
    ];

    const results = await collections.customers.aggregate(pipeline).toArray();

    try {
        const language = languageId ? await loaders.languagePackById.load(new ObjectId(languageId)) : null;

        const { i18n } = await createI18nInstance(language?._id?.toHexString() || 'en');

        await i18n.loadNamespaces(['common', 'applicationList', 'auditTrails', 'customerDetails', 'applicationExcel']);
        const { t } = i18n;

        if (!results.length) {
            res.status(404).send('No customer found in the within selected time frame');

            return;
        }

        const customers = (
            await Promise.all(
                results.map(async result => {
                    const customer = getLocalCustomerAggregatedFields(result?.latestCustomer);

                    const referenceApplications = [result?.latestApplication];
                    const journeyMap = await journeyByApplicationSuiteIdFromApplications(
                        referenceApplications,
                        loaders
                    );

                    const consentDataByApplicationSuiteId =
                        await consentDataByApplicationVersionIdFromApplicationJourneys(journeyMap.journeys, loaders);

                    return !isEmpty(customer)
                        ? {
                              ...result?.latestApplication,
                              support: {
                                  customer,
                                  referenceApplications,
                                  consentData:
                                      consentDataByApplicationSuiteId?.[
                                          result?.latestApplication?._versioning.suiteId.toHexString()
                                      ],
                              },
                          }
                        : null;
                })
            )
        ).filter(Boolean);

        const companyTimeZones = await Promise.all(
            applicationModules.map(async module => ({
                [module._id.toHexString()]: (await loaders.companyById.load(module.companyId)).timeZone,
            }))
        );

        const timeZonesMap = results.reduce((acc, result) => {
            const timezone = companyTimeZones?.find(
                timezone => !!timezone[result.latestApplication.moduleId.toHexString()]
            );

            acc[result.latestApplication._id.toHexString()] =
                timezone?.[result.latestApplication.moduleId.toHexString()];

            return acc;
        }, {});

        const support = {
            t,
            timeZonesMap,
        };

        const nonce = inputNonce ?? nanoid();
        const password = await getPassword(nonce);

        const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

        const workbook = await generateExportExcel(applicationModules, customers, support);

        res.set({
            ...(isPasswordProtected ? { 'X-EXCEL-PASSWORD': password } : {}),
            'Content-Disposition': `attachment; filename="Customer_Records.xlsx"`,
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const buffer = await workbook.outputAsync({ ...(isPasswordProtected && { password }) });

        const date = new Date();

        // get user from token
        const userToken = req.headers.Authorization as string;
        const session = await getSessionDataFromRequest(req, userToken);
        const { userId } = session;
        const user = await collections.users.findOne({ _id: userId });

        // Send email to user
        if (isPasswordProtected) {
            await mainQueue.add({
                type: 'sendExportApplicationPasswordEmail',
                password,
                date,
                company,
                applicationType: 'Customers',
                documentType: 'Excel',
                user,
            });
        }

        res.send(buffer);
    } catch (error) {
        next(error);
    }
};

export default exportCustomerRecords;
