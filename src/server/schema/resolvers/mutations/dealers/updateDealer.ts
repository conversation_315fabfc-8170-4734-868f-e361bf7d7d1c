import { Insurer, SystemBank, TranslatedString } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { DealerPolicyAction } from '../../../../permissions';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['updateDealer'] = async (
    root,
    { id, dealerInput },
    { getUser, getPermissionController }
) => {
    const user = await getUser();
    const simpleVersioning = await getSimpleVersioningByUserForUpdate(user._id);
    const permissionController = await getPermissionController();

    if (!permissionController.dealers.hasPolicyForAction(DealerPolicyAction.Update)) {
        throw new InvalidPermission();
    }

    const hasIntegrationPermission = permissionController.dealers.hasPolicyForAction(
        DealerPolicyAction.UpdateIntegration
    );

    const { collections } = await getDatabaseContext();

    const dealer = await collections.dealers.findOne({ _id: id });
    if (!dealer) {
        throw new Error('Dealer ID Invalid');
    }

    const {
        contact,
        disclaimers: { financingDisclaimer, insuranceDisclaimer },
        integrationDetails,
        limitFeature,
        ...othersDealerInput
    } = dealerInput;

    // Update bank's financing disclaimer
    const bankIds = financingDisclaimer.map(disclaimer => disclaimer.bankId);

    const currentBankDetails = (await collections.banks
        .find({ _id: { $in: bankIds } }, { projection: { _id: 1, financingDisclaimer: 1 } })
        .toArray()) as SystemBank[];

    currentBankDetails.forEach(current => {
        const updatedDisclaimer = financingDisclaimer.find(disclaimer => disclaimer.bankId.equals(current._id))
            .financingDisclaimer as TranslatedString;
        const bankFinancingDisclaimer = current.financingDisclaimer.overrides;
        const disclaimerIndex = bankFinancingDisclaimer.findIndex(disclaimer => disclaimer.dealerId.equals(id));
        if (disclaimerIndex >= 0) {
            bankFinancingDisclaimer[disclaimerIndex].value = [updatedDisclaimer];
        } else {
            bankFinancingDisclaimer.push({ dealerId: id, value: [updatedDisclaimer] });
        }

        collections.banks.findOneAndUpdate(
            { _id: current._id },
            { $set: { 'financingDisclaimer.overrides': bankFinancingDisclaimer } }
        );
    });

    // Update insurer's insurance disclaimer
    const insurerIds = insuranceDisclaimer.map(disclaimer => disclaimer.insurerId);

    const currentInsurerDetails = (await collections.insurers
        .find({ _id: { $in: insurerIds } }, { projection: { _id: 1, insuranceDisclaimer: 1 } })
        .toArray()) as Insurer[];

    currentInsurerDetails.forEach(current => {
        const updatedDisclaimer = insuranceDisclaimer.find(disclaimer => disclaimer.insurerId.equals(current._id))
            .insuranceDisclaimer as TranslatedString;
        const currentInsuranceDisclaimer = current.insuranceDisclaimer.overrides;
        const disclaimerIndex = currentInsuranceDisclaimer.findIndex(disclaimer => disclaimer.dealerId.equals(id));
        if (disclaimerIndex >= 0) {
            currentInsuranceDisclaimer[disclaimerIndex].value = [updatedDisclaimer];
        } else {
            currentInsuranceDisclaimer.push({ dealerId: id, value: [updatedDisclaimer] });
        }

        collections.insurers.findOneAndUpdate(
            { _id: current._id },
            { $set: { 'insuranceDisclaimer.overrides': currentInsuranceDisclaimer } }
        );
    });

    const dealerContact = { socialMedia: dealer.contact.socialMedia, ...contact };

    const hasRootPermission = permissionController.hasRootPermission();
    const company = await collections.companies.findOne({ _id: dealer.companyId });

    const { _id: dealerId, companyId, limitFeature: currentLimitFeature } = dealer;
    const shouldUpdateDealerUserAccess =
        hasRootPermission && company.allowLimitDealerFeature && limitFeature && limitFeature !== currentLimitFeature;

    if (shouldUpdateDealerUserAccess) {
        // get all userIds from userGroups related to the dealer
        const groupUserIds = await collections.userGroups
            .aggregate([
                { $match: { companyId, dealerIds: dealerId } },
                { $unwind: '$userIds' },
                { $group: { _id: null, userIds: { $addToSet: '$userIds' } } },
                { $project: { _id: 0, userIds: 1 } },
            ])
            .toArray();

        const userIds = groupUserIds[0]?.userIds || [];

        // remove dealer assignment from userGroups
        await collections.userGroups.updateMany({ companyId, dealerIds: dealerId }, { $pull: { dealerIds: dealerId } });

        if (userIds.length > 0) {
            const checks = await Promise.all(
                userIds.map(async userId => {
                    // check if user is assigned to any dealer (not only the current company)
                    const hasAnyDealer = await collections.userGroups.findOne({
                        userIds: userId,
                        dealerIds: { $exists: true, $not: { $size: 0 } }, // dealerIds is not empty
                    });

                    return !hasAnyDealer ? userId : null;
                })
            );

            const userIdsToBeInactivated = checks.filter(Boolean);

            // inactivate users that have no dealer assignment
            if (userIdsToBeInactivated.length > 0) {
                await collections.users.updateMany(
                    { _id: { $in: userIdsToBeInactivated } },
                    { $set: { isActive: false } }
                );
            }
        }
    }

    return collections.dealers.findOneAndUpdate(
        {
            $and: [
                { _id: id, isDeleted: false },
                permissionController.dealers.getFilterQueryForAction(DealerPolicyAction.Update),
            ],
        },
        {
            $set: {
                ...othersDealerInput,
                ...(hasIntegrationPermission ? { integrationDetails } : {}),
                contact: dealerContact,
                ...simpleVersioning,
                ...(hasRootPermission && company.allowLimitDealerFeature && { limitFeature }),
            },
        },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutation);
