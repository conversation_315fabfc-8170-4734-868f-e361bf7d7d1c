import { isNil, uniq, uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationJourneyKYCKind,
    ApplicationKYCReceivedAuditTrail,
    ApplicationModule,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    ConsentsAndDeclarationsPurpose,
    CustomerKind,
    Guarantor,
    KYCPreset,
    KycFieldPurpose,
    LegacyApplication,
    LocalCustomerField,
    LocalCustomerFieldKey,
    LocalCustomerManagementModuleKycField,
    ModuleType,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createNewCustomerVersion,
    getKYCFieldsFromPresets,
    getKYCPresetsForApplication,
    getLocalCustomerAggregatedFields,
} from '../../database/helpers';
import { getApplicationLogStages } from '../../utils/application';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../utils/versioning';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';
import ApplicantAgreementsStep from './ApplicantAgreementsStep';

export type GuarantorKYCStepPayload = {
    fields: LocalCustomerField[];
    saveDraft: boolean;
};

class GuarantorKYCStep<
    Application extends LegacyApplication,
    TApplicationModule extends ApplicationModule,
> extends JourneyStep<JourneyContext<Application, TApplicationModule>, GuarantorKYCStepPayload> {
    constructor(context: JourneyContext<Application, TApplicationModule>) {
        super(context, 'guarantor-kyc');
    }

    get isFinalized(): boolean {
        return this.context.journey.guarantorKYC?.completed || false;
    }

    async initialize(): Promise<void> {
        if (this.context.skipCompletedSteps) {
            if (this.isFinalized) {
                return this.next.initialize();
            }

            await this.context.updateApplicationStatusFromUser(ApplicationStatus.GuarantorDetailsPendingUpdate);
        }

        return super.initialize();
    }

    async finalize(): Promise<void> {
        await this.context.assignApplicationIdentifier();

        const { journey } = this.context;

        // synchronize applicant id on application with customer id on journey as finale
        await this.context.updateApplicationCustomer(journey.guarantorKYC.customerId, 'guarantor');

        return super.finalize();
    }

    // Finalize when draft customer data
    async finalizeDraft(): Promise<void> {
        await this.context.assignApplicationIdentifier();

        const { journey } = this.context;

        // synchronize applicant id on application with customer id on journey as finale
        return this.context.updateApplicationCustomer(journey.guarantorKYC.customerId, 'guarantor');
    }

    static async validateInputFields(
        kycFieldSettings: LocalCustomerManagementModuleKycField[],
        kycPresets: KYCPreset[],
        newCustomer: Guarantor
    ): Promise<Boolean> {
        // extract fields from kycPresets
        const allFields = getKYCFieldsFromPresets(kycFieldSettings, kycPresets);
        // extract LocalCustomerFieldKey which are mandatory
        const requiredFields: LocalCustomerFieldKey[] = uniq(
            allFields
                .filter(field => {
                    // trade in vehicle validation will be done when store it in application
                    if (
                        [
                            LocalCustomerFieldKey.CurrentVehicleSource,
                            LocalCustomerFieldKey.CurrentVehicleOwnership,
                            LocalCustomerFieldKey.CurrentVehicleMake,
                            LocalCustomerFieldKey.CurrentVehicleModel,
                            LocalCustomerFieldKey.CurrentVehicleEquipmentLine,
                            LocalCustomerFieldKey.CurrentVehicleModelYear,
                            LocalCustomerFieldKey.CurrentVehiclePurchaseYear,
                            LocalCustomerFieldKey.CurrentVehicleEngineType,
                            LocalCustomerFieldKey.CurrentVehicleMileage,
                            LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
                            LocalCustomerFieldKey.CurrentVehicleContractEnd,
                            LocalCustomerFieldKey.CurrentVehiclePotentialReplacement,
                            LocalCustomerFieldKey.CurrentVehicleVin,
                        ].includes(field.key)
                    ) {
                        return false;
                    }

                    return field.isRequired === true && field.purpose.includes(KycFieldPurpose.KYC);
                })
                .map(field => field.key)
        );

        // get aggreatedFields
        const aggreatedFields = getLocalCustomerAggregatedFields(newCustomer);

        // validate fields
        return !requiredFields.some(key => isNil(aggreatedFields[key]));
    }

    async updateGuarantor(payload: GuarantorKYCStepPayload): Promise<void> {
        // get initial customer
        const { collections } = await getDatabaseContext();
        const { application, journey, applicationModule, user, loaders } = this.context;

        if (applicationModule._type === ModuleType.SalesOfferModule) {
            throw new Error('Invalid application module type for guarantor KYC step');
        }
        const { fields: payloadFields, saveDraft } = payload;
        // right now we only have 1 guarantor, application should support multiple guarantor

        const customerModule = await loaders.customerModuleById.load(applicationModule.customerModuleId);

        const kycPresets = await getKYCPresetsForApplication(application, 'guarantor');

        kycPresets.forEach(kyc => {
            if (kyc.fields.some(field => field.key === LocalCustomerFieldKey.UAEDrivingLicense)) {
                kyc.fields.push({
                    isRequired: true,
                    key: LocalCustomerFieldKey.Birthday,
                    purpose: [KycFieldPurpose.KYC],
                });
            }

            const citizenship = kyc.fields.some(field => field.key === LocalCustomerFieldKey.Citizenship);
            const identityNumber = kyc.fields.some(field => field.key === LocalCustomerFieldKey.IdentityNumber);

            if (citizenship) {
                kyc.fields.push({
                    isRequired: false,
                    key: LocalCustomerFieldKey.Passport,
                    purpose: [KycFieldPurpose.KYC],
                });

                if (!identityNumber) {
                    kyc.fields.push({
                        isRequired: false,
                        key: LocalCustomerFieldKey.IdentityNumber,
                        purpose: [KycFieldPurpose.KYC],
                    });
                }
            }
        });
        const applicationKycIds = kycPresets.map(kyc => kyc._id);
        const mergedKycPresetIds: ObjectId[] = applicationKycIds;
        const newCustomerId = new ObjectId();

        // build up versioning
        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: application.applicantId };

        // create updated customer document
        const newCustomer: Guarantor = {
            _id: newCustomerId,
            _kind: CustomerKind.Guarantor,
            moduleId: applicationModule.customerModuleId,
            kycPresetIds: uniqWith((a, b) => a.equals(b), mergedKycPresetIds),
            fields: payloadFields,
            isDeleted: false,
            _versioning: getAdvancedVersioningForCreation(authoring),
        };

        if (!saveDraft) {
            // validate mandatory fields
            const isValid = await GuarantorKYCStep.validateInputFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),

                kycPresets,
                newCustomer
            );

            if (!isValid) {
                throw new Error('Invalid Inputs');
            }
        }

        await createNewCustomerVersion(newCustomer);

        await this.context.updateJourney({
            guarantorKYC: {
                moduleId: newCustomer.moduleId,
                customerId: newCustomer._id,
                completedAt: newCustomer._versioning.updatedAt,
                completed: !saveDraft,
                type: ApplicationJourneyKYCKind.GuarantorCustomer,
                kycIds: applicationKycIds,
            },
        });

        if (
            !ApplicantAgreementsStep.checkAgreementsCompletion(this.context.journey.guarantorAgreements, [
                ConsentsAndDeclarationsPurpose.KYC,
            ])
        ) {
            // we are still pending for agreements
            return;
        }

        if (!journey.guarantorKYC?.completed) {
            // prepare proper stage
            const stages = this.context.isApplyNewSubmission
                ? [
                      this.context.isApplyNewSubmissionForFinancing && ApplicationStage.Financing,
                      this.context.isApplyNewSubmissionForInsurance && ApplicationStage.Insurance,
                  ].filter(Boolean)
                : null;

            if (!saveDraft) {
                await this.context.updateApplicationStatus(
                    ApplicationStatus.GuarantorDetailsReceived,
                    authoring,
                    stages
                );
            }

            const kind = AuditTrailKind.ApplicationKYCReceived;
            // generate the trail
            const trail: ApplicationKYCReceivedAuditTrail = {
                _id: new ObjectId(),
                _kind: kind,
                _date: newCustomer._versioning.updatedAt,
                customerId: newCustomer._id,
                customerSuiteID: newCustomer._versioning.suiteId,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                author: getAuthorFromAuthoring(authoring),
                stages: getApplicationLogStages(application, kind, stages),
            };

            // register the trail
            await collections.auditTrails.insertOne(trail);
        }
    }

    async execute(payload: GuarantorKYCStepPayload): Promise<void> {
        await this.updateGuarantor(payload);

        if (payload.saveDraft) {
            return this.finalizeDraft();
        }

        return this.finalize();
    }
}

export default GuarantorKYCStep;
